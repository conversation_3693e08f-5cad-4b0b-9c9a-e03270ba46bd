#!/bin/bash
set -e
export LANG=en_US.UTF-8

# define vars
TEST_SERVERS=(na-test-dramato-server-1)
PROD_SERVERS=(na-prod-dramato-server-16 na-prod-dramato-server-17 na-prod-freereels-server-3)
TEST_DIR="/home/<USER>/www_manage"
PROD_DIR="/home/<USER>/www_manage"

# check params
if [[ $# != 1 ]]; then
  echo "Usage: $0 [env:test|prod]"
  exit 0
fi

# get base path
CURR_DIR=$(dirname "$0")
OUTPUT_DIR="$CURR_DIR/dist"

# parse parameters
ENV_NAME=$(echo "$1" | tr '[:upper:]' '[:lower:]')

if [[ "$ENV_NAME" == "prod" ]]; then
  SERVERS=("${PROD_SERVERS[@]}")
  DIR=$PROD_DIR
else
  SERVERS=("${TEST_SERVERS[@]}")
  DIR=$TEST_DIR
fi

pwd
ls -la

for SERVER in "${SERVERS[@]}"; do
  echo "----------------------------------"
  echo "Deploying to $SERVER"
  echo "----------------------------------"
  if [[ "$SERVER" == "${SERVERS[0]}" && "$ENV_NAME" == "prod" ]]; then
    ssh worker@$SERVER "rm -rf $DIR/dist; mkdir -p $DIR"
    rsync -avz $OUTPUT_DIR/ worker@$SERVER:$DIR/dist/
    echo "ossutil sync --update $DIR/dist oss://us-dramato-prod/frontend_static_admin/"
    ssh worker@$SERVER "ossutil sync --update $DIR/dist oss://us-dramato-prod/frontend_static_admin/"
  fi

  ssh worker@$SERVER "mkdir -p $DIR"
  rsync -avz $OUTPUT_DIR/ worker@$SERVER:$DIR
done

set +e
echo 'success'
