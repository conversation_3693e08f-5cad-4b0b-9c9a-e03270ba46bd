#!/bin/sh

REPO=https://git.tianmai.cn/fe/dramato-admin-web.git
JENKINS_SUFFIX="dramato-admin"

# 定义需要过滤的参数数组
optional_params="--force --no-build"

# 初始化变量
FORCE=false
NO_BUILD=false

# 检查所有参数中是否包含可选参数
for arg in "$@"; do
  case "$arg" in
    --force)
    FORCE=true
      ;;
    --no-build)
      NO_BUILD=true
      ;;
  esac
done

# 过滤掉可选参数，重新构建参数列表
filtered_args=""
for arg in "$@"; do
  skip=false
  for param in $optional_params; do
    if [ "$arg" = "$param" ]; then
      skip=true
      break
    fi
  done

  if [ "$skip" = false ]; then
    if [ -z "$filtered_args" ]; then
      filtered_args="$arg"
    else
      filtered_args="$filtered_args $arg"
    fi
  fi
done


# 重新设置位置参数
set -- $filtered_args

# 现在可以正确赋值 MODE 和 MESSAGE
MODE=$1
MESSAGE=$2

function info() {
  echo -e "\033[1;32m🍺 $1\033[0m"
}
function error() {
  echo -e "\033[1;31m🚨 $1\033[0m" >&2
}
function warn() {
  echo -e "\033[1;33m🚧 $1\033[0m"
}
function clean() {
  rm -rf $TEMP
}

# 如果 MODE 为 prod，则 JENKINS_PREFIX 为 prod；否则为 test
if [ "$MODE" == 'prod' ]; then
  JENKINS_PREFIX="prod-frontend/job/prod-"
  BRANCH=main
else
  JENKINS_PREFIX="test-frontend/job/test-"
  BRANCH=test
fi

# 获取 HOME 目录，兼容 linux 和 macOS
HOME_DIR="${HOME:-~}"
CURRENT_DIR=$(pwd)
PROJECT_DIR=$(git rev-parse --show-toplevel)
RELATIVE_PATH=${CURRENT_DIR#$PROJECT_DIR/}
SUB_PROJECT_NAME=$(echo $RELATIVE_PATH | awk -F'/' '{print $NF}')
CURRENT_REMOTE=$(git remote get-url origin)
TEMP="${HOME_DIR}/.kunlun_fe_deploy/$SUB_PROJECT_NAME-${MODE}"
JENKINS_JOB="${JENKINS_PREFIX}${JENKINS_SUFFIX}"
