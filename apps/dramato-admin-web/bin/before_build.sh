#!/bin/bash
set -e
export LANG=en_US.UTF-8

MODE=$1

APP_NAME="短剧管理后台"
COMMITTER_NAME=$(git log -1 --pretty=format:'%an')
COMMIT_HASH=$(git log -1 --pretty=format:'%h')
COMMIT_MESSAGE=$(git log -1 --pretty=format:'%s')
TIME=$(TZ='Asia/Shanghai' date "+%Y-%m-%d %H:%M:%S")
AUTH="yinghang.fang:110e0d2f54734f8b257a0427c7e6d1f1b5"
JOB_PREFIX="${MODE}-frontend/job"
ls -la
sh ./build.sh $MODE && {
  curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$APP_NAME 部署成功 \n环境: $MODE \n代号: $JOB_BASE_NAME #$BUILD_NUMBER \n\n内容: $COMMIT_MESSAGE \n\n提交者: $COMMITTER_NAME \n构建时间: $TIME\n\"}}" "https://open.feishu.cn/open-apis/bot/v2/hook/b66567cc-3d82-4b30-b4ad-273f2060c2ee"
} || {
  curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$APP_NAME 部署失败 \n环境: $MODE \n代号: $JOB_BASE_NAME #$BUILD_NUMBER \n\n内容: $COMMIT_MESSAGE\n\n提交者: $COMMITTER_NAME \n构建时间: $TIME\noutput 查看命令: curl -v 'http://$<EMAIL>:8080/job/$JOB_PREFIX/$JOB_BASE_NAME/$BUILD_NUMBER/consoleText'\"}}" "https://open.feishu.cn/open-apis/bot/v2/hook/b66567cc-3d82-4b30-b4ad-273f2060c2ee"
  false
}

set +e
echo 'success'
