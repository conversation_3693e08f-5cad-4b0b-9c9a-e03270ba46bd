/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { showFailToast } from '@skynet/ui'
import { get_k_sso_token } from './device-id.ts'

import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig,
} from 'axios'
import axios from 'axios'
import { cloneDeep, get, set } from 'lodash-es'
import router from 'src/router.tsx'

let confirmed = false
const lang = 'zh-CN'
export interface ConstructParams extends CreateAxiosDefaults {
  /**
   * 用来判断 response 是不是错误
   * @example
   * ```ts
   * isError: (response: AxiosResponse) => response.data.code !== 200
   * ```
   */
  isError?: (response: AxiosResponse) => boolean | Record<string, JsonValue> | undefined
  /**
   * 用来获取业务数据
   * 建议尽量获取全面的数据，不要只获取 data 而忽略 code
   * @example
   * ```ts
   * getData: (response: AxiosResponse) => response.data // 不推荐 response.data.data
   * ```
   */
  getData?: <T>(response: AxiosResponse<T>) => T
  /**
   * 请求拦截器
   */
  requestInterceptor?: [
    ((config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
  /**
   * 响应拦截器
   */
  responseInterceptor?: [
    ((response: AxiosResponse) => AxiosResponse) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
}
export class HttpClient {
  instance: AxiosInstance
  isError: ConstructParams['isError']
  getData: Exclude<ConstructParams['getData'], undefined> = response => response.data
  requestInterceptor: ConstructParams['requestInterceptor']
  responseInterceptor: ConstructParams['responseInterceptor']
  constructor(params: ConstructParams) {
    const { isError, getData, requestInterceptor, responseInterceptor, ...rest } = params
    this.instance = axios.create(rest)
    this.isError = isError
    this.getData = getData || this.getData
    this.requestInterceptor = requestInterceptor
    this.responseInterceptor = responseInterceptor
    this.intercept()
  }

  private intercept() {
    this.instance.interceptors.response.use((response: AxiosResponse) => {
      const error = this.isError ? this.isError(response) : false
      if (error === true) {
        return Promise.reject({ response })
      }
      if (error instanceof Object) {
        return Promise.reject(error)
      }
      return response
    })
    if (this.requestInterceptor) {
      this.instance.interceptors.request.use(...this.requestInterceptor)
    }
    if (this.responseInterceptor) {
      this.instance.interceptors.response.use(...this.responseInterceptor)
    }
    this.instance.interceptors.response.use(null, (err: AxiosError) => {
      if ('response' in err) {
        const response = err.response as AxiosResponse
        if (response.data.err_msg?.indexOf('code: 404') >= 0) {
          response.data.code = 404
        } else if (response.data.code === 40004 || response.data.code === 400) {
          showFailToast(response.data.message)
        }
      }
      const data = err.response?.data as { code: number } | undefined
      if (data?.code === 401) {
        if (confirmed) { return }
        const yes = window.confirm('登录过期，或是用户名密码错误')
        if (!yes) {
          confirmed = false
          return
        }
        if (yes) {
          confirmed = true
          void router.push({
            path: '/login',
            query: {
              returnTo: window.location.pathname + window.location.search,
            },
          })
        }
      }
      return Promise.reject(err)
    })
  }

  /**
   * 发送 GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.get<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  get<T = unknown>(
    url: string,
    params?: HttpRequestConfig['params'],
    config = {} as Omit<HttpRequestConfig, 'params'>,
  ) {
    return this._request<T>({ url, params, ...config, method: 'GET' })
  }

  /**
   * 发送 POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.post<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  post<T = unknown>(url: string, data?: any, config: HttpRequestConfig = {}) {
    return this._request<T>({ url, data, ...config, method: 'POST' })
  }

  /**
   * 发送 PUT 请求
   */
  put<T = unknown>(url: string, data?: JsonValue, config: HttpRequestConfig = {}) {
    return this._request<T>({ url, data, ...config, method: 'PUT' })
  }

  /**
   * 发送 DELETE 请求
   */
  delete<T = unknown>(
    url: string,
    params?: HttpRequestConfig['params'],
    config = {} as Omit<HttpRequestConfig, 'params'>,
  ) {
    return this._request<T>({ url, params, ...config })
  }

  _request<T = unknown>(config: HttpRequestConfig) {
    if (config.transformRequestData) {
      const data = cloneDeep(config.data)
      Object.entries(config.transformRequestData).forEach(([key, fnList]) => {
        fnList.forEach(fn => {
          try {
            const value = get(data, key)
            set(data, key, fn(value, data))
          } catch (error) {
            console.error('transformRequestData error:', error)
          }
        })
      })
      config.data = data
    }
    return this.instance.request<T>(config).then(this.getData).then(data => {
      if (config.transformResponseData) {
        const obj = cloneDeep(data) as Record<string, unknown>
        Object.entries(config.transformResponseData).forEach(([key, fnList]) => {
          fnList.forEach(fn => {
            try {
              const value = get(obj, key)
              set(obj, key, fn(value, obj))
            } catch (error) {
              console.error('transformResponseData error:', error)
            }
          })
        })
        return obj as typeof data
      }
      return data
    })
  }
}

export type HttpRequestConfig = AxiosRequestConfig & {
  transformRequestData?: Record<string, Array<(value: any, data: any) => any>>
  transformResponseData?: Record<string, Array<(value: any, data: any) => any>>
}

const commonRequestInterceptor: HttpClient['requestInterceptor'] = [
  config => {
    Object.assign(config.headers, {
      device: 'Web',
      token: get_k_sso_token(),
    })
    return config
  },
  null,
]

const commonParams: ConstructParams = {
  baseURL: import.meta.env.VITE_DRAMA_API_URL,
  timeout: 60000,
  withCredentials: true,
  isError: response => ![200, 404, 0].includes(response.data.code),
  getData: response => response.data,
  requestInterceptor: commonRequestInterceptor,
}

/**
 * 如果以下配置不满足你的需求，你可以修改，或者创建其他 httpClient
 */
export const httpClient = new HttpClient(commonParams)

export const keepError = (fn?: (err: unknown) => void) => (err: unknown) => {
  fn?.(err)
  throw err
}
