import { ZodErrorMap, ZodIssueCode, ZodParsedType, util } from 'zod'

// 这些代码来自 https://github.com/colinhacks/zod/blob/master/src/locales/en.ts
// 由 AI 自动把英文翻译成了中文
export const errorMap: ZodErrorMap = (issue, _ctx) => {
  let message: string
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      if (issue.received === ZodParsedType.undefined) {
        message = '必填'
      } else {
        message = `期望的是 ${issue.expected}，但接收到的是 ${issue.received}`
      }
      break
    case ZodIssueCode.invalid_literal:
      message = `无效的字面量值，期望的是 ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`
      break
    case ZodIssueCode.unrecognized_keys:
      message = `对象中包含未识别的键：${util.joinValues(issue.keys, ', ')}`
      break
    case ZodIssueCode.invalid_union:
      message = '输入无效'
      break
    case ZodIssueCode.invalid_union_discriminator:
      message = `无效的鉴别器值。期望的是 ${util.joinValues(issue.options)}`
      break
    case ZodIssueCode.invalid_enum_value:
      message = `无效的枚举值。期望的是 ${util.joinValues(issue.options)}，但接收到的是 '${issue.received}'`
      break
    case ZodIssueCode.invalid_arguments:
      message = '函数参数无效'
      break
    case ZodIssueCode.invalid_return_type:
      message = '函数返回类型无效'
      break
    case ZodIssueCode.invalid_date:
      message = '日期无效'
      break
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === 'object') {
        if ('includes' in issue.validation) {
          message = `无效输入：必须包含 "${issue.validation.includes}"`
          if (typeof issue.validation.position === 'number') {
            message = `${message}，且位置必须大于或等于 ${issue.validation.position}`
          }
        } else if ('startsWith' in issue.validation) {
          message = `无效输入：必须以 "${issue.validation.startsWith}" 开头`
        } else if ('endsWith' in issue.validation) {
          message = `无效输入：必须以 "${issue.validation.endsWith}" 结尾`
        } else {
          util.assertNever(issue.validation)
        }
      } else if (issue.validation !== 'regex') {
        message = `无效的 ${issue.validation}`
      } else {
        message = '无效'
      }
      break
    case ZodIssueCode.too_small:
      // eslint-disable-next-line no-case-declarations
      const minMessage = issue.exact ? '恰好' : issue.inclusive ? '至少' : '超过'
      if (issue.type === 'array') message = `数组的长度必须${minMessage} ${issue.minimum} 个元素`
      else if (issue.type === 'string') message = `字符串的长度必须${minMessage} ${issue.minimum} 个字符`
      else if (issue.type === 'number') message = `数字必须${minMessage} ${issue.minimum}`
      else if (issue.type === 'date') message = `日期必须${minMessage} ${new Date(Number(issue.minimum))}`
      else message = '输入无效'
      break
    case ZodIssueCode.too_big:
      // eslint-disable-next-line no-case-declarations
      const maxMessage = issue.exact ? '恰好' : issue.inclusive ? '最多' : '少于'
      if (issue.type === 'array') message = `数组的长度必须${maxMessage} ${issue.maximum} 个元素`
      else if (issue.type === 'string') message = `字符串的长度必须${maxMessage} ${issue.maximum} 个字符`
      else if (issue.type === 'number') message = `数字必须${maxMessage} ${issue.maximum}`
      else if (issue.type === 'bigint') message = `BigInt 数值必须${maxMessage} ${issue.maximum}`
      else if (issue.type === 'date') message = `日期必须${maxMessage} ${new Date(Number(issue.maximum))}`
      else message = '输入无效'
      break
    case ZodIssueCode.custom:
      message = '输入无效'
      break
    case ZodIssueCode.invalid_intersection_types:
      message = '交集结果无法合并'
      break
    case ZodIssueCode.not_multiple_of:
      message = `数字必须是 ${issue.multipleOf} 的倍数`
      break
    case ZodIssueCode.not_finite:
      message = '数字必须是有限的'
      break
    default:
      message = _ctx.defaultError
      util.assertNever(issue)
  }
  return { message }
}
