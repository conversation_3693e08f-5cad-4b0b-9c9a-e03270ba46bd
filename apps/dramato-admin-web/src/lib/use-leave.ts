/* eslint-disable @typescript-eslint/no-explicit-any */
import { reactive } from 'vue'
import { useRouter } from 'vue-router'

const state = reactive({
  isActive: false, // 是否启用监听
  message: '确定要离开吗？',
})

// 单例模式：确保只添加一次事件监听器
let removeRouteGuard: (() => void) | null = null

export function useGlobalLeaveWarning() {
  const router = useRouter()

  const handleBeforeUnload = (e: any) => {
    if (state.isActive) {
      e.preventDefault()
      e.returnValue = state.message
    }
  }

  const start = () => {
    if (!state.isActive) {
      state.isActive = true

      // 添加关闭浏览器提示
      window.addEventListener('beforeunload', handleBeforeUnload)

      // 添加路由跳转提示
      removeRouteGuard = router.beforeEach((to, from, next) => {
        if (state.isActive && !confirm(state.message)) {
          next(false) // 阻止跳转
        } else {
          // 跳转出去就需要重新出发start
          stop()
          next() // 允许跳转
        }
      })
    }
  }

  const stop = () => {
    if (state.isActive) {
      state.isActive = false

      // 移除关闭浏览器提示
      window.removeEventListener('beforeunload', handleBeforeUnload)

      // 移除路由跳转提示
      if (removeRouteGuard) {
        removeRouteGuard()
        removeRouteGuard = null
      }
    }
  }

  return {
    start,
    stop,
    state,
  }
}
