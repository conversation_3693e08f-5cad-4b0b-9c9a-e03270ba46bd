/* eslint-disable @typescript-eslint/no-explicit-any */
import { loadResource } from './dynamic-load'
import { ref } from 'vue'

declare const require: {
  config: (options: { paths: { [key: string]: string } }) => Promise<any>
  (modules: string[], callback: (...args: []) => void): void
}
const monacoLoading = ref(false)
let monacoEditorPromise: Promise<string> | null = null

export const loadMonacoEditor = async () => {
  if (monacoEditorPromise) {
    // If Monaco Editor is already loading or loaded, return the existing promise
    return monacoEditorPromise
  }

  monacoEditorPromise = new Promise(async (resolve, reject) => {
    try {
      // Dynamically load Monaco Editor's loader script
      await loadResource('https://static-v1.mydramawave.com/frontend_static/monaco-editor-0.52.2/min/vs/loader.js', 'js')
      const requirePath = 'https://static-v1.mydramawave.com/frontend_static/monaco-editor-0.52.2/min/vs'
      monacoLoading.value = true
      // Configure Monaco Editor's path
      await require.config({ paths: { vs: requirePath } })

      // Load Monaco Editor main module
      require(['vs/editor/editor.main'], () => {
        resolve('Monaco editor loaded successfully!')
        monacoLoading.value = false
      })
    } catch (error) {
      reject('Failed to load Monaco editor')
    }
  })

  return monacoEditorPromise
}

export const useLoadMonaco = () => {
  return {
    monacoLoading,
    loadMonacoEditor,
  }
}
