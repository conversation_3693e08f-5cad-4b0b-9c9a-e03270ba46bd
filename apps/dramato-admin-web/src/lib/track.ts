import { report } from '@skynet/client-track'
import { tryParse<PERSON>son } from '@skynet/shared'

const user = tryParse<PERSON>son<{ user_id?: string, userName?: string, displayName?: string }>(localStorage.getItem('userInfo'), {})

export const track = (origin: string, entity: string, event: 'show' | 'click', extra?: object) => {
  report({
    event: `${origin}_${entity}_${event}`,
    device_hash: window.navigator.userAgent,
    app_version: 'web',
    user_id: user.userName ?? 'unknown',
    event_info: JSON.stringify({
      origin,
      entity,
      event,
      user_name: user.displayName ?? 'unknown',
      ...extra,
    }),
  })
}
