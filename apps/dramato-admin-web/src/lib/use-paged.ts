import { omit } from 'lodash-es'
import { ref } from 'vue'

type UsePagedParams<T extends object, Q = Record<string, unknown>> = {
  initialPage?: number
  initialPageSize?: number
  initialQuery?: Q
  fetch: (p: { page?: number, page_size?: number } & Q) => Promise<ApiPagedResponse<T>>
}
const defaultPageSize = 20
export const usePaged = <T extends object, Q = Record<string, unknown> >(params: UsePagedParams<T, Q>) => {
  const loading = ref(false)
  const page = ref(params.initialPage ?? 1)
  const pageSize = ref(params.initialPageSize ?? defaultPageSize)
  const query = ref(params.initialQuery ?? {})
  const items = ref<T[]>([])
  const total = ref(0)
  function doFetch(p?: { page?: number, page_size?: number } & Q) {
    loading.value = true
    const _query = omit(p, ['page', 'page_size'])
    if (Object.keys(_query).length !== 0) query.value = _query
    const s = Object.assign({ page: params.initialPage || 1, page_size: params.initialPageSize || defaultPageSize }, p)
    return params.fetch(s)
      .then(response => {
        if (!response.data) return Promise.reject(new Error('No data returned from API'))
        items.value = response.data.items
        page.value = p?.page || 1
        pageSize.value = p?.page_size ?? response.data.page_info.size ?? pageSize.value
        total.value = response.data.page_info.total
      })
      .finally(() => setTimeout(() => loading.value = false))
  }
  return {
    loading,
    page,
    pageSize,
    items,
    total,
    query,
    fetch: doFetch,
  }
}
