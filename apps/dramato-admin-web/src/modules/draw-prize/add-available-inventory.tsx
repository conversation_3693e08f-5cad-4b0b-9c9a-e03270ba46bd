import { createComponent, fn, required, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
import { z } from 'zod'
import { usePrizeList } from './use-prize-list'
type AddAvailableInventoryOptions = {
  props: {
    prizeId: M.Prize['id']
  }
  emits: {
    beforeSave: (prizeId: M.Prize['id'], inventory: number) => void
    afterSave: (prizeId: M.Prize['id'], inventory: number) => void
  }
}
export const AddAvailableInventory = createComponent<AddAvailableInventoryOptions>({
  props: {
    prizeId: required,
  },
  emits: {
    beforeSave: fn,
    afterSave: fn,
  },
}, (props, { emit }) => {
  const { addAvailableInventory } = usePrizeList()
  const formData = ref({ inventory: 0 })
  const Form = CreateForm<{ inventory: number }>()
  const rules = z.object({
    inventory: z.number().int().min(1, '库存必须为正整数'),
  })
  const { error, validateAll } = useValidator(formData, rules)
  return () => (
    <x-add-available-inventory class="block">
      <Form
        error={error.value}
        class="flex flex-col"
        data={formData.value}
        onChange={(path, value) => set(formData.value, path, value)}
        onSubmit={onSubmit}
        items={[
          ['库存', 'inventory', { type: 'number' }, { transform: transformInteger }],
        ]}
        actions={<Button class="btn-primary btn btn-sm" type="submit">提交</Button>}
      />
    </x-add-available-inventory>
  )

  async function onSubmit() {
    if (!validateAll()) return
    emit('beforeSave', props.prizeId, formData.value.inventory)
    await addAvailableInventory(props.prizeId, formData.value.inventory)
    emit('afterSave', props.prizeId, formData.value.inventory)
  }
})

export default AddAvailableInventory
