namespace M {
  /**
   * 奖品
   */
  type Prize = {
    id: number
    round_number: number
    prize_name: string
    prize_image: string
    prize_level: number
    price: number
    /**
     * 奖品类型 :1.金币 2.会员(单位:天) 3.实物
     */
    prize_type: number
    prize_num: number
    weight: number
    inventory: number
    available_inventory: number
    warning_threshold: number
    is_infinite: number
    status: number
    supply_status: number
    updated: number
    operator: string
  }
  /**
   * 中奖记录
   */
  type PrizeRecord = {
    id: number
    user_id: number
    prize_name: string
    prize_type: number
    prize_num: number
    prize_level: number
    won_at: number
    delivery_status: string
    tracking_info?: TrackingInfo
  }
  type TrackingInfo = {
    tracking_status: string
    tracking_no: string
    tracking_company: string
    tracking_url: string
  }
}

namespace Api {
  namespace DrawPrize {

  }
}
