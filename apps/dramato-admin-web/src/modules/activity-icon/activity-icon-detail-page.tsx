import { createComponent, mc } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { Wrapper } from 'src/layouts/wrapper'
import { ActivityIconForm } from './components/activity-icon-form'
import { ActivityH5Form } from './components/activity-h5-form'
import { ActivityScheduleForm } from './components/activity-schedule-form'
import { useActivityIconStore } from './use-activity-icon-store'
import { useGlobalLeaveWarning } from 'src/lib/use-leave'

type ActivityIconDetailPageOptions = {
  props: {}
}
export const ActivityIconDetailPage = createComponent<ActivityIconDetailPageOptions>({
  props: {},
}, props => {
  const { currentActivityIcon, getCommodityList, getActivityIconById, btnList } = useActivityIconStore()
  const route = useRoute()
  const router = useRouter()
  const step = ref(0)
  btnList.value = []
  const { start } = useGlobalLeaveWarning()
  start()
  const setStep = (stepNum: number) => {
    if (stepNum === step.value) return
    step.value = stepNum
  }

  onMounted(async () => {
    if (route.params.id) {
      await getActivityIconById({
        event_id: +route.params.id,
      })
    } else {
      if (!currentActivityIcon.value.app_id) {
        void router.replace('/activity-icon')
        return
      }
    }
    void getCommodityList()
  })

  return () => (
    <div class="pt-4">
      <div class="text-sm breadcrumbs">
        <ul>
          <li><RouterLink to="/activity-icon">运营活动页管理</RouterLink></li>
          <li>{route.params.id ? '编辑活动页面' : '新建活动页面' }</li>
        </ul>
      </div>
      <Wrapper>
        <div class="flex">
          <aside class="w-[220px]">
            <ul class="steps steps-vertical">
              <li class={mc('step', 'step-primary')}>活动素材</li>
              <li class={mc('step', step.value > 0 ? 'step-primary' : '')}>落地页</li>
              <li class={mc('step', step.value > 1 ? 'step-primary' : '')}>任务计划</li>
            </ul>
          </aside>
          <div class="flex-1">
            {
              step.value === 0
                ? <ActivityIconForm onStep={setStep} />
                : step.value === 1 ? <ActivityH5Form onStep={setStep} /> : <ActivityScheduleForm onStep={setStep} />
            }
          </div>
        </div>
      </Wrapper>
    </div>
  )
})

export default ActivityIconDetailPage
