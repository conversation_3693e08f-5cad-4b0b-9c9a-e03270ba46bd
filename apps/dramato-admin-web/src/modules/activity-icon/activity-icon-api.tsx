import { httpClient } from 'src/lib/http-client'

export const apiGetActivityIconList = (data: M.IActivityIconSearchProps) => httpClient.post<ApiResponse<{
  list: M.IActivityIcon[]
  total: number
}>>('/active/event/list', data)

export const apiChangeActivityIconStatus = (data: {
  event_id: number
  status: number
}) => httpClient.post<ApiResponse<null>>('/active/event/update', data)

export const apiGetActivityIconById = (data: {
  event_id: number
}) => httpClient.post<ApiResponse<M.IActivityIconDetail>>('/active/event/get', data, {
  transformResponseData: {
    'data.whitelist_users': [(array: number[]) => {
      console.log('array:', array)
      return array?.join?.(',') ?? array
    }],
  },
})

export const apiUpdateActivityIcon = (data: M.IActivityIconDetail) => httpClient.post<ApiResponse<{
  data: {
    event_id: number
  }
}>>('/active/event/save', data, {
  transformRequestData: {
    whitelist_users: [(str?: string) => {
      return str?.split?.(',').map(item => parseInt(item.trim())).filter(item => !Number.isNaN(item)) ?? str
    }],
  },
})
