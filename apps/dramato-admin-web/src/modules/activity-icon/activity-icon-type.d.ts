declare namespace M {
  interface IActivityIconSearchProps {
    app_id?: number
    app_name?: string
    language_version_code?: string
    event_id?: number
    status?: number
    strategy_layer_id?: number[]
    page_info: {
      page_index: number
      page_size: number// 选填参数
    }
  }

  interface IActivityIcon {
    event_id: number
    event_name: string
    status: number // 状态: -1 全部 0 未上架 1 上架
    material_content: string
    created: string
    updated: string
    created_by: string
    updated_by: string
    strategy_layer_names: string
    app_id: number
    app_name: string
    language_version_code: string
  }

  interface IActivityButton {
    index?: number // 序号
    button_id?: number
    target_id?: number
    target_type?: number // 类型:1商品 2链接
    target_content?: string
    target_content_arr?: number[]
    show_content?: string
  }

  // 用于提交或者校验
  interface IActivityIconForm {
    id?: string
    event_name?: string
    app_id?: number
    app_name?: string
    language_version_code?: string

    status?: number // 状态:0-未上架,1-在线上,-1-已删除
    duration_days?: number // 活动持续天数,-1表示不限

    auto_online?: number // 是否自动上架1是0否
    auto_online_time?: number // 自动上架时间

    float_icon_enabled?: number // 是否启用悬浮图标
    float_icon_url: string // 悬浮图片url
    float_icon_pages: string // 悬浮图标展示页面,逗号分隔:1-首页,2-reward,3-store
    h5_url: string // h5页面地址
    h5_button_list?: IActivityButton[]
    strategy_conf?: {
      condition: number // 条件下发开启
      whitelist: number // 白名单下发开启
    }
    whitelist_users?: number[]
  }

  interface IActivityIconDetail {
    event_id?: number
    event_name?: string
    app_id?: number
    app_name?: string
    language_version_code?: string

    status?: number // 状态:0-未上架,1-在线上,-1-已删除
    duration_days?: number // 活动持续天数,-1表示不限

    auto_online?: number // 是否自动上架1是0否
    auto_online_bool?: boolean
    auto_online_time?: number // 自动上架时间
    auto_online_time_display?: string

    float_icon_enabled?: number // 是否启用悬浮图标
    float_icon_url?: string // 悬浮图片url

    float_icon_pages?: string // 悬浮图标展示页面,逗号分隔:1-首页,2-reward,3-store
    float_icon_pages_arr?: number[] // 悬浮图标展示页面 [1,2]

    h5_url?: string // h5页面地址
    h5_button_list?: IActivityButton[]
    strategy_conf?: {
      condition: number // 条件下发开启
      whitelist: number // 白名单下发开启
      strategy_id?: number // 策略id
      strategy_layer_ids?: number[] // 策略层级id
    }
    strategy_conf_arr?: number[] // 1 condition  2 whitelist

    whitelist_users_str?: string
    whitelist_users?: number[]
    created?: number // 创建时间
    updated?: number // 更新时间
    created_by?: string // 创建人
    updated_by?: string // 修改人
  }
}
