/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiGetActivityIconList, apiGetActivityIconById } from './activity-icon-api'
import { set } from 'lodash-es'
import { apiGetMemberList } from 'src/modules/member/member.api'
import { apiGetRechargeLevels } from 'src/modules/recharge-level/recharge-level-api'
import dayjs from 'dayjs'

const searchForm = ref<M.IActivityIconSearchProps>({
  app_id: 0,
  language_version_code: '',
  event_id: undefined,
  status: -1,
  page_info: {
    page_index: 1,
    page_size: 20, // 选填参数
  },
})
const list = ref<M.IActivityIcon[]>([])
const total = ref<number>(0)
const loading = ref(false)
const commodityList = ref<any[]>([])

// 详情需要先设置
const currentActivityIcon = ref<M.IActivityIconDetail>({})
const btnList = ref<M.IActivityButton[]>([])

const setBtnList = (list: M.IActivityButton[]) => {
  btnList.value = list
}

const addBtnToList = (btn: M.IActivityButton) => {
  btnList.value.push(btn)
}

const updateBtnList = (index: number, btn: M.IActivityButton) => {
  btnList.value.splice(index, 1, btn)
}

const setCurrentActivityIconByPath = (path: string, value: string | number | string[] | number[] | M.IActivityButton[]) => {
  set(currentActivityIcon.value, path, value)
}

const getActivityIconById = async (data: {
  event_id: number
}) => {
  const rs = await apiGetActivityIconById(data)
  currentActivityIcon.value = {
    ...rs.data,
    float_icon_pages_arr: rs.data?.float_icon_pages?.split(',').map(s => +s) || [],
    app_id: rs.data?.app_id,
    app_name: rs.data?.app_name,
  }

  btnList.value = (rs.data?.h5_button_list || []).map(row => {
    const targetType = row.target_type
    let target_content = row.target_content
    if (targetType === 1) {
      target_content = '' + row.target_id
    }
    return {
      ...row,
      target_content_arr: row.target_id ? [row.target_id] : [],
      show_content: row.target_content,
      target_content: target_content,
    }
  }) as M.IActivityButton[] || []

  searchForm.value.app_id = currentActivityIcon.value.app_id
  searchForm.value.app_name = currentActivityIcon.value.app_name
}

const getList = async () => {
  loading.value = true
  const rs = await apiGetActivityIconList(searchForm.value).finally(() => {
    loading.value = false
  })

  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
}

const onSearch = (isFirst?: boolean) => {
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 20,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearch()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearch()
}

const onReset = () => {
  searchForm.value.event_id = undefined
  searchForm.value.status = -1
  searchForm.value.strategy_layer_id = undefined
  onSearch(true)
}

const getCommodityList = async () => {
  commodityList.value = []
  const memberRes = await apiGetMemberList({
    app_id: '' + searchForm.value.app_id,
    page_size: 1000,
    status: 0,
    next: '',
  })
  const rechargeRef = await apiGetRechargeLevels({
    app_id: '' + searchForm.value.app_id,
    page_size: 1000,
    status: 0,
    next: '',
  })
  commodityList.value = commodityList.value.concat(memberRes.data?.items || []).concat(rechargeRef.data?.items || [])
}

export const useActivityIconStore = () => {
  return {
    searchForm,
    list,
    total,
    loading,
    getActivityIconById,
    currentActivityIcon,
    setCurrentActivityIconByPath,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    btnList,
    addBtnToList,
    setBtnList,
    updateBtnList,

    commodityList,
    getCommodityList,
  }
}
