/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, TableColumnOld, CreateTableOld, DateTime, CreateForm, Pager, transformNumber, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, watch } from 'vue'
import { useActivityIconStore } from './use-activity-icon-store'
import { useAppAndLangOptions } from '../options/use-app-options'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { useRouter } from 'vue-router'
import { apiChangeActivityIconStatus } from './activity-icon-api'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
type ActivityIconPageOptions = {
  props: {}
}

export const ActivityIconPage = createComponent<ActivityIconPageOptions>({
  props: {},
}, props => {
  const {
    searchForm,
    list,
    total,
    loading,
    currentActivityIcon,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
  } = useActivityIconStore()
  const { appOptions, languageOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: onSearch,
  })
  const router = useRouter()

  watch(() => searchForm.value.app_id, id => {
    searchForm.value.language_version_code = ''
    searchForm.value.app_name = appOptions.value.find(row => row.value === id)?.label
  })

  const Form = CreateForm<M.IActivityIconSearchProps>()
  const Table = CreateTableOld<M.IActivityIcon>()

  const {
    list: strategyLayerList,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  console.log('languageOptions', languageOptions)

  const columns: TableColumnOld<M.IActivityIcon>[] = [
    ['活动ID', 'event_id', { class: 'w-[100px]' }],
    ['活动页名称(Active Events)', 'event_name', { class: 'w-[200px]' }],
    ['用户分层', 'strategy_layer_names', { class: 'w-[200px]' }],
    ['使用状态', row => {
      return row.status === 1 ? <div class="badge badge-outline badge-primary">上架</div> : <div class="badge badge-ghost badge-primary">下架</div>
    }, { class: 'w-[100px]' }],
    ['应用ID', 'app_id', { class: 'w-[100px]' }],
    ['应用名称', 'app_name', { class: 'w-[200px]' }],
    ['语言', row => languageOptions.value.find(item => item.value === row.language_version_code)?.label || '-', { class: 'w-[100px]' }],
    ['素材类型', 'material_content', { class: 'w-[100px]' }],
    ['创建时间', row => <DateTime value={(+row?.created || 0) * 1000} />, { class: 'w-[150px]' }],
    ['创建人', 'created_by', { class: 'w-[130px]' }],
    ['更新时间', row => <DateTime value={(+row?.updated || 0) * 1000} />, { class: 'w-[150px]' }],
    ['修改人', 'updated_by', { class: 'w-[130px]' }],
    [<span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-link btn-sm" onClick={() => {
            const hideDialog = openDialog({
              title: '提示',
              mainClass: 'pb-0 px-5',
              body: (
                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-status-body>是否【{row.status === 1 ? '下架' : '上架'}】当前【{row.event_name}】</x-status-body>
                  <x-status-footer class="flex justify-end gap-x-[10px] w-full">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={async () => {
                      try {
                        await apiChangeActivityIconStatus({
                          event_id: row.event_id,
                          status: row.status === 1 ? 0 : 1,
                        })
                        onSearch()
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      } finally {
                        hideDialog()
                      }
                    }}
                    >确定
                    </button>
                  </x-status-footer>
                </x-status-confirm-dialog>
              ),
            })
          }}
          >{
              row.status === 0 ? '上架' : '下架'
            }
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => {
            currentActivityIcon.value = {
              language_version_code: searchForm.value.language_version_code,
              event_id: row.event_id || 0,
            }
            void router.push(`/activity-icon-detail/${row.event_id}`)
          }}
          >修改
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => {
            if (row.status === 1) {
              showFailToast('上架状态无法删除，请先下架')
              return
            } else {
              const hideDialog = openDialog({
                title: '提示',
                mainClass: 'pb-0 px-5',
                body: () => (
                  <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-status-body>是否删除【{row.event_name}】</x-status-body>
                    <x-status-footer class="flex justify-end gap-x-[10px] w-full">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={async () => {
                        try {
                          await apiChangeActivityIconStatus({
                            event_id: row.event_id,
                            status: -1,
                          })
                          onSearch()
                          showSuccessToast('操作成功')
                        } catch (error: any) {
                          showFailToast(error.response.data.message || '操作失败')
                        } finally {
                          hideDialog()
                        }
                      }}
                      >确定
                      </button>
                    </x-status-footer>
                  </x-status-confirm-dialog>
                ),
              })
            }
          }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[180px]' }],
  ]

  const onAddActivity = () => {
    currentActivityIcon.value = {
      app_id: searchForm.value.app_id,
      app_name: searchForm.value.app_name,
      language_version_code: searchForm.value.language_version_code,
      strategy_conf_arr: [1],
    }
    void router.push('/activity-icon-detail')
  }

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>运营活动页管理</li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              [
                requiredLabel('应用'),
                'app_id',
                {
                  type: 'select',
                  options: [{ label: '全部', value: 0 }, ...appOptions.value],
                  autoInsertEmptyOption: false,
                },
                {
                  transform: transformNumber,
                }],
              [
                requiredLabel('语言'),
                'language_version_code',
                {
                  type: 'select',
                  options: [{ label: '全部', value: '' }, ...languageOptions.value],
                  autoInsertEmptyOption: false,
                },
              ],
              [
                '活动页ID',
                'event_id',
                {
                  type: 'text',
                  placeholder: '请输入活动页ID',
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '活动页名称',
                'event_name',
                {
                  type: 'text',
                  placeholder: '请输入活动页名称',
                },
              ],
              [
                '状态',
                'status',
                {
                  type: 'select',
                  options: [{
                    value: -1,
                    label: '全部',
                  }, {
                    value: 1,
                    label: '上架',
                  }, {
                    value: 0,
                    label: '下架',
                  }],
                  autoInsertEmptyOption: false,
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '分层画像',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-end items-center">
            <Button class="btn btn-primary btn-sm" onClick={onAddActivity}>+ 新建活动页</Button>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={searchForm.value.page_info.page_index} v-model:size={searchForm.value.page_info.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ActivityIconPage
