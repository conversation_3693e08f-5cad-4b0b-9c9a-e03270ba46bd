/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, Button, Input } from '@skynet/ui'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'

import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { ref, watch } from 'vue'
import { set } from 'lodash-es'
import { useActivityIconStore } from '../use-activity-icon-store'

type ActivityBtnFormOptions = {
  props: {
    curBtn: M.IActivityButton
    index?: number
  }
  emits: {
    close: () => void
  }
}

export const ActivityBtnForm = createComponent<ActivityBtnFormOptions>({
  props: {
    curBtn: {
      target_content: '',
    },
    // -1 则是新增
    index: -1,
  },
  emits: {
    close: () => {},
  },
}, (props, { emit }) => {
  const {
    addBtnToList,
    updateBtnList,
    commodityList,
    currentActivityIcon
  } = useActivityIconStore()
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const Form = CreateForm<M.IActivityButton>()
  const form = ref<M.IActivityButton>({
    target_content: '',
  })
  const formRules = z.object({
    target_type: z.number().min(0, {
      message: '请输入指向类型',
    }),
    target_content: z.string().min(1, {
      message: '请输入指向目标',
    }),
  })
  const { error, validateAll } = useValidator(form, formRules)

  const onSave = () => {
    if (!validateAll()) return
    if (form.value.target_type !== 2) {
      const item: { id: number, title: string } = commodityList.value.find(row => row.id === form.value.target_content_arr![0])
      form.value.target_content =  form.value?.target_content_arr && form.value?.target_content_arr[0] ? '' + form.value.target_content_arr[0] : ''
      form.value.show_content = `${item.id},${item.title},${currentActivityIcon.value.app_name}`
    }
    if (props.index > -1) {
      updateBtnList(props.index, form.value)
    } else {
      addBtnToList(form.value)
    }
    emit('close')
  }

  watch(() => props.curBtn, () => {
    form.value = {
      ...props.curBtn,
    }
  }, {
    immediate: true,
  })

  return () => (
    <>
      <Form
        class="flex flex-col"
        data={form.value}
        onChange={(path, value) => {
          if (path === 'target_type') {
            form.value.target_content = ''
            form.value.target_content_arr = []
          }
          set(form.value, path, value)
        }}
        hasAction={false}
        error={error.value}
        items={[
          [
            requiredLabel('按钮项'),
            'button_id',
            {
              disabled: true,
              type: 'custom',
              render: ({ item, value }) => <div>button{value}</div>
            },
          ],
          [
            requiredLabel('指向类型'),
            'target_type',
            {
              type: 'radio',
              options: [{
                value: 1,
                label: '商品',
              }, {
                value: 2,
                label: '链接',
              }],
            },
          ],
          [
            requiredLabel('指向目标'),
            'target_content',
            {
              type: 'custom',
              render: () => (
                <div>
                  {
                    form.value.target_type === 2

                      ? (
                          <div class="flex items-center gap-1 input-bordered w-full h-8 input">
                            <Input type="text" inputClass="w-[480px]" modelValue={form.value.target_content} onUpdate:modelValue={e => form.value.target_content = e as string} placeholder="请输入deeplink地址" />
                          </div>
                        )
                      : (
                          <div class="flex space-x-4">
                            <FormMultiSelect
                              class="w-[500px]"
                              maxlength={1}
                              search={true}
                              popoverWrapperClass="z-popover-in-dialog"
                              options={commodityList.value.map((n, index) => {
                                return { value: n.id, label: `${n.id},${n.title},${currentActivityIcon.value.app_name}` }
                              })}
                              modelValue={form.value.target_content_arr}
                              onUpdate:modelValue={e => {
                                form.value.target_content_arr = e as number[]
                                form.value.target_content = e.join(',')
                              }}
                            />
                          </div>
                        )
                  }
                </div>
              ),
            }, {
              class: 'w-full'
            },
          ],
        ]}
      />
      <div class="flex justify-end gap-x-2">
        <Button class="btn btn-sm" onClick={() => {
          emit('close')
        }}
        >取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          onSave()
        }}
        >保存
        </Button>
      </div>
    </>
  )
})
