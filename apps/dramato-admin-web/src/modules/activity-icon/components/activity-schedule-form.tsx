/* eslint-disable @typescript-eslint/no-explicit-any */
import { bindLoading, createComponent, mc, useValidator } from '@skynet/shared'
import { CreateForm, Button, transformNumber, showSuccessToast, showFailToast, Icon, transformStringArray, transformNumberArray, transformTimestamp } from '@skynet/ui'
import { useActivityIconStore } from '../use-activity-icon-store'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { apiUpdateActivityIcon } from '../activity-icon-api'
import { onMounted, onUnmounted, ref } from 'vue'
import { useGlobalLeaveWarning } from 'src/lib/use-leave'
import { useRouter } from 'vue-router'
import { keepError } from 'src/lib/http-client'
import { set } from 'lodash-es'
import { useUserStrategyLayer } from 'src/modules/user-strategy-layer/use-user-strategy-layer'

type ActivityScheduleFormOptions = {
  props: {}
  emits: {
    step: (stepNum: number) => void
  }
}
export const ActivityScheduleForm = createComponent<ActivityScheduleFormOptions>({
  props: {},
  emits: {
    step: () => {},
  },
}, (props, { emit }) => {
  const router = useRouter()
  const { stop } = useGlobalLeaveWarning()
  const btnLoading = ref(false)
  const {
    currentActivityIcon,
  } = useActivityIconStore()
  const Form = CreateForm<M.IActivityIconDetail>()
  const formRules = z.object({
    auto_online_time: z.number().min(1, {
      message: '请选择开始日期',
    }),
    duration_days: z.number().min(0, {
      message: '请输入持续时间',
    }),
  })

  const { error, validateAll } = useValidator(currentActivityIcon, formRules)

  const prevStep = () => {
    emit('step', 1)
  }

  const onSave = async () => {
    const exclude: string[] = []
    if (currentActivityIcon.value.auto_online !== 1) {
      exclude.push('auto_online_time')
      exclude.push('duration_days')
    }
    if (!validateAll({ exclude: exclude })) return
    await bindLoading(apiUpdateActivityIcon(currentActivityIcon.value), btnLoading).catch(keepError((error: any) => {
      showFailToast(error.response.data.message || '操作失败')
    }))
    showSuccessToast({
      message: '操作成功',
      onClose: () => {
        stop()
        void router.replace('/activity-icon')
      },
    })
  }

  onUnmounted(() => {
    stop()
  })

  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  return () => (
    <div>
      <Form
        class="flex flex-col gap-4"
        data={currentActivityIcon.value}
        onChange={(path, value) => {
          set(currentActivityIcon.value, path, value)
        }}
        hasAction={false}
        error={error.value}
        items={[
          ['有效期', 'auto_online', { type: 'checkbox', label: '定时', trueValue: 1, falseValue: 0,
            valueTest: (value: 0 | 1) => value === 1,
            'onUpdate:modelValue': (value: 0 | 1) => {
              if (value === 1) return
              delete currentActivityIcon.value.auto_online_time
              delete currentActivityIcon.value.duration_days
            },
          },
          { hint: '上架后，将在有效期内激活生效，过期无效', class: 'col-span-2' },
          ],
          [
            currentActivityIcon.value.auto_online ? 'col-span-1' : 'hidden',
            [requiredLabel('开始日期'), 'auto_online_time', { type: 'datetime' }, { transform: transformTimestamp },
            ],
            [requiredLabel('持续时间(天)'), 'duration_days', { type: 'number' }, { transform: transformNumber }],
          ],
          () => <div>策略范围</div>,
          [
            '分层画像',
            'strategy_conf.strategy_layer_ids',
            {
              type: 'multi-select',
              search: true,
              popoverWrapperClass: 'z-popover-in-dialog',
              options: list.value.map((n, index) => {
                return { value: n.id, label: `${n.id}/${n.name}` }
              }),
            },
            {
              class: mc('col-span-1'),
            },
          ],
          // [
          //   'flex flex-col gap-0',
          //   // ['策略 ID', 'strategy_conf.strategy_id', { type: 'text' }, { errorVisible: false, class: 'flex-row items-center gap-4' }],
          //   // ['', 'strategy_conf.condition', { type: 'checkbox', label: '下发给符合条件的用户', valueTest: (value: number) => value === 1, trueValue: 1, falseValue: 0 }, { errorVisible: false }],
          //   ['', 'strategy_conf.whitelist', {
          //     type: 'checkbox',
          //     label: '下发给白名单用户',
          //     valueTest: (value: number) => value === 1,
          //     trueValue: 1,
          //     falseValue: 0,
          //     'onUpdate:modelValue': (value: 0 | 1) => {
          //       if (value === 0) {
          //         currentActivityIcon.value.whitelist_users = []
          //       }
          //     },
          //   }, { errorVisible: false }],
          // ],
          // [<span>白名单 <small class="text-gray-500 text-xs">请输入UID，用逗号分隔，只支持数字</small></span>, 'whitelist_users', { type: 'textarea' },
          //   {
          //     class: `${currentActivityIcon.value.strategy_conf?.whitelist === 1 ? 'col-span-2' : 'hidden'}`,
          //   },
          // ],
        ]}
      />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-primary btn-sm" onClick={() => prevStep()}>上一步 </Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => void onSave()}>
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          保存
        </Button>
      </div>
    </div>
  )
})
