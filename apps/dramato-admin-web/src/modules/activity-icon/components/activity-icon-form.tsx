/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CheckboxGroup, CreateForm, Switch, Button } from '@skynet/ui'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { useActivityIconStore } from '../use-activity-icon-store'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'

type ActivityIconFormOptions = {
  props: {
  }
  emits: {
    step: (stepNum: number) => void
  }
}
export const ActivityIconForm = createComponent<ActivityIconFormOptions>({
  props: {},
  emits: {
    step: () => {},
  },
}, (props, { emit }) => {
  const {
    currentActivityIcon,
    setCurrentActivityIconByPath,
  } = useActivityIconStore()

  const Form = CreateForm<M.IActivityIconDetail>()
  const formRules = z.object({
    event_name: z.string().min(1, {
      message: '请输入活动页名称',
    }),
    float_icon_pages: z.string().min(1, {
      message: '请选择展示页面',
    }),
    float_icon_url: z.string().min(1, {
      message: '请上传素材',
    }),
  })
  const { error, validateAll } = useValidator(currentActivityIcon, formRules)

  const nextStep = () => {
    if (!validateAll()) return
    emit('step', 1)
    console.log(currentActivityIcon.value, 'step 1  currentActivityIcon---')
  }

  return () => (
    <>
      <Form
        class="gap-y-3 grid grid-cols-1"
        hasAction={false}
        error={error.value}
        onChange={(path, value: any) => {
          setCurrentActivityIconByPath(path, value)
        }}
        items={[
          [
            '活动ID',
            'event_id',
            {
              type: 'text',
              disabled: true,
            },
            {
              class: `${!!currentActivityIcon.value?.event_id ? 'block' : 'hidden'}`,
            },
          ],
          [
            requiredLabel('活动页名称(Active Events)'),
            'event_name',
            {
              type: 'text',
              placeholder: '请输入活动名称',
            }, {
              hint: `例如：圣诞节会员促销-英语区-美国`,
            },
          ],
          [
            '展示悬浮icon',
            'float_icon_enabled',
            {
              type: 'custom',
              render: () => (
                <Switch modelValue={!!currentActivityIcon.value?.float_icon_enabled} onUpdate:modelValue={(e: unknown) => {
                  setCurrentActivityIconByPath('float_icon_enabled', !!e ? 1 : 0)
                }}
                />
              ),
            },
          ],
          [
            requiredLabel('使用素材'),
            'float_icon_url',
            {
              type: 'custom',
              render: () => (
                <x-upload-cover class="gap-y-2 grid">
                  <Uploader
                    accept="png,jpg,jpeg"
                    maxsize={1024 * 1024 * 10}
                    class="border-[1px] border-dashed rounded-md cursor-pointer overflow-hidden size-[200px]"
                    onUploadSuccess={d => {
                      setCurrentActivityIconByPath('float_icon_url', d.temp_path as string)
                    }}
                    isImage={true}
                    uploadUrl="/popup/upload/image"
                  >
                    {
                      currentActivityIcon.value.float_icon_url
                        ? <img src={currentActivityIcon.value.float_icon_url.includes('https://') ? currentActivityIcon.value.float_icon_url : 'https://static-v1.mydramawave.com/popup/image/' + currentActivityIcon.value.float_icon_url} class="object-cover size-full" />
                        : <span class="flex justify-center items-center size-full">上传素材</span>
                    }
                  </Uploader>
                  <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                </x-upload-cover>
              ),
            },
          ],
          [
            requiredLabel('展示页面'),
            'float_icon_pages',
            {
              type: 'custom',
              render: () => (
                <CheckboxGroup
                  class="-mt-1.5"
                  modelValue={currentActivityIcon.value.float_icon_pages_arr}
                  onUpdate:modelValue={(e: unknown) => {
                    currentActivityIcon.value.float_icon_pages_arr = e as number[]
                    setCurrentActivityIconByPath('float_icon_pages', (e as number[]).join(','))
                    return
                  }}
                  options={[{
                    label: '首页',
                    value: 1,
                  }, {
                    label: 'reward',
                    value: 2,
                  },
                  // , {
                  //   label: 'store',
                  //   value: 3,
                  // }
                  ]}
                />
              ),
            },
          ],
        ]}
        data={currentActivityIcon.value}
      />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-primary btn-sm" onClick={() => {
          nextStep()
        }}
        >下一步
        </Button>
      </div>
    </>

  )
})
