/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, TableColumnOld, CreateTableOld, Button, openDialog } from '@skynet/ui'
import { useActivityIconStore } from '../use-activity-icon-store'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { ActivityBtnForm } from './activity-btn-form'
import { trim } from 'lodash-es'

type ActivityH5FormOptions = {
  props: {}
  emits: {
    step: (stepNum: number) => void
  }
}
export const ActivityH5Form = createComponent<ActivityH5FormOptions>({
  props: {},
  emits: {
    step: () => {},
  },
}, (props, { emit }) => {
  const {
    currentActivityIcon,
    setCurrentActivityIconByPath,
    btnList,
  } = useActivityIconStore()
  const Form = CreateForm<M.IActivityIconDetail>()
  const formRules = z.object({
    h5_url: z.string().min(1, {
      message: '请输入H5地址',
    }),
  })
  const { error, validateAll } = useValidator(currentActivityIcon, formRules)

  const Table = CreateTableOld<M.IActivityButton>()

  const columns: TableColumnOld<M.IActivityButton>[] = [
    ['按钮项', (row, index) => {
      return `button${index + 1}`
    }],
    ['button_id', 'button_id'],
    ['指向类型', row => {
      return <div class="badge badge-outline badge-primary">{row.target_type === 1 ? '商品' : '链接'}</div>
    }],
    ['指向目标', 'show_content'],
    [<span class="px-3">操作</span>, (row, index) => (
      <div class="flex gap-x-2">
        <Button
          class="btn btn-outline btn-xs"
          onClick={() => {
            const hideDialog = openDialog({
              title: '编辑按钮',
              mainClass: 'pb-0 px-5',
              hideParentWhenChildOpen: true,
              body: () => (
                <ActivityBtnForm index={index} curBtn={row} onClose={() => hideDialog()} />
              ),
            })
          }}
        >
          编辑
        </Button>
        <Button
          class="btn btn-outline btn-xs"
          onClick={() => {
            const showTipsDialog = () => {
              const hideDialog = openDialog({
                title: '提示',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-status-body>是否确认删除?</x-status-body>
                    <x-status-footer class="flex justify-end gap-x-[10px] w-full">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        btnList.value.splice(index, 1)
                        hideDialog()
                      }}
                      >确定
                      </button>
                    </x-status-footer>
                  </x-status-confirm-dialog>
                ),
              })
            }

            showTipsDialog()
          }}
        >
          删除
        </Button>
      </div>
    ), {
      class: 'w-[100px]',
    },
    ],
  ]

  const nextStep = () => {
    if (!validateAll()) return
    emit('step', 2)
    const newBtnList = btnList.value.map((btn, index) => {
      return {
        ...btn,
        index: index + 1,
      }
    })
    setCurrentActivityIconByPath('h5_button_list', newBtnList)
  }

  const prevStep = () => {
    emit('step', 0)
  }

  const addButton = () => {
    const hideDialog = openDialog({
      title: '新增按钮',
      mainClass: 'pb-0 px-5',
      hideParentWhenChildOpen: true,
      body: () => (
        <ActivityBtnForm curBtn={{ target_content: '', target_type: 1, button_id: (btnList.value.length + 1) }} onClose={() => hideDialog()} />
      ),
    })
  }

  return () => (
    <>
      <Form
        class="gap-y-3 grid grid-cols-1"
        hasAction={false}
        error={error.value}
        onChange={(path, value: any) => {
          if (path === 'float_icon_pages') setCurrentActivityIconByPath(path, value.join(','))
          if (path === 'h5_url') {
            setCurrentActivityIconByPath(path, trim(value))
            return
          }
          setCurrentActivityIconByPath(path, value)
        }}
        items={[
          [
            requiredLabel('落地页'),
            'h5_url',
            {
              type: 'text',
              placeholder: '请输入h5地址',
            }, {
              hint: () => {
                return (
                  <div class="flex items-center space-x-4">
                    {requiredLabel('请部署H5上线时，与研发确定好按钮顺序')}
                    <a class="link link-primary" target="_blank" href={currentActivityIcon.value.h5_url}>预览</a>
                  </div>
                )
              },
            },
          ],
          [
            () => (
              <div class="flex items-center gap-x-2">
                <span>指定按钮</span>
                <Button class="btn btn-outline btn-xs" onClick={addButton}>新增</Button>
              </div>
            ),
            'h5_button_list',
            {
              type: 'custom',
              render: () => (
                <Table
                  list={btnList.value || []}
                  columns={columns}
                />
              ),
            }, {
              hint: () => <a class="link link-primary" target="blank" href="https://rg975ojk5z.feishu.cn/docx/AkgadiZueoPf1mxwQSMct2HXn4h">deeplink文档</a>,
            },
          ],
        ]}
        data={currentActivityIcon.value}
      />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-primary btn-sm" onClick={() => {
          prevStep()
        }}
        >上一步
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          nextStep()
        }}
        >下一步
        </Button>
      </div>
    </>
  )
})
