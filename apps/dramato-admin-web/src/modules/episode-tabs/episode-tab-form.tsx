import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useEpisodeTabs } from './use-episode-tabs.tsx'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer.tsx'
import { onMounted } from 'vue'

export const EpisodeTagsForm = createComponent(null, () => {
  const {
    tags,
    tab,
    hideTagsFormDialog,
    onSave,
  } = useEpisodeTabs()
  const Form = CreateForm<M.Banner>()

  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  const formRules = z.object({
    tab_name: z.string().min(1, '请输入标题').max(50, '最多50个字符'),
    tab_index: z.number().min(1, '请选择标签顺序'),
    listing_status: z.number().min(1, '请选择上架状态'),
  })

  const { error, validateAll } = useValidator(tab, formRules)

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'label_ids') {
              const ids = (value as string || '').split(',').map(Number)
              const labels = tags.value.filter(item => ids.includes(item.label_id))
              set(tab.value || {}, 'label_names', labels.map(item => item.content).join(','))
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('tab名称'),
              path: 'tab_name',
              input: {
                type: 'textarea',
                maxlength: 50,
                placeholder: '请输入标题，1-50个字符',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('tab业务名称（英文/不要空格/不要emoji）'),
              path: 'tab_key',
              input: {
                type: 'textarea',
                maxlength: 50,
                placeholder: '请输入标题，1-50个字符',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('排序'),
              path: 'tab_index',
              transform: transformInteger,
              input: {
                type: 'text',
                placeholder: '请输入排序',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('是否上架'),
              path: 'listing_status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
            },
            [
              '分层画像',
              'strategy_layer_ids',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: list.value.map((n, index) => {
                  return { value: n.id, label: `${n.id}/${n.name}` }
                }),
                // maxlength: 1,

              },
              {
                class: 'col-span-3',
              },
              // {
              //   transform: [
              //     (raw?: unknown) => raw ? [+raw] : [],
              //     (display: string[]): number => display[0] ? +(display[0] || '') : 0,
              //   ] as const,
              // },
            ],
          ]}
          data={tab.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(tab.value as M.EpisodeTab.List.Item)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
