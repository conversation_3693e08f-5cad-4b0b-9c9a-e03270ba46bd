/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiDeleteEpisodeTab, apiGetEpisodeTabList, apiSaveEpisodeTab, apiUpdateEpisodeTabStatus } from './episode-tab-api'
import { EpisodeTagsForm } from './episode-tab-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeTabs = () => {
  return {
    tags, Form, tabParams, Table, list, loading, search, changeTab, tab, checkedItems, batchDelete, batchUp, batchDown,
    showTagsFormDialog, hideTagsFormDialog, onSave,
  }
}

const Form = CreateForm<M.EpisodeTab.List.Request>()
const tabParams = ref<M.EpisodeTab.List.Request>({})

const Table = CreateTableOld<M.EpisodeTab.List.Item>()
const list = ref<M.EpisodeTab.List.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([])

const voidTab = {
  id: 0,
  platform: 1, // 平台 1-IOS 2-Android
  tab_name: '', // 名称
  tab_index: 1, // 排序
  tab_key: '', // 唯一key
  listing_status: 1, // 状态 1 上架 2 下架
}
const tab = ref<Partial<M.EpisodeTab.List.Item>>({
  ...voidTab,
})

const checkedItems = ref<M.EpisodeTab.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiGetEpisodeTabList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  return list.value
}

const changeTab = (item: Partial<M.EpisodeTab.List.Item>) => {
  tab.value = Object.assign({}, voidTab, item)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeTab.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeTagsForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const onSave = (d: M.EpisodeTab.List.Item) => {
  void apiSaveEpisodeTab({
    app_id: tabParams.value.app_id,
    language: tabParams.value.language,
    item: d,
  }).then(d => {
    console.log('>>> d')

    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.message || '保存失败', 'error')
  })
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeTabStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeTabStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              is_up: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiDeleteEpisodeTab({
              ids: '[' + checkedItems.value.map(item => item.id).join(',') + ']',
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
