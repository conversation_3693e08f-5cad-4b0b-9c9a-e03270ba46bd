import { httpClient } from 'src/lib/http-client'

export const apiGetEpisodeTabList = (data: M.EpisodeTab.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeTab.List.Response>>('/hometab/tab/list', data)

export const apiUpdateEpisodeTabStatus = (data: M.EpisodeTab.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/tab/listing_status', data)

export const apiSaveEpisodeTab = (data: M.EpisodeTab.Save.Request) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/hometab/tab/save', data)

export const apiDeleteEpisodeTab = (d: M.EpisodeTab.Delete.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/tab/delete', d)

export const apiSortEpisodeTab = (d: M.EpisodeTab.Sort.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/tab/sort/update', d)

export const apiEpisodeTabPreview = (d: M.EpisodeTab.PreviewTab.Request) =>
  httpClient.post<ApiResponse<M.EpisodeTab.PreviewTab.Response>>('/hometab/tab/tab_preview', d)

export const apiEpisodeTabContentPreview = (d: M.EpisodeTab.ContentPreviewTab.Request) =>
  httpClient.post<ApiResponse<M.EpisodeTab.ContentPreviewTab.Response>>('/hometab/tab/index_preview', d)
