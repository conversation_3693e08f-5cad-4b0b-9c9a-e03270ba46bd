import { createComponent, fn, getQuery } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { Component } from 'vue'
import { useRouter } from 'vue-router'

type ErrorPageOptions = {
  props: {
    message?: string
    image?: Component | null
  }
  emits: {
    refresh: (path: string) => void
  }
}
export const ErrorPage = createComponent<ErrorPageOptions>({
  props: {
    message: '',
    image: null,
  },
  emits: {
    refresh: fn,
  },
}, (props, { emit }) => {
  const fromPath = getQuery('fromPath', '')
  const message = getQuery('message', props.message)
  const router = useRouter()
  const onRefresh = () => {
    if (fromPath) {
      void router.push(fromPath)
    }
    emit('refresh', fromPath)
  }
  return () => (
    <div class="w-screen h-screen bg-[var(--fill-3)] flex justify-center items-center">
      <div class="w-1/2 h-auto flex flex-col justify-between items-center gap-4">
        {props.image}
        <p class="text-center text-[var(--text-1)] text-base font-normal">{message || '抱歉，页面开小差了，请刷新一下吧'}</p>
        <Button
          class="h-8 px-3 rounded-[200px] border border-[var(--brand-6)] border-solid text-base font-medium text-[var(--brand-6)] bg-transparent"
          onClick={onRefresh}
        >
          刷新
        </Button>
      </div>
    </div>
  )
})
export default ErrorPage
