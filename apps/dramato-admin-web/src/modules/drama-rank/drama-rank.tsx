import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, Image, CreateForm, TableColumnOld, Pager, SvgIcon } from '@skynet/ui'
import { ref, reactive, onMounted } from 'vue'
import { set } from 'lodash-es'
import { apiGetRankList } from './drama-rank-api'
import { countryList, tagList } from './const'
import { RouterLink } from 'vue-router'

type DramaRankOptions = {
  props: {}
}

export const DramaRank = createComponent<DramaRankOptions>({
  props: {},
}, props => {
  const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
  })
  const loading = ref(false)
  const list = ref<M.IDramaRank[]>()
  const form = ref({
    tag: [],
    region: [],
  })
  const Table = CreateTableOld<M.IDramaRank>()
  const QueryForm = CreateForm<{
    tag: string[]
    region: string[]
  }>()
  const columns: TableColumnOld<M.IDramaRank>[] = [
    ['排行', 'rangking', { class: 'w-[60px]' }],
    ['封面', row => <Image src={row.cover_url} />, { class: 'w-[130px]' }],
    ['短剧', row => {
      return <RouterLink to={`/material?playlet_name=${row.playlet_name}`}>{row.playlet_name}</RouterLink>
    }, { class: 'w-[160px]' }],
    ['标签', row => {
      return (
        <div class="space-x-1">
          {row.playlet_tags && row.playlet_tags?.split(',').slice(0, 3).map(tag => <button class="btn btn-xs btn-outline">{tag}</button>)}
          <span class="tooltip" data-tip={row.playlet_tags}>
            { row.playlet_tags && row.playlet_tags?.split(',').length > 3 ? <SvgIcon name="ic_c_more" /> : '' }
          </span>
        </div>
      )
    }, { class: 'w-[190px] overflow-hidden clamp-line-1' }],
    ['投放素材数', 'material_cnt', { class: 'w-[70px]' }],
    ['投放国家/地区', row => {
      return (
        <div class="space-x-1">
          {row.country_ids && row.country_ids?.split(',').slice(0, 3).map(tag => <button class="btn btn-xs btn-outline">{tag}</button>)}
          <span class="tooltip" data-tip={row.country_ids}>
            { row.country_ids && row.country_ids?.split(',').length > 3 ? <SvgIcon name="ic_c_more" /> : '' }
          </span>
        </div>
      )
    }, { class: 'w-[190px] overflow-hidden clamp-line-1' }],
    ['投放产品', row => (
      <div class="tooltip" data-tip={row.product_ids}>
        <span>{
          row.product_ids.split(',').map(item => {
            if (item.split('-') && item.split('-')[0]) {
              return item.split('-')[0]
            } else {
              return item
            }
          })
        }
        </span>
      </div>
    ),
    { class: 'w-[160px]' },
    ],
    ['投放天数/天', 'release_day', { class: 'w-[70px]' }],
  ]

  const getList = async () => {
    const params = {
      page: pageInfo.page,
      country: form.value.region.join(',') ? form.value.region.join(',') : '全部',
      tags: form.value.tag.join(',') ? form.value.tag.join(',') : '全部',
    }
    loading.value = true
    try {
      const { data } = await apiGetRankList(params)
      list.value = data?.records
      pageInfo.total = data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = async () => {
    pageInfo.page = 1
    await getList()
  }

  const onReset = async () => {
    form.value.tag = []
    form.value.region = []
    await onQuery()
  }

  const onPageChange = async () => {
    await getList()
  }
  const onPageSizeChange = async (n: number) => {
    pageInfo.pageSize = n
    await onQuery()
  }

  onMounted(async () => {
    await getList()
  })

  return () => (

    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>短剧排行榜（周榜）</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              { label: '国家/地区：', path: 'region', input: { type: 'multi-select', search: { debounce: 20, placeholder: '搜索' }, options: countryList.map(name => {
                return { value: name, label: name }
              }) } },
              { label: '题材标签：', path: 'tag', input: { type: 'multi-select', search: { debounce: 20, placeholder: '搜索' }, options: tagList.map(name => {
                return { value: name, label: name }
              }) } },
            ]}
          />
        ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.page} v-model:size={pageInfo.pageSize} total={pageInfo.total} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default DramaRank
