<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="用户昵称" class="w-[250px]">
          <el-input v-model="searchForm.nickname" placeholder="用户昵称" />
        </el-form-item>
        <el-form-item label="用户ID" class="w-[250px]">
          <el-input v-model="searchForm.uid" placeholder="用户ID" />
        </el-form-item>
        <el-form-item label="公司名称" class="w-[250px]">
          <el-input v-model="searchForm.company" placeholder="公司名称" />
        </el-form-item>
        <el-form-item label="认证状态" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.auth_status" placeholder="认证状态">
            <el-option class="w-[150px]" v-for="(item, key) in companyStatusOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="nickname" label="用户昵称" width="200" />
        <el-table-column prop="auth_status" label="用户ID" width="300">
          <template #default="scope">
            <div>用户类型：{{ scope.row.user_type == 2 ? '个人用户' : '企业用户' }}</div>
            {{ scope.row.uid || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系方式" width="200" />
        <el-table-column prop="company" label="公司名称/姓名" width="200" />
        <el-table-column prop="usci" label="营业执照号" width="200" />
        <el-table-column prop="licence_pic" label="营业执照照片" width="200" />
        <el-table-column prop="apply_reason" label="申请理由" width="200" />
        <el-table-column prop="auth_status" label="认证状态" width="100">
          <template #default="scope">
            {{ companyStatusOptions[scope.row.auth_status].label || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="auth_user" label="审核人" width="200" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-button v-if="scope.row.auth_status === 1" type="primary" size="mini"
                @click="handlePreview(scope.row)">审核</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_index" v-model:page-size="searchForm.page_size"
          :total="total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>

    </div>
    <el-dialog v-model="dialogVisible" title="审核详情" width="70%">
      <div>
        <el-form :model="formData" label-width="120px">
          <h1>个人信息</h1>
          <el-form-item label="头像">
            <el-image class="w-[100px] h-[100px] rounded-full" :src="formData.avatar" :zoom-rate="1.2" :max-scale="7"
              :min-scale="0.2" :preview-src-list="[formData.avatar]" :initial-index="4" fit="cover" />
            <Uploader accept="png,jpg,jpeg" :maxsize="1024 * 1024 * 10"
              class=" border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer" @UploadSuccess="d => {
                formData.avatar = d.temp_path?.includes('https://') ? d.temp_path : 'https://static-v1.mydramawave.com/popup/image/' + d.temp_path
              }" :isImage="true" :multiple="false" uploadUrl="/popup/upload/image">
              <el-button>上传</el-button>
            </Uploader>
          </el-form-item>
          <el-form-item label="昵称">
            <el-input v-model="formData.nickname" type="text" placeholder="昵称" />
          </el-form-item>
          <el-form-item label="联系方式">
            <div>{{ formData.phone }}</div>
          </el-form-item>
          <el-form-item label="认证信息">
            <div>{{ formData.company }}</div>
          </el-form-item>

          <h1>认证信息</h1>

          <el-form-item label="公司名称">
            <el-input v-model="formData.company" type="text" placeholder="公司名称" />
          </el-form-item>
          <el-form-item label="营业执照号">
            <el-input v-model="formData.usci" type="text" placeholder="营业执照号" />
          </el-form-item>
          <el-form-item label="营业执照照片">
            <el-image style="width: 100px; height: 100px" :src="formData.licence_pic" :zoom-rate="1.2" :max-scale="7"
              :min-scale="0.2" :preview-src-list="[formData.licence_pic]" :initial-index="4" fit="cover" />
          </el-form-item>
        </el-form>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit">通过</el-button>
          <el-button type="danger" @click="handleReject">不通过</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="rejectVisible" title="请输入不通过原因" width="50%">
      <div>
        <el-form :model="rejectData" label-width="120px">
          <el-form-item label="中文">
            <el-input v-model="rejectData.failed_reason" type="text" placeholder="输入中文原因" />
          </el-form-item>
          <el-form-item label="英语">
            <el-input v-model="rejectData.failed_reason_en" type="text" placeholder="输入英文原因" />
          </el-form-item>
        </el-form>
        <div class="flex justify-end">
          <el-button type="primary" @click="rejectVisible = false">取消</el-button>
          <el-button type="danger" @click="handleRejectConfirm">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
import { useCompanyAdmin } from './use-company-admin';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'

const {
  companyStatusOptions,
  config,
  saveAudit,
  getConfig,
  onSearchCompanyList,
  list,
  loading,
  searchForm,
  total,
  onReset
} = useCompanyAdmin()


const route = useRoute()
const router = useRouter()

const dialogVisible = ref(false)
const formData = ref({})
const rejectVisible = ref(false)
const rejectData = ref({
  id: 0,
  auth_status: 2,
  failed_reason: '',
  failed_reason_en: ''
})


// 处理操作方法
const handlePreview = (row: M.CompanyItem) => {
  formData.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}


const handleSubmit = () => {
  ElMessageBox.confirm('确定通过吗？', '提示', {
    confirmButtonText: '通过',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    saveAudit({
      id: formData.value.id,
      auth_status: 2,
      failed_reason: '',
    }).then((res: any) => {
      if (res.code === 200) {
        ElMessage.success('通过成功')
        dialogVisible.value = false
        onSearchCompanyList()
      } else {
        ElMessage.error(res.msg)
      }
    })
  })
}

const handleReject = () => {
  rejectVisible.value = true
}

const handleRejectConfirm = () => {
  saveAudit({
    id: formData.value.id,
    auth_status: 3,
    failed_reason: rejectData.value.failed_reason,
    failed_reason_en: rejectData.value.failed_reason_en,
  }).then((res: any) => {
    if (res.code === 200) {
      ElMessage.success('不通过成功')
      dialogVisible.value = false
      rejectVisible.value = false
      onSearchCompanyList()
    } else {
      ElMessage.error(res.msg)
    }
  })
}












// ----------------
onMounted(() => {
  console.log('mounted')
  getConfig();
  onSearchCompanyList();

})
const multipleSelection = ref<M.CompanyItem[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchCompanyList();
}



const handleSelectionChange = (val: M.CompanyItem[]) => {
  multipleSelection.value = val
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_index = val
  onSearchCompanyList()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_size = val
  onSearchCompanyList()
}

</script>

<style scoped></style>
