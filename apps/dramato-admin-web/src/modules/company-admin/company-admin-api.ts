/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_sso_token } from 'src/lib/device-id'
import { httpClient } from 'src/lib/http-client'

export const apiGetPushConfig = () =>
  httpClient.get<ApiResponse<M.PushConfigData>>('/push/v2/config')

export const apiGetDialogConfig = () => httpClient.get<ApiResponse<M.PopupConfig>>('/popup/config/items', {})

export const apiSaveAcitve = (data: any) =>
  httpClient.post<ApiResponse>('/activity/add', data)

export const apiUpdateAcitve = (data: any) =>
  httpClient.post<ApiResponse>('/activity/update', data)

export const apiGetAcitve = (data: any) =>
  httpClient.get<ApiResponse>('/activity/detail', data)

export const apiOptionActive = (data: any) =>
  httpClient.post<ApiResponse>('/activity/option', data)

export const apiGetActivityList = (data: M.CompanyItem) =>
  httpClient.post<ApiResponse<M.CompanyItemResponse>>('/open_platform_in/user/audit_list', data)

export const apiSaveAudit = (data: M.AuditItem) =>
  httpClient.post<ApiResponse>('/open_platform_in/user/audit', data)
