/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { apiGetActivityList, apiGetDialogConfig, apiSaveAudit } from './company-admin-api'
import { createCachedFn } from '@skynet/shared'

const initFromData = {
  id: undefined,    // id
  uid: "",         // 用户ID
  phone: "",       // 手机号
  avatar: "",      // 头像
  nickname: "",    // 昵称
  company: "",     // 公司名称
  usci: "",        // 统一社会信用代码
  licence_pic: "", // 营业执照图片
  auth_user: "",   // 审核人
  auth_status: 0,  // 认证状态 0:全部 1: 未认证 2: 已认证 3：认证失败
  failed_reason: "" // 审核失败原因
}

const form = ref<M.CompanyItem>(cloneDeep(initFromData))

const config = ref<M.Config>({
  lang_items: {
    'de-DE': 'German',
    'en-US': 'English',
    'es-MX': 'Spanish',
    'fr-FR': 'French',
    'id-ID': 'Indonesian',
    'it-IT': 'Italian',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'pt-PT': 'Portuguese',
    'ru-RU': 'Russian',
    'th-TH': 'Thai',
    'tl-PH': 'Filipino',
    'tr-TR': 'Turkish',
    'vi-VN': 'Vietnamese',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
  },
})


export const useCompanyAdmin = createCachedFn(() => {
  const searchForm = ref<M.CompanySearchOptionNew>({
    nickname:"",  // 昵称
    phone:"",   // 手机号
    auth_status:0,  // 认证状态 0:全部 1: 未认证 2: 已认证 3：认证失败
    uid: "",  // 用户id
    company: "",  // 公司名称
    page_index: 1,
    page_size: 20
  })

  const companyStatusOptions = [
    { label: '全部', value: 0 },
    { label: '未认证', value: 1 },
    { label: '已认证', value: 2 },
    { label: '认证失败', value: 3 }
  ]

  const list = ref<M.CompanyItem[]>([])
  const total = ref<number>(10)
  const applicationList = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
  const closeEditPushNotificationModal = ref(() => { })
  const loading = ref(false)
  const isUpdating = ref(false)

  const InitPushNotificationOption: M.PushNotification = {
    priority: 3,
    user_identify_val: '',
    notify_btn_bg_color: '#FC2763',
  }

  const currentNotification = ref<M.PushNotification>(InitPushNotificationOption)

  const getList = async () => {
    loading.value = true
    const rs = await apiGetActivityList(searchForm.value)
    list.value = rs.data?.list || []
    total.value = rs.data?.total || 0
    loading.value = false
  }

  const onSearchCompanyList = (isFirst?: boolean) => {
    if (isFirst) {
      searchForm.value.page_index = 1
      searchForm.value.page_size = 20
    }
    void getList()
  }

  const saveAudit = async (data: M.AuditItem) => {
    const rs = await apiSaveAudit(data)
    return rs
  }

  const onPageChange = (page_index: number) => {
    searchForm.value.page_index = page_index
    onSearchCompanyList()
  }

  const onPageSizeChange = (page_size: number) => {
    searchForm.value.page_size = page_size
    searchForm.value.page_index = 1
    onSearchCompanyList()
  }

  const onReset = (list_type?: number) => {
    searchForm.value.nickname = ''
    searchForm.value.phone = ''
    searchForm.value.auth_status = 0
    searchForm.value.uid = ''
    searchForm.value.page_index = 1
    searchForm.value.page_size = 20
    searchForm.value.company = ''
    onSearchCompanyList(true)
  }

  const pushConfig = ref<M.PushConfigData>()

  const getConfig = async () => {
    const res = await apiGetDialogConfig()
    if (!res?.data) return
    config.value = res?.data
  }

  const getLangCode = (lang: string) => {
    return Object.keys(config.value.lang_items).find(key => config.value.lang_items[key] === lang)
  }

  const appTypeOptions = [
    { label: '全部', value: 3 },
    { label: '安卓', value: 1 },
    { label: 'IOS', value: 2 },
  ]
  const activityTypeOptions = [
    { label: '全部', value: 0 },
    { label: '评论活动', value: 1 }
  ]

  const activityPageOptions = [
    { label: '评论页面', value: 1 }
  ]
  const activityStatusOptions = [
    { label: '不限', value: 0 },
    { label: '待发布', value: 1 },
    { label: '已发布', value: 2 },
    { label: '下架', value: 3 }
  ]

  return {
    searchForm,
    form,
    initFromData,
    list,
    total,
    closeEditPushNotificationModal,
    InitPushNotificationOption,
    loading,
    onPageChange,
    onPageSizeChange,
    onReset,
    onSearchCompanyList,
    currentNotification,
    applicationList,
    isUpdating,
    getConfig,
    config,
    pushConfig,
    getLangCode,
    appTypeOptions,
    activityTypeOptions,
    activityPageOptions,
    activityStatusOptions,
    companyStatusOptions,
    saveAudit
  }
})
