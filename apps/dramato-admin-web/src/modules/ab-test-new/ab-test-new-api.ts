import { httpClient } from 'src/lib/http-client'

export const apiGetABTestList = (data: M.ABTestNew.params) =>
  httpClient.post<ApiResponse<M.ABTestNew.ListResponse>>('/abtestlayer/list', data)

export const apiCreateABTest = (data: M.ABTestNew.CreateAbTest) =>
  httpClient.post<ApiResponse<boolean>>('/abtestlayer/create', data)

export const apiEditABTest = (data: M.ABTestNew.CreateAbTest) =>
  httpClient.post<ApiResponse<boolean>>('/abtestlayer/update', data)

export const apiEditABTestStatus = (data: { id: number, state: number }) =>
  httpClient.post<ApiResponse<boolean>>('/abtestlayer/set-state', data)

export const apiDeleteABTest = (data: { id: number }) =>
  httpClient.post<ApiResponse<boolean>>('/abtestlayer/delete', data)

export const apiStat = (data: { id: number }) =>
  httpClient.post<ApiResponse<M.ABTestNew.StatResponse>>('/abtestlayer/stat', data)

export const apiGetUserStrategyLayerList = (data: M.UserStrategyLayer.params) =>
  httpClient.post<ApiResponse<M.UserStrategyLayer.ListResponse>>('/strategy-group/layer/list', data)

export const apiGetArea = () => httpClient.get<ApiResponse<{
  areas: Array<{
    area_id: number
    area_name: string
    layers: Array<
      {
        layer_id: number
        layer_name: string
      }
    >
  }>
  layer_dic: {
    [key: number]: string
  }
}>>('/abtestlayer/select', {})
