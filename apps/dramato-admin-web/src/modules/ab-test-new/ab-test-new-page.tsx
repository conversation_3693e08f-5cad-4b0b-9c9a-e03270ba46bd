/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, DateTime, openDialog, Pager, showAlert, transformNumber } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useABTest } from './use-ab-test-new'
import { onMounted } from 'vue'
import { useUserStrategyLayer } from './use-user-strategy-layer'
import { cloneDeep } from 'lodash-es'
import { apiGetArea } from './ab-test-new-api'
import SeriesPackageAddForm from '../series-package-new/series-package-add-form'
import { apiListSeriesPackage } from '../series-package-new/series-package-api'
import { useSeriesPackageAddFormStore } from '../series-package-new/use-series-package-add-form-store'
import { dialogMainClass } from '../series-package-new/series-package-page'

type ABTestOptions = {
  props: {}
}
export const ABTest = createComponent<ABTestOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    onCreateBtnClick,
    onEditBtnClick,
    onUpdateState,
    onDelete,
    dataBack,
  } = useABTest()

  onMounted(() => {
    layerPage.value = 1
    layerPageSize.value = 9999
    void layerSearch(layerPage.value)
    void search(1)

    void apiGetArea().then(res => {
      areas.value = res.data?.areas || []
      layers.value = Object.keys(res.data?.layer_dic || {}).map(item => ({
        label: res.data?.layer_dic[+item] || '',
        value: +item,
      }))
    })
  })

  const {
    layerList,
    layerPage,
    layerPageSize,
    layerSearch,
    areas,
    layers,
  } = useUserStrategyLayer()

  const getSeIds = (row: any) => {
    const arr = []
    let result = ''
    for (const i in row.version_config) {
      if (row.version_config[i]['strategy_id']) {
        arr.push(row.version_config[i]['strategy_id'])
      }
    }
    if (arr.length > 0) {
      result = arr.join(';')
    }

    return result
  }

  const getStraNames = (row: any) => {
    const arr = []
    let result = ''
    const strArr = row?.strategy_layer_ids || []
    const layerListData = JSON.parse(JSON.stringify(layerList.value))
    for (const i in strArr) {
      for (const j in layerListData) {
        if (strArr[i] == layerListData[j]['id']) {
          arr.push(layerListData[j]['name'])
        }
      }
    }
    if (arr.length > 0) {
      result = arr.join(';')
    }

    return result
  }

  const getLayerName = (id: number) => {
    // const layerArr = ['', 'IAP', '广告', '剧集定价', '弹窗', 'push', '首页', '活动', '其他', '付费卡点']
    return layers.value.find(item => item.value === id)?.label || ''
  }

  return () => (
    <NavFormTablePager>
      {{
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              page.value = 1
              pageSize.value = 20
              void search(1)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['实验编号', 'id', { type: 'number' }, { transform: transformNumber }],
              ['状态', 'state', { type: 'select', options: [{ label: '进行中', value: 1 }, { label: '暂停', value: 2 }] }, { transform: transformNumber }],
              ['排序', 'sort', { type: 'select', options: [
                { label: '创建时间：从新到旧', value: 1 },
                { label: '创建时间：从旧到新', value: 2 },
                { label: 'ID：从大到小', value: 3 },
                { label: 'ID：从小到大', value: 4 },
              ] }, { transform: transformNumber }],
              { label: '策略组ID/名称：', path: 'strategy_id_or_name', input: { type: 'text' } },
              { label: '用户分层ID/名称：', path: 'layer_id_or_name', input: { type: 'text' } },
              ['分桶场景', 'layer_id', { type: 'select', options: layers.value }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            ABTest表
            <Button class="btn btn-primary btn-sm" onClick={onCreateBtnClick}>新增ABTest</Button>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['实验编号', 'id', { class: 'w-[100px]' }],
            ['实验描述', 'description', { class: 'w-[200px]' }],
            ['实验类型', row => (
              <span>{['-空-', '常规实验', '高优实验(先画像，再分流，谨慎使用)'][row?.type || 0]}</span>
            ), {
              class: 'w-[120px]',
            }],
            ['剧包ID', row => (
              <x-badge class="flex flex-wrap gap-1">{
                row.series_package_ids?.split(',')?.map(idstr => (
                  <button class="link link-primary" onClick={async () => {
                    const { formData, curStep, closeAddDialog } = useSeriesPackageAddFormStore()
                    const res = await apiListSeriesPackage({ id: +idstr, page_info: { page_index: 1, page_size: 10 } })
                    curStep.value = 1
                    if (!res.data?.list) {
                      return showAlert('未找到剧包信息')
                    }
                    formData.value = cloneDeep(res.data.list[0])
                    formData.value.needDisabled = true
                    delete formData.value.state
                    delete formData.value.created
                    delete formData.value.updated
                    delete formData.value.created_operator_name
                    delete formData.value.updated_operator_name
                    delete formData.value.created_operator_id
                    delete formData.value.updated_operator_id
                    delete formData.value.deleted
                    console.log('formData.value', formData.value)

                    closeAddDialog.value = openDialog({
                      title: '查看剧包',
                      body: () => <SeriesPackageAddForm />,
                      mainClass: dialogMainClass,
                      customClass: '!w-[1000px] overflow-hidden',
                    })
                  }}>{idstr}</button>
                ))
              }</x-badge>
            ), { class: 'w-[200px]' }],
            ['策略组ID', row => (
              <span>{getSeIds(row)}</span>
            ), {
              class: 'w-[180px]',
            },
            ],
            ['流量域', row => (
              <span>{['付费流量', '免费流量'][row?.area_id || 0]}</span>
            ), {
              class: 'w-[120px]',
            },
            ],
            ['分桶场景', row => (
              <span>{getLayerName(row.layer_id)}</span>
            ), {
              class: 'w-[120px]',
            },
            ],
            ['用户分层', row => (
              <span class="inline-block w-full truncate">{getStraNames(row)}</span>
            ), {
              class: 'w-[180px]',
            },
            ],
            ['放量', row => (
              <span class="inline-block w-full truncate">{row.traffic_interval}%</span>
            ), { class: 'w-[80px]' }],
            ['对照/实验组数', row => row.version_config?.length, { class: 'w-[100px]' }],
            ['取样方式', row => ['-空-', '策略筛选', '随机筛选'][row.sampling_type || 0], { class: 'w-[100px]' }],
            ['状态', row => ['-空-', '实验中', '停止实验'][row.state || 0], { class: 'w-[100px]' }],
            ['创建时间', row => <DateTime value={(row.created || 0) * 1000} />, { class: 'w-[200px]' }],
            ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
            ['更新时间', row => <DateTime value={(row.updated || 0) * 1000} />, { class: 'w-[200px]' }],
            ['更新人', 'updated_operator_name', { class: 'w-[150px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn btn-outline btn-xs" onClick={() => onUpdateState(row.id || 0, row.state === 1 ? 2 : 1)}>{row.state === 1 ? '停止实验' : '启动实验'}</Button>
                <Button class="btn btn-outline btn-xs" onClick={() => onEditBtnClick(cloneDeep(row))}>编辑</Button>
                <Button class="btn btn-outline btn-xs" onClick={() => onDelete(row.id || 0)}>删除</Button>
                <Button class="btn btn-outline btn-xs" onClick={() => dataBack(row.id || 0)}>数据回收</Button>
              </div>
            ), {
              class: 'w-[280px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default ABTest
