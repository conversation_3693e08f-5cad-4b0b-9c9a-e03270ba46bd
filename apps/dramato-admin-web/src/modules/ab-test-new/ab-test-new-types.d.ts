declare namespace M {
  namespace ABTestNew {
    interface params {
      page_info?: {
        page_index?: number
        page_size?: number
      }
      id?: 0
      state?: 0
      sort?: 0
      sort_type?: 0
    }

    interface ABTest extends CreateAbTest {
      state: number
      priority: number
      updated: number
      updated_operator_id: string
      updated_operator_name: string
      created: number
      created_operator_id: string
      created_operator_name: string
      layer_id: number
      area_id?: number
    }

    interface statConfigItem {
      strategy_id: string
      vars_name: string
      traffic_interval: string
      whitelist: string
      is_strategy: boolean
    }
    interface ABTestStat {
      id?: number
      area_id?: number
      layer_id?: number
      description?: string
      priority?: number
      traffic_interval?: number
      sampling_type?: number
      strategy_layer_ids?: string
      state?: number
      updated?: number
      updated_operator_id?: string
      updated_operator_name?: string
      created?: number
      created_operator_id?: string
      created_operator_name?: string
      version_config?: statConfigItem[]
    }
    interface verStatItem {
      version_name: string
      user_total: number
      pay_rate: number
      pay_arpu: number
      pay_member_arpu: number
      pay_uv: number
      pay_pv: number
      ad_ecpm: number
      ad_request: number
      ad_show: number
      ad_click: number
      ad_income: number
    }

    interface ListResponse {
      list: ListItem[]
      total: number
    }
    interface StatResponse {
      exp_detail: {}
      version_stat: verStatItem[]
    }

    interface PriceConfig {
      c: string[]
      v: number
    }

    interface VersionConfig {
      traffic_interval?: number
      strategy_id?: string
      vars_name?: string
      whitelist?: string
      is_strategy?: boolean
    }

    interface CreateAbTest {
      id?: number
      layer_id?: number
      description?: string
      sampling_type?: number
      strategy_layer_ids?: string[]
      version_config?: VersionConfig[]
      traffic_interval?: number
    }
  }
}
