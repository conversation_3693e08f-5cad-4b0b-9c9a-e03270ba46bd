/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateABTest, apiDeleteABTest, apiStat, apiEditABTest, apiEditABTestStatus, apiGetABTestList } from './ab-test-new-api'
import { ABTestForm } from './ab-test-new-form'
import { ABTestStat } from './ab-test-new-stat'

export const useABTest = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    showADDialog,
    closeABTestDialog,
    closeStatDialog,
    currentABTest,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
    onUpdateState,
    onDelete,
    dataBack,
    statData,
    versionStatData,
    closeStatDataDialog,
  }
}

const Form = CreateForm<M.ABTestNew.params>()
const params = ref<M.ABTestNew.params>({
  // platform: 'ios',/
})

const Table = CreateTableOld<M.ABTestNew.ABTest>()
const list = ref<M.ABTestNew.ABTest[]>([])
const statData = ref<M.ABTestNew.ABTestStat>({})
const versionStatData = ref<M.ABTestNew.verStatItem[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)

const currentABTest = ref<M.ABTestNew.CreateAbTest>({
  version_config: [{}],
})

const sortVal = {
  1: { sort: 'created', sort_type: 'desc' },
  2: { sort: 'created', sort_type: 'asc' },
  3: { sort: 'id', sort_type: 'desc' },
  4: { sort: 'id', sort_type: 'asc' },
  5: { sort: 'priority', sort_type: 'desc' },
  6: { sort: 'priority', sort_type: 'asc' },
}

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  let sort = undefined
  let sort_type = undefined
  if (params.value.sort) {
    sort = sortVal[params.value.sort]['sort']
    sort_type = sortVal[params.value.sort]['sort_type']
  }
  const res = await apiGetABTestList({
    ...params.value,
    sort,
    sort_type,
    page_info: {
      page_index: _page, page_size: pageSize.value,
    },

  })
    .finally(() => {
      loading.value = false
    })
  const dataList = res.data?.list || []
  for (const i in dataList) {
    dataList[i]['strategy_layer_ids'] = dataList[i]['strategy_layer_ids'].split(',')
  }
  list.value = dataList
  total.value = res.data?.total || 0
  page.value = _page
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeABTestDialog = ref()
const closeStatDialog = ref()
const closeStatDataDialog = ref()

const showADDialog = () => {
  closeABTestDialog.value = openDialog({
    title: currentABTest.value.id ? '编辑ABTest' : '新建ABTest',
    body: () => <ABTestForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentABTest.value = {
    version_config: [{
      is_strategy: false,
    }],
    traffic_interval: 0,
  }
  showADDialog()
}

const onEditBtnClick = (r: M.ABTestNew.ABTest) => {
  currentABTest.value = {
    ...r,
    version_config: (r.version_config || []).map((i, idx) => {
      return {
        ...i,
        is_strategy: !!i.strategy_id,
      }
    }),
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeABTestDialog.value && closeABTestDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search(page.value)
}

const switchRequestParams = () => ({
  ...currentABTest.value,
  traffic_interval: Number(currentABTest.value.traffic_interval),
  version_config: (currentABTest.value.version_config || []).map((i, idx) => {
    const vars_name = idx === 0 ? 'base' : `exp${idx}`
    return {
      ...i,
      vars_name,
      strategy_id: !([9, 29].includes(currentABTest.value.layer_id || 0)) || i.is_strategy ? i.strategy_id : '',
    }
  }),
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    const data = JSON.parse(JSON.stringify(switchRequestParams()))
    data['strategy_layer_ids'] = data['strategy_layer_ids']?.join(',')
    await apiCreateABTest(data)
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    const data = JSON.parse(JSON.stringify(switchRequestParams()))
    data['strategy_layer_ids'] = data['strategy_layer_ids']?.join(',')
    await apiEditABTest(data)
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}

const onUpdateState = async (id: number, state: number) => {
  try {
    await apiEditABTestStatus({ id, state })
    showAlert('更新成功')
    void search(page.value)
  } catch (error: any) {
    showAlert(error.response.data.message || '更新失败', 'error')
  }
}

const onDelete = async (id: number) => {
  try {
    await apiDeleteABTest({ id })
    showAlert('删除成功')
    void search(page.value)
  } catch (error: any) {
    showAlert(error.response.data.message || '删除失败', 'error')
  }
}

const dataBack = async (id: number) => {
  const res = await apiStat({ id })
  statData.value = res.data?.exp_detail || {}
  versionStatData.value = res.data?.version_stat || []
  closeStatDialog.value = openDialog({
    title: '数据回收',
    body: () => <ABTestStat />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}
