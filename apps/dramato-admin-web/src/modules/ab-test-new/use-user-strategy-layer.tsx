/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserStrategyLayerList } from './ab-test-new-api'

export const useUserStrategyLayer = () => {
  return {
    Form,
    params,
    Table,
    layerList,
    loading,
    layerPage,
    layerPageSize,
    total,
    layerSearch,
    isUpdating,
    areas,
    layers,
  }
}

const Form = CreateForm<M.UserStrategyLayer.params>()
const params = ref<M.UserStrategyLayer.params>({
  // platform: 'ios',
})

const Table = CreateTableOld<M.UserStrategyLayer.ListItem>()
const layerList = ref<M.UserStrategyLayer.ListItem[]>([])
const loading = ref<boolean>(false)
const layerPage = ref<number>(0)
const layerPageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)
const areas = ref<Array<{
  area_id: number
  area_name: string
  layers: Array<
    {
      layer_id: number
      layer_name: string
    }
  >
}>>([])
const layers = ref<Array<{
  label: string
  value: number
}>>([])

// const currentAd = ref<M.UserStrategyLayer.CreateAd>({})

const layerSearch = async (_page?: number) => {
  _page = _page || layerPage.value + 1
  loading.value = true
  const res = await apiGetUserStrategyLayerList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * layerPageSize.value, size: layerPageSize.value,
    },
  })
    .finally(() => {
      loading.value = false
    })
  layerList.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  layerPage.value = _page
}
