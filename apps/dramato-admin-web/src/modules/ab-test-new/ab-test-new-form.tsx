/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, RadioGroup, showAlert, transformNumber } from '@skynet/ui'
import { isNumber, set, uniq } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { number, z } from 'zod'
import { useABTest } from './use-ab-test-new'
import { onMounted, ref } from 'vue'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { useUserStrategyLayer as useUserStrategyLayer2 } from './use-user-strategy-layer'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { apiGetArea } from './ab-test-new-api'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'

type ABTestFormOptions = {
  props: {}
}

export const ABTestForm = createComponent<ABTestFormOptions>({
  props: {},
}, props => {
  const {
    currentABTest,
    closeABTestDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useABTest()

  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  const { areas } = useUserStrategyLayer2()

  const MultiSelect = CreateFormMultiSelect<string>()
  const Form = CreateForm<M.ABTest.CreateAbTest>()
  const trafficIntervalRef = ref()

  const formRules = z.object({
    description: z.string().min(1, '请输入'),
    sampling_type: z.number().min(1, '请选择'),
    strategy_layer_ids: z.array(z.string().min(1, {
      message: '请选择',
    })),
    layer_id: z.number().min(1, '请选择'),
    series_package_ids: z.string().min(1, '请选择'),
  })

  const { error, validateAll } = useValidator(currentABTest, formRules)
  const currentVersion = ref<M.ABTestNew.VersionConfig>({})

  const trafficChange = (val: any) => {
    currentABTest.value.traffic_interval = val.target.value
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])
  })

  const seriesPackageStore = useSeriesPackageStore()

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentABTest.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('流量域'),
              'area_id',
              {
                type: 'select', options: areas.value.map(item => ({
                  label: item.area_name,
                  value: item.area_id,
                })),
                disabled: !!currentABTest.value.id,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('流量分桶'),
              'layer_id',
              {
                type: 'select', options: (areas.value.find(i => i.area_id === currentABTest.value?.area_id)?.layers || []).map(item => ({
                  label: item.layer_name,
                  value: item.layer_id,
                })),
                disabled: !!currentABTest.value.id,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('实验类型'),
              'type',
              {
                type: 'radio',
                options: [9, 29].includes(currentABTest.value.layer_id || 0) ? [{ label: '常规实验', value: 1, disabled: !!currentABTest.value.id }] : [
                  { label: '常规实验', value: 1, disabled: !!currentABTest.value.id },
                  { label: '高优实验(先画像，再分流，谨慎使用)', value: 2, disabled: !!currentABTest.value.id },
                  // { label: '分层筛选', value: 3 }
                  // { label: '随机筛选', value: 2 }
                ],
              },
              {
                // transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('实验描述'),
              'description',
              {
                type: 'textarea',
                placeholder: '请输入实验描述，以便识别',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('放量条'),
              'traffic_interval',
              {
                type: 'custom',
                render: () => {
                  return (
                    <>
                      <traffic-interval-slide>
                        <input type="range" min="0" max="1000" onChange={e => trafficChange(e)} value={currentABTest.value.traffic_interval} class="range range-primary" />
                      </traffic-interval-slide>
                      <traffic-interval-num>
                        <input
                          type="number"
                          class={mc('grow border')}
                          value={currentABTest.value.traffic_interval}
                          onInput={(e: Event) => {
                            currentABTest.value.traffic_interval = Number((e.target as HTMLInputElement).value || '')
                            if (!isNumber(currentABTest.value.traffic_interval)) {
                              currentABTest.value.traffic_interval = 0
                            }
                            if (Number(currentABTest.value.traffic_interval) > 1000) {
                              currentABTest.value.traffic_interval = 1000
                            }
                            if (Number(currentABTest.value.traffic_interval) < 0) {
                              currentABTest.value.traffic_interval = 0
                            }
                          }}
                        />
                      </traffic-interval-num>
                    </>
                  )
                },
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('取样方式'),
              'sampling_type',
              {
                type: 'radio',
                options: [{ label: '分层筛选', value: 1 },
                  // { label: '随机筛选', value: 2 }
                ],
              },
              {
                // transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('用户分层ID'),
              'strategy_layer_ids',
              {
                type: 'custom',
                render: () => {
                  return (
                    <>
                      <MultiSelect
                        class="w-full"
                        search={true}
                        popoverWrapperClass="z-popover-in-dialog"
                        options={list.value.map((n, index) => {
                          return { value: String(n.id), label: `${n.id}/${n.name}` }
                        })}
                        modelValue={currentABTest.value.strategy_layer_ids}
                        onUpdate: modelValue={e => {
                          currentABTest.value.strategy_layer_ids = e
                        }}
                      />
                    </>
                  )
                },
              },
              {
                transform: [
                  (raw?: unknown) => raw ? [+raw] : [],
                  (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                ] as const,
              },
            ],
            [11, 31, 9, 29].includes(currentABTest.value.layer_id || 0) && [
              [9, 29].includes(currentABTest.value.layer_id || 0) ? requiredLabel('剧包ID') : '剧包ID',
              'series_package_ids',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: seriesPackageStore.list.value.map((n, index) => {
                  return { value: n.id, label: `${n.id}/${n.package_name}` }
                }),
              },
              {
                transform: [
                  (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                  (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
                ] as const,
              },
            ],
            [
              '对照/实验组',
              'version_config',
              {
                type: 'custom',
                render: () => {
                  return (
                    <x-ab-test-group class="flex flex-col gap-y-2">
                      {/* <x-tips class="text-[14px] text-gray-500">请按照A/B testing平台对应填写</x-tips> */}
                      {
                        currentABTest.value.version_config && currentABTest.value.version_config.length > 0
                          ? currentABTest.value.version_config.map((item, index) => {
                            return (
                              <x-ab-test-version class="flex flex-col gap-y-2 rounded-md border p-2 text-[14px]" key={index} index={index}>
                                <x-title class="flex flex-row items-center justify-between">
                                  {index === 0 ? 'Base' : `Exp${index}`}
                                  <Icon name="ant-design:delete-filled" class={mc(index === 0 ? 'hidden' : '')} onClick={() => {
                                    if (!currentABTest.value.version_config || currentABTest.value.version_config.length <= 1) {
                                      return
                                    }
                                    currentABTest.value.version_config = currentABTest.value.version_config.filter((_, i) => i !== index)
                                  }}
                                  />
                                </x-title>
                                <x-ab-test-item>
                                  <label><span class="text-[#ff0000]">* </span>流量占比：{item.traffic_interval}% : 100%</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="number"
                                      class={mc('grow')}
                                      value={item.traffic_interval}
                                      onInput={(e: Event) => {
                                        item.traffic_interval = Number((e.target as HTMLInputElement).value || '')
                                        if (!isNumber(item.traffic_interval)) {
                                          item.traffic_interval = 0
                                        }
                                        if (Number(item.traffic_interval) > 100) {
                                          item.traffic_interval = 100
                                        }
                                        if (Number(item.traffic_interval) < 0) {
                                          item.traffic_interval = 0
                                        }
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>
                                {[9, 29].includes(currentABTest.value.layer_id || 0) && (
                                  <x-ab-test-item>
                                    <label><span class="text-[#ff0000]">* </span>是否修改卡点集</label>
                                    <label class={mc('flex items-center gap-1 h-8')}>
                                      <RadioGroup
                                        modelValue={!!item.is_strategy}
                                        onUpdate:modelValue={v => {
                                          item.is_strategy = v as boolean
                                          if (!item.is_strategy) {
                                            item.strategy_id = ''
                                          }
                                        }}
                                        options={[
                                          { label: '是', value: true },
                                          { label: '否', value: false },
                                        ]}
                                      />
                                    </label>
                                  </x-ab-test-item>
                                ) }
                                {
                                  [9, 29].includes(currentABTest.value.layer_id || 0) && !item.is_strategy ? null
                                    : (
                                        <x-ab-test-item>
                                          <label>{(index > 0 || [4, 24, 9, 29].includes(currentABTest.value.layer_id || 0)) && <span class="text-[#ff0000]">* </span>}{[9, 29].includes(currentABTest.value.layer_id || 0) ? '卡点集' : '执行策略组ID'}
                                            {currentABTest.value.layer_id === 2
                                            && <span class="ml-2 text-gray-500">示例：{`{"admob":100000065,"max":100000065}`}</span>}
                                          </label>
                                          <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                            <input
                                              type="text"
                                              class={mc('grow')}
                                              value={item.strategy_id}
                                              onFocus={() => {
                                                currentVersion.value = item
                                              }}
                                              onInput={(e: Event) => {
                                                currentVersion.value.strategy_id = (e.target as HTMLInputElement).value || ''
                                              }}
                                              onKeydown={(e: KeyboardEvent) => {
                                                if (e.key !== 'Enter') {
                                                  return
                                                }
                                                if (currentVersion.value.strategy_id === item.strategy_id) {
                                                  return
                                                }
                                                if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                                  return
                                                }
                                                currentABTest.value.version_config[index].strategy_id = currentVersion.value.strategy_id
                                              }}
                                              onBlur={() => {
                                                if (currentVersion.value.strategy_id === item.strategy_id) {
                                                  return
                                                }
                                                if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                                  return
                                                }
                                                currentABTest.value.version_config[index].strategy_id = currentVersion.value.strategy_id
                                              }}
                                              placeholder={(index === 0 && [11, 31].includes(currentABTest.value.layer_id || 0)) ? '对照组的策略ID为空=命中线上生效中的策略组（按优先级匹配）' : [9, 29].includes(currentABTest.value.layer_id || 0) ? '2-999' : ''}
                                            />
                                          </label>
                                        </x-ab-test-item>
                                      )
                                }
                                <x-ab-test-item>
                                  <label>白名单ID</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="text"
                                      class={mc('grow')}
                                      value={item.whitelist}
                                      onFocus={() => {
                                        currentVersion.value = item
                                      }}
                                      onInput={(e: Event) => {
                                        currentVersion.value.whitelist = (e.target as HTMLInputElement).value || ''
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>

                              </x-ab-test-version>
                            )
                          })
                          : null
                      }
                      <Button class="btn btn-outline btn-sm  w-[120px]" onClick={() => {
                        if (!currentABTest.value.version_config) {
                          return
                        }
                        currentABTest.value.version_config.push({})
                      }}
                      >新增实验组
                      </Button>
                    </x-ab-test-group>
                  )
                },
              },
              {
                class: mc('col-span-1'),
              },
            ],
          ] as any}
          data={currentABTest.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeABTestDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentABTest.value?.id ? void onCreate() : void onEdit()
            console.log('currentABTest.value', currentABTest.value)

            try {
              const exclude: string[] = []

              if (currentABTest.value.sampling_type !== 1) {
                exclude.push('strategy_layer_ids')
              }

              const hasSameId = currentABTest.value.version_config?.some(i => {
                const ids = i.strategy_id?.split(',') || []
                return uniq(ids).length !== ids.length
              })

              if ([9, 29].includes(currentABTest.value.layer_id || 0)) {
                const hasSameSeriesPackageId = currentABTest.value.version_config?.some(i => {
                  return i?.strategy_id && i?.strategy_id.length > 0
                })

                if (!hasSameSeriesPackageId) {
                  return showAlert('至少有一个对照组或实验组修改卡点集！', 'error')
                }

                const hasErrorSeriesPackageId = currentABTest.value.version_config?.some(i => {
                  const regex = /^\d+$/
                  const t = regex.test(i?.strategy_id || '')
                  if (i?.strategy_id && !t) {
                    return true
                  }
                  if (i?.strategy_id && (+(i.strategy_id || '') > 999 || +(i.strategy_id || '') <= 1)) {
                    return true
                  }
                  return false
                })

                if (hasErrorSeriesPackageId) {
                  return showAlert('卡点集格式(2-999)错误，请仔细检查核对！', 'error')
                }
              } else {
                exclude.push('series_package_ids')
              }

              if (hasSameId) {
                return showAlert('配置中存在相同策略ID，请仔细检查核对！', 'error')
              }

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
