/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, SvgIcon, transformNumber } from '@skynet/ui'
import { isNumber, set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { number, z } from 'zod'
import { useABTest } from './use-ab-test-new'
import { ref } from 'vue'

type ABTestFormOptions = {
  props: {}
}

export const ABTestStat = createComponent<ABTestFormOptions>({
  props: {},
}, props => {
  const {
    statData,
    versionStatData,
    closeABTestDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useABTest()

  const Form = CreateForm<M.ABTest.CreateAbTest>()

  const formatIconClass = (val1: number, val2: number) => {
    if (val1 < val2) {
      return 'text-[var(--error-6)] rotate-180'
    } else {
      return 'text-[var(--green-6)]'
    }
  }


  const getSeIds = (row: any) => {
    let arr = []
    let result = ''
    for (let i in row.version_config) {
      if (row.version_config[i]['strategy_id']) {
        arr.push(row.version_config[i]['strategy_id'])
      }
    }
    if (arr.length > 0) {
      result = arr.join(';')
    }

    return result
  }

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <ab-stat>
          详细指标请跳转：<a class="hover:text-[var(--brand-6)]" href="https://bi.aliyun.com/product/view.htm?module=dashboard&productId=95609dfc-6115-4974-8a9e-feb6c8695a23&menuId=5103a759-4432-4b61-a6cf-c756034befea" target='_blank' >BI数据空间看板</a>
        </ab-stat>
        <ab-stat>
          <ab-stat-row class="flex w-full">
            <ab-stat-col class="border w-[100px] p-1">实验id</ab-stat-col>
            <ab-stat-col class="border flex-1 p-1">{statData.value.id}</ab-stat-col>
          </ab-stat-row>
          <ab-stat-row class="flex w-full">
            <ab-stat-col class="border w-[100px] p-1">策略id</ab-stat-col>
            <ab-stat-col class="border flex-1 p-1">{getSeIds(statData.value)}</ab-stat-col>
          </ab-stat-row>
          <ab-stat-row class="flex w-full">
            <ab-stat-col class="border w-[100px] p-1">用户分层id</ab-stat-col>
            <ab-stat-col class="border flex-1 p-1">{statData.value.strategy_layer_ids}</ab-stat-col>
          </ab-stat-row>
          <ab-stat-row class="flex w-full">
            <ab-stat-col class="border w-[100px] p-1"></ab-stat-col>
            <ab-stat-col class="border flex-1 flex">
              <ab-stat-col class="border flex-1 text-center">
                进组用户数
              </ab-stat-col>
              {/* <ab-stat-col class="border flex-1 text-center">
                总ARPU
              </ab-stat-col>
              <ab-stat-col class="border flex-1 text-center">
                订阅ARPU
              </ab-stat-col>
              <ab-stat-col class="border flex-1 text-center">
                充值率
              </ab-stat-col>
              <ab-stat-col class="border flex-1 text-center">
                充值uv
              </ab-stat-col>
              <ab-stat-col class="border flex-1 text-center">
                充值pv
              </ab-stat-col> */}
            </ab-stat-col>
          </ab-stat-row>
          {
            versionStatData.value.map(item => {
              return (
                <ab-stat-row class="flex w-full">
                  <ab-stat-col class="border w-[100px] p-1">{item.version_name}</ab-stat-col>
                  <ab-stat-col class="border flex-1 flex">
                    <ab-stat-col class="border flex-1 text-center">
                      {item.user_total}
                    </ab-stat-col>
                    {/* <ab-stat-col class="border flex-1 text-center">
                      {item.pay_arpu}
                      {item.pay_arpu != versionStatData.value[0].pay_arpu &&
                        <ab-stat-icon class={formatIconClass(item.pay_arpu, versionStatData.value[0].pay_arpu)}>
                          {item.pay_arpu > versionStatData.value[0].pay_arpu ? '⬆' : '⬇'}
                        </ab-stat-icon>
                      }
                    </ab-stat-col>
                    <ab-stat-col class="border flex-1 text-center">
                      {item.pay_member_arpu}
                      {item.pay_member_arpu != versionStatData.value[0].pay_member_arpu &&
                        <ab-stat-icon class={formatIconClass(item.pay_member_arpu, versionStatData.value[0].pay_member_arpu)}>
                          {item.pay_member_arpu > versionStatData.value[0].pay_member_arpu ? '⬆' : '⬇'}
                        </ab-stat-icon>
                      }
                    </ab-stat-col>
                    <ab-stat-col class="border flex-1 text-center">
                      {item.pay_rate}
                      {item.pay_rate != versionStatData.value[0].pay_rate &&
                        <ab-stat-icon class={formatIconClass(item.pay_rate, versionStatData.value[0].pay_rate)}>
                          {item.pay_rate > versionStatData.value[0].pay_rate ? '⬆' : '⬇'}
                        </ab-stat-icon>
                      }
                    </ab-stat-col>
                    <ab-stat-col class="border flex-1 text-center">
                      {item.pay_uv}
                      {item.pay_uv != versionStatData.value[0].pay_uv &&
                        <ab-stat-icon class={formatIconClass(item.pay_uv, versionStatData.value[0].pay_uv)}>
                          {item.pay_uv > versionStatData.value[0].pay_uv ? '⬆' : '⬇'}
                        </ab-stat-icon>
                      }
                    </ab-stat-col>
                    <ab-stat-col class="border flex-1 text-center">
                      {item.pay_pv}
                      {item.pay_pv != versionStatData.value[0].pay_pv &&
                        <ab-stat-icon class={formatIconClass(item.pay_pv, versionStatData.value[0].pay_pv)}>
                          {item.pay_pv > versionStatData.value[0].pay_pv ? '⬆' : '⬇'}
                        </ab-stat-icon>
                      }
                    </ab-stat-col> */}
                  </ab-stat-col>
                </ab-stat-row>
              )
            })
          }
        </ab-stat>
      </div>
    </>
  )
})
