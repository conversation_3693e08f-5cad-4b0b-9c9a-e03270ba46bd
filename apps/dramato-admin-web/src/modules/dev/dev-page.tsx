import { createComponent } from '@skynet/shared'
import { AreaSelect } from '@skynet/ui'
import { ref } from 'vue'

type DevOptions = {
  props: {}
}
export const DevPage = createComponent<DevOptions>({
  props: {},
}, props => {
  const dev = ref<[number, number, number, number]>([0, 0, 0.5, 0.5])
  return () => (
    <div>
      <AreaSelect modelValue={dev.value} onUpdate:modelValue={v => {
        dev.value = v
      }} class="m-16"
      >
        <div class="size-[400px] bg-red-400" />
      </AreaSelect>
    </div>
  )
})
