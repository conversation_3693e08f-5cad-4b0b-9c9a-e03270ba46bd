declare namespace M {
  type Language = string
  namespace BannerManageData {
    interface BannerSearchProps {
      series_resource_id: number
      title: string
      en_cover: number
      other_cover: number
      resource_type: number
      release_round: number
      page_info?: {
        page_index?: number
        page_size?: number// 选填参数
      }
    }

    interface BannerListParam {
      name: string
      value: string
    }
    interface BannerListParams {
      resource_type: BannerListParam[]
      release_round: BannerListParam[]
    }

    interface coverLanguageItem {
      cover: string
      language: string[]
    }
    interface BannerManageListModel {
      series_resource_id: number
      title: string
      en_cover?: string
      bg_color?: string
      other_cover?: string
      resource_type: string
      release_round: string
      cover?: coverLanguageItem[]
    }

    interface BannerListRequestResponse {
      total: number
      list: BannerManageListModel[]
    }

    interface BannerCustomCoverSave {
      series_resource_id: number
      title: string
      img: string[]
    }

    interface Banner {
      id?: number// banner_id，编辑banner时必传
      app_id?: number | string
      language?: Language
      langs?: Language[]
      sort_no?: number//
      title?: string// 标题
      // cover?: string// 图片地址，TODO:也许是oss对象信息
      // vertical_cover?: string // 竖屏图片地址，TODO:也许是oss对象信息
      // bg_color?: string
      label_id?: number
      link_type?: number// 跳转地址类型，1：deeplink，2：h5链接
      link?: string// 链接
      lower_version?: string// 生效版本号
      state?: number// 状态，1：已生效，10：已停用
      updated?: number// 更新时间
      end_time?: number
      op_user_name?: string
      timing_type?: number// 定时类型，0：不支持，1：定时停用，2：定时开启
      timing_time?: string // 定时生效/停止时间
      start_time?: number

      tags?: BTag[]
      seriesResourceId?: number
      deeplink?: string
      imageType?: number
      commonCover?: string
      home_tab_info_module_id?: number // 模块id
    }

    interface BTag {
      label_id: number
      content: string
      language: string
      language_code: string
    }

    namespace Save {
      interface coverLanguageItem {
        cover: string
        language: string[]
        is_delete?: boolean
      }
      interface Params {
        series_resource_id?: number
        title?: string
        en_cover?: string
        bg_color?: string
        other_cover?: string
        cover?: coverLanguageItem[]
      }
    }
  }
}
