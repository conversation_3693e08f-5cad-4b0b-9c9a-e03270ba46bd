import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { onMounted } from 'vue'
import { Uploader } from '../common/uploader/uploader.tsx'
import { useBanner } from './use-banner.tsx'

export const CustomCoverForm = createComponent(null, () => {
  const {
    customCoverData,
    hideCustomCoverFormDialog,
    onSave,
    optionSelectedStatus,
    languageOptions,
    getConfig,
  } = useBanner()
  const Form = CreateForm<M.BannerManageData.Save.Params>()

  const formRules = z.object({
    cover: z.array(z.object({
      cover: z.string().min(1, '请上传图片'),
      language: z.array(z.string()).min(1, '请选择语言'),
    })).min(1, '请上传图片'),
  })

  const { error, validateAll } = useValidator(customCoverData, formRules)
  onMounted(() => {
    if (languageOptions.value.length == 0) {
      void getConfig()
    }
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid flex-1 grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(customCoverData.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('资源ID'),
              path: 'series_resource_id',
              input: {
                type: 'text',
                placeholder: '请输入资源ID',
                disabled: true,
              },
            },
            {
              label: requiredLabel('资源名'),
              path: 'title',
              input: {
                type: 'text',
                placeholder: '请输入资源名',
                disabled: true,
              },
            },
            () => (
              <div>
                <div><span class="mr-1 text-[red]">*</span>上传封面</div>
                <Uploader
                  accept="png,jpg,jpeg"
                  maxsize={1024 * 1024 * 10}
                  class="btn btn-outline btn-sm m-2 cursor-pointer"
                  onUploadSuccess={d => {
                    const imgUrl = d.temp_path?.includes('https://') ? d.temp_path : 'https://static-v1.mydramawave.com/banner/cover/' + d.temp_path
                    if (!customCoverData.value?.cover) {
                      customCoverData.value!.cover = []
                    }
                    customCoverData.value?.cover.push({
                      cover: imgUrl,
                      language: [],
                    })
                  }}
                  isImage={true}
                  uploadUrl="/banner/upload/cover"
                >
                  <span class="flex size-full items-center justify-center">上传图片</span>
                </Uploader>
                <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>

              </div>
            ),
            [
              '封面语言配置',
              'cover',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      {
                        (r.value || [])
                          .map((i: M.BannerManageData.Save.coverLanguageItem, idx: number) => (
                            <div class={mc('flex items-center gap-2 pb-2 border-b-2 border-gray-300', i.is_delete ? 'hidden' : '')}>
                              <img src={i.cover} class="mr-6 h-auto w-[150px]" />
                              <div class="flex flex-1 flex-wrap gap-3">
                                {
                                  languageOptions.value.map(item => (
                                    <label class="cursor-pointer">
                                      <input type="checkbox"
                                        value={item}
                                        checked={i.language.includes(item)}
                                        disabled={!i.language.includes(item) && optionSelectedStatus.value[item]}
                                        onChange={e => {
                                          const target = e.target as HTMLInputElement
                                          if (target.checked) {
                                            i.language.push(item)
                                            optionSelectedStatus.value[item] = true
                                          } else {
                                            const newArr1 = i.language.filter((s: string) => s !== item)
                                            i.language = newArr1
                                            optionSelectedStatus.value[item] = false
                                          }
                                        }}
                                        class="tm-radio mr-1 cursor-pointer" />
                                      {item}
                                    </label>
                                  ),
                                  )
                                }
                              </div>
                              <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                if (!customCoverData.value?.cover) {
                                  return
                                }
                                customCoverData.value.cover.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg',
              },
            ],
          ]}
          data={customCoverData.value as M.BannerManageData.Save.Params}
        />
      </div>
      <div class="mt-2 flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideCustomCoverFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)
            return
          }
          void onSave()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
