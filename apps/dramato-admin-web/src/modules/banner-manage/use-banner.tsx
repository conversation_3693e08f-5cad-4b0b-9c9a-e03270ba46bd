/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiGetBannerListParams, apiGetBannerColorList, apiGetBannerList, apiBannerCoverDel, apiBatchCreateBanner } from './banner-api'
import { apiGetDialogConfig } from '../episode-theatres/episode-theatre-api'
import { openDialog, showFailToast, showAlert } from '@skynet/ui'
import { CustomCoverForm } from './custom-cover-form'
import { number } from 'zod'
import { cloneDeep } from 'lodash-es'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

const searchForm = ref<M.BannerManageData.BannerSearchProps>({
  series_resource_id: 0,
  title: '',
  en_cover: 0,
  other_cover: 0,
  resource_type: 0,
  release_round: 0,
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})

const list = ref<M.BannerManageData.BannerManageListModel[]>([])
const total = ref<number>(0)
const applicationList = ref<Array<Required<M.Application>>>([])
const params = ref<M.BannerManageData.BannerListParams>({
  resource_type: [],
  release_round: [],
})
const loading = ref(false)
const colors = ref<string[]>([])
const customCoverData = ref<M.BannerManageData.Save.Params>()
const optionSelectedStatus = ref<any>({})
const getColors = () => {
  void apiGetBannerColorList().then((res: { data: { list: never[] } }) => {
    colors.value = [''].concat(res.data?.list) || []
  })
}

const getList = async () => {
  try {
    loading.value = true
    const rs = await apiGetBannerList(searchForm.value)
    list.value = rs.data?.list || []
    total.value = rs.data?.total || 0
    loading.value = false
  } catch (error: any) {
    showFailToast(error.response.data.message || '获取失败')
  }
}

const languageOptions = ref<string[]>([])
const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) {
    return
  }
  languageOptions.value = Object.values(res?.data.lang_items)
  if (customCoverData.value?.cover) {
    languageOptions.value.forEach((option: any) => {
      optionSelectedStatus.value[option] = false
    })
    customCoverData.value.cover.forEach((option: any) => {
      option.language?.map((item: any) => {
        optionSelectedStatus.value[item] = true
      })
    })
  } else { // 重置
    languageOptions.value.forEach((option: any) => {
      optionSelectedStatus.value[option] = false
    })
  }
  return languageOptions.value
}

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearch()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearch()
}

const onReset = () => {
  searchForm.value = {
    series_resource_id: 0,
    title: '',
    en_cover: 0,
    other_cover: 0,
    resource_type: 0,
    release_round: 0,
    page_info: {
      page_index: 1,
      page_size: 10,
    },
  }
  onSearch(true)
}

const getBannerListParams = async () => {
  const res = await apiGetBannerListParams()
  params.value = res.data!
}

const hideCustomCoverFormDialog = ref()
const showCustomCoverFormDialog = (d: Partial<M.BannerManageData.BannerManageListModel>) => {
  customCoverData.value = cloneDeep(d)
  // 修改初始化
  if (d.cover) {
    languageOptions.value.forEach((option: any) => {
      optionSelectedStatus.value[option] = false
    })
    d.cover.forEach((option: any) => {
      option.language?.map((item: any) => {
        optionSelectedStatus.value[item] = true
      })
    })
  } else { // 重置
    languageOptions.value.forEach((option: any) => {
      optionSelectedStatus.value[option] = false
    })
  }
  hideCustomCoverFormDialog.value = openDialog({
    title: d.cover ? '修改定制化封面' : '新增定制化封面',
    body: () => <CustomCoverForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}
const deleteCustomCover = async (d: Partial<M.BannerManageData.BannerManageListModel>) => {
  const res = await apiBannerCoverDel([d.series_resource_id!])
  showAlert('删除成功', 'success')
  onSearch()
}
const onSave = async () => {
  const data = [{
    series_resource_id: customCoverData.value?.series_resource_id,
    bg_color: customCoverData.value?.bg_color,
    en_cover: customCoverData.value?.en_cover,
    other_cover: customCoverData.value?.other_cover,
    cover: customCoverData.value?.cover,
  }]
  await apiBatchCreateBanner(data).then(rs => {
    hideCustomCoverFormDialog.value && hideCustomCoverFormDialog.value()
    onSearch()
  }).catch(error => {
    console.log(error)
    showAlert(error.response.data.ret_msg || '保存失败', 'error')
  })
}

export const useBanner = () => {
  return {
    searchForm,
    applicationList,
    list,
    total,
    loading,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    getColors,
    colors,
    getBannerListParams,
    params,
    showCustomCoverFormDialog,
    hideCustomCoverFormDialog,
    deleteCustomCover,
    customCoverData,
    onSave,
    optionSelectedStatus,
    languageOptions,
    getConfig,
  }
}
