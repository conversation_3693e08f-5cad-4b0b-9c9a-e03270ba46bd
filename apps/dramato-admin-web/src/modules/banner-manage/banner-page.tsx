import { createComponent, mc } from '@skynet/shared'
import { Button, TableColumnOld, showAlert, CreateForm, CreateTableOld, Pager, SvgIcon, transformInteger, transformNumber, openDialog } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useBanner } from './use-banner.tsx'
import { watch } from 'vue'
import { set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { useAppAndLangOptions } from '../options/use-app-options.tsx'
import { Uploader } from '../common/uploader/uploader.tsx'
import { apiBatchCreateBanner } from './banner-api.ts'

export const BannerPage = createComponent(null, () => {
  const { onSearch, total, params, searchForm, onReset, list, colors, onPageChange, onPageSizeChange,
    loading, getColors, getBannerListParams, showCustomCoverFormDialog, deleteCustomCover } = useBanner()

  const Form = CreateForm<M.BannerSearchProps>()
  const Table = CreateTableOld<M.BannerManageData.BannerManageListModel>()

  const columns: TableColumnOld<M.BannerManageData.BannerManageListModel>[] = [
    ['资源ID', 'series_resource_id', { class: 'w-[60px]' }],
    ['资源名', 'title', { class: 'w-[160px]' }],
    ['配色方案', row => {
      return (
        <select class="select select-bordered w-full max-w-xs"
          onChange={(e: Event) => {
            row.bg_color = (e.target as HTMLSelectElement).value
          }}
        >
          {colors.value.map(color => (
            <option selected={color === row.bg_color} value={color}>{color}</option>
          ))}
        </select>
      )
    }, { class: 'w-[160px]' }],

    ['英语封面', banner => (
      <x-upload-cover class="grid gap-y-2">
        <Uploader
          accept="png,jpg,jpeg"
          maxsize={1024 * 1024 * 10}
          class="cursor-pointer overflow-hidden rounded-md border border-dashed"
          onUploadSuccess={d => {
            banner.en_cover = d.temp_path
          }}
          isImage={true}
          uploadUrl="/banner/upload/cover"
        >
          {
            banner.en_cover
              ? (
                  <div class="relative">
                    <img src={banner.en_cover.includes('https://') ? banner.en_cover : 'https://static-v1.mydramawave.com/banner/cover/' + banner.en_cover} class="size-full object-cover" />
                    <SvgIcon class="absolute right-2 top-2 size-4 cursor-pointer" name="ic_close_fill" onClick={(e: Event) => {
                      e.stopPropagation()
                      banner.en_cover = ''
                    }}
                    />
                  </div>
                )
              : <span class="flex size-full h-48 items-center justify-center">上传封面</span>
          }
        </Uploader>
        <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
      </x-upload-cover>
    ),
    { class: 'w-[320px]' },
    ],
    ['小语种封面', banner => (
      <x-upload-cover class="grid gap-y-2">
        <Uploader
          accept="png,jpg,jpeg"
          maxsize={1024 * 1024 * 10}
          class="cursor-pointer overflow-hidden rounded-md border border-dashed"
          onUploadSuccess={d => {
            banner.other_cover = d.temp_path
          }}
          isImage={true}
          uploadUrl="/banner/upload/cover"
        >
          {
            banner.other_cover
              ? (
                  <div class="relative">
                    <img src={banner.other_cover.includes('https://') ? banner.other_cover : 'https://static-v1.mydramawave.com/banner/cover/' + banner.other_cover} class="size-full object-cover" />
                    <SvgIcon class="absolute right-2 top-2 size-4 cursor-pointer" name="ic_close_fill" onClick={(e: Event) => {
                      e.stopPropagation()
                      banner.other_cover = ''
                    }}
                    />
                  </div>
                )
              : <span class="flex size-full h-48 items-center justify-center">上传封面</span>
          }
        </Uploader>
        <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
      </x-upload-cover>
    ),
    { class: 'w-[320px]' },
    ],
    ['定制封面配置信息', row => {
      return (
        <div>{row.cover ? row.cover.map(item => item.language).join('、') : '无'}</div>
      )
    }, { class: 'w-[160px]' }],
    ['定制封面操作', row => {
      return (
        <div class="flex flex-col items-center justify-center gap-2">
          <Button class={mc('btn btn-xs', row.cover ? 'btn-outline' : 'btn-primary')} onClick={() => showCustomCoverFormDialog(row.title ? row : {})}>{row.cover ? '编辑' : '新增'}</Button>
          <Button class={mc('btn btn-outline btn-xs hidden', row.cover && 'block')}
          // onClick={() => deleteCustomCover(row)}
            onClick={() => {
              const showTipsDialog = () => {
                const hideDialog = openDialog({
                  title: '',
                  mainClass: 'pb-0 px-5',
                  body: (
                    <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                      <x-status-body>是否确认删除定制封面?</x-status-body>
                      <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                        <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                        <button class="btn btn-primary btn-sm" onClick={async () => {
                          await deleteCustomCover(row)
                          hideDialog()
                        }}
                        >确定
                        </button>
                      </x-status-footer>
                    </x-status-confirm-dialog>
                  ),
                })
              }
              showTipsDialog()
            }}
          >删除</Button>
        </div>
      )
    }, { class: 'w-[160px]' }],
    ['配色展示', banner => (
      <div
        key={banner.series_resource_id}
        class="relative h-[160px] w-full overflow-hidden rounded-md opacity-100 transition-opacity duration-500"
      >
        <div class="absolute left-0 top-0 w-full p-2 text-white" style={{ backgroundColor: banner.bg_color }} />
        {banner.en_cover ? <img src={banner.en_cover?.includes('https://') ? banner.en_cover : 'https://static-v1.mydramawave.com/banner/cover/' + banner.en_cover} alt={banner.title} class="mt-4 w-full object-cover" /> : <span class="mt-4 inline-block h-8">暂无封面</span>}
      </div>
    ),
    { class: 'w-[320px]' },
    ],
  ]

  const { appOptions } = useAppAndLangOptions(() => '', {
    onSuccess: () => {
      getColors()
      void getBannerListParams()
      onSearch()
    },
  })

  const onBatchSave = () => {
    void apiBatchCreateBanner(list.value.map(item => ({
      series_resource_id: item.series_resource_id,
      bg_color: item.bg_color,
      en_cover: item.en_cover,
      other_cover: item.other_cover,
    })))
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/banner">Banner管理</RouterLink></li>
          </ul>
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-between">
            <span>Banner列表</span>
            <x-table-actions-right class="flex gap-x-2">
              {/* <Button onClick={onBatchSave} class="btn btn-sm">提交到线上</Button> */}
              <Button onClick={() => {
                const hideDeleteDialog = openDialog({
                  title: '删除',
                  mainClass: 'pb-0 px-5',
                  body: (
                    <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                      <x-delete-episode-body>确认是否提交到线上?</x-delete-episode-body>
                      <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                        <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                        <button class="btn btn-primary btn-sm" onClick={() => {
                          void apiBatchCreateBanner(list.value.map(item => ({
                            series_resource_id: item.series_resource_id,
                            bg_color: item.bg_color,
                            en_cover: item.en_cover,
                            other_cover: item.other_cover,
                          }))).then(() => {
                            showAlert('提交成功！')
                            hideDeleteDialog()
                          }).catch(error => {
                            showAlert('操作失败')
                          })
                        }}
                        >确定
                        </button>
                      </x-delete-episode-footer>
                    </x-delete-episode-confirm-dialog>
                  ),
                })
              }} class="btn btn-sm">提交到线上</Button>

            </x-table-actions-right>
          </x-table-actions>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              if (path === 'en_cover' || path === 'other_cover') {
                console.log('path', path, value)
                set(searchForm.value, path, value === 'true' ? true : value === 'false' ? false : value)
                return
              }
              set(searchForm.value, path, value)
            }}
            items={[
              {
                label: '资源ID',
                path: 'series_resource_id',
                input: {
                  type: 'number',
                },
                transform: transformNumber,
              },
              {
                label: '资源名',
                path: 'title',
                input: {
                  type: 'text',
                },
              },
              {
                label: '是否有英语banner图',
                path: 'en_cover',
                input: {
                  type: 'select',
                  options: [
                    { label: '是', value: 2 },
                    { label: '否', value: 1 },
                  ],
                },
                transform: transformInteger,
              },
              {
                label: '是否有小语种banner图',
                path: 'other_cover',
                input: {
                  type: 'select',
                  options: [
                    { label: '是', value: 2 },
                    { label: '否', value: 1 },
                  ],
                },
                transform: transformInteger,
              },
              {
                label: '发行轮次',
                path: 'release_round',
                transform: transformInteger,
                input: {
                  type: 'select',
                  options: params.value.release_round.map(item => ({ label: item.name, value: item.value })),
                  class: 'col-span-1',
                },
                class: 'col-span-1',
              },
              {
                label: '发行类型',
                path: 'resource_type',
                transform: transformInteger,
                input: {
                  type: 'select',
                  options: params.value.resource_type.map(item => ({ label: item.name, value: item.value })),
                },
              },
            ]}
          />
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default BannerPage
