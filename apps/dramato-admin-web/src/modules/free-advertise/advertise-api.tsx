import { httpClient } from 'src/lib/http-client'

export const apiGetAdvertiseList = (data: M.AdvertiseSearchParams) => {
  return httpClient.post<ApiResponse<{
    list: M.Advertise[]
    page_info: {
      offset: number
      size: number
      has_more: boolean
      total: number
    }
  }>>('/ads/free/list', data)
}

export const apiCreateAdvertise = (data: M.Advertise) => {
  return httpClient.post<ApiResponse<M.Advertise>>('/ads/free/create', data)
}

export const apiEditAdvertise = (data: M.Advertise) => {
  return httpClient.post<ApiResponse<M.Advertise>>('/ads/free/edit', data)
}
