/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformNumber } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { ref, watch } from 'vue'
import { z } from 'zod'
import { useAdvertisePage } from './use-advertise'
type AdvertiseFormOptions = {
  props: {
    advertise?: M.Advertise
  }
  emits: {
    cancel: Fn
    submit: (data: M.Advertise) => void
  }
}
export const AdvertiseForm = createComponent<AdvertiseFormOptions>({
  props: {
    advertise: {
      id: undefined,
      app_id: undefined,
      name: '',
      ad_unit: '',
      ad_type: 'Rewarded',
      coins: undefined,
      period: 'daily',
      mediation: 'bidding',
      count_limit: undefined,
      platform: 'ios',
      ad_platform: 'admob',
    },
  },
  emits: {
    cancel: fn,
    submit: fn,
  },
}, (props, { emit }) => {
  const Form = CreateForm<M.Advertise>()
  const formData = ref<M.Advertise>(props.advertise)
  const formRules = z.object({
    name: z.string().min(1, '必填'),
    ad_unit: z.string().min(1, '必填'),
    ad_type: z.string().min(1, '必填'),
    coins: z.number().min(1, '必填').max(999999, '最大999999').optional(),
    period: z.string().min(1, '必填').optional(),
    count_limit: z.number().min(1, '必填').max(999, '最大999').optional(),
    mediation: z.string().min(1, '必填'),
    version: z.string().min(1, '必填'),
    platform: z.string().min(1, '必填'),
  })
  const { error, validateAll } = useValidator(formData, formRules)
  const { periodMap } = useAdvertisePage()

  watch(() => props.advertise, () => {
    formData.value = props.advertise
  }, {
    immediate: true,
  })

  const submit = () => {
    const exclude: any[] = []
    if (formData.value.ad_type === 'Native') {
      exclude.push('coins', 'count_limit', 'period')
    }
    if (!validateAll({ exclude })) {
      console.log(error.value)
      return
    }
    emit('submit', formData.value)
  }

  return () => (
    <x-advertise-form>
      <Form
        class="grid gap-y-3 grid-cols-1"
        hasAction={false}
        error={error.value}
        data={formData.value}
        onChange={(path, value) => {
          set(formData.value || {}, path, value)
        }}
        items={[
          {
            label: () => (
              <x-label-with-tips class="flex flex-col space-y-1">
                <div class="text-sm text-gray-500">请检查广告组Ad unit ID与app端的对应正确</div>
                <div>{requiredLabel('广告ID')}</div>
              </x-label-with-tips>
            ),
            path: 'id',
            input: {
              type: 'custom',
              render: () => <x-advertise-id>{formData.value?.id || 'To be generated'}</x-advertise-id>,
            },
          },
          {
            label: '平台',
            path: 'platform',
            input: {
              type: 'select',
              options: [{
                label: 'iOS',
                value: 'ios',
              }, {
                label: 'Android',
                value: 'android',
              }],
              autoInsertEmptyOption: false,
              disabled: !!formData.value.id,
            },
          },
          {
            label: requiredLabel('最低版本号'),
            path: 'version',
            input: {
              type: 'text',
            },
          },
          {
            label: requiredLabel('广告位名称'),
            path: 'name',
            input: {
              type: 'text',
              placeholder: '请输入命名，以便识别',
            },
          },
          [
            requiredLabel('聚合平台'),
            'ad_platform',
            {
              type: 'radio',
              options: [{ label: 'admob', value: 'admob' }, { label: 'max', value: 'max' }, { label: 'meta', value: 'meta' }],
            },
            {
              class: mc('col-span-1'),
            },
          ],
          {
            label: requiredLabel('Ad unit'),
            path: 'ad_unit',
            input: {
              type: 'text',
              placeholder: '请从广告平台提取填入',
            },
          },
          {
            label: requiredLabel('类型'),
            path: 'ad_type',
            input: {
              type: 'select',
              options: [
                { label: 'Rewarded', value: 'Rewarded' },
                { label: 'Interstitial', value: 'Interstitial' },
                { label: 'Native', value: 'Native' },
                { label: 'App Open', value: 'App Open' },
                { label: 'Banner', value: 'Banner' },
              ],
            },
          },
          {
            label: 'Coins 价格',
            path: 'coins',
            input: {
              type: 'number',
              placeholder: '参考价值',
            },
            transform: transformNumber,
            class: mc(formData.value.ad_type === 'Native' ? 'hidden' : ''),
          },
          [
            {
              label: '频次',
              path: 'count_limit',
              input: {
                type: 'number',
                placeholder: '次',
              },
              transform: transformNumber,
              class: mc('w-2/3', formData.value.ad_type === 'Native' ? 'hidden' : ''),
            },
            {
              label: '',
              path: 'period',
              input: {
                type: 'select',
                options: (Object.keys(periodMap) as Array<keyof typeof periodMap>).map((item: keyof typeof periodMap) => {
                  return {
                    label: periodMap[item],
                    value: item,
                  }
                }),
              },
              class: mc('w-1/3 justify-end', formData.value.ad_type === 'Native' ? 'hidden' : ''),
            },
          ],
          {
            label: requiredLabel('Mediation模式'),
            path: 'mediation',
            input: {
              type: 'select',
              options: [
                { label: 'bidding', value: 'bidding' },
                { label: 'waterfall', value: 'waterfall' },
              ],
            },
          },
        ]}
      />
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => {
          emit('cancel')
        }}
        >取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={submit}>确定</Button>
      </div>
    </x-advertise-form>
  )
})
