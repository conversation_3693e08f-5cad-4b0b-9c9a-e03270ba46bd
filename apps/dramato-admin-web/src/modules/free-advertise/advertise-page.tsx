import { createComponent, fn } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTableOld, DateTime, Pager } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { RouterLink } from 'vue-router'
import { useAdvertisePage } from './use-advertise'
import { set } from 'lodash-es'
import { onMounted, watch } from 'vue'
type AdvertisePageOptions = {
  props: {
    checkedItem?: M.Advertise[]
    platform?: string
  }
  emits: {
    add: (item: M.Advertise) => void
    remove: (item: M.Advertise) => void
  }
}
export const FreeAdvertisePage = createComponent<AdvertisePageOptions>({
  props: {
    checkedItem: [],
    // @ts-expect-error never mind
    platform: undefined,
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  const Form = CreateForm<M.AdvertiseSearchParams>()
  const Table = CreateTableOld<M.Advertise>()
  const { onSearch, onReset, onCreate, onEdit, searchForm, list, total, periodMap } = useAdvertisePage()

  watch(() => props.platform, () => {
    console.log('platform', props.platform)
    if (props.platform) {
      set(searchForm.value, 'platform', props.platform)
    }
  }, { immediate: true })

  onMounted(() => {
    onSearch(true)
  })
  return () => (
    <NavFormTablePager>
      {{
        nav: (
          <x-hide-when-in-dialog>
            <ul>
              <li><RouterLink to="/advertise">广告管理</RouterLink></li>
            </ul>
          </x-hide-when-in-dialog>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              {
                label: '平台',
                path: 'platform',
                input: {
                  type: 'select',
                  class: 'w-[300px]',
                  autoInsertEmptyOption: false,
                  options: [
                    { label: 'iOS', value: 'ios' },
                    { label: 'Android', value: 'android' },
                  ],
                  disabled: !!props.platform,
                },
              },
              [
                '聚合平台',
                'ad_platform',
                {
                  type: 'select',
                  options: [{ label: 'admob', value: 'admob' }, { label: 'max', value: 'max' }, { label: 'meta', value: 'meta' }],
                },
              ],
              { label: '广告ID', path: 'advertise_id', input: { type: 'text' } },
              {
                label: 'Ad Unit',
                path: 'advertise_unit',
                input: { type: 'text' },
              },
            ]}
          />
        ),
        tableActions: () => ((
          <x-hide-when-in-dialog>
            <x-table-actions class="w-full flex justify-between items-center">
              <span>广告位/广告组</span>
              <Button class="btn btn-primary btn-sm" onClick={onCreate}>新增广告</Button>
            </x-table-actions>
          </x-hide-when-in-dialog>
        )
        ),

        table: () => (
          <Table list={list.value} columns={[
            [
              '',
              row => {
                const id = row.id
                return (
                  <x-show-when-in-dialog class="hidden">
                    <Checkbox
                      label=""
                      disabled={!!!id}
                      modelValue={props.checkedItem.map(i => i.id).includes(id)}
                      onUpdate:modelValue={(value: unknown) => {
                        if (value) {
                          const found = list.value.find(i => i.id === id)
                          if (!found) return
                          emit('add', found)
                        } else {
                          emit('remove', row)
                        }
                      }}
                    />
                  </x-show-when-in-dialog>
                )
              },
              { class: 'w-[30px]' },
            ],
            ['广告ID', 'id', { class: 'w-[200px]' }],
            ['广告位名称', 'name', { class: 'w-[200px]' }],
            ['广告类型', 'ad_type', { class: 'w-[200px]' }],
            ['聚合平台', 'ad_platform', { class: 'w-[100px]' }],
            ['Ad unit', 'ad_unit', { class: 'w-[200px]' }],
            ['Coins价格', 'coins', { class: 'w-[200px]' }],
            ['频次', row => {
              return (
                <x-frequency class="flex flex-col">
                  <span>{row.count_limit}次/{periodMap[row.period as keyof typeof periodMap] ?? '--'}</span>
                  <span class="text-gray-500 text-sm">per user</span>
                </x-frequency>
              )
            }, { class: 'w-[100px]' }],
            ['Mediation模式', 'mediation', { class: 'w-[200px]' }],
            ['创建时间', row => <DateTime value={(row.created || 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />, { class: 'w-[200px]' }],
            ['创建人', 'create_user', { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={(row.updated || 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />, { class: 'w-[200px]' }],
            ['更新人', 'update_user', { class: 'w-[100px]' }],
            ['操作', row => {
              return (
                <x-hide-when-in-dialog class="flex items-center justify-center">
                  <x-button class="btn btn-primary btn-link btn-sm" onClick={() => onEdit(row)}>编辑</x-button>
                </x-hide-when-in-dialog>
              )
            }, { class: 'w-[80px] text-center hide-when-in-dialog' }],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                v-model:page={searchForm.value.page_info.offset}
                v-model:size={searchForm.value.page_info.size}
                onUpdate:page={() => onSearch()}
                onUpdate:size={() => onSearch()}
                total={total.value}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default FreeAdvertisePage
