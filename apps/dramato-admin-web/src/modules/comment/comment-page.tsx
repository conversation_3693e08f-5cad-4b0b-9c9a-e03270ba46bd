/* eslint-disable @typescript-eslint/no-explicit-any */
import { bindLoading, createComponent, mc, openedInDev } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTable, CreateTableOld, DateTime, Icon, openDialog, Pager, showFailToast, showSuccessToast, SvgIcon, TableColumn, transformDatetime, transformNumber } from '@skynet/ui'
import { AxiosError, isAxiosError } from 'axios'
import dayjs from 'dayjs'
import { cloneDeep, get, isEmpty, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref } from 'vue'
import { z } from 'zod'
import { apiBatchDeleteComment, apiDeleteComment, apiExportExcel, apiGetCommentList, apiGetCommentStat, apiGetReportList, apiReport, apiAuditComment } from './comment-api'

import { langKey, langValue } from 'src/modules/resource/constant'
import { useTableStore } from '@skynet/ui/table/use-table-store'
import { keepError } from 'src/lib/http-client'

export const CommentPage = createComponent(null, props => {
  const QueryForm = CreateForm<M.QueryCommentParams>()
  const ReportQueryForm = CreateForm<M.CommentReportQuery>()
  const Table1 = CreateTable<M.DataPanel>()
  const Table2 = CreateTable<M.DataPanel>()
  const Table3 = CreateTable<M.DataPanel>()
  const Table = CreateTable<M.CommentItem>()
  const ReportTable = CreateTable<M.CommentReportItem>()
  const tableStore = useTableStore('commentList')
  const { selectedRowIds } = tableStore
  const list = ref<M.CommentItem[]>([])

  const dataPanelValue = ref<M.DataPanel[]>([])

  const tableLoading = ref(false)
  const total = ref(0)
  const queryForm = ref<M.QueryCommentParams>({
    page_size: 10,
    page: 1,
    created_start: dayjs().subtract(openedInDev ? 300 : 1, 'day').startOf('date').format('YYYY-MM-DD HH:mm:ss'),
    created_end: dayjs().startOf('date').subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss'),
    language_codes: ['all'],
    resource_title: '',
    resource_id: undefined,
    series_key: '',
    series_title: '',
    episode_key: '',
    user_id: undefined,
    sort_type: 'desc',
    sort_column: 'created',
    user_type: undefined,
  })

  const onPageSizeChange = (page_size: number) => {
    queryForm.value.page_size = page_size
    void onQuery()
  }
  const onPageChange = (page: number) => {
    queryForm.value.page = page

    void getList()
  }

  const onReset = () => {
    queryForm.value.language_codes = ['all']
    queryForm.value.resource_title = ''
    queryForm.value.resource_id = undefined
    queryForm.value.series_key = ''
    queryForm.value.series_title = ''
    queryForm.value.episode_key = ''
    queryForm.value.user_id = undefined
    queryForm.value.created_start = dayjs().subtract(1, 'day').startOf('date').format('YYYY-MM-DD HH:mm:ss')
    queryForm.value.created_end = dayjs().startOf('date').subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss')
  }

  const onQueryChange = async () => {
    void onQuery()
    await getCommentStat()
  }

  const onQuery = async () => {
    queryForm.value.page = 1
    await getList()
  }

  const onClickBatchDelete = () => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否批量删除？</x-status-body>
          <x-status-footer class="flex justify-end gap-x-[10px] w-full">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                console.log('selectedRowIds.value:', selectedRowIds.value)
                await apiBatchDeleteComment(selectedRowIds.value)
                void getList()
                showSuccessToast('删除成功')
              } catch (error: unknown) {
                if (isAxiosError(error)) {
                  showFailToast(error.response?.data.message ?? '操作失败')
                }
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }
  const onClickAudit = (id: number) => {
    if (id > 0) { selectedRowIds.value = [id] }
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>{id > 0 ? '是否审核通过评论ID【' + id + '】' : '是否批量审核？'}</x-status-body>
          <x-status-footer class="flex justify-end gap-x-[10px] w-full">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                console.log('selectedRowIds.value:', selectedRowIds.value)
                await apiAuditComment(selectedRowIds.value)
                void getList()
                showSuccessToast('审核成功')
              } catch (error: unknown) {
                if (isAxiosError(error)) {
                  showFailToast(error.response?.data.message ?? '操作失败')
                }
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }
  const getCommentStat = async () => {
    const queryData = cloneDeep(queryForm.value)
    queryData.created_start = dayjs(queryData.created_start).unix()
    queryData.created_end = dayjs(queryData.created_end).unix()

    if (queryData.user_id) {
      queryData.user_id = Number(queryData.user_id)
    } else {
      queryData.user_id = undefined
    }
    const res = await apiGetCommentStat({
      ...queryData,
      language_codes: queryData.language_codes.includes('all') ? [] : queryData.language_codes,
    })
    if (!res.data) return
    dataPanelValue.value = [res.data || {}]
  }

  const toggleSort = (column: 'created' | 'like_num' | 'id') => () => {
    queryForm.value.sort_column = column
    queryForm.value.sort_type = queryForm.value.sort_type == 'desc' ? 'asc' : 'desc'
    void onQueryChange()
  }

  const onClickDelete = (row: M.CommentItem) => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否删除评论ID【{row.id}】</x-status-body>
          <x-status-footer class="flex justify-end gap-x-[10px] w-full">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                await apiDeleteComment({ id: row.id })
                void onQuery()
                showSuccessToast('操作成功')
              } catch (error: unknown) {
                if (isAxiosError(error)) {
                  showFailToast(error.response?.data.message ?? '操作失败')
                }
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const columns: TableColumn<M.CommentItem>[] = [
    ['资源名', 'resource_title'],
    ['资源ID', 'resource_id'],
    ['剧名', 'series_title'],
    ['剧ID', 'series_key'],
    ['集数', 'serial_number'],
    ['集ID', 'episode_key'],
    ['语言', 'default_language_code'],
    ['用户ID', 'user_id'],
    ['机审状态', row => (
      <span class={mc(' whitespace-nowrap', row.audit_status == 2 ? 'text-red-500' : row.audit_status == 1 ? 'text-green-500' : 'text-gray-500')}>{[
        { label: '待人工审核', value: 2 },
        { label: '已通过', value: 1 },
        { label: '待人工审核', value: 0 },
      ].find(item => item.value === row.audit_status)?.label ?? '待人工审核'}
      </span>
    )],
    ['评论内容',
      row => (
        <span>{row.content}{row.status == 0 && (` (${dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm:ss')} 已删除)`)}</span>
      ),
      { width: 'minmax(auto,30em)' },
    ],
    ['评论ID', 'id'],
    [
      () => (
        <span class="flex items-center gap-x-1 cursor-pointer text-blue-400" onClick={toggleSort('like_num')}>
          点赞数
          {queryForm.value.sort_column === 'like_num' && (
            <Icon key={queryForm.value.sort_type} name={queryForm.value.sort_type === 'desc' ? 'line-md:chevron-small-down' : 'line-md:chevron-small-up'} />
          )}
        </span>
      ),
      'like_num',
    ],
    [
      () => (
        <span class="flex items-center gap-x-1 cursor-pointer text-blue-400" onClick={toggleSort('created')}>
          创建日期
          {queryForm.value.sort_column === 'created' && (
            <Icon key={queryForm.value.sort_type} name={queryForm.value.sort_type === 'desc' ? 'line-md:chevron-small-down' : 'line-md:chevron-small-up'} />
          )}
        </span>
      ),
      row => !!row.created ? dayjs(row.created * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'],

    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <Button class="btn-primary btn btn-sm mr-1" onClick={() => onClickAudit(row.id)}>审核</Button>
            {row.status === 0 ? '已删除' : <Button class="btn btn-sm" onClick={() => onClickDelete(row)}>删除</Button>}
          </div>
        )
      },
      { class: 'sticky right-0 bg-white shadow-lg' },
    ],
  ]

  const getList = async () => {
    if (!queryForm.value.created_start) {
      queryForm.value.created_start = dayjs().subtract(1, 'day').startOf('date').format('YYYY-MM-DD HH:mm:ss')
    }
    if (!queryForm.value.created_end) {
      queryForm.value.created_end = dayjs().startOf('date').subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss')
    }
    if (queryForm.value.language_codes.length == 0) {
      queryForm.value.language_codes = ['all']
    }
    const queryData = JSON.parse(JSON.stringify(queryForm.value))
    if (queryData.user_id) {
      queryData.user_id = Number(queryData.user_id)
    } else {
      queryData.user_id = undefined
    }
    const res = await bindLoading(apiGetCommentList({
      ...queryData,
      language_codes: queryData.language_codes.includes('all') ? [] : queryData.language_codes,
      created_start: dayjs(queryData.created_start).unix(),
      created_end: dayjs(queryData.created_end).unix(),
      page_info: {
        page_index: queryData.page,
        page_size: queryData.page_size,
      },
    }), tableLoading).catch(keepError(e => {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }))
    if (!res.data) return
    list.value = res.data.list as unknown as M.CommentItem[] || []
    total.value = res.data.total || 0
    selectedRowIds.value = []
  }

  const languageOptions = ['all', ...langKey]
  const langValueOptions = ['全部', ...langValue]

  const exportExcel = async () => {
    if (!queryForm.value.created_start) {
      queryForm.value.created_start = dayjs().subtract(1, 'day').startOf('date').format('YYYY-MM-DD HH:mm:ss')
    }
    if (!queryForm.value.created_end) {
      queryForm.value.created_end = dayjs().startOf('date').subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss')
    }

    const queryData = JSON.parse(JSON.stringify(queryForm.value))
    if (queryData.user_id) {
      queryData.user_id = Number(queryData.user_id)
    } else {
      queryData.user_id = undefined
    }
    const blob = await apiExportExcel({
      ...queryData,
      language_codes: queryData.language_codes.includes('all') ? [] : queryData.language_codes,
      created_start: dayjs(queryData.created_start).unix(),
      created_end: dayjs(queryData.created_end).unix(),
      page_info: {
        page_index: queryData.page,
        page_size: queryData.page_size,
      },
    }).then(response => response.blob())
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `评论列表_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const setDayRange = (type: 'last7d' | 'yesterday' | 'last24h' | 'today') => {
    if (type == 'last7d') {
      queryForm.value.created_start = dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      queryForm.value.created_end = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    } else if (type == 'yesterday') {
      queryForm.value.created_start = dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      queryForm.value.created_end = dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss')
    } else if (type == 'last24h') {
      queryForm.value.created_start = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss')
      queryForm.value.created_end = dayjs().format('YYYY-MM-DD HH:mm:ss')
    } else if (type == 'today') {
      queryForm.value.created_start = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
      queryForm.value.created_end = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }
  }

  onMounted(async () => {
    await getList()
    await getCommentStat()
    void searchReport(1)
  })

  const currentTab = ref('comment')

  const onListTypeChange = (type: string) => {
    currentTab.value = type
  }

  const renderTabs = () => (
    <x-nav-tab role="tablist" class="tabs-boxed tabs w-1/3 bg-slate-200 mt-10">
      <x-tab class={mc('tab', currentTab.value === 'comment' ? 'tab-active' : '')} onClick={() => onListTypeChange('comment')}>评论管理</x-tab>
      <x-tab class={mc('tab', currentTab.value === 'report' ? 'tab-active' : '')} onClick={() => onListTypeChange('report')}>违规评论</x-tab>
    </x-nav-tab>
  )

  const renderCommentContentPanel = () => (
    <NavFormTablePager>{{
      form: () => (
        <QueryForm class="w-full flex flex-row"
          onSubmit={onQueryChange}
          onReset={onReset}
          data={queryForm.value}
          onChange={(path, value) => {
            set(queryForm.value, path, value)
          }}
          items={[

            {
              label: requiredLabel('时间范围'),
              path: 'created_start',
              input: { type: 'datetime', rawFormat: 'YYYY-MM-DD HH:mm:ss', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
              transform: transformDatetime,
            },
            {
            // label: '时间范围-结束',
              label: () => (
                <div class="flex justify-between py-1">
                  <span />
                  <div class="flex gap-1 text-[var(--brand-5)]">
                    <span class="text-xs cursor-pointer hover:text-[var(--brand-7)]" onClick={() => { setDayRange('last7d') }}>过去7天</span>
                    <span class="text-xs cursor-pointer hover:text-[var(--brand-7)]" onClick={() => { setDayRange('yesterday') }}>昨天</span>
                    <span class="text-xs cursor-pointer hover:text-[var(--brand-7)]" onClick={() => { setDayRange('last24h') }}>过去24h</span>
                    <span class="text-xs cursor-pointer hover:text-[var(--brand-7)]" onClick={() => { setDayRange('today') }}>今天</span>
                  </div>
                </div>
              ),
              path: 'created_end',
              input: { type: 'datetime', rawFormat: 'YYYY-MM-DD HH:mm:ss', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
              transform: transformDatetime,
            },
            {
              label: requiredLabel('语言'),
              path: 'language_codes',
              input: {
                type: 'multi-select',
                options: languageOptions.map((lang, index) => ({
                  label: langValueOptions[index],
                  value: lang,
                })),
                disabledItemClass: 'opacity-50 cursor-not-allowed',
                onClear: () => {
                  queryForm.value.language_codes = []
                },
                popoverWrapperClass: 'z-popover-in-dialog',
              },
            },
            { label: '资源ID/资源名', path: 'resource_title', input: { type: 'text' } },
            { label: '剧ID/剧名', path: 'series_title', input: { type: 'text' } },
            { label: '集ID', path: 'episode_key', input: { type: 'text' } },
            { label: '用户ID', path: 'user_id', input: { type: 'text' } },

            {
              label: '是否真实用户',
              path: 'user_type',
              input: {
                type: 'select',
                options: [{
                  label: '真实用户',
                  value: 1,
                }, {
                  label: '非真实用户',
                  value: 2,
                }],
              },
              transform: transformNumber,
            },

          ]}
        />
      ),
      tableActions: () => (
        <x-table-actions class="flex justify-between items-center">
          <span>数据看板</span>
        </x-table-actions>
      ),
      table: () => (
        <>
          <x-summary class="flex gap-8 px-8 overflow-x-auto">
            <x-table>
              <Table1
                list={dataPanelValue.value || []}
                class="tm-table-fix-last-column"
                columns={[
                  ['新增评论数', 'new_comment_num'],
                  ['评论用户数', 'new_comment_user_num'],
                  ['新增评论剧数', 'new_comment_series_num'],
                  ['总剧数', 'series_num'],
                ]}
              />
            </x-table>
            <x-tables>
              <x-table class="flex items-center gap-x-4">
                {dataPanelValue.value.length > 0 && <div class="whitespace-nowrap">新增评论数</div>}
                <Table2
                  list={dataPanelValue.value || []}
                  class="tm-table-fix-last-column"
                  columns={[
                    ['每个评论用户', row => ((row.new_comment_num / row.new_comment_user_num) || 0).toFixed(2)],
                    ['每部新增评论剧', row => ((row.new_comment_num / row.new_comment_series_num) || 0).toFixed(2)],
                    ['每部剧', row => ((row.new_comment_num / row.series_num) || 0).toFixed(2)],
                  ]}
                />
              </x-table>
              <x-table class="flex items-center gap-x-4">
                {dataPanelValue.value.length > 0 && <div class="whitespace-nowrap">评论用户数</div>}
                <Table3
                  list={dataPanelValue.value || []}
                  class="tm-table-fix-last-column"
                  columns={[
                    ['每个活跃用户', row => row.new_comment_num],
                    ['每部新增评论剧', row => ((row.new_comment_user_num / row.new_comment_series_num) || 0).toFixed(2)],
                    ['每部剧', row => ((row.new_comment_user_num / row.series_num) || 0).toFixed(2)],
                  ]}
                />
              </x-table>

            </x-tables>
          </x-summary>

          <hr />

          <h2 class="m-4">评论详情</h2>
          <hr />

          <Table
            store={tableStore}
            multiSelect
            actions={() => (
              <>
                <Button disabled={isEmpty(selectedRowIds.value)} class="btn btn-sm" onClick={() => onClickBatchDelete()}>批量删除</Button>
                <Button disabled={isEmpty(selectedRowIds.value)} class="btn btn-sm" onClick={() => onClickAudit(-1)}>批量审核</Button>
                <Button class="ml-auto btn btn-sm" onClick={() => exportExcel()}>导出excel</Button>
              </>
            )}
            list={list.value || []}
            columns={columns}
            loading={tableLoading.value}
          />
        </>
      ),
      pager: () => (
        <Pager class="justify-end" v-model:page={queryForm.value.page} v-model:size={queryForm.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
      ),
    }}
    </NavFormTablePager>
  )

  const commentReportPage = ref<number>(1)
  const commentReportPageSize = ref<number>(20)
  const commentReportTotal = ref<number>(1)
  const commentReportList = ref<any[]>([])
  const commentReportLoading = ref<boolean>(false)
  const listParams = ref<M.CommentReportQuery>({
    languages: [],
  })

  const searchReport = async (_page?: number) => {
    _page = _page || commentReportPage.value + 1
    commentReportLoading.value = true
    const res = await apiGetReportList({ ...listParams.value, page_info: { page_index: commentReportPage.value, page_size: commentReportPageSize.value } })
      .finally(() => {
        commentReportLoading.value = false
      })
    commentReportList.value = res.data?.items || []
    commentReportTotal.value = res.data?.total || 0
    commentReportPage.value = _page
  }

  const ReportColumns: TableColumn<M.CommentReportItem>[] = [
    ['剧ID', 'series_key'],
    ['集ID', 'episode_key'],
    ['语言', 'language'],
    ['举报用户ID', 'user_id'],
    ['举报类型', row => [{
      label: '违法有害',
      value: 1,
    }, {
      label: '低俗色情',
      value: 2,
    }, {
      label: '攻击谩骂',
      value: 3,
    }, {
      label: '垃圾广告',
      value: 4,
    }, {
      label: '不实信息',
      value: 5,
    }, {
      label: '侵害未成年',
      value: 6,
    }, {
      label: '侵权/抄袭',
      value: 7,
    }, {
      label: '其他问题',
      value: 8,
    }].find(item => item.value == row.complain_type)?.label || '未知'],
    ['举报内容', 'description', { width: 'minmax(auto,30em)' }],
    [
      '举报日期',
      row => !!row.created ? dayjs(row.created * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
    ],
    ['评论用户ID', 'comment_user_id'],
    ['评论内容', 'comment_content', { width: 'minmax(auto,30em)' }],
    [
      '评论日期',
      row => !!row.comment_created ? dayjs(row.comment_created * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
    ],
    ['操作时间', row => (<DateTime value={(row?.op_ts || 0) * 1000} />), { class: 'w-[150px]' }],
    ['后台操作人', 'op_user_name', { class: 'w-[200px]' }],
    ['操作结果', row => [{
      label: '通过举报',
      value: 1,
    }, {
      label: '未处理',
      value: 2,
    }].find(item => item.value == row.audit_status)?.label || '未操作'],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            {row.op_list.includes('approve') && <Button class="btn btn-sm btn-primary" onClick={() => onClickReport([row.id])}>通过举报</Button>}
          </div>
        )
      },
      { class: 'sticky right-0 bg-white shadow-lg' },
    ],
  ]

  const tableReportStore = useTableStore('reportList')
  const { selectedRowIds: selectedReportRowIds } = tableReportStore

  const onClickReport = (id_list: number[]) => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否通过用户举报？</x-status-body>
          <x-status-footer class="flex justify-end gap-x-[10px] w-full">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                await apiReport({
                  id_list, // 记录id列表
                  audit_result_type: 'approve', /// /操作结果，approve：举报属实（通过举报），reject：不属实（用户屏蔽）
                })
                void searchReport(commentReportPage.value)
                showSuccessToast('操作成功')
              } catch (error: unknown) {
                if (isAxiosError(error)) {
                  showFailToast(error.response?.data.message ?? '操作失败')
                }
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const renderReportContentPanel = () => (
    <NavFormTablePager>{{
      form: () => (
        <ReportQueryForm class="w-full flex flex-row"
          onSubmit={() => searchReport(1)}
          onReset={() => {
            listParams.value = {
              languages: [],
            }
            commentReportPage.value = 1
            void searchReport(1)
          }}
          data={listParams.value}
          onChange={(path, value) => {
            set(listParams.value, path, value)
          }}
          items={[

            {
              label: '时间范围-开始',
              path: 'start_date',
              input: { type: 'datetime', rawFormat: 'YYYY-MM-DD HH:mm:ss', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
              transform: transformDatetime,
            },
            {
              label: '时间范围-结束',
              path: 'end_date',
              input: { type: 'datetime', rawFormat: 'YYYY-MM-DD HH:mm:ss', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
              transform: transformDatetime,
            },
            {
              label: '语言',
              path: 'languages',
              input: {
                type: 'multi-select',
                options: languageOptions.slice(1).map((lang, index) => ({
                  label: langValueOptions.slice(1)[index],
                  value: lang,
                })),
                disabledItemClass: 'opacity-50 cursor-not-allowed',
                onClear: () => {
                  listParams.value.languages = []
                },
                popoverWrapperClass: 'z-popover-in-dialog',
              },
            },
            { label: '剧ID', path: 'series_key', input: { type: 'text' } },
            { label: '集ID', path: 'episode_key', input: { type: 'text' } },
            { label: '用户ID', path: 'user_id', input: { type: 'number' }, transform: transformNumber },

            {
              label: '举报类型',
              path: 'complain_type',
              input: {
                type: 'select',
                options: [{
                  label: '违法有害',
                  value: 1,
                }, {
                  label: '低俗色情',
                  value: 2,
                }, {
                  label: '攻击谩骂',
                  value: 3,
                }, {
                  label: '垃圾广告',
                  value: 4,
                }, {
                  label: '不实信息',
                  value: 5,
                }, {
                  label: '侵害未成年',
                  value: 6,
                }, {
                  label: '侵权/抄袭',
                  value: 7,
                }, {
                  label: '其他问题',
                  value: 8,
                }],
              },
              transform: transformNumber,
            },

          ]}
        />
      ),
      table: () => (
        <>
          <ReportTable
            list={commentReportList.value || []}
            columns={ReportColumns}
            loading={commentReportLoading.value}
            store={tableReportStore}
            multiSelect
            actions={() => (
              <>
                <Button disabled={isEmpty(selectedReportRowIds.value)} class="ml-auto btn btn-sm" onClick={() => onClickReport(selectedReportRowIds.value)}>批量通过举报</Button>
              </>
            )}
          />
        </>
      ),
      pager: () => (
        <Pager
          class="justify-end"
          v-model:page={commentReportPage.value}
          v-model:size={commentReportPageSize.value}
          total={commentReportTotal.value}
          onUpdate:page={() => {
            void searchReport(commentReportPage.value)
          }}
          onUpdate:size={() => {
            void searchReport(commentReportPage.value)
          }}
        />
      ),
    }}
    </NavFormTablePager>
  )

  return () => (
    <div class="space-y-4">
      {renderTabs()}
      {currentTab.value === 'comment' && renderCommentContentPanel()}
      {currentTab.value === 'report' && renderReportContentPanel()}
    </div>
  )
})

export default CommentPage
