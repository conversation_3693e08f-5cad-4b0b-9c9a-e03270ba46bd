import { createComponent, mc, useValidator } from '@skynet/shared'
import { CreateTableOld, CreateForm, transformNumber, TableColumnOld, Pager, showFailToast, Button, openDialog, showSuccessToast, CheckboxGroup, Checkbox, transformInteger, Radio } from '@skynet/ui'
import { nextTick, onMounted, ref, watch } from 'vue'
import { set, get, cloneDeep } from 'lodash-es'
import { periodList, platformList, currencyList, memberAttr } from 'src/lib/constant.ts'
import dayjs from 'dayjs'
import { apiGetProductList, apiUpdateProduct, apiUpdateStatusProduct, apiUpdateMemberPriority } from './comment-api'
import { AxiosError } from 'axios'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetAppOptions } from 'src/modules/application/application.api'
import { z } from 'zod'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Icon } from '@skynet/ui/icon/icon'

type MemberPageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.ProductItem[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean,

    store?: string,
    productType?: string
  }
  emits: {
    // hide: () => void
    add: (id: M.ProductItem) => void
    remove: (id: M.ProductItem) => void
    change: (data: any) => void
  }
}

export const MemberPage = createComponent<MemberPageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
    store: '',
    productType: ''
  },
  emits: {
    add: (item: M.ProductItem) => {},
    remove: (item: M.ProductItem) => {},
    change: (item: M.ProductItem) => {}
  },
}, (props, { emit }) => {
  const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'
  const QueryForm = CreateForm<M.QueryProductParams>()
  const Form = CreateForm<M.ProductItem>()
  const Table = CreateTableOld<M.ProductItem>()
  const btnLoading = ref(false)
  const list = ref<M.ProductItem[]>([])
  const tableLoading = ref(false)
  const productTypeList = ref([{value: "recharge", label: '充值'}, {value: "membership", label:"订阅"}])
  const statusOptionsList = ref([{value: 1, label: '可用'}, {value: 2, label: '不可用'}])
  const total = ref(0)
  const defaultData = {
    id: undefined,
    product_type: 'recharge',
    name: '',
    currency: '',
    price: 0,
    discount_price: 0,
    store: 'Apple Store',
    sku_id: '',
    period: 'weekly',
    group_identifier: '',
    status: 1,
    update_user: '',
    created: 0,
    updated: 0,
    deadline: 0,
    apple_id: '',
    operation: 1,
  }
  const queryForm = ref<M.QueryProductParams>({
    page_size: 10,
    page: 0,
    next: '',
    has_more: true,
    sku_id: '',
    name: '',
    store: props.store,
    status: 0,
    product_type: ''
  })
  const form = ref<M.ProductItem>(cloneDeep(defaultData))
  const dialogRef = ref(() => {})

  const onPageSizeChange = (page_size: number) => {
    queryForm.value.next = ''
    queryForm.value.page_size = page_size
    void onQuery()
  }
  const onPageChange = (page: number) => {
    queryForm.value.page = page
    queryForm.value.next = page - 1 > 0 ? `${(page - 1) * queryForm.value.page_size}` : ''

    void getList()
  }


  const onReset = () => {
    queryForm.value.store = ''
    queryForm.value.name = ''
    queryForm.value.product_type = ''
    queryForm.value.status = 0
  }

  const onClose = () => {
    form.value = cloneDeep(defaultData)
    dialogRef.value()
  }

  const onQuery = async () => {
    queryForm.value.page = 1
    queryForm.value.next = ''
    queryForm.value.has_more = true
    await getList()
  }



  const columns: TableColumnOld<M.ProductItem>[] = [
    [
      '选择',
      row => {
        const data = row as object
        const id = row.id as number
        return (
          <Radio
            label=""
            onUpdate:checked={(val: any) => emit('change', data)}
            name="selectProduct"
            value={id}
          />
        )
      },
      { class: mc('w-[60px]') },
    ],
    // ['商品ID', 'id', { class: 'w-[70px]' }],
    ['参考名称', 'name', { class: 'w-[160px]' }],
    ['类型', row => 
      <span>{productTypeList.value.find((item: any) => item.value === row.product_type)?.label}</span>
    , { class: 'w-[160px]' }],

    ['价格($)', 'price', { class: 'w-[160px]' }],
    ['订阅优惠($)', 'discount_price', { class: 'w-[160px]' }],
    ['商品ID', 'sku_id', { class: 'w-[160px]' }],
    // ['状态', row => 
    //   <span>{statusOptionsList.value.find((item: any) => item.value === row.status)?.label}</span>
    // , { class: 'w-[160px]' }],
    // [
    //   '更新时间',
    //   row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
    //   { class: 'w-[180px]' },
    // ],
    // ['更新人', 'update_user', { class: 'w-[160px]' }],

  ]

  const getList = async () => {
    try {
      tableLoading.value = true
      const res = await apiGetProductList({
        ...queryForm.value,
        product_type: props.productType
      })
      list.value = res.data?.items as unknown as M.ProductItem[] || []
      queryForm.value.has_more = res.data?.page_info.has_more || false
      queryForm.value.next = res.data?.page_info.next || ''
      total.value = res.data?.total || 0
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    } finally {
      tableLoading.value = false
    }
  }

  onMounted(async () => {
    await getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: props.hasNav
          ? () => (
              <ul>
                <li>支付商品库</li>
              </ul>
            )
          : null,
        form: () => (
          <QueryForm class="w-full flex flex-row"
            onSubmit={onQuery}
            onReset={onReset}
            data={queryForm.value}
            onChange={(path, value) => {
              set(queryForm.value, path, value)
            }}
            items={[
              // { label: () => (
              //   <div>应用： <span class="text-sm">上架商店：{ queryForm.value.store }</span></div>
              // ), 
              // { label: '商品平台', path: 'status', input: { type: 'select', options: [{
              //   value: 1,
              //   label: '展示',
              // },
              { label: '商品平台', path: 'store', input: { type: 'select', disabled: true, options: [
                {value: 'Apple Store', label: 'Apple Store'},
                {value: 'Google Play', label: 'Google Play'},
              ] } },
              { label: '参考名称：', path: 'name', input: { type: 'text' } },

            ]}
          />
        ),
        table: () => (
          <Table
            list={list.value || []}
            class="tm-table-fix-last-column max-h-[350px]"
            columns={columns}
            loading={tableLoading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={queryForm.value.page} v-model:size={queryForm.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default MemberPage
