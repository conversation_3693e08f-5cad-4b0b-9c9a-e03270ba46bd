declare namespace M {

  interface CommentItem {
    id: number
    resource_title: string
    resource_id: number
    series_key: string
    series_title: string
    serial_number: number
    episode_key: string
    default_language_code: string
    user_id: number
    content: string
    like_num: number
    created: number
    updated: number
    /**
     * 状态
     * 0: 已删除
     * 1: 未删除
     */
    status: number
    audit_status: number
  }
  interface UpdateStatusItem {
    id: number | undefined
    operation: number
  }

  interface QueryCommentParams {
    page_size: number // 每页数据
    page: number

    created_start: string | number
    created_end: string | number
    language_codes: any
    resource_title: string
    resource_id?: number
    series_key: string
    series_title: string
    episode_key: string
    user_id?: number
    sort_type?: 'desc' | 'asc' // 默认降序，从大到小
    sort_column?: 'like_num' | 'created' | 'id' // 默认 created
    user_type?: number
  }

  interface DataPanel {
    new_comment_num: number
    new_comment_user_num: number
    new_comment_series_num: number
    series_num: number
  }

  interface CommentReportQuery {
    start_date?: string // 开始日期
    end_date?: string // 结束日期
    languages?: string[] // 语言类型,全部传空
    series_key?: string // 剧集 ID
    episode_key?: string // 集 ID
    user_id?: number // 用户 ID
    complain_type?: number // 举报类型，1:违法有害、2:低俗色情、3:攻击谩骂、4:垃圾广告、5:不实信息、6:侵害未成年、7:侵权/抄袭、8:其他问题
  }

  interface CommentReportItem {
    id: number // 投诉记录唯一 ID
    series_key: string // 剧集 ID
    episode_key: string // 集 ID
    language: string // 语言类型
    user_id: number // 举报用户 ID
    complain_type: number // 举报类型
    description: string // 举报内容
    created: number // 举报时间（时间戳）
    comment_user_id: number // 评论用户 ID
    comment_content: string // 评论内容
    comment_created: number // 评论创建时间（时间戳）
    audit_status: number // 审核状态，1：属实，2：不属实
    op_user_name: string // 后台操作人
    op_ts: number // 操作时间（时间戳）
    op_list: string[] // 可操作列表 approve:通过举报，reject：用户屏蔽
  }
}

// {
//   "start_date": "2023-01-01 00:00:00",  // 开始日期
//   "end_date": "2023-12-31 00:00:00",    // 结束日期
//   "languages": ["en"],           // 语言类型,全部传空
//   "series_key": "series123",    // 剧集 ID
//   "episode_key": "episode456",  // 集 ID
//   "user_id": 12345,             // 用户 ID
//   "complain_type": 1,           // 举报类型，1:违法有害、2:低俗色情、3:攻击谩骂、4:垃圾广告、5:不实信息、6:侵害未成年、7:侵权/抄袭、8:其他问题
//   "page_info": {                // 分页信息
//       "page_index": 1,          // 当前页索引
//       "page_size": 20          // 每页大小
//   }
// }
