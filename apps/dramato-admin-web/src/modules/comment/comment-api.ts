import { openedInDev } from '@skynet/shared'
import { get_k_sso_token } from 'src/lib/device-id'
import { httpClient } from 'src/lib/http-client'

export const apiGetCommentStat = (data: any) =>
  httpClient.post<ApiResponse<M.DataPanel>>('/comment/stat', data)

export const apiGetCommentList = (data: any) =>
  httpClient.post<ApiResponse<{ list: M.CommentItem[], total: number }>>('/comment/list', data)

export const apiDeleteComment = (data: any) =>
  httpClient.post<ApiResponse>('/comment/delete', data)

export const apiBatchDeleteComment = (ids: number[]) =>
  httpClient.post<ApiResponse>('/comment/batch-delete', { ids })
export const apiAuditComment = (ids: number[]) =>
  httpClient.post<ApiResponse>('comment/audit', { ids })

export const apiExportExcel = (data: any) =>
  fetch(`${import.meta.env.VITE_DRAMA_API_URL}/comment/export`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(data),
  })

export const apiGetReportList = (data: any) =>
  httpClient.post<ApiResponse<{ items: M.CommentItem[], total: number }>>('/content/complain/list', data)

export const apiReport = (data: any) =>
  httpClient.post<ApiResponse>('/content/complain/audit', data)
