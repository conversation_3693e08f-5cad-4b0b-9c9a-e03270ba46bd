/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted, watch } from 'vue'
import { Button, Checkbox, DateTime, openDialog, showAlert, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { cloneDeep, set } from 'lodash-es'
import { useEpisodeTags } from './use-episode-tags'
import { apiDeleteEpisodeTags, apiUpdateEpisodeTagsStatus } from './episode-tags-api'
import { useAppAndLangOptions } from '../options/use-app-options'
import { requiredLabel } from 'src/lib/required-label'

type EpisodeTagsOptions = {
  props: {}
}
export const EpisodeTags = createComponent<EpisodeTagsOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    checkedItems,
    batchDelete,
    batchUp,
    batchDown,
    showTagsFormDialog,
  } = useEpisodeTags()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => params.value.app_id, {
    onSuccess: search,
  })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !params.value.app_id) {
      params.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => params.value.app_id, id => {
    params.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  onMounted(() => {

  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>商业分类Tab</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              void search()
            }}
            onSubmit={() => search()}
            data={params.value}
            items={[
              {
                label: requiredLabel('应用'),
                path: 'app_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  class: 'w-[240px]',
                  autoInsertEmptyOption: false,
                  options: appOptions.value,
                },
              },
              {
                label: requiredLabel('语言'),
                path: 'language',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: languageOptions.value,
                },
              },
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            app按顺序从左到右显示；当只有一个Tab时，不显示顶部分类栏
            <x-actions class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchUp}>批量上架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDown}>批量下架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '新建Tab')}>新建Tab</Button>
            </x-actions>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['', row => (
              <Checkbox
                label=""
                modelValue={!!checkedItems.value.find(i => i.id === row.id)}
                onUpdate:modelValue={(v: boolean) => {
                  const idx = checkedItems.value.findIndex(i => i.id === row.id)
                  if (idx > -1) {
                    checkedItems.value.splice(idx, 1)
                  } else {
                    checkedItems.value.push(row)
                  }
                }}
              />
            ), { class: 'w-[40px]' }],
            ['顺序', 'tab_index', { class: 'w-[100px]' }],
            ['Tab名称', 'tab_name', { class: 'w-[100px]' }],
            ['Tab类型', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { value: 1, label: 'Popular' },
                { value: 2, label: 'New' },
                { value: 3, label: '自定义' },
              ].find(item => item.value === row.tab_type)?.label ?? '自定义'}
              </span>
            ), { class: 'w-[150px]' }],
            ['下属剧集标签', 'label_names', { class: 'w-[200px]' }],
            ['覆盖剧数', 'series_count', { class: 'w-[100px]' }],
            ['剧集排序规则', row => '上架时间排序', { class: 'w-[100px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.creator_name, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.operator_name, { class: 'w-[100px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 0 },
              ].find(item => item.value === row.listing_status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.id) return
                      void apiUpdateEpisodeTagsStatus({
                        ids: [row.id],
                        is_up: row.listing_status !== 1 ? 1 : 2,
                      })
                        .then(() => {
                          void search()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    {row.listing_status !== 1 ? '上架' : '下架'}
                  </Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '修改Tab')}>修改</Button>
                  <Button
                    class={mc('btn btn-outline btn-xs', row.listing_status !== 1 ? '' : 'hidden')}
                    onClick={() => {
                      const showTipsDialog = () => {
                        const hideDialog = openDialog({
                          title: '',
                          mainClass: 'pb-0 px-5',
                          body: (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-status-body>是否确认删除tab【{row.tab_name}】?</x-status-body>
                              <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" onClick={() => {
                                  void apiDeleteEpisodeTags({
                                    ids: `${row.id}`,
                                  })
                                    .then(() => {
                                      void search()
                                    })
                                    .catch((error: any) => {
                                      showAlert(error.response.data.message, 'error')
                                    })
                                  hideDialog()
                                }}
                                >确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }

                      showTipsDialog()
                    }}
                  >
                    删除
                  </Button>
                </div>
              ), {
                class: 'w-[100px]',
              },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeTags
