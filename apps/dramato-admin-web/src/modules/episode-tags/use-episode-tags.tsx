/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiDeleteEpisodeTags, apiGetEpisodeTagsList, apiGetLabelList, apiSaveEpisodeTags, apiUpdateEpisodeTagsStatus } from './episode-tags-api'
import { EpisodeTagsForm } from './episode-tags-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeTags = () => {
  return {
    tags,
    Form,
    params,
    Table,
    list,
    loading,
    search,
    changeTab,
    tab,
    checkedItems,
    batchDelete,
    batchUp,
    batchDown,
    showTagsFormDialog,
    hideTagsFormDialog,
    onSave,
    getTags
  }
}

const Form = CreateForm<M.EpisodeTags.ListRequest.Params>()
const params = ref<M.EpisodeTags.ListRequest.Params>({})

const Table = CreateTableOld<M.EpisodeTags.ListRequest.Item>()
const list = ref<M.EpisodeTags.ListRequest.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([
//   {
//     language_code: 'zh-CN',
//     label_id: 29,
//     content: '婚前恋爱',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 30,
//     content: '爱情',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 31,
//     content: '命运',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 32,
//     content: '甜蜜恋爱',
//   },
])
const tab = ref<M.EpisodeTags.ListRequest.Item>({})

const checkedItems = ref<M.EpisodeTags.ListRequest.Item[]>([])

const getTags = async (language: string) => {
  const rs = await apiGetLabelList({
    language,
  })
  tags.value = rs.data?.list || []
}

const search = async () => {
  loading.value = true
  void getTags(params.value.language || '')
  const res = await apiGetEpisodeTagsList({
    ...params.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
}

const changeTab = (item: M.EpisodeTags.ListRequest.Item) => {
  tab.value = Object.assign({}, item)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: M.EpisodeTags.ListRequest.Item, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeTagsForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const onSave = (d: M.EpisodeTags.ListRequest.Item) => {
  void apiSaveEpisodeTags({
    app_id: params.value.app_id,
    language: params.value.language,
    items: { ...d, listing_status: 0 },
  }).then(d => {
    console.log('>>> d')

    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.message || '保存失败', 'error')
  })
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeTagsStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认下架tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeTagsStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 2,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDelete = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认删除tab【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiDeleteEpisodeTags({
                ids: checkedItems.value.map(item => item.id).join(','),
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}
