declare namespace M {
  namespace EpisodeTags {
    namespace ListRequest {
      interface Params {
        app_id?: number
        language?: string
      }

      interface Item {
        id?: number
        tab_name?: string // 名称
        tab_index?: number // 排序
        tab_key?: string // 唯一key
        tab_type?: number // Tab类型 1 Popular(不可修改) 2 New(只能修改名称或者删除) 3 自定义
        label_ids?: string // 标签ID
        label_names?: string // 标签名称
        series_count?: number // 覆盖剧数
        rank_type?: number // 排序规则
        listing_status?: number // 状态 1 上架 0 下架
        created?: number // 创建时间
        creator_name?: string // 创建人
        updated?: number // 更新时间
        operator_name?: string // 修改人
        home_display?: number // 是否展示在首页 0 是 1 否
        languages?: string[] // 语言类型
      }

      interface Response {
        list: Item[]
      }
    }

    namespace SaveRequest {
      interface Params extends ListRequest.Params {
        items: ListRequest.Item
      }
    }

    namespace UpdateStatusRequest {
      interface Params extends ListRequest.Params {
        ids: number[]
        is_up: number // 是否上架  1 上架 2 下架
      }
    }

    namespace GetSeriesCountRequest {
      interface Params extends ListRequest.Params {
        label_ids: string
      }

      interface Response {
        count: number
      }
    }

    namespace DeleteRequest {
      interface Params extends ListRequest.Params {
        ids: string
      }
    }
  }
}
