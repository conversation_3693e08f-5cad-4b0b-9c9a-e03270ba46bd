import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
// import { useBanner } from './use-banner.tsx'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useEpisodeTags } from './use-episode-tags.tsx'
import { onMounted, watch } from 'vue'
import { apiGetEpisodeSeriesCount } from './episode-tags-api.tsx'

export const EpisodeTagsForm = createComponent(null, () => {
  const {
    tags,
    tab,
    hideTagsFormDialog,
    onSave,
    params,
  } = useEpisodeTags()
  const Form = CreateForm<M.Banner>()

  const formRules = z.object({
    tab_name: z.string().min(1, '请输入标题').max(50, '最多50个字符'),
    tab_index: z.number().min(1, '请选择标签顺序'),
    // label_ids: z.string().min(1, '请选择下属剧集标签'),
    rank_type: z.number().min(1, '请选择排序方式'),
    tab_type: z.number().min(1, '请选择标签类型'),
    home_display: z.number().min(0, '请选择首页展示'),
    // languages: z.array(z.string()).min(1, '请选择语言'),
  })

  const { error, validateAll } = useValidator(tab, formRules)

  // const { languageOptions } = useAppAndLangOptions(() => params.value.app_id, {
  // onSuccess: search,
  // })

  watch(
    () => tab.value.label_ids,
    () => {
      if (!tab.value.label_ids) {
        tab.value.series_count = 0
        return
      }

      void apiGetEpisodeSeriesCount({
        label_ids: tab.value.label_ids,
        app_id: params.value.app_id,
        language: params.value.language,
      }).then(res => {
        tab.value.series_count = res.data?.count || 0
      })
    },
    {
      deep: true,
      immediate: true,
    },
  )

  onMounted(() => {
    if (!tab.value.label_ids) {
      tab.value.series_count = 0
      return
    }

    void apiGetEpisodeSeriesCount({
      label_ids: tab.value.label_ids,
      app_id: params.value.app_id,
      language: params.value.language,
    }).then(res => {
      tab.value.series_count = res.data?.count || 0
    })
  })

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            console.log(value)

            if (path === 'label_ids') {
              if (!!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0)) {
                return
              }
              const ids = (value as string || '').split(',').map(Number)
              const labels = tags.value.filter(item => ids.includes(item.label_id))
              set(tab.value || {}, 'label_names', labels.map(item => item.content).join(','))
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('顺序'),
              path: 'tab_index',
              transform: transformInteger,
              input: {
                type: 'select',
                options: Array.from({ length: 100 }, (_, index) => ({
                  value: index + 1,
                  label: String(index + 1),
                })),
                disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0),
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            // {
            //   label: requiredLabel('资源语言类型'),
            //   path: 'languages',
            //   input: {
            //     type: 'multi-select',
            //     options: languageOptions.value,
            //     popoverWrapperClass: 'z-popover-in-dialog',
            //   },
            //   class: 'col-span-3',
            // },
            {
              label: requiredLabel('标签是否展示在首页上'),
              path: 'home_display',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 0, label: '是' },
                  { value: 1, label: '否' },
                ],
              },
            },
            {
              label: requiredLabel('Tab栏名称'),
              path: 'tab_name',
              input: {
                type: 'textarea',
                maxlength: 50,
                placeholder: '请输入标题，1-50个字符',
                // disabled: !!tab.value?.id && [1].includes(tab.value?.tab_type || 0),
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('Tab类型'),
              path: 'tab_type',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: 'Popular(只能修改名称或者删除)', disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0) },
                  { value: 2, label: 'New(只能修改名称或者删除)', disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0) },
                  { value: 4, label: 'Free', disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0) },
                  { value: 3, label: '自定义', disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0) },
                ],

              },
            },
            {
              label: requiredLabel('下属剧集标签'),
              path: 'label_ids',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: tags.value.map(tag => ({
                  value: tag.label_id,
                  label: tag.content,
                })),
              },
              transform: [
                (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
              ] as const,
              //   transform: transformStringArray,
              class: 'col-span-3',
            },
            {
              label: requiredLabel('覆盖剧数'),
              path: 'series_count',
              input: {
                type: 'text',
                disabled: true,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('排序方式'),
              path: 'rank_type',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  {
                    label: '上架时间（从新到旧）',
                    value: 1,
                    disabled: !!tab.value?.id && [1, 2].includes(tab.value?.tab_type || 0),
                  },
                ],
              },
            },
          ]}
          data={tab.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(tab.value)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
