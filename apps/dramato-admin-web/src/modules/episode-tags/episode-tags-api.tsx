import { httpClient } from 'src/lib/http-client'

export const apiGetEpisodeTagsList = (data: M.EpisodeTags.ListRequest.Params) =>
  httpClient.post<ApiResponse< M.EpisodeTags.ListRequest.Response>>('/homepage/module_config/tab_content/list', data)

export const apiUpdateEpisodeTagsStatus = (data: M.EpisodeTags.UpdateStatusRequest.Params) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/module_config/tab_content/listing_status', data)

export const apiSaveEpisodeTags = (data: M.EpisodeTags.SaveRequest.Params) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/homepage/module_config/tab_content/save', data)

export const apiGetEpisodeSeriesCount = (d: M.EpisodeTags.GetSeriesCountRequest.Params) =>
  httpClient.post<ApiResponse<M.EpisodeTags.GetSeriesCountRequest.Response>>('/homepage/module_config/tab_content/series_count', d)

export const apiDeleteEpisodeTags = (d: M.EpisodeTags.DeleteRequest.Params) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/module_config/tab_content/delete', d)

export const apiGetLabelList = (data: {
  language: string
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITag[]
  }>>('/series_resource/label_lang_list', data)
