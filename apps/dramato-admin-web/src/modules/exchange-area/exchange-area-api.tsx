import { httpClient } from 'src/lib/http-client'

export const apiGetExchangeAreaList = (data: M.IExchangeAreaSearchProps) => httpClient.post<ApiResponse<{
  items: M.IExchangeArea[]
  total: number
  page_info: {
    next: number
    has_more: boolean
  }
}>>('/exchange/gift/list', data)

export const apiGetGiftList = () => httpClient.get<ApiResponse<{
  items: M.IGift[]
}>>('/exchange/gift/types')

export const apiUpdateListStatus = (data: {
  id: number[]
  is_up: number
}) => httpClient.post<ApiResponse<null>>('/exchange/gift/amazon_card/listing_status', data)

export const apiUpdateTransferStatus = (data: {
  id: number
  type: string
}) => httpClient.post<ApiResponse<null>>('/exchange/gift/transfer/do_transfer', data)

export const apiUploadAmazonCardExcel = (data: {
  file: File
}) => httpClient.post<ApiResponse<null>>('/exchange/gift/amazon_card/upload', data, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
})

export const apiUpdateDailyLimit = (data: {
  type: string
  count: number
}) => httpClient.post<ApiResponse<null>>('/exchange/gift/set_daily_count', data)

export const apiGetDailyLimit = (data: {
  type: string
}) => httpClient.get<ApiResponse<{
  count: number
}>>('/exchange/gift/daily_count', data)
