import { ref } from 'vue'
import { apiGetExchangeAreaList, apiGetGiftList } from './exchange-area-api'
import { bindLoading } from '@skynet/shared'
import dayjs from 'dayjs'

const searchForm = ref<M.IExchangeAreaSearchProps>({
  value: undefined,
  receive_status: undefined,
  transfer_status: undefined,
  country: '',
  type: '',
  next: '',
  page_size: 10,
})
const list = ref<M.IExchangeArea[]>([])
const total = ref<number>(0)
const loading = ref(false)
const gifts = ref<M.IGift[]>([])
const page = ref<number>(1)
const size = ref<number>(10)

const getList = async () => {
  loading.value = true
  const rs = await bindLoading(apiGetExchangeAreaList(searchForm.value), loading)
  list.value = rs.data?.items || []
  total.value = rs.data?.total || 0
  searchForm.value.next = rs.data?.page_info.next || ''
}

const getLocalTime = (time: number, timezone: number) => {
  if (!time) return '--'
  return dayjs.unix(time).add(timezone, 'hour').format('YYYY-MM-DD HH:mm:ss')
}

const getGifts = async () => {
  const rs = await apiGetGiftList()
  gifts.value = rs.data?.items || []
}

const onSearch = (isFirst?: boolean) => {
  if (isFirst) {
    page.value = 1
    size.value = 10
    searchForm.value.next = ''
    searchForm.value.page_size = 10
  }
  void getList()
}

const onPageChange = (n: number) => {
  page.value = n
  searchForm.value.next = n - 1 > 0 ? `${(n - 1) * searchForm.value.page_size}` : ''
  onSearch()
}

const onSizeChange = (n: number) => {
  page.value = 1
  searchForm.value.page_size = n
  searchForm.value.next = ''
  onSearch()
}

const onReset = () => {
  page.value = 1
  size.value = 10
  searchForm.value.value = undefined
  searchForm.value.receive_status = undefined
  searchForm.value.transfer_status = undefined
  searchForm.value.country = ''
  searchForm.value.type = gifts.value[0].type ?? ''
  searchForm.value.next = ''
  searchForm.value.page_size = 10
  onSearch(true)
}

export const useExchangeAreaStore = () => {
  return {
    searchForm,
    list,
    total,
    loading,
    gifts,
    onSearch,
    onPageChange,
    onSizeChange,
    onReset,
    getGifts,
    getLocalTime,
    page,
    size,
  }
}
