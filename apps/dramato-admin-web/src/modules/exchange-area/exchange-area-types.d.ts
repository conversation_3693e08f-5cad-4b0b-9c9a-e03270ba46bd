declare namespace M {

  interface IExchangeAreaSearchProps {
    value?: number
    receive_status?: number
    transfer_status?: number
    country: string
    type: string
    next: string | number
    page_size: number
  }

  interface IExchangeArea {
    id: number
    show_card_value: string
    receive_user_email: string
    card_secret: string
    receive_status: number
    transfer_status: number // 转账状态 1-审核中 2-审核拒绝 3-转账中 4-已转账 5-转账失败
    listing_status: number
    receive_user_id: string
    receive_user_country: string
    receive_user_coins_7days: string
    receive_time: number
    transfer_time: number
    receive_timezone: number
    created: number
    updated: number
  }

  interface IGift {
    name: string
    type: string
  }
}
