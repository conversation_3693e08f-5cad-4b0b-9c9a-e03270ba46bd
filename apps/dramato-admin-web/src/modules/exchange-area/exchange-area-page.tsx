import { bindLoading, createComponent, mc } from '@skynet/shared'
import { Button, TableColumnOld, CreateTableOld, CreateForm, Pager, transformNumber, showSuccessToast, openDialog, showFailToast, Empty } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useExchangeAreaStore } from './use-exchange-area-store'
import { set } from 'lodash-es'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { computed, onMounted, Ref, ref, watch } from 'vue'
import { apiGetDailyLimit, apiUpdateDailyLimit, apiUpdateListStatus, apiUpdateTransferStatus, apiUploadAmazonCardExcel } from './exchange-area-api'
import { requiredLabel } from 'src/lib/required-label'
import { get_k_sso_token } from 'src/lib/device-id'

dayjs.extend(utc)

export const ExchangeAreaPage = createComponent(null, () => {
  const {
    searchForm,
    list,
    total,
    loading,
    gifts,
    page,
    size,
    onSearch,
    onPageChange,
    onSizeChange,
    onReset,
    getGifts,
  } = useExchangeAreaStore()

  const Form = CreateForm<M.IExchangeAreaSearchProps>()
  const Table = CreateTableOld<M.IExchangeArea>()
  const dataReady = ref(false)
  const dailyLimit = ref()

  const updateListStatusLoading = ref(false)
  const updateTransferStatusLoading = ref(false)
  const downloadTemplateLoading = ref(false)
  const updateDailyLimitLoading = ref(false)

  const transferStatusTxt = ['审核中', '审核拒绝', '转账中', '已转账', '转账失败']
  const transferWhiteList = ['ovo_transfer', 'brazil', 'philippines', 'mexico', 'vietnam', 'india', 'thailand']
  const emailWhiteList: string[] = []
  const phoneWhiteList: string[] = ['ovo_transfer', 'brazil', 'philippines', 'mexico', 'vietnam', 'india', 'thailand']

  watch(() => gifts.value, () => {
    if (gifts.value.length > 0) {
      dataReady.value = true
      set(searchForm.value, 'type', gifts.value[0].type)
      onSearch(true)
    }
  })

  const updateListStatus = async (id: number, is_up: number) => {
    const res = await bindLoading(apiUpdateListStatus({ id: [id], is_up }), updateListStatusLoading)
    if (res.code === 200) {
      showSuccessToast(is_up === 1 ? '上架成功' : '下架成功')
      onSearch(true)
    }
  }

  const updateOvoTransferStatus = async (id: number) => {
    const res = await bindLoading(apiUpdateTransferStatus({ id, type: 'ovo_transfer' }), updateTransferStatusLoading)
    if (res.code === 200) {
      showSuccessToast('打款成功')
      onSearch(true)
    }
  }

  onMounted(async () => {
    await getGifts()
  })

  const getLocalTime = (time: number, timezone: number) => {
    if (!time) return '--'
    return dayjs.unix(time).utc().add(timezone, 'hour').format('YYYY-MM-DD HH:mm:ss')
  }

  watch(() => searchForm.value.type, () => {
    dailyLimit.value = undefined
    list.value = []
    onSearch(true)
    void getDailyLimit()
  })

  // @ts-expect-error never mind
  const columns: Ref<TableColumnOld<M.IExchangeArea>[]> = computed(() => [
    ['序列', 'id', { class: 'w-[100px]' }],
    ['价值', 'show_card_value', { class: 'w-[200px]' }],
    (searchForm.value.type === 'amazon_card' ? ['卡密', 'card_secret', { class: 'w-[200px]' }] : [emailWhiteList.includes(searchForm.value.type) ? '邮箱' : phoneWhiteList.includes(searchForm.value.type) ? '手机号' : '邮箱', 'receive_user_email', { class: 'w-[200px]' }]),
    ['状态', row => searchForm.value.type === 'amazon_card' ? (row.receive_status === 2 ? '已发放' : '未发放') : transferStatusTxt[row.transfer_status - 1], { class: 'w-[100px]' }],
    (searchForm.value.type === 'amazon_card' ? ['上架状态', row => row.listing_status === 1 ? '已上架' : '已下架', { class: 'w-[200px]' }] : <Empty />),
    ['申请人（UID）', 'receive_user_id', { class: 'w-[150px]' }],
    ['发放人地区', 'receive_user_country', { class: 'w-[130px]' }],
    ['近7天获得金币数', 'receive_user_coins_7days', { class: 'w-[150px]' }],
    ['发放时间（当地时间）', row => getLocalTime(searchForm.value.type === 'amazon_card' ? row.receive_time : row.transfer_time, row.receive_timezone ?? 0), { class: 'w-[200px]' }],
    (searchForm.value.type === 'amazon_card' ? ['上传时间（北京时间）', row => dayjs.unix(row.created).format('YYYY-MM-DD HH:mm:ss'), { class: 'w-[200px]' }] : ['申请时间（当地时间）', row => getLocalTime(row.receive_time, row.receive_timezone ?? 0), { class: 'w-[200px]' }]),
    [<span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap items-center justify-start gap-x-3">
          {searchForm.value.type === 'amazon_card' ? <Button disabled={updateListStatusLoading.value} class="btn btn-outline btn-xs" onClick={() => updateListStatus(row.id, row.listing_status === 1 ? 2 : 1)}>{row.listing_status === 1 ? '下架' : '上架'}</Button> : null}
          {transferWhiteList.includes(searchForm.value.type) ? <Button disabled={updateTransferStatusLoading.value || row.transfer_status === 4} class="btn btn-outline btn-xs" onClick={() => updateOvoTransferStatus(row.id)}>确认打款</Button> : null}
        </div>
      ),
      { class: 'w-[90px]' }],
  ])

  const batchImport = () => {
    const uploading = ref(false)
    const inputRef = ref<HTMLInputElement>()
    const fileChange = async (e: Event) => {
      const files = (e.target as HTMLInputElement)?.files || []
      const fileList = Array.from(files)
      if (fileList && fileList.length > 0) {
        const file = fileList[0]
        const ext = file.name.split('.').pop()?.toLowerCase() || ''
        if (ext !== 'xlsx' && ext !== 'xls') {
          showFailToast('仅支持上传xlsx或xls文件');
          (inputRef.value as HTMLInputElement).value = ''
          return
        }
        try {
          uploading.value = true
          await apiUploadAmazonCardExcel({ file })
          showSuccessToast('导入成功！')
          closeDialog()
          onSearch(true)
          // eslint-disable-next-line
        } catch (error: any) {
          showFailToast(error.response.data.message || '导入失败！');
          (inputRef.value as HTMLInputElement).value = ''
        } finally {
          uploading.value = false
        }
      }
    }
    const closeDialog = openDialog({
      title: '批量导入',
      mainClass: 'pb-0 px-5',
      body: () => (
        <div>
          <div class="mt-4">
            <input ref={inputRef} disabled={uploading.value} class="file-input file-input-primary file-input-xs w-full max-w-xs" type="file" multiple={false} accept="csv" onChange={fileChange} />
            <div class="mt-2 text-xs">{requiredLabel('仅支持上传xlsx或xls文件')}</div>
          </div>
        </div>
      ),
    })
  }

  const getDailyLimit = async () => {
    const res = await apiGetDailyLimit({ type: searchForm.value.type })
    if (res.data) {
      dailyLimit.value = res.data.count
    }
  }

  const submitDailyLimit = async () => {
    if (dailyLimit.value === undefined) {
      showFailToast('请输入单日发放上限')
      return
    }
    // 正整数
    if (!Number.isInteger(dailyLimit.value) || dailyLimit.value <= 0) {
      showFailToast('单日发放上限必须是正整数')
      return
    }
    await bindLoading(apiUpdateDailyLimit({ count: dailyLimit.value, type: searchForm.value.type }), updateDailyLimitLoading)
  }

  const downloadTemplate = async () => {
    try {
      const queryParams = {
        type: searchForm.value.type,
      } as Record<string, string>
      if (searchForm.value.value !== undefined) {
        queryParams.value = searchForm.value.value.toString()
      }
      if (searchForm.value.receive_status !== undefined) {
        queryParams.receive_status = searchForm.value.receive_status.toString()
      }
      if (searchForm.value.transfer_status !== undefined) {
        queryParams.transfer_status = searchForm.value.transfer_status.toString()
      }
      if (searchForm.value.country !== undefined) {
        queryParams.country = searchForm.value.country
      }
      const queryString = new URLSearchParams(queryParams).toString()
      const url = `${import.meta.env.VITE_DRAMA_API_URL}/exchange/gift/${searchForm.value.type}/download?${queryString}`
      // 发送请求到服务器
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Device: 'Web',
          Token: get_k_sso_token() || '',
        },
      })
      // 检查响应是否成功
      if (!response.ok) {
        showFailToast('下载失败')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        showFailToast('下载的文件为空')
        return
      }
      // 创建一个下载链接并触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${searchForm.value.type}_template.xlsx` // 设置下载的文件名
      document.body.appendChild(link)
      link.click() // 自动点击链接以开始下载
      document.body.removeChild(link) // 清理链接
      URL.revokeObjectURL(link.href) // 释放内存
    } catch (error) {
      showFailToast('下载失败')
    }
  }

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>兑换专区管理</li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            dataReady={dataReady.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              ['金额', 'value', { type: 'number', placeholder: '请输入金额' }, { transform: transformNumber }],
              ['发放状态', 'receive_status', { type: 'select', options: [{ label: '已发放', value: 2 }, { label: '未发放', value: 1 }] }, { transform: transformNumber }],
              ['打款状态', 'transfer_status', { type: 'select', options: [
                { label: '审核中', value: 1 },
                { label: '审核拒绝', value: 2 },
                { label: '转账中', value: 3 },
                { label: '已转账', value: 4 },
                { label: '转账失败', value: 5 },
              ] }, { transform: transformNumber }],
              ['发放人地区', 'country', { type: 'text', placeholder: '请输入地区' }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex items-center justify-between gap-x-5">
            <x-exchange-gift-tab class="flex-1">
              <div role="tablist" class="tabs-boxed tabs flex flex-wrap gap-x-2 bg-white">
                {
                  gifts.value.map(gift => (
                    <a
                      role="tab"
                      class={mc('tab text-black', searchForm.value?.type === gift.type ? 'tab-active text-primary font-bold' : '')}
                      onClick={() => {
                        set(searchForm.value, 'type', gift.type)
                      }}
                    >
                      {gift.name}
                    </a>
                  ))
                }
              </div>
            </x-exchange-gift-tab>
            <div class="flex items-center gap-x-3">
              单日发放上限：<input type="number" class="input input-xs input-bordered" placeholder="请输入单日发放上限" v-model={dailyLimit.value} />
              <Button class="btn btn-primary btn-sm" disabled={updateDailyLimitLoading.value} onClick={submitDailyLimit}>提交</Button>
              {searchForm.value.type === 'amazon_card' ? <Button class="btn btn-primary btn-sm" onClick={batchImport}>批量导入</Button> : null}
              {/* <Button class="btn btn-primary btn-sm" onClick={downloadTemplate} disabled={downloadTemplateLoading.value}>下载</Button> */}
            </div>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-last-column"
            list={list.value || []}
            columns={columns.value}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={page.value} v-model:size={size.value} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ExchangeAreaPage
