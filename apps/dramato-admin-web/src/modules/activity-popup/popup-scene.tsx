import { createComponent } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { usePopupScene } from './use-popup-scene'
import { PopupSceneForm } from './popup-scene-form'
import { useAppAndLangOptions } from '../options/use-app-options'

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

export const PopupScene = createComponent(null, () => {
  const {
    onSearch,
    total,
    searchForm,
    onReset,
    currentPopupScene,
    InitPopupSceneOption,
    closeEditPopupSceneModal,
    list,
    onPageChange,
    onPageSizeChange,
    loading,
    config,
    onUpdate,
    getConfig,
  } = usePopupScene()

  const Form = CreateForm<M.DialogScene.Scene>()
  const Table = CreateTableOld<M.DialogScene.Scene>()

  const { appOptions } = useAppAndLangOptions(() => undefined, {})

  const columns: TableColumnOld<M.DialogScene.Scene>[] = [
    ['弹窗ID', 'popup_id', { class: 'w-[200px]' }],
    ['弹窗名称', 'popup_name', { class: 'w-[260px]' }],
    ['权重', 'priority'],
    ['展示屏次', row => row.max + '/' + ['空', '每天'][row.freq_limit_type || 0]],
    ['冷却时间', row => row.interval + '/' + config.value.interval_unit_items[row?.interval_unit || 0]],
    ['状态', row => ['下架', '线上'][row?.popup_status || 0], { class: 'w-[160px]' }],
    ['有效期', row => (<><DateTime value={(row?.start_time as number || 0) * 1000} /> ~ <DateTime value={(row?.end_time as number || 0) * 1000} /></>), { class: 'w-[300px]' }],
    ['弹窗类型', row => config.value.popup_type_items[row?.popup_type || 1], { class: 'w-[160px]' }],
    ['应用/版本', row => appOptions.value.find(i => i.value === row?.app_id)?.label + ' ' + (row?.app_version || '空') + ['', '+'][row.version_compare || 0], { class: 'w-[260px]' }],
    ['语言', 'language_version_code', { class: 'w-[160px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-link btn-sm" onClick={() => {
            currentPopupScene.value = {
              ...row,
            }

            closeEditPopupSceneModal.value = openDialog({
              title: '编辑弹窗场景',
              body: () => <PopupSceneForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[800px] overflow-hidden',
            })
          }}
          >编辑
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => onUpdate(+(row?.id || 0), row?.popup_name || '')}>
            删除
          </Button>
        </div>
      ),
      { class: 'w-[140px]' },
    ],
  ]

  onMounted(() => {
    onSearch()
    void getConfig()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/banner">弹窗场景</RouterLink></li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              [
                '类型',
                'scene_type',
                {
                  type: 'select',
                  options: Object.keys(config.value.popup_scene_items).map(key => ({
                    label: config.value.popup_scene_items[+key],
                    value: +key,
                  }))
                  ,
                },
                {
                  transform: transformNumber,
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="w-full flex justify-between items-center">
            <span>弹窗场景列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => {
                currentPopupScene.value = {
                  ...InitPopupSceneOption,
                }
                closeEditPopupSceneModal.value = openDialog({
                  title: '新建弹窗场景',
                  body: () => <PopupSceneForm />,
                  mainClass: dialogMainClass,
                  customClass: '!w-[800px] overflow-hidden',
                })
              }}
              >新建弹窗场景
              </Button>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default PopupScene
