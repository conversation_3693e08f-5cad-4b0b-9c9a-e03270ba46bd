/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { openDialog, showAlert, showFailToast, showSuccessToast } from '@skynet/ui'
import { apiEditActivityPopupScene, apiGetActivityPopupSceneList, apiGetDialogConfig, updateDialogStatusScene } from './activity-popup-api'

const searchForm = ref<M.ActivityPopupSearchProps>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
  scene_type: 1,
})

const list = ref<M.DialogScene.Scene[]>([])
const total = ref<number>(0)
const closeEditPopupSceneModal = ref(() => {})
const loading = ref(false)
const isUpdating = ref(false)

const InitPopupSceneOption: M.DialogScene.Scene = {
  freq_limit_type: 1,
  interval: 30,
  interval_unit: 2,
}

const currentPopupScene = ref<M.DialogScene.Scene>(InitPopupSceneOption)

const getList = async () => {
  loading.value = true
  const rs = await apiGetActivityPopupSceneList(searchForm.value).finally(() => {
    loading.value = false
  })

  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
}

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearch()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearch()
}

const onReset = () => {
  searchForm.value.app_id = undefined
  onSearch(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeEditPopupSceneModal.value && closeEditPopupSceneModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  onSearch()
}

const switchRequestParams = () => ({
  ...currentPopupScene.value,
  id: currentPopupScene.value.id || 0,
//   start_time: currentPopupScene.value.start_time ? new Date(currentPopupScene.value.start_time || '').getTime() / 1000 : undefined,
//   end_time: currentPopupScene.value.end_time ? new Date(currentPopupScene.value.end_time || '').getTime() / 1000 : undefined,
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditActivityPopupScene(switchRequestParams())
    showSuccessToast('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.message || '创建失败')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditActivityPopupScene(switchRequestParams())
    showSuccessToast('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.message || '编辑失败')
  }
}

const onUpdate = (id: number, title: string) => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除【{title}】?</x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void updateDialogStatusScene({ id }).then(() => {
              showAlert('删除成功')
              onSearch()
            }).catch((error: any) => {
              showAlert(error.response.data.err_msg || '删除失败', 'error')
            })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
export const usePopupScene = () => {
  return {
    searchForm,
    list,
    total,
    loading,
    closeEditPopupSceneModal,
    InitPopupSceneOption,
    currentPopupScene,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    onCreate,
    onEdit,
    isUpdating,
    config,
    onUpdate,
    getConfig,
  }
}

const config = ref<M.PopupConfig>({
  popup_type_items: {
    1: '新用户首日优惠',
    1000: '解锁礼包',
    1001: '打折票',
    1002: '优惠活动',
    2: '未付费老用户首充',
    3: '收藏鼓励折扣',
    7: '首页冷启动',
  },
  popup_scene_items: {
    1: '进入收银台',
    2: '退出收银台',
    3: '打开支付弹窗',
    4: '关闭支付弹窗',
    5: '退出播放器',
    6: '首页进入',
    7: '冷启首页加载时',
    8: 'Deeplink弹窗',
  },
  popup_jump_target_items: {
    1: '商品',
    2: '链接',
  },
  content_items: {
    content: '文案',
    content_color: '文案颜色',
    expire_color: '过期日期颜色',
    sub_title: '子标题',
    title: '标题',
  },
  lang_items: {
    'de-DE': 'German',
    'en-US': 'English',
    'es-MX': 'Spanish',
    'fr-FR': 'French',
    'id-ID': 'Indonesian',
    'it-IT': 'Italian',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'pt-PT': 'Portuguese',
    'ru-RU': 'Russian',
    'th-TH': 'Thai',
    'tl-PH': 'Filipino',
    'tr-TR': 'Turkish',
    'vi-VN': 'Vietnamese',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
  },
  interval_unit_items: {
    1: '秒',
    2: '分钟',
    3: '小时',
  },
})

const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) {
    return
  }
  config.value = res?.data
}
