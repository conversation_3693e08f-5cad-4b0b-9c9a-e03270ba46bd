/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { openDialog, showAlert, showFailToast, showSuccessToast, SvgIcon } from '@skynet/ui'
import { apiEditActivityPopup, apiGetActivityPopupList, apiGetDialogConfig, updateDialogStatus } from './activity-popup-api'
import { mc } from '@skynet/shared'
import { apiGetMemberList } from '../member/member.api'
import { apiGetRechargeLevels } from '../recharge-level/recharge-level-api'
import { cloneDeep } from 'lodash-es'

const searchForm = ref<M.ActivityPopupSearchProps>({
  language_version_code: '',
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})

const list = ref<M.ActivityPopup[]>([])
const total = ref<number>(2)
const closeEditActivityPopupModal = ref(() => {})
const loading = ref(false)
const isUpdating = ref(false)

const InitActivityPopupOption: M.ActivityPopup = {
  jump_method: 1,
  product_type: 'recharge',
  target_type: 1,
  content_i18n: {
    'en-US': {
    },
  },
  language_version_codes: ['en-US'],
}

const currentActivityPopup = ref<M.ActivityPopup>(cloneDeep(InitActivityPopupOption))

const getList = async () => {
  loading.value = true
  const rs = await apiGetActivityPopupList(searchForm.value).finally(() => {
    loading.value = false
  })

  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
}

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearch()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearch()
}

const onReset = () => {
  searchForm.value.app_id = undefined
  searchForm.value.language_version_code = ''
  onSearch(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeEditActivityPopupModal.value && closeEditActivityPopupModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  onSearch()
}

const switchRequestParams = () => ({
  ...currentActivityPopup.value,
  start_time: currentActivityPopup.value.start_time ? new Date(currentActivityPopup.value.start_time || '').getTime() / 1000 : undefined,
  end_time: currentActivityPopup.value.end_time ? new Date(currentActivityPopup.value.end_time || '').getTime() / 1000 : undefined,
})

const isCoupon = () => currentActivityPopup.value.popup_type === 1014

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditActivityPopup(switchRequestParams())
    showSuccessToast('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.err_msg || '创建失败')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditActivityPopup(switchRequestParams())
    showSuccessToast('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.err_msg || '编辑失败')
  }
}

const onPreviewActivityPopup = () => {
  const closePreviewActivityPopup = openDialog({
    title: '',
    mainClass: 'pb-0 px-0',
    body: (
      <x-activity-popup-preview>
        <img src={currentActivityPopup.value.image} alt="" class="w-[375px] h-auto" />
      </x-activity-popup-preview>
    ),
    customClose: () => (
      <x-activity-popup-preview-close
        class="top-[-30px] right-0 fixed flex justify-center items-center bg-white rounded-full cursor-pointer size-6"
        onClick={closePreviewActivityPopup}
      >
        <SvgIcon
          class={mc('size-3  text-gray-500')}
          name="ic_close"
        />
      </x-activity-popup-preview-close>
    ),
    customClass: '!w-[375px] p-0 overflow-visible',
  })
}

const onUpdate = (status: number, popup_id: number, title: string) => {
  const m: { [key: string]: string } = { '-1': '删除', 0: '下架', 1: '上架' }
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认{m[`${status}`]} 弹窗 【{title}】?</x-status-body>
        <x-status-footer class="flex justify-end gap-x-[10px] w-full">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void updateDialogStatus({ status, popup_id }).then(() => {
              showAlert('更新成功')
              onSearch()
            }).catch((error: any) => {
              showAlert(error.response.data.err_msg || '更新失败', 'error')
            })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
export const useActivityPopup = () => {
  return {
    searchForm,
    list,
    total,
    loading,
    closeEditActivityPopupModal,
    InitActivityPopupOption,
    currentActivityPopup,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    onCreate,
    onEdit,
    onPreviewActivityPopup,
    isUpdating,
    config,
    onUpdate,
    getConfig,
    commodityList,
    getCommodityList,
    popup_payment_type_items_filter_ids_for_create,
    getConfigByType,
    isCoupon,
    popupItemsConfig,
    getLangCode,
    checkAlias,
  }
}

// 弹窗优化3.0需要过滤的id
const popup_payment_type_items_filter_ids_for_create = ['1', '2', '3', '1000', '1001']

const config = ref<M.PopupConfig>({
  popup_type_items: {
    1: '新用户首日优惠',
    1000: '解锁礼包',
    1001: '打折票',
    1002: '优惠活动',
    2: '未付费老用户首充',
    3: '收藏鼓励折扣',
    7: '首页冷启动',
  },
  popup_scene_items: {
    1: '进入收银台',
    2: '退出收银台',
    3: '打开支付弹窗',
    4: '关闭支付弹窗',
    5: '退出播放器',
    6: '首页进入',
    7: '冷启首页加载时',
    8: 'Deeplink弹窗',
  },
  popup_jump_target_items: {
    1: '商品',
    2: '链接',
  },
  content_items: {
    content: '文案',
    content_color: '文案颜色',
    expire_color: '过期日期颜色',
    sub_title: '子标题',
    title: '标题',
  },
  lang_items: {
    'de-DE': 'German',
    'en-US': 'English',
    'es-MX': 'Spanish',
    'fr-FR': 'French',
    'id-ID': 'Indonesian',
    'it-IT': 'Italian',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'pt-PT': 'Portuguese',
    'ru-RU': 'Russian',
    'th-TH': 'Thai',
    'tl-PH': 'Filipino',
    'tr-TR': 'Turkish',
    'vi-VN': 'Vietnamese',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
  },
  interval_unit_items: {
    1: '秒',
    2: '分钟',
    3: '小时',
  },
  popup_link_type_items: {
    1002: '优惠活动(历史)',
    1004: '反馈弹窗(历史)',
    1005: '通用内容弹窗',
    1011: '反馈弹窗V2',
    5: '首页内容弹窗(冷启)(历史)',
  },
  popup_payment_type_items: {
    1: '新用户首日优惠(历史)',
    2: '未付费老用户首充(历史)',
    3: '收藏鼓励折扣(历史)',
    1000: '解锁礼包(历史)',
    1001: '打折票(历史)',
    1006: '通用限时弹窗',
    1007: '通用支付弹窗',
    1008: '收藏鼓励折扣V2',
    1009: '解锁礼包V2',
    1010: '打折V2',
    1012: '分享页弹窗V2',
    1013: '开箱转盘抽奖弹窗',
  },
  popup_coupon_type_items: {
    1014: '兑换券弹窗',
  },
  content_item_list_v2: [
    {
      key: 'title',
      desc: '标题',
      color_key: 'color',
      color_desc: '标题颜色',
    },
    {
      key: 'content1',
      desc: '内容1',
      color_key: 'color',
      color_desc: '内容1颜色',
    },
    {
      key: 'content2',
      desc: '内容2',
      color_key: 'color',
      color_desc: '内容2颜色',
    },
    {
      key: 'content3',
      desc: '内容3',
      color_key: 'color',
      color_desc: '内容3颜色',
    },
    {
      key: 'button',
      desc: '按钮',
      color_key: 'color',
      color_desc: '按钮颜色',
    },
  ],
})

const getConfigByType = (type: number) => {
  if (type === 1) return config.value.popup_payment_type_items
  if (type === 2) return config.value.popup_link_type_items
  if (type === 3) return config.value.popup_coupon_type_items
}

const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) return
  config.value = res?.data
}

const getLangCode = (lang: string) => {
  return Object.keys(config.value.lang_items).find(key => config.value.lang_items[key] === lang)
}

const commodityList = ref<any[]>([])

const getCommodityList = async (app_id: number) => {
  commodityList.value = []
  const memberRes = await apiGetMemberList({
    app_id: '' + app_id,
    page_size: 1000,
    status: 0,
    next: '',
  })
  const rechargeRef = await apiGetRechargeLevels({
    app_id: '' + app_id,
    page_size: 1000,
    status: 0,
    next: '',
  })
  commodityList.value = commodityList.value.concat(memberRes.data?.items || []).concat(rechargeRef.data?.items || [])
}

// 配置指定弹窗显示的表单选项
const popupItemsConfig: Record<number, string[]> = {
  1013: ['extra_json', 'button_color'], // 开箱转盘抽奖弹窗
  1012: ['content1_color', 'content1'], // 分享页弹窗V2
  1011: ['title', 'content1', 'content2'], // 反馈弹窗V2
  1010: ['content1_color', 'content1'], // 打折V2
  1009: ['content1_color', 'content1', 'content2_color', 'content2', 'content3_color', 'content3'], // 解锁礼包V2
  1008: ['content1_color', 'content1'], // 收藏鼓励折扣V2
  1005: [], // 通用内容弹窗
  1006: ['content1_color', 'content1', 'button_color'], // 通用限时弹窗
  1007: ['content1_color', 'content1'], // 通用支付弹窗
  1014: ['title', 'title_color', 'content1_color', 'content1', 'button_color', 'button'], // 兑换券弹窗
}

const aliasMap: Record<number, Record<string, string>> = {
  1014: {
    content1: '使用说明',
    content1_color: '使用说明颜色',
  }, // 兑换券弹窗
}

// 如果使用了别名返回别名，否则返回原值
const checkAlias = (key: string, type: number = 0) => {
  return aliasMap[type]?.[key]
}
