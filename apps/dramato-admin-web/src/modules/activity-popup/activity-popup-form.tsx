/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, openDialog, transformDatetime, transformInteger, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader'
import dayjs from 'dayjs'
import { computed, ref, watch } from 'vue'
import { useActivityPopup } from './use-activity-popup'
import { checkDeepLink } from './activity-popup-api'
import { useAppAndLangOptions } from '../options/use-app-options'
import { MultiLangForm } from './activity-popup-multi-lang-form'

export const ActivityPopupForm = createComponent(null, props => {
  const {
    currentActivityPopup,
    onEdit,
    onCreate,
    closeEditActivityPopupModal,
    isUpdating,
    config,
    commodityList,
    getCommodityList,
    getConfigByType,
    isCoupon,
    popupItemsConfig,
    searchForm,
    getLangCode,
    checkAlias,
  } = useActivityPopup()

  const isUploading = ref(false)
  const isUploadedFail = ref(false)
  const Form = CreateForm<M.ActivityPopup>()
  const formRules = z.object({
    popup_name: z.string().min(1, '请输入弹窗名称'),
    popup_type: z.number().min(0, '请选择弹窗类型'),
    // status: z.number().min(0, '请选择状态'),
    limited_time: z.number().min(0, '请选择限时时间').optional(),
    // interval: z.number().min(1, '请选择间隔时间').max(9999, '支持输入1-9999的整数'),
    // max: z.number().min(1, '请输入展示次数').max(9999, '支持输入1-9999的整数'),
    target_link: z.string().min(1, '请输入'),
    image: z.string().min(1, '请上传封面').optional(),
    jump_method: z.number().min(1, '请选择'),
    app_id: z.number().min(1, '请选择生效应用'),
    // version_compare: z.number().min(0, '请选择'),
    // app_version: z.string().min(1, '请选择生效版本号').optional(),
    // product_type: z.string().min(1, '请选择生效产品类型'),
    language_version_codes: z.array(z.string()).min(1, '请选择生效语言版本'),
    target_type: z.number().min(1, '请选择'),
    jump_interval: z.number().min(0, '请填写'),
    // state: z.number().min(1, '请选择状态'),
  })

  const { error, validateAll } = useValidator(currentActivityPopup, formRules)

  const { appOptions, languageOptions } = useAppAndLangOptions(() => currentActivityPopup.value.app_id)

  watch(() => currentActivityPopup.value.app_id, () => {
    if (!currentActivityPopup.value.app_id) {
      commodityList.value = []
    } else {
      void getCommodityList(currentActivityPopup.value?.app_id || 0)
    }
  }, { deep: true, immediate: true })

  watch(() => currentActivityPopup.value.target_type, () => {
    if (currentActivityPopup.value.target_type === 3) {
      currentActivityPopup.value.popup_type = 1014
    }
  }, { immediate: true })

  const isNeedMultiLang = computed(() => {
    return (currentActivityPopup.value.language_version_codes.length === 1 && !currentActivityPopup.value.language_version_codes?.includes('en-US'))
      || currentActivityPopup.value.language_version_codes.length > 1
  })

  const showMultiLang = (key: string) => {
    // 如果只有英语，不需要多语言
    if (!isNeedMultiLang.value) return
    const close = openDialog({
      title: '多语言',
      body: (
        <MultiLangForm key={key}
          onSubmit={(data: Record<string, any>) => {
            console.log('data', data)
            Object.keys(data).forEach(lang => {
              set(currentActivityPopup.value, `content_i18n.${lang}.${key}`, data[lang])
            })
            close()
            console.log('currentActivityPopup.value', currentActivityPopup.value.content_i18n)
          }}
          langs={currentActivityPopup.value.language_version_codes || []}
          values={Object.keys(currentActivityPopup.value.content_i18n).reduce((acc, lang) => {
            acc[lang] = currentActivityPopup.value.content_i18n[lang][key]
            return acc
          }, {} as Record<string, string>)}
        />
      ),

    })
  }
  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            console.log('path', path, value, typeof value)

            if (['start_time', 'end_time'].includes(path) && value === 'Invalid Date') {
              set(currentActivityPopup.value || {}, path, undefined)
              return
            }

            if (path === 'language_version_codes') {
              // 因为多语言的数据结构是一个对象，所以需要特殊处理
              // language保存元素语言的类型
              // language_version_code保存元素语言code码
              // 选中多语言后，同时要更新content_i18n
              set(currentActivityPopup.value || {}, path, value)
              currentActivityPopup.value.language_version_codes?.forEach((v: string) => {
                if (!currentActivityPopup.value.content_i18n[v]) {
                  currentActivityPopup.value.content_i18n[v] = {}
                }
              })
              console.log(currentActivityPopup.value.language_version_codes)
              return
            }

            if (path === 'product_type' || path === 'target_type') {
              set(currentActivityPopup.value || {}, 'target_link', '')
            }

            set(currentActivityPopup.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('弹窗名称'),
              'popup_name',
              {
                type: 'textarea',
                maxlength: 100,
                placeholder: '请输入标题，最大支持100字符',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('弹窗类型'),
              'target_type',
              {
                type: 'radio',
                options: [
                  { label: '支付类', value: 1, disabled: currentActivityPopup.value.popup_id },
                  { label: '链接类', value: 2, disabled: currentActivityPopup.value.popup_id },
                  { label: '兑换券弹窗', value: 3, disabled: currentActivityPopup.value.popup_id },
                ],
              },
              {
                // transform: transformNumber,
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('弹窗模版'),
              'popup_type',
              {
                type: 'select',
                options: Object.keys(getConfigByType(currentActivityPopup.value.target_type!) as {}).map(key => ({
                  label: getConfigByType(currentActivityPopup.value.target_type!)?.[+key] ?? '',
                  value: +key,
                })),
                autoInsertEmptyOption: false,
                disabled: currentActivityPopup.value.popup_id,
              },
              {
                transform: transformNumber,
                class: 'col-span-1',
              },
            ],
            [
              [
                requiredLabel('生效应用'),
                'app_id',
                {
                  type: 'select',
                  options: appOptions.value.filter(i => i.label.includes('Drama')),
                },
                {
                  class: 'col-span-1',
                  transform: transformInteger,
                },
              ],
              // [
              //   requiredLabel('>='),
              //   'version_compare',
              //   {
              //     type: 'radio',
              //     options: [
              //       {
              //         label: '是',
              //         value: 1,
              //       },
              //       {
              //         label: '否',
              //         value: 0,
              //       },
              //     ],
              //   },
              //   {
              //     class: 'col-span-1',
              //     // transform: transformNumber,
              //   },
              // ],
              // [
              //   '版本号',
              //   'app_version',
              //   {
              //     type: 'text',
              //   },
              //   {
              //     class: 'col-span-1',
              //   },
              // ],
              // [
              //   requiredLabel('语言'),
              //   'language_version_code',
              //   {
              //     type: 'select',
              //     options: languageOptions.value,
              //     autoInsertEmptyOption: false,
              //   },
              // ],
            ],
            [
              requiredLabel('兑换券ID'),
              'coupon_info.coupon_id',
              {
                type: 'text',
                placeholder: '请从券库里复制1个兑换券ID',

              },
              {
                transform: transformInteger,
                class: mc('col-span-1', isCoupon() ? '' : 'hidden'),
              },
            ],
            [
              requiredLabel('所兑换的剧'),
              'coupon_info.series_key',
              {
                type: 'text',
                placeholder: '请从剧库里复制1个短剧ID',

              },
              {
                class: mc('col-span-1', isCoupon() ? '' : 'hidden'),
              },
            ],
            [[
              requiredLabel('发券的数量'),
              'coupon_info.coupon_num',
              {
                type: 'select',
                options: Array.from({ length: 10 }).map((_, index) => {
                  return { value: index + 1, label: `${index + 1}张` }
                }),

              },
              {
                transform: transformInteger,
                class: mc('col-span-1', isCoupon() ? '' : 'hidden'),
              },
            ],
            ['', '', {
              type: 'custom',
              render: () => {
                return (
                  <x-tip>
                    为此用户在弹窗一次性领取的数量
                  </x-tip>
                )
              },
            }, {
              class: mc('col-span-1 mt-7', isCoupon() ? '' : 'hidden'),
            }],
            ],
            [
              requiredLabel('下发语言'),
              'language_version_codes',
              {
                type: 'checkbox-group',
                options: languageOptions.value.map(i => ({ label: i.label, value: getLangCode(i.value) })),
                class: 'col-span-1 flex-wrap',
              },
              {
                class: 'col-span-1 flex-wrap',
              },
            ],
            [
              '',
              '',
              {
                type: 'custom',
                render: () => {
                  return (
                    <x-tip class="text-gray-500">
                      注意:
                      <ul>
                        <li>1. 选择下发语言弹窗才会下发，至少选择1个，可多选语言</li>
                        <li>2. 若需下发文案，请填写对应语言，否则对应语言弹窗内容为空</li>
                        <li>3. 已填写语言文案的，可不选择不下发(不勾选下发语言)</li>
                      </ul>
                    </x-tip>
                  )
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('跳转链接'),
              'target_link',
              {
                type: 'textarea',
                maxlength: 200,
                placeholder: '请输入标题，最大支持200字符, deeplink格式为: dramato://xxx',
              },
              {
                class: mc('col-span-1', currentActivityPopup.value.target_type !== 2 ? 'hidden' : ''),
              },
            ],
            [
              requiredLabel('商品ID'),
              'target_link',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: commodityList.value.map((n, index) => {
                  return { value: '' + n.id, label: `${n.id}/${n.title}` }
                }),
                maxlength: 1,
              },
              {
                class: mc('col-span-1', currentActivityPopup.value.target_type !== 1 ? 'hidden' : ''),
                transform: [
                  (raw?: unknown) => raw ? [raw] : [],
                  (display: string[]): string => display[0] ? '' + (display[0]) : '',
                ] as const,
              },
            ],
            [
              '限时时间',
              'limited_time',
              {
                type: 'number',
                placeholder: '支持 0-100整数输入，0表示不限制',
                suffix: 'h',
              },
              {
                transform: transformInteger,
                class: mc('col-span-1'),
              },
            ],
            [
              '限时文字颜色（点击色块选择色值）',
              'limited_time_color',
              {
                type: 'color',
                placeholder: '色值例如 0XAARRGGBB',
                hasAlpha: true,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('跳转方式'),
              'jump_method',
              {
                type: 'radio',
                options: [
                  {
                    label: '手动点击跳转',
                    value: 1,
                  },
                  {
                    label: '自动跳转',
                    value: 2,
                    disabled: isCoupon(),
                  },
                ],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '自动跳转倒计时',
              'jump_interval',
              {
                type: 'number',
                placeholder: '支持 0-300整数输入',
                suffix: 's后自动跳转',
              },
              {
                transform: transformInteger,
                class: mc('col-span-1', isCoupon() ? 'hidden' : ''),
              },
            ],
            [
              '开始时间',
              'start_time',
              {
                type: 'datetime',
                min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformDatetime,
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：这里设定的是(UTC-08:00)北京时间
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              '结束时间',
              'end_time',
              {
                type: 'datetime',
                min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                disabled: !currentActivityPopup.value.start_time,
              },
              {
                transform: transformDatetime,
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：这里设定的是(UTC-08:00)北京时间
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('图片'),
              'image',
              {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      beforeUpload={() => {
                        if (isUpdating.value) {
                          return Promise.resolve(false)
                        }
                        isUploading.value = true
                        return Promise.resolve(isUploading.value)
                      }}
                      onUploadSuccess={d => {
                        currentActivityPopup.value.image = d.temp_path
                        isUploading.value = false
                        isUploadedFail.value = false
                      }}
                      onUploadFailed={() => {
                        isUploading.value = false
                        isUploadedFail.value = true
                      }}
                      isImage={true}
                      multiple={false}
                      uploadUrl="/popup/upload/image"
                    >
                      {
                        currentActivityPopup.value.image && !isUploading.value
                          ? <img src={currentActivityPopup.value.image.includes('https://') ? currentActivityPopup.value.image : 'https://static-v1.mydramawave.com/popup/image/' + currentActivityPopup.value.image} class="size-full object-cover" />
                          : (
                              <span class="size-full flex items-center justify-center">{
                                isUploading.value
                                  ? (
                                      <div class="flex flex-col gap-y-2 items-center">
                                        <span class="loading loading-spinner size-4" />
                                        <span>上传中</span>
                                      </div>
                                    )
                                  : '上传图片'
                              }
                              </span>
                            )
                      }
                    </Uploader>
                  </x-upload-cover>

                ),
              },
              {
                class: 'col-span-1',
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    { isUploadedFail.value && <span class="text-red-500">上传失败, 请重试</span>}
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      支持 png、jpg、jpeg 格式图片，大小限制 10M 以内
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            ...(currentActivityPopup.value?.popup_type && currentActivityPopup.value?.popup_type >= 1005
              ? config.value.content_item_list_v2?.map((item: any) => {
                return [
                  [
                    () => <div class="flex justify-between"><span>{checkAlias(item.key, currentActivityPopup.value?.popup_type) || item.desc}（英语）</span><Button class="btn btn-primary btn-sm" disabled={!isNeedMultiLang} onClick={() => showMultiLang(item.key)}>多语言</Button> </div>,
                      `content_i18n[en-US][${item.key}]`,
                      {
                        type: 'textarea',
                      },
                      {
                        class: mc('col-span-1', currentActivityPopup.value?.popup_type + '', (currentActivityPopup.value?.popup_type && popupItemsConfig[currentActivityPopup.value?.popup_type]?.includes(item.key)) ? '' : 'hidden'),
                      },
                  ],
                  [
                    () => <div class="flex justify-between"><span>{checkAlias(item.color_key, currentActivityPopup.value?.popup_type) || item.color_desc}</span><Button class="btn btn-primary btn-sm" disabled={!isNeedMultiLang} onClick={() => showMultiLang(item.color_key)}>多语言</Button> </div>,
                      `content_i18n[en-US][${item.color_key}]`,
                      {
                        type: 'color',
                        hasAlpha: true,
                      },
                      {
                        class: mc('col-span-1', currentActivityPopup.value?.popup_type + '', (currentActivityPopup.value?.popup_type && popupItemsConfig[currentActivityPopup.value?.popup_type]?.includes(item.color_key)) ? '' : 'hidden'),
                      },
                  ],
                ]
              })
                .flat()
              : Object.keys(config.value.content_items).map(key => {
                return [
                  config.value.content_items[key],
                `content[${key}]`,
                {
                  type: key.includes('color') ? 'color' : 'textarea',
                },
                {
                  class: mc('col-span-1'),
                },
                ]
              }) as any),
          ]}
          data={currentActivityPopup.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditActivityPopupModal.value}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} disabled={isUpdating.value} onClick={async () => {
          const next = () => !currentActivityPopup.value?.popup_id ? void onCreate() : void onEdit()

          const showTipsDialog = () => {
            const hideDialog = openDialog({
              title: '',
              mainClass: 'pb-0 px-5',
              body: (
                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-status-body>deeplink 对应短剧已下线，请确认是否保存</x-status-body>
                  <x-status-footer class="w-full flex justify-end gap-x-[10px]">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      next()
                      hideDialog()
                    }}
                    >确定
                    </button>
                  </x-status-footer>
                </x-status-confirm-dialog>
              ),
            })
          }
          try {
            const exclude: string[] = []
            if (+(currentActivityPopup.value.jump_method || '') !== 2) {
              exclude.push('jump_interval')
            }

            if (currentActivityPopup.value.target_type !== 1) {
              exclude.push('product_type')
            }

            if (currentActivityPopup.value.version_compare === 0) {
              exclude.push('app_version')
            }
            if (isCoupon()) {
              exclude.push('target_link')
            }
            if (!validateAll({ exclude })) {
              console.log('error', error.value)
              return
            }

            if (currentActivityPopup.value.target_type === 2) {
              const res = await checkDeepLink({
                target_app_ids: `${currentActivityPopup.value.app_id || ''}`,
                link: currentActivityPopup.value.target_link || '',
              })

              if (res.code !== 200) {
                showTipsDialog()
                return
              }
            }

            next()
          } catch (error) {
            showTipsDialog()
          }
        }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
