/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, required } from '@skynet/shared'
import { Button, CreateForm, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useActivityPopup } from './use-activity-popup'

type PopupConditionOptions = {
  props: {
    key: string // 多语言的key
    langs: string[]
    values: Record<string, string>
  }
  emits: {
    submit: (data: Record<string, any>) => void
  }
}
export const MultiLangForm = createComponent<PopupConditionOptions>({
  props: {
    key: required,
    langs: required,
    values: required,
  },
  emits: {
    submit: fn,
  },
}, (props, { emit }) => {
  const Form = CreateForm<Record<string, string[]>>()
  const formData = ref<Record<string, any>>(props.values)

  const { getLangCode, currentActivityPopup } = useActivityPopup()

  const { languageOptions } = useAppAndLangOptions(() => currentActivityPopup.value.app_id)

  const onSubmit = () => {
    emit('submit', formData.value)
  }

  return () => (
    <>
      <h5 class="text-gray-500 text-xs">*请注意补齐需要下发的多语言文案，否则弹窗可能显示为空</h5>
      <Form data={formData.value} class="flex-col"
        onChange={(path, value: any) => {
          formData.value = { ...formData.value, [path]: value }
        }}
        onSubmit={onSubmit}
        actions={
          <Button class="btn btn-primary btn-sm" type="submit">提交</Button>
        }
        items={
          [
            ...languageOptions.value.filter(l => props.langs.includes(getLangCode(l.value) || '') && l.value !== 'English').map(lang => [ // 英语默认在外面填
              lang.label,
              getLangCode(lang.value),
              {
                type: 'text',
                class: 'col-span-2',
              },
            ]),
          ] as any
        }
      />
    </>
  )
})
