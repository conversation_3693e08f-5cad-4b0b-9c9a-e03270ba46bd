/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, openDialog, transformInteger, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { usePopupScene } from './use-popup-scene'
import { ActivityPopup } from './activity-popup-page'
import { ref } from 'vue'

export const PopupSceneForm = createComponent(null, props => {
  const {
    currentPopupScene,
    onEdit,
    onCreate,
    closeEditPopupSceneModal,
    isUpdating,
    config,
  } = usePopupScene()

  const Form = CreateForm<M.DialogScene.Scene>()
  const formRules = z.object({
    popup_id: z.number().min(1, '请输入弹窗名称'),
    scene_type: z.number().min(1, '请选择弹窗场景'),
    freq_limit_type: z.number().min(1, '请选择频率限制类型'),
    interval: z.number().min(0, '请选择间隔时间').max(60, '支持输入0-60的整数'),
    max: z.number().min(1, '请输入展示次数').max(9999, '支持输入1-9999的整数'),
    priority: z.number().min(1, '请填写优先级').max(999, '支持输入1-999的整数'),
  })

  const { error, validateAll } = useValidator(currentPopupScene, formRules)

  const checkItem = ref<M.ActivityPopup[]>([])

  const showImportDialog = () => {
    const hide = openDialog({
      title: '导入弹窗',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <ActivityPopup
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            checkedItem={checkItem.value}
            // platform={appOptions.value.find(i => i.value === searchForm.value.app_id)?.platform}
            onAdd={item => {
              checkItem.value = [...checkItem.value, item]
              checkItem.value = checkItem.value?.filter(i => i.popup_id === item.popup_id)
              console.log('checkItem.value', checkItem.value)
            }}
            onRemove={item => {
              checkItem.value = checkItem.value?.filter(i => i.popup_id !== item.popup_id)
            }}
          />
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white border-gray-200 border-t pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              currentPopupScene.value.popup_id = +(checkItem.value[0].popup_id || 0)
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-800px',
    })
  }

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            console.log('path', path, value, typeof value)

            if (['start_time', 'end_time'].includes(path) && value === 'Invalid Date') {
              set(currentPopupScene.value || {}, path, undefined)
              return
            }

            set(currentPopupScene.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('弹窗ID'),
              'popup_id',
              {
                type: 'number',
                disabled: true,
                suffix: (
                  <Button class="btn btn-primary btn-xs" onClick={() => {
                    showImportDialog()
                  }}
                  >选择弹窗
                  </Button>
                ),
              },
              {
                class: 'col-span-1',
                transform: transformNumber,
                hint: () => (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      请选择已配置完成的弹窗ID，同一场景下不可重复添加
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('权重值'),
              'priority',
              {
                type: 'number',
                max: 999,
                min: 1,
                placeholder: '请输入优先级, 支持输入 1-999 的整数',
              },
              {
                transform: transformInteger,
                hint: () => (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      1. 权重越大优先级越高，同一场景不可有重复的权重
                    </x-tip>
                    <x-tip>
                      2. 若同一弹窗不同语言，可以以各位数区分，例如：英语版本权重699，日语版本权重698
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('弹窗场景'),
              'scene_type',
              {
                type: 'select',
                options: Object.keys(config.value.popup_scene_items).map(key => ({
                  label: config.value.popup_scene_items[+key],
                  value: +key,
                }))
                ,
              },
              {
                transform: transformNumber,
                class: 'col-span-1',
              },
            ],
            [
              [
                requiredLabel('展示频次'),
                'max',
                {
                  type: 'number',
                  placeholder: '请输入展示次数，支持输入1-9999的整数',
                  suffix: '次',
                },
                {
                  transform: transformInteger,
                  class: mc('col-span-1'),
                },
              ],
              [
                requiredLabel('单位'),
                'freq_limit_type',
                {
                  type: 'radio',
                  options: [
                    {
                      label: '每日',
                      value: 1,
                    },
                    {
                      label: '永久',
                      value: 4,
                    },
                  ],
                },
                {
                  transform: transformInteger,
                  class: mc('col-span-1'),
                },
              ],
            ],
            [
              [
                requiredLabel('间隔时间'),
                'interval',
                {
                  type: 'number',
                  placeholder: '请输入间隔时间，支持输入1-9999的整数',
                },
                {
                  transform: transformInteger,
                  class: mc('col-span-1'),
                },
              ],
              [
                requiredLabel('单位'),
                'interval_unit',
                {
                  type: 'radio',
                  options: Object.keys(config.value.interval_unit_items).map(key => ({
                    label: config.value.interval_unit_items[+key],
                    value: +key,
                  })),
                },
                {
                  transform: transformInteger,
                  class: mc('col-span-1'),
                },
              ],
            ],
            [
              '',
              '',
              {
                type: 'custom',
                render: () => (
                  <x-upload-cover-tip class=" text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      仅在频次大于等于2时有效，决定当天展示1次后再展示的间隔
                    </x-tip>
                    <x-tip>
                      请注意弹窗的频率体验，以及更低权重弹窗的展示机会
                    </x-tip>
                    <x-tip>
                      请慎重：若设0，则会展示次数完成后，才能展示更低权重的弹窗。
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
          ]}
          data={currentPopupScene.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditPopupSceneModal.value}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} disabled={isUpdating.value} onClick={() => {
          const next = () => !currentPopupScene.value?.id ? void onCreate() : void onEdit()

          try {
            const exclude: string[] = []

            if (!validateAll({ exclude })) {
              return
            }

            next()
          } catch (error) {
            console.log('error', error)
          }
        }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
