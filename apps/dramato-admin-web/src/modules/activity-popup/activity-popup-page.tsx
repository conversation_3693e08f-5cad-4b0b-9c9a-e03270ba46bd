import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import dayjs from 'dayjs'
import { cloneDeep, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { useAppAndLangOptions } from '../options/use-app-options'
import { ActivityPopupForm } from './activity-popup-form'
import { useActivityPopup } from './use-activity-popup'
import { usePopupConditionStore } from './use-popup-condition-store'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] h-[80vh] overflow-hidden'

type ActivityPopupOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.ActivityPopup[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.ActivityPopup) => void
    remove: (id: M.ActivityPopup) => void
  }
}

export const ActivityPopup = createComponent<ActivityPopupOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.ActivityPopup) => {},
    remove: (item: M.ActivityPopup) => {},
  },
}, (props, { emit }) => {
  const { onSearch, total, searchForm, onReset, currentActivityPopup, InitActivityPopupOption, closeEditActivityPopupModal, list,
    onPageChange, onPageSizeChange, loading, config, onUpdate, getConfig } = useActivityPopup()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: () => {
      void getConfig()
      // void getCommodityList()
      onSearch()
    },
  })

  // watch(appOptions, () => {
  //   if (appOptions.value.length > 0 && !searchForm.value.app_id) {
  //     searchForm.value.app_id = appOptions.value[0].value
  //   }
  // })

  // watch(() => searchForm.value.app_id, id => {
  //   searchForm.value.language_version_code = id ? (languageOptions.value[0]?.value) : ''
  // })

  const Form = CreateForm<M.ActivityPopupSearchProps>()
  const Table = CreateTableOld<M.ActivityPopup>()

  const { showPopupCondition } = usePopupConditionStore()

  const columns: TableColumnOld<M.ActivityPopup>[] = [
    [
      '',
      row => {
        const id = row.popup_id
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={props.checkedItem.map(i => i.popup_id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.popup_id).includes(id)) {
                  emit('add', list.value.find(i => i.popup_id === id)!)
                }
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.popup_id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['弹窗ID', 'popup_id', { class: 'w-[50px]' }],
    ['弹窗名称', 'popup_name', { class: 'w-[260px]' }],
    ['用户分层名称', 'strategy_layer_names', { class: 'w-[200px]' }],
    ['弹窗类型', row => config.value.popup_type_items[row?.popup_type || 1], { class: 'w-[160px]' }],
    ['状态', row => ['下架', '线上'][row?.status || 0], { class: 'w-[60px]' }],
    ['应用/版本', row => appOptions.value.find(i => i.value === row?.app_id)?.label + ' ' + (row?.app_version || '空') + ['', '+'][row.version_compare || 0], { class: 'w-[260px]' }],
    ['下发语言', row => row.language_version_codes?.join(','), { class: 'w-[160px]' }],
    ['跳转类型', row => config.value.popup_jump_target_items[row.target_type || 0], { class: 'w-[160px]' }],
    ['是否限时', row => !row.limited_time ? '不限时' : row.limited_time + 'h', { class: 'w-[160px]' }],
    ['有效期', row => (<><DateTime value={(row?.start_time as number || 0) * 1000} /> ~ <DateTime value={(row?.end_time as number || 0) * 1000} /></>), { class: 'w-[300px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-link btn-sm" onClick={() => {
            currentActivityPopup.value = {
              ...row,
              start_time: row?.start_time ? dayjs(+(row?.start_time || '') * 1000).format('YYYY-MM-DD HH:mm:ss') : undefined,
              end_time: row?.end_time ? dayjs(+(row?.end_time || '') * 1000).format('YYYY-MM-DD HH:mm:ss') : undefined,
            }

            closeEditActivityPopupModal.value = openDialog({
              title: '编辑弹窗管理',
              body: () => <ActivityPopupForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[800px] overflow-hidden',
            })
          }}
          >编辑
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => { showPopupCondition(row.popup_id!, row.popup_name!) }}>
            弹窗条件
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => onUpdate(row?.status === 0 ? 1 : 0, +(row?.popup_id || 0), row?.popup_name || '')}>
            {row?.status === 0 ? '上线' : '下线'}
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => onUpdate(-1, +(row?.popup_id || 0), row?.popup_name || '')}>
            删除
          </Button>
        </div>
      ),
      { class: mc('w-[260px]', props.hasActions ? '' : 'hidden') },
    ],
  ]

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => props.hasNav
          ? (
              <ul>
                <li><RouterLink to="/banner">弹窗管理</RouterLink></li>
              </ul>
            )
          : null,
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              [
                requiredLabel('应用'),
                'app_id',
                {
                  type: 'select',
                  options: appOptions.value.filter(i => i.label.includes('Drama')),
                  // disabled: props.appIdSelectDisabled,
                },
                {
                  transform: transformNumber,
                }],
              [
                requiredLabel('语言'),
                'language_version_code',
                {
                  type: 'select',
                  options: languageOptions.value,
                },
              ],
              [
                '版本',
                'app_version',
                {
                  type: 'text',
                },
              ],
              [
                '状态',
                'status',
                {
                  type: 'select',
                  options: [
                    { label: '上架', value: 1 },
                    { label: '未上架', value: 0 },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '类型',
                'popup_type',
                {
                  type: 'select',
                  options: Object.keys(config.value.popup_type_items).map(key => ({
                    label: config.value.popup_type_items[+key],
                    value: +key,
                  }))
                  ,
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '分层画像',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="w-full flex justify-between items-center">
                  <span>弹窗管理列表</span>
                  <x-table-actions-right class="flex gap-x-2">
                    <Button class="btn btn-primary btn-sm" onClick={() => {
                      currentActivityPopup.value = cloneDeep(InitActivityPopupOption)
                      closeEditActivityPopupModal.value = openDialog({
                        title: '新建弹窗管理',
                        body: () => <ActivityPopupForm />,
                        mainClass: dialogMainClass,
                        customClass: '!w-[800px] overflow-hidden',
                      })
                    }}
                    >新建弹窗管理
                    </Button>
                  </x-table-actions-right>

                </x-table-actions>
              )
            : null
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default ActivityPopup
