/* eslint-disable @typescript-eslint/no-explicit-any */
import { bindLoading, createComponent, fn, mc, required } from '@skynet/shared'
import { Button, showAlert } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { set } from 'lodash-es'
import { keepError } from 'src/lib/http-client'
import { onMounted, ref } from 'vue'
import { ProfileCustomRenderers, profileDefaultValues } from '../strategy-group/profile-custom-renders'
import { popupApi } from './activity-popup-api'
import { usePopupConditionStore } from './use-popup-condition-store'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'

type PopupConditionOptions = {
  props: {
    popupId: number
  }
  emits: {
    submit: Fn
  }
}
export const PopupCondition = createComponent<PopupConditionOptions>({
  props: {
    popupId: required,
  },
  emits: {
    submit: fn,
  },
}, (props, { emit }) => {
  const { userProfileList, fetchingUserProfileList, fetchUserProfileList, formData, Form } = usePopupConditionStore(props.popupId)
  const loading = ref(false)

  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  onMounted(async () => {
    const [_, response] = await bindLoading(Promise.all([fetchUserProfileList(), popupApi.getPersona(props.popupId)]), loading)
    // 填充默认值
    userProfileList.value.forEach(item => {
      set(formData.value, item.item_code, profileDefaultValues[item.item_code] ?? [''])
    })
    if (!response.data) return
    // 用户填写的值
    Object.assign(formData.value, {
      ...response.data.user_config.custom_users_config,
      condition_type: response.data.condition_type,
      strategy_id: response.data.strategy_id,
      strategy_layer_ids: response.data.strategy_layer_ids
    })
    console.log('formData.value:', JSON.stringify(formData.value))
  })
  const onChange = (path: string, value: unknown) => {
    set(formData.value, path, value)
  }
  const onSubmit = async () => {
    await popupApi.updatePersona(props.popupId, formData.value)
      .catch(keepError(() => showAlert('提交失败')))
    emit('submit')
    showAlert('提交成功')
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  return () => (
    (fetchingUserProfileList.value && userProfileList.value)
      ? <span>Loading...</span>
      : (
          <>
            <Form data={formData.value} class="flex-col"
              onChange={onChange}
              onSubmit={onSubmit}
              actions={
                <Button class="btn btn-primary btn-sm" type="submit">提交</Button>
              }
              items={
                [
                  // [
                  //   '条件类型',
                  //   'condition_type',
                  //   {
                  //     type: 'radio',
                  //     options: [
                  //       { label: '画像', value: 1 },
                  //       { label: '策略', value: 2 },
                  //     ],
                  //   },
                  //   {
                  //     // transform: transformNumber,
                  //     class: 'col-span-1',
                  //   },
                  // ],
                  [
                    '分层画像',
                    'strategy_layer_ids',
                    {
                      type: 'multi-select',
                      search: true,
                      popoverWrapperClass: 'z-popover-in-dialog',
                      options: list.value.map((n, index) => {
                        return { value: n.id, label: `${n.id}/${n.name}` }
                      }),
                    },
                    {
                      class: mc('col-span-1'),
                    },
                  ],
                  // [
                  //   '策略组Id',
                  //   'strategy_id',
                  //   {
                  //     type: 'text',
                  //   },
                  //   {
                  //     // transform: transformNumber,
                  //     class: mc('col-span-1', formData.value.condition_type !== 2 ? 'hidden' : ''),
                  //   },
                  // ],
                  // ...(formData.value.condition_type !== 2
                  //   ? userProfileList.value.map(item =>
                  //     [
                  //       item.item_name,
                  //       item.item_code,
                  //       Object.keys(ProfileCustomRenderers).includes(item.item_code)
                  //         ? {
                  //             type: 'custom',
                  //             render: ProfileCustomRenderers[item.item_code](item),
                  //           }
                  //         : {
                  //             type: 'checkbox-group',
                  //             minLength: 1,
                  //             options: item.item_child.map(child => ({ value: child.item_value, label: child.item_name })),
                  //           },
                  //       { errorVisible: false },
                  //     ] as const)
                  //   : []),
                ] as any
              }
            />
          </>
        )
  )
})
