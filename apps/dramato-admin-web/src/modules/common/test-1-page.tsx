import { createComponent } from '@skynet/shared'
import { Button, Tooltip } from '@skynet/ui'
type Test1PageOptions = {
  props: {}
}
export const Test1Page = createComponent<Test1PageOptions>({
  props: {},
}, props => {
  return () => (
    <div>
      <Tooltip popWrapperClass="bg-red-500" popContent={() => <div>hi</div>} triggerType="click">
        <Button>test</Button>
      </Tooltip>
    </div>
  )
})

export default Test1Page
