import { get_k_sso_token } from 'src/lib/device-id.ts'

export function formatSubtitles(srtText: string) {
  const lines = srtText.split('\n')
  const timeRegex = /^\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}\s*$/

  // 找到所有时间轴行索引
  const timeIndexes: number[] = []
  for (let i = 0; i < lines.length; i++) {
    if (timeRegex.test(lines[i].trim())) {
      timeIndexes.push(i)
    }
  }

  const result = []

  for (let idx = 0; idx < timeIndexes.length; idx++) {
    const start = timeIndexes[idx]
    const nextTimeIndex = idx + 1 < timeIndexes.length ? timeIndexes[idx + 1] : lines.length

    const contentStart = start + 1
    let contentEnd = nextTimeIndex - 1

    // 检查下一个时间轴前一行是否是“疑似序号行”
    // 原则：如果该行不是空，并且紧贴时间轴（即 nextTimeIndex - 1），就不算内容
    while (contentEnd >= contentStart && lines[contentEnd].trim() === '') {
      contentEnd-- // 先移除尾部空行
    }

    // 如果不是最后一个时间段，且下一个时间轴上方的行不是空行，就可能是序号行，排除掉
    if (idx + 1 < timeIndexes.length && contentEnd === nextTimeIndex - 1 && lines[contentEnd].trim() !== '') {
      contentEnd--
    }

    const rawContentLines = lines.slice(contentStart, contentEnd + 1)

    // 再移除尾部空行（保留头部空行）
    let lastNonEmptyIndex = rawContentLines.length - 1
    while (lastNonEmptyIndex >= 0 && rawContentLines[lastNonEmptyIndex].trim() === '') {
      lastNonEmptyIndex--
    }

    const contentLines = rawContentLines.slice(0, lastNonEmptyIndex + 1)

    result.push({
      time: lines[start].trim(),
      content: contentLines.join('\n'),
    })
  }

  return result
}

export function srtToVtt(srtContent: string) {
  const vttContent = 'WEBVTT\n\n'
  const srtLines = srtContent.split('\n')
  let vtt = vttContent
  let subtitleBlock = ''

  for (let i = 0; i < srtLines.length; i++) {
    const timeMatch = srtLines[i].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)
    // 数字下是时间轴的才是序号
    if (srtLines[i].match(/^\d+$/) && i < srtLines.length - 1 && srtLines[i + 1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)) {
      // Skip subtitle index
      continue
    }

    if (timeMatch) {
      const startTime = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}.${timeMatch[4]}`
      const endTime = `${timeMatch[5]}:${timeMatch[6]}:${timeMatch[7]}.${timeMatch[8]}`
      subtitleBlock += `${startTime} --> ${endTime}\n`
    } else if (srtLines[i].trim()) {
      subtitleBlock += `${srtLines[i].trim()}\n`
    } else {
      vtt += subtitleBlock + '\n'
      subtitleBlock = ''
    }
  }

  // Append the last subtitle block if it exists
  if (subtitleBlock.trim()) {
    vtt += subtitleBlock + '\n'
  }

  return vtt
}

export async function loadAndConvertSrtToVtt(url: string) {
  const response = await fetch(url)
  const srtContent = await response.text()
  return srtToVtt(srtContent)
}

export const getSeconds = (timeString: string) => {
  if (timeString.split(' --> ').length < 2) return
  // 提取第一个时间部分
  const firstTime = timeString.split(' --> ')[0] // "00:01:29,560"
  // 提取时、分、秒部分，忽略毫秒
  const [hours, minutes, seconds] = firstTime.split(':')
  const pureSeconds = parseInt(seconds.split(',')[0], 10) // 去掉毫秒部分

  // 计算总秒数
  return parseInt(hours, 10) * 3600 + parseInt(minutes, 10) * 60 + pureSeconds
}

// 解析字幕的时间范围
const getSubtitleTimeRange = (lineContent: string): [number | undefined, number | undefined] => {
  const timeMatch = lineContent.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)

  if (timeMatch) {
    const startTimeInSeconds = getSecondsFromMatch(timeMatch, 1) // 获取开始时间
    const endTimeInSeconds = getSecondsFromMatch(timeMatch, 5) // 获取结束时间
    return [startTimeInSeconds, endTimeInSeconds]
  }

  return [undefined, undefined]
}

// 从时间匹配结果中提取秒数
const getSecondsFromMatch = (match: RegExpMatchArray, startIndex: number): number => {
  const hours = parseInt(match[startIndex], 10)
  const minutes = parseInt(match[startIndex + 1], 10)
  const seconds = parseInt(match[startIndex + 2], 10)
  const milliseconds = parseInt(match[startIndex + 3], 10)

  return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
}

export const findSubtitleLineByTime = (lines: string[], time: number): number | undefined => {
  for (let i = 0; i < lines.length; i++) {
    const lineContent = lines[i]
    // 提取字幕的开始时间和结束时间
    const [startTime, endTime] = getSubtitleTimeRange(lineContent)

    // 判断当前播放时间是否在该时间范围内
    if (startTime !== undefined && endTime !== undefined) {
      if (time >= startTime && time <= endTime) {
        return i
      }
    }
  }
}

export const getSubtitleContent = (path: string): Promise<string> => {
  if (!path) return Promise.resolve('')
  if (path.indexOf('processing') !== -1) return Promise.resolve('processing')
  return new Promise(resolve => {
    void fetch(path, {
      headers: {
        'Content-Type': "application/plain",
        Device: 'Web',
        Token: get_k_sso_token(),
      }
    })
      .then(response => response.blob())
      .then(blob => {
        const reader = new FileReader()
        reader.onload = function (event) {
          const content = event.target?.result as string
          if (content.indexOf('<!doctype html>') === 0) {
            resolve('')
          } else {
            resolve(content)
          }
        }
        reader.readAsText(blob)
      })
  })
}

export function matchSubtitleErrors(srtText: string, errors: M.IResourceSubtitleDetailError[], canReplace?: boolean): M.IResourceSubtitleDetailError[] {
  const lines = srtText.split('\n')
  const timeReg = /^\d{2}:\d{2}:\d{2},\d{3} -->/

  // 记录每个字幕块的起止范围（用行号）
  const blocks: { startLine: number, endLine: number }[] = []

  for (let i = 0; i < lines.length; i++) {
    if (timeReg.test(lines[i])) {
      const startLine = i - 1 >= 0 ? i - 1 : i // 起点是时间轴行上一行（如果有）
      let endLine = lines.length - 1

      for (let j = i + 1; j < lines.length; j++) {
        if (timeReg.test(lines[j])) {
          endLine = j - 1 // 到下一个时间轴行的上一行
          break
        }
      }

      blocks.push({ startLine, endLine })
    }
  }

  const result: M.IResourceSubtitleDetailError[] = []

  for (const error of errors) {
    const blockIndex = blocks.findIndex(
      (block: { startLine: number, endLine: number }) => error.line >= block.startLine && error.line <= block.endLine
    )
    if (blockIndex !== -1) {
      result.push({ ...error, subtitleIndex: blockIndex + 1, canReplace })
    }
  }

  return result
}

/**
 * 高亮关键词
 * @param text 原始文本
 * @param keywords 要高亮的关键词数组
 * @param className 高亮的 class 名
 * @returns 返回高亮后的 HTML 字符串
 */
export function highlightKeywords(
  text: string,
  keywords: string[],
): string {
  if (!keywords.length) return text;

  // 转义关键词中的特殊字符，避免正则误解
  const escapeRegExp = (str: string) =>
    str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  const pattern = new RegExp(
    `(${keywords.map(escapeRegExp).join('|')})`,
    'g'
  );

  return text.replace(pattern, `<span class="subtitle-highlight">$1</span>`);
}
