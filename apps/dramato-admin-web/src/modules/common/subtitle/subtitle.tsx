/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { formatSubtitles, getSeconds, findSubtitleLineByTime, getSubtitleContent } from './until'
import { ref, watch } from 'vue'

import { ElSelect, ElOption, ElInput } from 'element-plus'
import { useExpose } from '@skynet/ui/use/use-expose'
import { openDialog, showFailToast, SvgIcon } from '@skynet/ui'
import { trim } from 'lodash-es'
import { matchSubtitleErrors } from 'src/modules/common/subtitle/until'

type SubtitleOptions = {
  props: {
    // 字幕地址
    url?: string
    // 视频当前播放时间秒
    currentTime?: number
    // 这部剧可选角色
    characters?: M.ITerminology[]
    emotions?: string[]
    effects?: string[]
    emotions_cn?: string[]
    effects_cn?: string[]
    // 已标注角色
    annotations?: Api.RoleMark.IAnnotation[]
    // 是否展示已标注角色
    showCharacters?: boolean
    // 是否可编辑字幕
    isEditable?: boolean
    // 是否可以标记角色
    rolePick?: boolean
    // 是否同步滚动
    isAsyncScroll?: boolean
    // 是否允许选中多个
    isBatchSelect?: boolean
    // 是否直接展示输入框
    showContentText?: boolean
    errors?: M.IResourceSubtitleDetailError[]
  }
  emits: {
    // 调整视频时间
    videoProgress: (s: number) => void
    // 角色标注
    annotationChange: (annotations: Api.RoleMark.IAnnotation[]) => void
    changeRoleOrder: (setNextLine?: boolean) => void
    cancelBatchSelect: () => void
  }
}

export const Subtitle = createComponent<SubtitleOptions>({
  props: {
    url: '',
    currentTime: 0,
    characters: [],
    emotions: [],
    effects: [],
    emotions_cn: [],
    effects_cn: [],
    annotations: [],
    showCharacters: true,
    isEditable: true,
    rolePick: true,
    isAsyncScroll: true,
    isBatchSelect: false,
    showContentText: true,
    errors: [],
  },
  emits: {
    videoProgress: (s: number) => {},
    annotationChange: (annotations: Api.RoleMark.IAnnotation[]) => {},
    changeRoleOrder: (setNextLine?: boolean) => {},
    cancelBatchSelect: () => {},
  },
}, (props, { emit }) => {
  const loading = ref(false)
  const code = ref('')
  const highlightLineNum = ref(-1)
  const list = ref<Api.RoleMark.ISubtitleLine[]>([])
  const srtTimestampRegex = /^([0-9]{2}):([0-5][0-9]):([0-5][0-9]),([0-9]{3}) --> ([0-9]{2}):([0-5][0-9]):([0-5][0-9]),([0-9]{3})$/
  const tempTime = ref('')
  const subtitleChange = ref(false)
  const currentLine = ref<Api.RoleMark.ISubtitleLine>()
  const selectLines = ref<Api.RoleMark.ISubtitleLine[]>([])

  watch(() => props.isBatchSelect, () => {
    currentLine.value = undefined
    selectLines.value = []
  })

  watch(() => [props.url, props.errors], async newVal => {
    loading.value = true
    subtitleChange.value = false
    currentLine.value = undefined
    code.value = await getSubtitleContent(props.url)
    let errTips: M.IResourceSubtitleDetailError[] = []
    if (props.errors.length > 0) {
      errTips = matchSubtitleErrors(code.value, props.errors)
    }
    list.value = formatSubtitles(code.value).map((item, index) => {
      const selectedCharacters = props.annotations.filter(o => o.order === index + 1)
      const err = errTips.find(tip => tip.subtitleIndex === index + 1)
      return {
        ...item,
        selectedCharacters,
        order: index + 1,
        // 默认第一个
        effect: selectedCharacters[0]?.effect || '',
        emotion: selectedCharacters[0]?.emotion || '',
        err,
      }
    })
    emit('changeRoleOrder')
    loading.value = false
  }, {
    immediate: true,
  })

  const highlightSubtitleLine = (lineNumber: number) => {
    highlightLineNum.value = lineNumber
  }

  watch(() => props.currentTime, (newTime: number) => {
    const lineNumber = findSubtitleLineByTime(list.value.map(row => row.time), newTime)
    const highlightIndex = lineNumber !== undefined ? lineNumber : -1
    highlightSubtitleLine(highlightIndex) // 调用合并后的高亮函数
    if (props.isAsyncScroll) Array.from(document.querySelectorAll('.subtitle-line'))[highlightIndex]?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' })
  })

  const formatTimeLine = (str: string) => {
    const temp = trim(str)
    const arr = temp.split('-->')
    if (arr.length !== 2) {
      return
    }
    const result = `${trim(arr[0])} --> ${trim(arr[1])}`
    if (!srtTimestampRegex.test(result)) {
      return
    }
    return result
  }

  const renderTimeLine = (str: string, index: number) => {
    const curLine = list.value[index]
    tempTime.value = str
    return (
      <x-subtitle-timeline
        class={mc('border-collapse border cursor-pointer border-white bg-gray-100 py-1 text-sm', index === highlightLineNum.value && 'bg-blue-300')}
      >
        <div class="flex items-center space-x-1 px-1 text-[var(--text-2)]" onClick={() => {
          if (!props.isEditable) return
          curLine.timeEditing = true
          subtitleChange.value = true
        }}>
          <SvgIcon name="ic_play_phone" class="size-5" onClick={() => {
            try {
              const r = getSeconds(str)
              if (r !== undefined) {
                emit('videoProgress', r)
              }
            } catch (err) {
              console.error('时间格式错误')
            }
          }} />
          <div>
            {
              curLine.timeEditing ? (
                <ElInput
                  modelValue={curLine.time}
                  type="textarea"
                  rows={2}
                  ref={input => input && (input as any).focus()}
                  onBlur={() => {
                    const _str = formatTimeLine(curLine.time)
                    if (!_str) {
                      curLine.time = curLine.oldTime || ''
                      showFailToast('时间格式不正确')
                      return
                    }
                    curLine.time = _str
                    curLine.oldTime = _str
                    curLine.timeEditing = false
                  }}
                  onKeydown={(evt: Event | KeyboardEvent) => {
                    if ('key' in evt && evt.key === 'Enter' && 'shiftKey' in evt && !evt.shiftKey) {
                      evt.preventDefault()
                      const _str = formatTimeLine(curLine.time)
                      if (!_str) {
                        curLine.time = curLine.oldTime || ''
                        showFailToast('时间格式不正确')
                        return
                      }
                      curLine.time = _str
                      curLine.oldTime = _str
                      curLine.timeEditing = false
                    }
                  }}
                  onUpdate:modelValue={e => {
                    curLine.time = e
                  }}
                  style="width: 100%"
                />
              ) : <div class="line-clamp-1"><span class="font-bold text-[var(--text-1)]">{index + 1}</span> &nbsp; {str}</div>
            }
          </div>
        </div>
      </x-subtitle-timeline>
    )
  }

  const renderSubtitleContent = (str: string, index: number, err?: M.IResourceSubtitleDetailError) => {
    const curLine = list.value[index]
    return props.showContentText ? (
      <div
        class="border-collapse border border-white bg-gray-100 px-1 py-2 text-sm">
        <textarea
          class="textarea-bordered textarea w-full"
          v-model={curLine.content}
          rows={2}
        />
        {err ? (
          <div class="text-red-500 flex items-center space-x-1">
            <SvgIcon class="size-4" name="ic_exclamation" />
            <span title={err.err_content} class="line-clamp-1 break-words flex-1">{err.err_content}</span>
          </div>
        ) : null}
      </div>
    ) : (
      <div class="border-collapse border border-white bg-gray-100 px-1 py-2 text-sm">
        {curLine.contentEditing ? (
          <ElInput
            v-model={curLine.content}
            type="textarea"
            rows={1}
            ref={input => input && (input as any).focus()}
            onBlur={() => {
              curLine.contentEditing = false
            }}
            onKeydown={(evt: Event | KeyboardEvent) => {
              if ('key' in evt && evt.key === 'Enter' && 'shiftKey' in evt && !evt.shiftKey) {
                evt.preventDefault()
                curLine.contentEditing = false
              }
            }}
          />
        ) : (
          <pre
            class="whitespace-pre-wrap break-words"
            onClick={() => {
              if (!props.isEditable) return
              subtitleChange.value = true
              curLine.contentEditing = true
            }}
          >
            {curLine.content || '请输入字幕'}
          </pre>
        )}
      </div>
    )
  }

  const renderRolePicker = (row: Api.RoleMark.ISubtitleLine, index: number) => {
    return (
      <div
        class="flex flex-col space-y-3 h-full border-collapse relative border border-white bg-gray-100"
      >
        <ElSelect
          modelValue={row.selectedCharacters?.filter(o => o.character_id)?.map(item => item.character_id)}
          {...(props.rolePick ? { filterable: true } : {})}
          multiple
          placeholder="请选择"
          collapse-tags
          class="!w-[180px]"
          disabled={!props.rolePick}
          onUpdate:modelValue={(e: number[]) => {
            const arr = props.characters.filter(o => e.includes(o.character_id!)).map(item => {
              return {
                ...item,
                effect: row.effect,
                emotion: row.emotion,
              }
            })

            // if (arr.length === 0 && (list.value[index].emotion || list.value[index].effect)) {
            //   arr = [
            //     {
            //       name: '',
            //       character_id: 0,
            //       official_name: '',
            //       emotion: list.value[index].emotion,
            //       effect: list.value[index].effect
            //     }
            //   ]
            // }

            list.value.splice(index, 1, {
              ...list.value[index],
              selectedCharacters: arr as Api.RoleMark.IAnnotation[],
              order: index + 1,
              emotion: arr[0]?.emotion || '',
              effect: arr[0]?.effect || '',
            })
            emit('changeRoleOrder')
          }}
        >
          {
            props.characters.map(character => {
              return (
                <ElOption
                  key={character.character_id}
                  label={character.name || character.term}
                  value={character.character_id!} />
              )
            })
          }
        </ElSelect>
        <div class="bg-black-100 w-[50px] h-[32px] absolute -top-[12px] left-0" onClick={() => {
          if (props.rolePick) currentLine.value = { ...row }
        }} />

        <div class="flex justify-between items-center">
          <ElSelect
            modelValue={row.emotion}
            placeholder="情感"
            class="!w-[85px]"
            clearable
            disabled={!props.rolePick || !row.selectedCharacters || row.selectedCharacters.length === 0}
            onUpdate:modelValue={(e: string) => {
              const selectedCharacters = row.selectedCharacters?.map(item => {
                return {
                  ...item,
                  emotion: e,
                }
              })
              list.value.splice(index, 1, {
                ...list.value[index],
                selectedCharacters,
                order: index + 1,
                emotion: e,
              })
            }}
          >
            {
              props.emotions.map((e, emotionIndex) => {
                return (
                  <ElOption
                    key={e}
                    label={props.emotions_cn[emotionIndex]}
                    value={e} />
                )
              })
            }
          </ElSelect>

          <ElSelect
            modelValue={row.effect}
            placeholder="特效"
            class="!w-[85px]"
            clearable
            disabled={!props.rolePick || !row.selectedCharacters || row.selectedCharacters.length === 0}
            onUpdate:modelValue={(e: string) => {
              const selectedCharacters = row.selectedCharacters?.map(item => {
                return {
                  ...item,
                  effect: e,
                }
              })
              list.value.splice(index, 1, {
                ...list.value[index],
                selectedCharacters,
                order: index + 1,
                effect: e,
              })
            }}
          >
            {
              props.effects.map((e, effectIndex) => {
                return (
                  <ElOption
                    key={e}
                    label={props.effects_cn[effectIndex]}
                    value={e} />
                )
              })
            }
          </ElSelect>
        </div>

      </div>
    )
  }

  const addTop = (index: number) => {
    subtitleChange.value = true
    const lastItem = list.value[index]
    list.value.splice(index, 0, {
      time: lastItem.time,
      oldTime: lastItem.time,
      content: '',
      name: '',
      order: Date.now(),
    })
  }
  const addBottom = () => {
    subtitleChange.value = true
    const lastItem = list.value[list.value.length - 1]
    list.value.push({
      time: lastItem.time,
      oldTime: lastItem.time,
      content: '',
      name: '',
      order: Date.now(),
    })
  }

  const getContent = (): Api.RoleMark.ISubtitleLine[] => list.value

  const setNextLine = () => {
    if (!currentLine.value || !currentLine.value.order) return
    const lineIndex = list.value.findIndex(item => item.order === currentLine.value?.order)
    if (lineIndex < list.value.length - 1) {
      currentLine.value = {
        ...list.value[lineIndex + 1],
      }
      if (!props.isAsyncScroll) Array.from(document.querySelectorAll('.subtitle-line'))[lineIndex + 1]?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' })
    }
  }

  const setRole = (row: Api.RoleMark.ICharacter): void => {
    if (props.isBatchSelect) {
      // 批量选角色
      if (selectLines.value.length === 0) return
      for (let i = 0; i < selectLines.value.length; i++) {
        const line = selectLines.value[i]
        const lineIndex = list.value.findIndex(item => item.order === line?.order)
        if (lineIndex < 0 || !line) return
        const obj = {
          ...list.value[lineIndex],
          selectedCharacters: [{
            ...row,
            effect: list.value[lineIndex].effect || '',
            emotion: list.value[lineIndex].emotion || '',
          }] as Api.RoleMark.IAnnotation[],
          effect: list.value[lineIndex].effect || '',
          emotion: list.value[lineIndex].emotion || '',
        }
        list.value.splice(lineIndex, 1, obj)
      }
      selectLines.value = []
    } else {
      // 单个选中
      const lineIndex = list.value.findIndex(item => item.order === currentLine.value?.order)
      if (lineIndex < 0 || !currentLine.value) return
      list.value[lineIndex].selectedCharacters = [{
        ...row,
        effect: list.value[lineIndex].effect || '',
        emotion: list.value[lineIndex].emotion || '',
      }] as Api.RoleMark.IAnnotation[]
      currentLine.value = {
        ...list.value[lineIndex],
        effect: list.value[lineIndex].effect || '',
        emotion: list.value[lineIndex].emotion || '',
      }
      list.value.splice(lineIndex, 1, currentLine.value)
      setNextLine()
    }
  }

  // 父组建通过getContent获取字幕和标注内容
  useExpose({
    getContent,
    getContentChange: () => subtitleChange.value,
    setRole,
    setNextLine,
  })

  return () => (
    <x-subtitle
      v-loading={loading.value}
      class="block">
      {
        list.value.map((line, index) => {
          return (
            <div>
              <x-subtitle class="block relative">
                {
                  props.isBatchSelect ? (
                    <div
                      key={line.order}
                      class={mc('absolute top-4 left-0 bottom-0 right-0 z-up cursor-pointer border rounded-lg border-line-1',
                        selectLines.value.some(l => l.order === line.order)
                          ? 'border-blue-500 bg-transparent'
                          : 'border-white bg-black opacity-20')}
                      onClick={() => {
                        const lineIndex = selectLines.value.findIndex(r => r.order === line.order)
                        if (lineIndex > -1) {
                          selectLines.value.splice(lineIndex, 1)
                        } else {
                          selectLines.value.push({
                            ...line,
                          })
                        }
                      }} />
                  ) : null
                }
                <div class={mc('h-4 w-full text-center opacity-0', props.isEditable && !props.isBatchSelect ? 'hover:opacity-100' : '')}>
                  <SvgIcon name="ic_plus_circle" class="-mt-2 size-4 cursor-pointer" onClick={() => {
                    if (props.isEditable && !props.isBatchSelect) addTop(index)
                  }} />
                </div>
                <x-subtitle-item class={mc('subtitle-line group hover relative border rounded-lg border-line-1 flex flex-row px-2',
                  (currentLine.value?.order && currentLine.value?.order === line.order)
                    ? 'border-sky-500'
                    : 'border-white')}>
                  <div class="flex flex-1 flex-col space-y-2">
                    {
                      renderTimeLine(line.time, index)
                    }
                    {
                      renderSubtitleContent(line.content, index, line.err)
                    }
                  </div>
                  <div>
                    {
                      props.showCharacters ? renderRolePicker(line, index) : null
                    }
                  </div>
                  <div
                    class={mc('absolute right-3 -top-3 hidden size-4  items-center justify-center rounded-lg bg-white', props.isEditable ? 'group-hover:flex' : '')}
                  >
                    <SvgIcon name="ic_del" class="size-4 cursor-pointer" onClick={() => {
                      const hideDeleteDialog = openDialog({
                        title: '删除字幕行',
                        mainClass: 'pb-0 px-5',
                        body: (
                          <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                            <x-delete-episode-body>确认删除【{line.order}】行吗？</x-delete-episode-body>
                            <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                              <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                              <button class="btn btn-primary btn-sm" onClick={() => {
                                subtitleChange.value = true
                                currentLine.value = undefined
                                list.value.splice(index, 1)
                                hideDeleteDialog()
                              }}
                              >确定
                              </button>
                            </x-delete-episode-footer>
                          </x-delete-episode-confirm-dialog>
                        ),
                      })
                    }} />
                  </div>
                </x-subtitle-item>
              </x-subtitle>
            </div>
          )
        })
      }
      <div class={mc('cursor-pointe h-4 w-full text-center opacity-0', props.isEditable && !props.isBatchSelect ? 'hover:opacity-100' : '')} onClick={() => {
        if (props.isEditable && !props.isBatchSelect) addBottom()
      }}>
        <SvgIcon name="ic_plus_circle" class="-mt-2 size-4" />
      </div>
    </x-subtitle>
  )
})

export default Subtitle
