import lottie from 'lottie-web'
import { createComponent } from '@skynet/shared'
import { onMounted, ref } from 'vue'

type options = {
  props: {
    src: Object | null
    path?: string
    loop?: boolean
    autoplay?: boolean
  }
}

export const LottieWeb = createComponent<options>({
  props: {
    src: null,
    path: '',
    loop: true,
    autoplay: true,
  },
}, props => {
  const lottieBox = ref(null)
  onMounted(() => {
    if (lottieBox.value) {
      lottie.loadAnimation({
        container: lottieBox.value,
        renderer: 'svg', // 渲染方式:svg：支持交互、不会失帧、canvas、html：支持3D，支持交互
        loop: props.loop, // 循环播放，默认：true
        autoplay: props.autoplay, // 自动播放 ，默认true
        path: props.path, // 网络路径
        animationData: props.src && Object.keys(props.src).length == 0 ? '' : props.src, // 本地路径，优先级更高
      })
    }
  })

  return () => (<div ref={lottieBox} />)
})

export default LottieWeb
