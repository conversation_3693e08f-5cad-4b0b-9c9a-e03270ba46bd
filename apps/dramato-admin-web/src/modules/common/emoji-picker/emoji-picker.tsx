/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, emit } from '@skynet/shared'
import data from 'emoji-mart-vue-fast/data/all.json'
import 'emoji-mart-vue-fast/css/emoji-mart.css'
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { Picker, EmojiIndex } from 'emoji-mart-vue-fast/src'

type EmojiPickerOptions = {
  props: {
    platform?: 'apple' | 'google' | 'twitter' | 'facebook'
  }
  emits: {
    insertEmoji: (e: any) => void
  }
}
export const EmojiPicker = createComponent<EmojiPickerOptions>({
  props: {
    platform: 'apple'
  },
  emits: {
    insertEmoji: () => {},
  },
}, (props, { emit }) => {
  const emojiIndex = new EmojiIndex(data)
  return () => (
    <Picker data={emojiIndex} set={props.platform} onSelect={(e: any) => {
      emit('insertEmoji', e)
    }}
    />
  )
})

export default EmojiPicker
