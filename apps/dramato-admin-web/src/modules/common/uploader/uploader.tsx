/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, SlotFn } from '@skynet/shared'
import { CreateTableOld, showFailToast } from '@skynet/ui'
import { useDropZone } from '@vueuse/core'
import axios from 'axios'
import { httpClient } from 'src/lib/http-client.ts'
import { onMounted, onUnmounted, ref } from 'vue'
import { useUploader } from './use-uploader.ts'

export type UploadImage = {
  status?: 'success' | 'fail' | 'pending' | 'wait'
  thumbnail?: string
  url?: string
  file?: File
  temp_path?: string
  show_cover?: string
  before_upload_resp?: void | undefined | null | boolean | File | Blob | string
}

export type UploadedFile = {
  status: string
  url: string
  file: File
  name: string
}

type UploaderOptions = {
  props: {
    maxsize?: number
    accept?: string
    multiple?: boolean
    isImage?: boolean
    uploadUrl?: string
    ossKeyType?: string
    // 尺寸只有在图片的时候才校验
    dimension?: [number, number]
    beforeUpload?: (obj: { files: File[] }) => Promise<void | undefined | null | boolean | File | Blob | string> | void
    beforeRemove?: () => void
    checkFile?: (file: File) => boolean
    showFileList?: boolean
    disabled?: boolean
  }
  emits: {
    uploadSuccess: (uploadFile: UploadImage) => void
    uploadFailed: (uploadFile: UploadImage) => void
  }
  slots: {
    default?: SlotFn
  }
}

export const Uploader = createComponent<UploaderOptions>({
  props: {
    maxsize: 2 * 1024 * 1024 * 1024,
    accept: 'jpg,png,jpeg',
    multiple: true,
    ossKeyType: '',
    uploadUrl: '/episode_series/cover_upload',
    beforeUpload: () => Promise.resolve(),
    beforeRemove: () => { },
    isImage: true,
    dimension: [0, 0],
    checkFile: () => true,
    showFileList: true,
    disabled: false,
  },
  emits: {
    uploadSuccess: () => { },
    uploadFailed: () => { },
  },
}, (props, { emit, slots }) => {
  const dropZoneRef = ref<HTMLDivElement>()
  const input = ref<HTMLInputElement>()
  const slotInput = ref<HTMLInputElement>()
  const { ossData, ossDataLoading, ossDataBjLoading, getOssData, getResourceOssData, resetOssData, ossDataBj } = useUploader()

  const errors = ref<string[]>([])
  const fileList = ref<Array<{ file: File, status: '上传成功' | '上传失败' | '上传中' | '等待中' }>>([])

  const Table = CreateTableOld<{ file: File, status: '上传成功' | '上传失败' | '上传中' | '等待中' }>()

  const onDrop = (files: File[] | null) => {
    void handleFileChange(files ?? [])
  }

  const onFileChange = async ($event: Event) => {
    const target = $event.target as HTMLInputElement
    await handleFileChange(Array.from(target.files ?? []))
  }

  function formatFileSize(size: number) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
    let index = 0
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024
      index++
    }
    return `${size.toFixed(2)} ${units[index]}`
  }

  const processBatches = (tasks: Array<any>, batchSize: number) => {
    const batches = []
    for (let i = 0; i < tasks.length; i += batchSize) {
      batches.push(tasks.slice(i, i + batchSize))
    }

    return batches.reduce((promiseChain: any, batch: any) => {
      return promiseChain.then(() => {
        return Promise.all(batch.map((task: any) => task()))
      })
    }, Promise.resolve())
  }

  const checkDimension = (files: any[], dimension: number): Promise<boolean> => {
    const ps = files.map(file => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = function (e) {
          const img = new Image()
          img.onload = function () {
            // 上下浮动10%
            if (img.width / img.height < dimension * (1 - 0.1) || img.width / img.height > dimension * (1 + 0.1)) {
              reject(false)
            }
            resolve(true)
          }
          img.src = (e.target as any).result
        }
        reader.readAsDataURL(file)
      })
    })
    return Promise.allSettled(ps).then(reses => {
      if (reses.every(res => res.status !== 'rejected')) return true
      return false
    })
  }

  const handleFileChange = async (files: File[]) => {
    if (files.length === 0) return
    let beforeUploadResp: void | undefined | null | boolean | File | Blob | string
    errors.value = []
    try {
      const accept = props.accept.trim()
      const notValidType = files.some(file => {
        const ext = file.name.split('.').pop()?.toLowerCase() || ''
        return !(accept.split(',').map(i => i.toLocaleLowerCase()).includes(ext.toLocaleLowerCase()))
      })

      const notValidSize = files.some(file => {
        return file.size > props.maxsize
      })

      if (notValidType) {
        (slotInput.value as HTMLInputElement).value = ''
        throw new Error(`只支持${accept}类型文件上传`)
      }

      if (notValidSize) {
        (slotInput.value as HTMLInputElement).value = ''
        throw new Error(`上传文件最大${formatFileSize(props.maxsize)}`)
      }

      if (props.dimension[0] !== 0 && props.dimension[1] !== 0) {
        const validDimension = await checkDimension(files, props.dimension[0] / props.dimension[1])
        if (!validDimension) {
          (slotInput.value as HTMLInputElement).value = ''
          throw new Error(`图片分辨率只支持${props.dimension[0]}:${props.dimension[1]}`)
        }
      }

      if (props.beforeUpload && typeof props.beforeUpload === 'function') {
        beforeUploadResp = await props.beforeUpload({
          files: files,
        })
        if (beforeUploadResp === false) return
      }

      const asyncList = []
      for (const file of files) {
        const url = URL.createObjectURL(file)
        let temp_path = ''
        if (props.isImage) {
          const rs = await httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>(
            props.uploadUrl, { file }, { headers: { 'Content-Type': 'multipart/form-data' }, timeout: 60 * 1000 })
          temp_path = rs?.data?.cover || rs?.data?.image || rs?.data?.path || ''
          emit('uploadSuccess', { status: 'success', url, file, temp_path, show_cover: rs?.data?.show_cover || '' })
          if (!props.multiple) return
          continue
        }
        if (props.multiple) {
          fileList.value.push({ file, status: '等待中' })
        } else {
          fileList.value = [{ file, status: '等待中' }]
        }
        const data = props.ossKeyType === 'resource' ? ossDataBj.value : ossData.value
        const f = new File([file], file.name, {
          type: file.name.endsWith('.srt') || file.name.endsWith('.srtx') ? 'application/x-subrip' : file.name.endsWith('.psd') ? 'image/vnd.adobe.photoshop' : file.type,
        })
        const formData = new FormData()
        formData.append('key', data?.dir + '/' + f.name)
        formData.append('policy', data?.policy || '')
        formData.append('OSSAccessKeyId', data?.accessid || '')
        formData.append('success_action_status', '200')
        formData.append('signature', data?.signature || '')
        formData.append('file', f)
        let failCount = 0

        const onSuccess = () => {
          temp_path = data?.dir + '/' + f.name
          fileList.value.forEach(item => {
            if (props.checkFile && typeof props.checkFile === 'function' && !(props.checkFile(file))) {
              item.status = '上传失败'
            } else {
              item.file.name === f.name && item.file.size === f.size && (item.status = '上传成功');
              (slotInput.value as HTMLInputElement).value = ''
              emit('uploadSuccess', { status: 'success', url, file: f, temp_path, before_upload_resp: beforeUploadResp })
            }
          })
        }

        const uploadRequest = () => {
          return new Promise((resolve, reject) => {
            fileList.value.forEach(item => {
              item.file.name === f.name && item.file.size === f.size && (item.status = '上传中')
            })

            void axios.post(data?.host || '', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }).then(() => {
              onSuccess()
              resolve(null)
            }).catch((error: any) => {
              if (error.response.status === 403) {
                if (props.ossKeyType === 'resource') {
                  void getResourceOssData().then(uploadRequest).then(resolve)
                } else {
                  void getOssData().then(uploadRequest).then(resolve)
                }
                return
              }
              if (failCount === 3) {
                showFailToast(error.response?.data?.message || '服务忙碌，稍后再试')
                reject()
                return
              }
              failCount = failCount + 1
              void uploadRequest().then(resolve)
            })
          })
        }

        asyncList.push(uploadRequest)
      }

      processBatches(asyncList, 5).then(() => {
        console.log('All upload tasks completed.')
      })
      // void uploadRequest()
    } catch (e: unknown) {
      const error = e as any
      console.log(error, '>>> ')
      if (props.isImage && !props.multiple) {
        emit('uploadFailed', {
          status: 'fail',
        })
      }
      showFailToast(error.message || error.response?.data?.message || '服务忙碌，稍后再试')
      if (error.response?.data?.code === 422) {
        const validationError = ((error.response.data as { errors: unknown }).errors ?? []) as { field: string, message: string }[]
        errors.value.push(...validationError.map(e => e.message))
      }
      if (error.response?.data?.code === 413) {
        errors.value.push('文件太大')
      }
      if (error.response?.data?.message) {
        errors.value.push(error.response.data.message)
      }

      throw new Error(error.message || error.response?.data?.message || '服务忙碌，稍后再试')
    }
  }

  const { isOverDropZone } = useDropZone(dropZoneRef, onDrop)

  const open = () => {
    input.value?.click()
  }

  onMounted(() => {
    if (props.ossKeyType === 'resource') {
      if (ossDataBj.value || ossDataBjLoading.value) {
        return
      }
      void getResourceOssData()
    } else {
      if (ossData.value || ossDataLoading.value) {
        return
      }
      void getOssData()
    }
  })
  // fix: oss切换中美bug
  onUnmounted(() => {
    resetOssData()
  })

  return () => (
    <x-uploader ref={dropZoneRef} class="block col-span-full gap-y-[25px]" onClick={() => { !props.disabled && slotInput.value?.click() }}>
      {
        !!slots.default
          ? (
              <>
                <input ref={slotInput} type="file" accept={props.accept} multiple={props.multiple} name="file" class="sr-only" onChange={e => onFileChange(e)} />
                {slots.default()}
              </>
            )
          : (
              <>
                <div class={mc('flex justify-center rounded-lg border border-dashed border-gray-900/25 ', isOverDropZone.value ? 'bg-gray-300 border-green/25 border-2' : 'bg-white')}>
                  <label for="file" class="text-primary-400 focus-within:ring-primary-700 hover:text-primary-300 relative box-border w-full cursor-pointer rounded-md px-6 py-10 text-center font-semibold focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2" onClick={() => open()}>
                    <input ref={input} type="file" accept={props.accept} multiple={props.multiple} name="file" class="sr-only" onChange={e => onFileChange(e)} />
                    <span>上传图片</span>
                  </label>
                </div>
                {errors.value.length > 0 && (
                  <p class="w-full py-2 text-center text-xs leading-5 text-red-600">
                    { errors.value.map(error => (<span key={error}> {error} </span>)) }
                  </p>
                )}
              </>
            )
      }
      {
        props.showFileList && fileList.value.length > 0 && (
          <Table list={fileList.value} columns={[
            ['序号', (row, index) => Number((row.file?.name || '').match(/\d+/g)?.[0] || index + 1)],
            ['文件名称', row => row.file.name],
            ['进度', row => row?.status, { class: 'w-[140px]' }],
          ]}
          />
        )
      }

    </x-uploader>
  )
})
