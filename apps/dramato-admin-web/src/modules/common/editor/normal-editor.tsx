/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, onMounted, watch, onBeforeUnmount, PropType, shallowRef, toRaw } from 'vue'
import { useLoadMonaco } from 'src/lib/load-monaco-editor'
import * as Monaco from 'monaco-types'
import { useExpose } from '@skynet/ui/use/use-expose'

export default defineComponent({
  props: {
    customClass: {
      type: String as PropType<string>,
      required: false,
    },
    code: {
      type: String as PropType<string>,
      required: false,
      default: '',
    },
    options: {
      type: Object as PropType<object>,
      required: false,
    },
    onChange: {
      type: Function as PropType<(value: string, event: Monaco.editor.IModelContentChangedEvent) => void>,
      required: false,
    },
    insertText: {
      type: String as PropType<string>,
      required: false,
    },
  },
  setup(props) {
    const { loadMonacoEditor, monacoLoading } = useLoadMonaco()

    // must be shallowRef, if not, editor.getValue() won't work
    const editorRef = shallowRef()

    const containerRef = ref()

    let _subscription: Monaco.IDisposable | undefined
    let __prevent_trigger_change_event = false

    onMounted(async () => {
      await loadMonacoEditor()
      const editor = editorRef.value = window.monaco.editor.create(containerRef.value, {
        value: props.code,
        ...props.options,
      })

      window.monaco.editor.defineTheme('customTheme', {
        base: 'vs',
        inherit: true,
        rules: [
          { token: 'error', foreground: 'FF0000' }, // 红色
        ],
        colors: {},
      })

      _subscription = editor.onDidChangeModelContent((event: Monaco.editor.IModelContentChangedEvent) => {
        if (!__prevent_trigger_change_event) {
          props.onChange && props.onChange(toRaw(editor.getValue()), event)
        }
      })
    })

    onBeforeUnmount(() => {
      if (_subscription)
        _subscription.dispose()
    })

    watch(() => props.code, v => {
      if (!editorRef.value) return
      const editor = editorRef.value
      const model = editor.getModel()
      if (v !== toRaw(model.getValue())) {
        editor.pushUndoStop()
        __prevent_trigger_change_event = true
        // pushEditOperations says it expects a cursorComputer, but doesn't seem to need one.
        model.pushEditOperations(
          [],
          [
            {
              range: model.getFullModelRange(),
              text: v,
            },
          ],
        )
        editor.pushUndoStop()
        __prevent_trigger_change_event = false
      }
      // if (v !== editorRef.value.getValue()) {
      //   editorRef.value.setValue(v)
      // }
    })

    watch(() => props.insertText, v => {
      if (v) {
        insertTextFun(v)
      }
    })

    const getEditor = () => {
      return editorRef.value
    }

    const insertTextFun = (text: string) => {
      if(!editorRef.value) return
      editorRef.value.focus()
      const position = editorRef.value.getPosition()
      if (position.column === 1 && position.lineNumber === 1) return
      editorRef.value.executeEdits('', [
        {
          range: new window.monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
          text: text,
        },
      ])
      editorRef.value.setPosition({
        lineNumber: position.lineNumber,
        column: position.column + text.length
      })
      editorRef.value.focus()
    }

    useExpose({
      getEditor,
      insertTextFun,
    })

    return () => {
      return (
        monacoLoading.value
          ? (
              <div class="flex h-[55vh] w-full items-center justify-center">
                <span>加载中……</span>
              </div>
            )
          : <div ref={containerRef} class={props.customClass} />
      )
    }
  },
})
