import { createComponent, fn } from '@skynet/shared'
import { Checkbox } from '@skynet/ui'
import { ref } from 'vue'
type WhiteListOptions = {
  props: {
    list: string[]
  }
  emits: {
    update: (value: string) => void
  }
}
export const WhiteList = createComponent<WhiteListOptions>({
  props: {
    list: [],
  },
  emits: {
    update: fn,
  },
}, (props, { emit }) => {
  const userWhiteListEnabled = ref<boolean>(props.list.length > 0)
  return () => (
    <div>
      <Checkbox v-model={userWhiteListEnabled.value} label="下发给白名单用户" />
      <textarea value={props.list?.toString() ?? ''}
        onInput={e => emit('update', (e.target as HTMLTextAreaElement).value)}
        placeholder="请输入 UID，用逗号或者换行分隔"
        class="textarea textarea-bordered textarea-sm w-full min-h-[8em]"
        disabled={!userWhiteListEnabled.value}
      />
    </div>
  )
})
