import { createComponent, mc } from '@skynet/shared'
import { onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, openDialog, Pager, transformNumber, showAlert } from '@skynet/ui'
import { cloneDeep, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiGetAppOptions } from '../application/application.api'
import { RechargeLevelForm } from './recharge-level-form'
import { UnMemberForm } from './un-member-form'
import { useRechargeLevelPage } from './use-recharge-level'
import { RouterLink } from 'vue-router'
import Cookies from 'js-cookie'
import qs from 'qs'
import { showFailToast } from '@skynet/ui'
import { apiGetProductUseDate } from './recharge-level-api'

const dialogMainClass = 'h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'

type TopUpLevelPageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.RechargeLevel[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.RechargeLevel) => void
    remove: (id: M.RechargeLevel) => void
  }
}
export const TopUpLevelPage = createComponent<TopUpLevelPageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.RechargeLevel) => {},
    remove: (item: M.RechargeLevel) => {},
  },
}, (props, { emit }) => {
  const {
    initTopUpLevel,
    searchForm,
    list,
    originalList,
    onSearch,
    onReset,
    applicationList,
    currentLevel,
    closeEditRechargeLevelModal,
    onEditRechargeLevelPriority,
    closeEditUnMemberModal,
    form,
    defaultData,
    loading,
    total,
    page,
    onPageChange,
    onPageSizeChange,
  } = useRechargeLevelPage()

  const Form = CreateForm<M.RechargeLevelSearchParams>()
  const Table = CreateTableOld<M.RechargeLevel>()
  const currentPriority = ref(1)

  const columns: TableColumnOld<M.RechargeLevel>[] = [
    [
      '',
      row => {
        const id = row.id as number
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={props.checkedItem.map(i => i.id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.id).includes(id)) {
                  const found = originalList.value.find(i => i.id === id)
                  if (!found) return
                  emit('add', found)
                }
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['档位说明（内部用）', 'title_admin', { class: 'w-[240px]' }],
    ['序号', row => (
      <label class={mc('h-8', row.status === 1 ? '' : 'hidden')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          onInput={(e: Event) => {
            currentPriority.value = Number((e.target as HTMLInputElement).value) || 0
            void onEditRechargeLevelPriority({
              id: row.id || 0,
              priority: currentPriority.value,
            })
          }}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ),
    { class: mc('w-[100px]', props.hasPriority ? '' : 'hidden') },
    ],
    ['类型', row => <span>{row.first_recharge ? (row.first_recharge === 1 ? '首充' : '再充值') : '常规'}</span>, { class: 'w-[120px]' }],
    ['币种', 'currency', { class: 'w-[100px]' }],
    ['价格', 'discount_price', { class: 'w-[100px]' }],
    ['Coins', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    ['Bonus', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    ['赠送比%', row => row?.delivery_details?.bonus_rate || 0, { class: 'w-[100px]' }],
    ['商品ID', 'sku_id', { class: 'w-[230px]' }],
    ['上架商城', 'store', { class: 'w-[120px]' }],
    ['应用名称', row => applicationList.value.find(app => +(app.id || '') === +(row?.app_id || ''))?.app_name, { class: 'w-[200px]' }],
    ['状态',
      row => (
        <div class="flex items-center space-x-1">
          {row.status === 1
            ? (
                <>
                  <div class="badge badge-xs bg-green-600" />
                  <div>展示</div>
                </>
              )
            : (
                <>
                  <div class="badge badge-xs bg-gray-300" />
                  <div>不展示</div>
                </>
              )}
        </div>
      ),
      { class: 'w-[100px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm') : '-',
      { class: 'w-[150px]' },
    ],
    ['更新人', 'update_user', { class: 'w-[200px]' }],
    ['展示平台', 'show_platform', { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          <Button class={mc('btn btn-link btn-sm', row.membership_type === 'consumable' ? 'hidden' : '')} onClick={() => {
            currentLevel.value = cloneDeep(row)
            closeEditRechargeLevelModal.value = openDialog({
              title: '编辑充值档位',
              body: <RechargeLevelForm />,
              mainClass: dialogMainClass,
            })
          }}
          >编辑
          </Button>
          <Button class={mc('btn btn-link btn-sm', row.membership_type === 'consumable' ? '' : 'hidden')} onClick={() => {
            form.value = cloneDeep(row)
            closeEditUnMemberModal.value = openDialog({
              title: '编辑非续订会员',
              body: <UnMemberForm />,
              mainClass: dialogMainClass,
            })
          }}
          >编辑
          </Button>
          <Button class="btn btn-link btn-sm" onClick={() => {
            void apiGetProductUseDate({ id: row.id || 0 }).then(rs => {
              if (!rs.data?.items || rs.data?.items?.length === 0) {
                return showAlert('该档位暂无使用记录', 'error')
              }
              openDialog({
                title: '使用情况',
                body: () => (
                  <x-content class="flex flex-col gap-y-2">
                    {
                      rs.data?.items?.map(item => {
                        return (
                          <div class="flex flex-row flex-wrap gap-x-2  break-all">
                            <span>{item.label.replace('3', '')}：</span>
                            <span>{item.ids.join(',')}</span>
                          </div>
                        )
                      })
                    }
                  </x-content>
                ),
              })
            })
          }}>
            使用情况
          </Button>
        </div>
      ),
      { class: mc('w-[200px]', props.hasActions ? '' : 'hidden') },
    ],
  ]

  onMounted(() => {
    list.value = []
    searchForm.value.app_id = ''
    loading.value = true
    void apiGetAppOptions({ app_name: '' }).then(res => {
      if (!res.data) return
      applicationList.value = (res.data.list || []).filter(i => i.app_name.includes('Drama'))
      searchForm.value.app_id = `${applicationList.value.filter(i => i.platform === props.platform)[0]?.id || ''}`
      set(searchForm.value, 'store', props.platform === 1 ? 'Apple Store' : props.platform === 2 ? 'Google Play' : '')
      onSearch(true)
    })
  })

  watch(
    () => [searchForm.value.app_id, props.hasCheckItem],
    () => {
      const platform = applicationList.value.find(item => item.id === +(searchForm.value.app_id || ''))?.platform
      if (platform === 1) {
        set(searchForm.value, 'store', 'Apple Store')
        set(searchForm.value, 'show_platform', '')
      } else if (platform === 2) {
        set(searchForm.value, 'store', 'Google Play')
        set(searchForm.value, 'show_platform', '')
      } else {
        set(searchForm.value, 'store', '')
        props.hasCheckItem && set(searchForm.value, 'show_platform', 'ios')
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  const get_k_sso_token = () => {
    return Cookies.get('k-sso-token') || Cookies.get('k_sso_token') || localStorage.getItem('k-sso-token') || localStorage.getItem('k_sso_token')
  }
  const exportExcel = async () => {
    const sort = {}
    const result = JSON.parse(JSON.stringify(searchForm.value))

    try {
      const queryString = qs.stringify({ ...result, sort }).toString()
      // const queryString = encodeURIComponent({ ...listParams.value, sort})

      // 发送请求到服务器
      const response = await fetch(`${import.meta.env.VITE_DRAMA_API_URL}/wallet/product/recharge/export?${queryString}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Device: 'Web',
          Token: get_k_sso_token() || '',
        },
      })
      // 检查响应是否成功
      if (!response.ok) {
        showFailToast('下载失败')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        showFailToast('下载的文件为空')
        return
      }
      // 创建一个下载链接并触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `充值档位.xlsx` // 设置下载的文件名
      document.body.appendChild(link)
      link.click() // 自动点击链接以开始下载
      document.body.removeChild(link) // 清理链接
      URL.revokeObjectURL(link.href) // 释放内存
    } catch (error) {
      showFailToast('下载失败')
    }
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => props.hasNav
          ? (
              <ul>
                <li><RouterLink to="/recharge-level">充值档位管理</RouterLink></li>
              </ul>
            )
          : null,
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              {
                label: () => (
                  <div>应用： <span class="text-sm">上架商店：{ searchForm.value.store ? searchForm.value.store : '无' }</span></div>),
                path: 'app_id',
                input: {
                  type: 'select',
                  class: 'w-[300px]',
                  autoInsertEmptyOption: false,
                  options: applicationList.value.map(item => ({
                    label: item.app_name,
                    value: item.id,
                  })),
                  disabled: props.appIdSelectDisabled,
                },
              },
              { label: '档位ID', path: 'id',
                input: { type: 'number' }, transform: transformNumber },
              { label: '档位说明', path: 'title_admin',
                input: { type: 'text' } },
              // { label: '商品名称', path: 'title', input: { type: 'text' } },
              { label: '商品ID', path: 'product_id', input: { type: 'text' } },
              {
                label: '档位属性',
                path: 'first_recharge',
                transform: transformNumber,
                input: {
                  type: 'select',
                  options: [
                    { label: '常规', value: 0 },
                    { label: '首充', value: 1 },
                    { label: '再充值', value: 2 },
                  ],
                },
              },
              { label: '价格', path: 'price',
                input: { type: 'number' }, transform: transformNumber },
              { label: '状态',
                path: 'status',
                transform: transformNumber,
                input: { type: 'select', options: [
                  {
                    label: '展示',
                    value: 1,
                  },
                  {
                    label: '不展示',
                    value: 2,
                  },
                ] } },
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="flex w-full items-center justify-between">
                  <span>充值档位列表</span>
                  <div class="flex gap-x-2">
                    <Button class="btn btn-primary btn-sm" onClick={() => {
                      form.value = cloneDeep(defaultData)
                      form.value.app_id = +searchForm.value.app_id
                      closeEditUnMemberModal.value = openDialog({
                        title: '新建非续订会员',
                        body: <UnMemberForm />,
                        mainClass: dialogMainClass,
                      })
                    }}
                    >新建非续订会员
                    </Button>
                    <Button class="btn btn-primary btn-sm" onClick={() => {
                      currentLevel.value = {
                        ...initTopUpLevel,
                        app_id: +searchForm.value.app_id,
                      }

                      closeEditRechargeLevelModal.value = openDialog({
                        title: '新建充值档位',
                        body: <RechargeLevelForm />,
                        mainClass: dialogMainClass,
                      })
                    }}
                    >新建充值档位
                    </Button>
                    {/* {!props.hasCheckItem && <Button class="btn btn-primary btn-sm" onClick={() => exportExcel()}>导出Excel</Button>} */}
                  </div>
                </x-table-actions>
              )
            : null
        ),
        table: () => <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={page.value}
                v-model:size={searchForm.value.page_size}
                total={total.value}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default TopUpLevelPage
