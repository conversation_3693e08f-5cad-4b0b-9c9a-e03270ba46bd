import { httpClient } from 'src/lib/http-client'

export const apiGetRechargeLevels = (data: M.RechargeLevelSearchParams) =>
  httpClient.post<ApiResponse<{
    items: M.RechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet/product/recharge/list', data)

export const apiCreateRechargeLevel = (data: M.RechargeLevel) =>
  httpClient.post<ApiResponse<{
    items: M.RechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet/product/create', data)

export const apiEditRechargeLevel = (data: M.RechargeLevel) =>
  httpClient.post<ApiResponse<{
    items: M.RechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet/product/edit', data)

export const apiUpdateRechargeLevelStatus = (data: { id: number, status: number }) =>
  httpClient.post<ApiResponse<null>>('/wallet/product/status', data)

export const apiUpdateRechargeLevelPriority = (data: { id: number, priority: number }) =>
  httpClient.post<ApiResponse<null>>('/wallet/product/priority', data)

export const apiGetProductUseDate = (data: { id: number }) =>
  httpClient.post<ApiResponse<{ items: Array<{
    label: string
    ids: number[]
  }> }>>('/wallet/product/use_stat', data)
