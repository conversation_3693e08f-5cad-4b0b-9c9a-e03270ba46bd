/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, DialogFooter, Input, openDialog, showAlert, transformInteger, transformNumber, transformNumber2 } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useRechargeLevelPage } from './use-recharge-level'
import { currencyList } from 'src/lib/constant'
import { ref, watch } from 'vue'

export const RechargeLevelForm = createComponent(null, () => {
  const { currentLevel, applicationList, onEdit, onCreate, closeEditRechargeLevelModal } = useRechargeLevelPage()
  const Form = CreateForm<M.RechargeLevel>()

  const formRules = z.object({
    title_admin: z.string().min(1, '请输入档位说明').max(40, '最多40个字符'),
    app_id: z.number({ message: '请选择生效应用' }),
    priority: z.number({ message: '请选择序号' }).max(12, '最大12').min(1, '最小1'),
    currency: z.string().min(1, '请输入币种'),
    discount_price: z.number({ message: '请输入现价' }),
    delivery_details: z.object({
      quanity: z.number({ message: '请输入售卖金币数' }).max(99999, '最大99999').min(1, '最小1'),
      bonus: z.number({ message: '请输入赠送金币数' }).max(99999, '最大99999').optional(),
    }),
    store: z.string().min(1, '请选择上架商店'),
    sku_id: z.string().min(1, '请输入商品ID'),
    show_platform: z.string().min(1, '请选择展示平台'),
  })

  const { error, validateAll } = useValidator(currentLevel, formRules)

  watch(
    () => currentLevel.value.app_id,
    () => {
      const platform = applicationList.value.find(item => item.id === currentLevel.value.app_id)?.platform
      if (platform === 1) {
        set(currentLevel.value, 'store', 'Apple Store')
      } else if (platform === 2) {
        set(currentLevel.value, 'store', 'Google Play')
      } else if (platform === 3) {
        set(currentLevel.value, 'store', '')
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  const calcCoins = () => {
    if (!currentLevel.value.discount_price) {
      return showAlert('请输入价格', 'error')
    }

    if (!currentLevel.value.delivery_details) {
      currentLevel.value.delivery_details = {}
    }

    currentLevel.value.delivery_details.quanity = (currentLevel.value.discount_price + 0.01) * 100
  }

  const rate = ref<number>()

  const calcBonus = () => {
    if (!currentLevel.value.delivery_details) {
      currentLevel.value.delivery_details = {}
    }

    if (!currentLevel.value.delivery_details.quanity) {
      return showAlert('请输入兑换金币数（Coins）', 'error')
    }

    rate.value = currentLevel.value.delivery_details.bonus_rate || 0

    const closeDialog = openDialog({
      title: '请输入赠送比例%：',
      body: () => (
        <div class="relative h-[100px] pt-1">
          <Input
            type="number"
            inputClass="input input-bordered input-sm w-full max-w-xs"
            modelValue={rate.value}
            onUpdate:modelValue={v => {
              rate.value = Math.round(Number(v || ''))
            }} />
          <DialogFooter okText="确定" onOk={() => {
            if (!rate.value) {
              return showAlert('请输入赠送比例%', 'error')
            }
            closeDialog()
            currentLevel.value.delivery_details!.bonus_rate = Math.round(rate.value)
            currentLevel.value.delivery_details!.bonus = Math.ceil(currentLevel.value.delivery_details!.quanity! * (currentLevel.value.delivery_details!.bonus_rate / 100))
          }} cancelText="" onCancel={closeDialog}
          />
        </div>
      ),
    })
  }

  const refreshBonusRate = () => {
    if (!currentLevel.value.delivery_details) {
      currentLevel.value.delivery_details = {}
    }

    if (!currentLevel.value.delivery_details.quanity) {
      return showAlert('请输入兑换金币数（Coins）', 'error')
    }

    if (!currentLevel.value.delivery_details.bonus) {
      return showAlert('请输入赠送金币数（Bonus）', 'error')
    }

    currentLevel.value.delivery_details.bonus_rate = Math.round(((currentLevel.value.delivery_details.bonus / currentLevel.value.delivery_details.quanity) * 100))
  }

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid grid-cols-1 gap-y-3"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentLevel.value || {}, path, value)
          }}
          items={[
            <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">商店信息</h1>,
            { label: requiredLabel('上架商店'),
              path: 'store',
              input: {
                type: 'select',
                disabled: true,
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: 'Apple Store',
                    value: 'Apple Store',
                  },
                  {
                    label: 'Google Play',
                    value: 'Google Play',
                  },
                ],
              },
              class: mc(currentLevel.value.store == '' ? 'hidden' : ''),
            },
            { label: requiredLabel('商品ID（product_id）'), path: 'sku_id', input: { type: 'text' } },
            {
              label: requiredLabel('币种'),
              path: 'currency',
              input: {
                type: 'select',
                options: currencyList,
                autoInsertEmptyOption: false,
                disabled: !!currentLevel.value.id,
              },
            },
            {
              label: requiredLabel('价格'),
              path: 'discount_price',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber2,
            },
            {
              label: requiredLabel('档位属性'),
              path: 'first_recharge',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { label: '常规档位', value: 0 },
                  { label: '首充档位', value: 1 },
                  { label: '再充值', value: 2 },
                ],
              },
              hint: '首充档位仅会展示给第一次充值的用户，而再充值反之',
            },
            <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">档位配置</h1>,
            {
              label: requiredLabel('档位说明'),
              path: 'title_admin',
              input: {
                type: 'text',
                maxlength: 40,
                placeholder: '请输入档位说明，1-40个字符',
              },
            },
            {
              label: requiredLabel('生效应用'),
              path: 'app_id',
              input: {
                type: 'select',
                options: applicationList.value.filter(i => i.platform === (currentLevel.value.store === 'Google Play' ? 2 : currentLevel.value.store === 'Apple Store' ? 1 : 3)).map(item => ({
                  label: item.app_name,
                  value: item.id,
                })),
                autoInsertEmptyOption: false,
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber,
            },
            {
              label: requiredLabel('支付面板展示'),
              path: 'status',
              transform: transformInteger,
              input: { type: 'radio',
                options: [
                  {
                    label: '展示',
                    value: 1,
                  },
                  {
                    label: '不展示',
                    value: 2,
                  },
                ],
              },
            },
            {
              label: requiredLabel('序号'),
              path: 'priority',
              input: {
                type: 'select',
                options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(i => ({
                  label: '' + i,
                  value: i,
                })),
              },
              transform: transformInteger,
              class: currentLevel.value?.status === 2 ? 'hidden' : '',
            },
            {
              label: <x-title class="flex items-center justify-between">{requiredLabel('兑换金币数（Coins）')} <span class="cursor-pointer text-sm text-blue-400" onClick={calcCoins}>计算</span></x-title>,
              path: 'delivery_details.quanity',
              input: {
                type: 'number',
                placeholder: '请输入整数1-99999',
                min: 1,
                max: 99999,
                suffix: <>个</>,
              },
              transform: transformInteger,
            },
            {
              label: <x-title class="flex items-center justify-between">赠送金币数（Bonus） <span class="cursor-pointer text-sm text-blue-400" onClick={calcBonus}>按比例输入</span></x-title>,
              path: 'delivery_details.bonus',
              input: {
                type: 'number',
                placeholder: '请输入整数1-99999',
                min: 1,
                max: 99999,
                suffix: <>个</>,
              },
              transform: transformInteger,
            },
            {
              label: <x-title class="flex items-center justify-between">赠送比 <span class="cursor-pointer text-sm text-blue-400" onClick={refreshBonusRate}>刷新</span></x-title>,
              path: 'delivery_details.bonus_rate',
              input: {
                type: 'number',
                suffix: <>%</>,
                disabled: true,
              },
              transform: transformNumber,
            },
            {
              label: '角标文案',
              path: 'slogan',
              input: {
                type: 'text',
                // disabled: true,
              },
            },
            [requiredLabel('展示平台：'), 'show_platform', { type: 'radio', options: [
              {
                value: 'ios',
                label: 'iOS',
              },
              {
                value: 'h5',
                label: 'H5',
              },
            ] }, {
              class: mc(currentLevel.value.store !== '' ? 'hidden' : ''),
            }],
          ] as any}
          data={currentLevel.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditRechargeLevelModal.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (currentLevel.value.status === 2) {
            exclude.push('priority')
          }
          if (currentLevel.value.store === '') {
            exclude.push('store')
          }
          if (currentLevel.value.store !== '') {
            exclude.push('show_platform')
          }
          if (!validateAll({ exclude })) {
            return
          }
          console.log(currentLevel.value)

          !currentLevel.value?.id ? void onCreate() : void onEdit()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
