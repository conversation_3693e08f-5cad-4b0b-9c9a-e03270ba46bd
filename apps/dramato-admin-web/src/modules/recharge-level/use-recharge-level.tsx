/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { showAlert, showFailToast } from '@skynet/ui'
import { cloneDeep } from 'lodash-es'
import { apiCreateRechargeLevel, apiEditRechargeLevel, apiGetRechargeLevels, apiUpdateRechargeLevelPriority, apiUpdateRechargeLevelStatus } from './recharge-level-api'
const searchForm = ref<M.RechargeLevelSearchParams>({
  status: 0,
  page_size: 100,
  next: '',
  app_id: '',
})
const applicationList = ref<Array<Required<M.Application>>>([])
const list = ref<M.RechargeLevel[]>([])
const originalList = ref<M.RechargeLevel[]>([])
const total = ref<number>(0)
const page = ref(1)
const closeEditRechargeLevelModal = ref(() => {})
const closeEditUnMemberModal = ref(() => {})
const loading = ref(false)

const initTopUpLevel = {
  store: 'Google Play',
  first_recharge: 0,
  status: 2,
  delivery_details: {},
  currency: 'USD',
}

const currentLevel = ref<M.RechargeLevel>(initTopUpLevel)

const getList = async () => {
  loading.value = true
  const rs = await apiGetRechargeLevels(searchForm.value).finally(() => {
    loading.value = false
  })
  originalList.value = rs.data?.items || []
  list.value = (originalList.value).map(i => ({
    ...i,
    price: +((i?.price || 0) / 100).toFixed(2),
    discount_price: +((i?.discount_price || 0) / 100).toFixed(2),
  }),
  )
  total.value = rs.data?.total || 0
  searchForm.value.next = rs.data?.page_info.next || ''
}

const switchLevel = () => ({
  ...currentLevel.value,
  product_type: 'recharge',
  price: +((currentLevel.value?.price || 0) * 100).toFixed(0),
  discount_price: +((currentLevel.value?.discount_price || 0) * 100).toFixed(0),
  status: +(currentLevel.value?.status || ''),
  first_recharge: +(currentLevel.value?.first_recharge || ''),
  apple_store: Object.keys(currentLevel.value?.apple_store || {}).length > 0 ? currentLevel.value?.apple_store : undefined,
  google_play: Object.keys(currentLevel.value?.google_play || {}).length > 0 ? currentLevel.value?.google_play : undefined,
})

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_size = 100
    searchForm.value.next = ''
  }
  void getList()
}

const onPageChange = (n: number) => {
  page.value = n
  searchForm.value.next = n - 1 > 0 ? `${(n - 1) * searchForm.value.page_size}` : ''
  onSearch()
}

const onPageSizeChange = (n: number) => {
  searchForm.value.page_size = n
  searchForm.value.next = ''
  onSearch()
}

const onReset = () => {
  page.value = 1
  searchForm.value = {
    status: 0,
    page_size: 100,
    next: '',
    app_id: searchForm.value.app_id || `${applicationList.value[0]?.id || ''}`,
    store: '',
  }
  onSearch(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  closeEditRechargeLevelModal.value && closeEditRechargeLevelModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  searchForm.value.next = page.value - 1 > 0 ? `${(page.value - 1) * searchForm.value.page_size}` : ''
  onSearch()
}
const onUnMemberEditSuccess = (isCreate?: boolean) => {
  closeEditUnMemberModal.value && closeEditUnMemberModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  searchForm.value.next = page.value - 1 > 0 ? `${(page.value - 1) * searchForm.value.page_size}` : ''
  onSearch()
}
// Recharge

const onEditRechargeLevelStatus = async (d: M.RechargeLevel) => {
  try {
    await apiUpdateRechargeLevelStatus({
      id: d?.id || 0,
      status: d?.status === 1 ? 2 : 1,
    })
    onEditSuccess()
    showAlert(`${d?.status === 1 ? '禁用' : '启用'}成功`)
  } catch (error: any) {
    showFailToast(error.response.data.message || `${d?.status === 1 ? '禁用' : '启用'}失败`)
  }
}

const onEditRechargeLevelPriority = async (d: M.RechargeLevel) => {
  try {
    const priority = d?.priority || 0
    if (priority < 1) {
      showFailToast('序号不能小于1')
      return
    }
    if (priority > 12) {
      showFailToast('序号不能大于12')
      return
    }
    await apiUpdateRechargeLevelPriority({
      id: d?.id || 0,
      priority,
    })
    onEditSuccess()
    showAlert('编辑成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '更新序号失败')
  }
}

const onCreate = async () => {
  try {
    await apiCreateRechargeLevel(switchLevel())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    showFailToast(error.response.data.message || '创建失败')
  }
}

const onEdit = async () => {
  try {
    await apiEditRechargeLevel(switchLevel())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    showFailToast(error.response.data.message || '编辑失败')
  }
}
// 非续订会员相关
const defaultData = {
  id: undefined,
  app_id: undefined,
  app_name: '',
  product_type: 'recharge',
  title: '',
  slogan: '',
  tips: '',
  discount_desc: '',
  description: '',
  currency: 'USD',
  price: undefined,
  discount_price: undefined,
  platform: '',
  store: '',
  status: 2,
  props: [],
  membership_type: 'consumable',
  delivery_details: {
    quanity: 1,
    bonus: undefined,
    daily_bonus: undefined,
    period: 'daily',
  },
  sku_id: '',
  priority: undefined,
  first_recharge: 0,
  has_discount: 0,
  time_limit: undefined,
}
const form = ref<M.MemberLevel>(cloneDeep(defaultData))
export const useRechargeLevelPage = () => {
  return {
    searchForm,
    list,
    originalList,
    total,
    page,
    applicationList,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    currentLevel,
    onCreate,
    onEdit,
    closeEditRechargeLevelModal,
    initTopUpLevel,
    onEditRechargeLevelStatus,
    onEditRechargeLevelPriority,
    loading,
    form,
    defaultData,
    closeEditUnMemberModal,
    onUnMemberEditSuccess,
  }
}
