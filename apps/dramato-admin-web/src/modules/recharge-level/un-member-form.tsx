/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformInteger, transformNumber, transformNumber2, showSuccessToast, showFailToast, usePopover, Icon } from '@skynet/ui'
import { set, get, cloneDeep } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { AxiosError } from 'axios'
import { apiCreateMember, apiUpdateMember } from '../member/member.api'
import { periodList, consumablePeriodList, currencyList } from 'src/lib/constant.ts'
import { useRechargeLevelPage } from './use-recharge-level'

import { ref, watch } from 'vue'

export const UnMemberForm = createComponent(null, () => {
  const numbers = Array.from({ length: 12 }, (_, index) => ({
    value: index + 1,
    label: String(index + 1),
  }))
  const rules = z.object({
    title: z.string().min(1, '请输入商品名称'),
    title_admin: z.string().min(1, '请输入档位说明'),
    app_id: z.number().min(1, {
      message: '请选择应用名称',
    }),
    priority: z.number().min(1, {
      message: '请输入正确序号',
    }).int({
      message: '请输入正确序号 1-12',
    }).max(12, {
      message: '请输入正确序号 1-12',
    }),
    delivery_details: z.object({
      quanity: z.number().min(1, {
        message: '请输入兑换金币数',
      }).int({
        message: '请输入1-99999之前的整数',
      }).max(99999, {
        message: '请输入1-99999之前的整数',
      }),
      period: z.string().min(1, {
        message: '请选择订阅周期单位',
      }),
    }),
    price: z.number().min(0.01, {
      message: '请输入价格',
    }).refine((value: number) => {
      const regex = /^\d+(\.\d{1,2})?$/
      return regex.test('' + value)
    }, {
      message: '金额必须为正数，且最多包含两位小数',
    }),
    sku_id: z.string().min(1, '请输入商品ID'),
  })

  const Form = CreateForm<M.MemberLevel>()
  const btnLoading = ref(false)
  const { form, defaultData, applicationList, closeEditUnMemberModal, onUnMemberEditSuccess } = useRechargeLevelPage()
  const dialogRef = ref(() => {})
  const { error, validateAll } = useValidator(form, rules)

  watch(() => [
    form.value.app_id,
    form.value.status,
  ], newVal => {
    const platform = applicationList.value.find(item => item.id === form.value.app_id)?.platform
    if (platform === 1) {
      set(form.value, 'store', 'Apple Store')
      set(form.value, 'platform', 'iOS')
    } else if (platform === 2) {
      set(form.value, 'store', 'Google Play')
      set(form.value, 'platform', 'Android')
    } else if (platform === 3) {
      set(form.value, 'store', 'h5')
      set(form.value, 'platform', 'h5')
    }
  }, {
    immediate: true,
  })

  const onSave = async () => {
    const exclude: string[] = []
    if (form.value.status !== 1) {
      exclude.push('priority')
      form.value.priority = 0
    }
    if (validateAll({ exclude: exclude })) {
      try {
        let res
        const params = cloneDeep(form.value)
        btnLoading.value = true
        params.price = params.price ? +(params.price * 100).toFixed(2) : 0
        params.discount_price = params.price
        if (form.value.platform === 'h5') {
          params.platform = ''
          params.store = ''
        }
        if (!form.value.id) {
          res = await apiCreateMember(params)
        } else {
          res = await apiUpdateMember(params)
        }
        if (res.code === 200) {
          showSuccessToast('操作成功！')
        } else {
          showFailToast(res?.message || '服务忙碌，稍后再试')
        }
        onQuery()
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
      } finally {
        btnLoading.value = false
      }
    } else {
      btnLoading.value = false
      console.log(error, 'error')
      return
    }
    onClose()
  }
  const onQuery = () => {
    console.log('刷新列表')
    onUnMemberEditSuccess()
  }
  const onClose = () => {
    form.value = cloneDeep(defaultData)
    dialogRef.value()
  }
  const getPeriodName = (row: M.MemberLevel) => {
    return consumablePeriodList.find(item => item.value === row?.delivery_details?.period)?.label
  }
  const triggerElementRef = ref()
  usePopover({
    triggerElement: triggerElementRef,
    content: () => (
      <x-tips class="flex flex-col gap-y-2">
        {
          [
            'Unlimited access to all series for 1 week',
            'Unlimited access to all series for 7 days',
            'Unlimited access to all series for 1 month',
            'Unlimited access to all series for 1 year',
          ].map(i => <x-tip class="cursor-pointer" onClick={() => form.value.description = i}>{i}</x-tip>)
        }
      </x-tips>
    ),
    placement: 'bottom-start',
    class: 'overflow-visible',
    offset: 10,
    arrowVisible: false,
    triggerType: 'hover',
    wrapperClass: 'p-2 bg-white border-[1px] border-[#eee] border-[solid] rounded-xs z-popover-in-dialog',
  })
  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="flex w-full flex-col flex-nowrap"
          data={form.value}
          onChange={(path, value) => {
            if (path === 'app_id') {
              form.value.app_name = applicationList.value.find(row => row.app_key === value)?.app_name
            }
            set(form.value, path, value)
          }}
          hasAction={false}
          error={error.value}
          items={[
            <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">商店信息</h1>,
            { label: requiredLabel('上架商店'),
              path: 'store',
              input: {
                type: 'select',
                disabled: true,
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: 'Apple Store',
                    value: 'Apple Store',
                  },
                  {
                    label: 'Google Play',
                    value: 'Google Play',
                  },
                ],
              },
              class: mc(form.value.store == 'h5' ? 'hidden' : ''),
            },
            { label: requiredLabel('权益类型'),
              path: 'membership_type',
              input: {
                type: 'radio',
                disabled: true,
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: '非续订会员',
                    value: 'consumable',
                  },
                ],
                class: 'gap-2',
              },
            },
            { label: <div>{requiredLabel('商品ID（product_id）')}<span class={mc('text-[12px] text-red-700 hidden', form.value.membership_type == 'consumable' && 'block')}>此类型注意要使用充值商品</span></div>, path: 'sku_id', input: { type: 'text' } },
            { label: requiredLabel('商品名称'), path: 'title', input: { type: 'text' } },
            {
              label: requiredLabel('币种'),
              path: 'currency',
              input: {
                type: 'select',
                options: currencyList,
                autoInsertEmptyOption: false,
                disabled: !!form.value.id,
              },
            },
            {
              label: requiredLabel('价格'),
              path: 'price',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
                // disabled: !!form.value.id,
              },
              transform: transformNumber2,
            },
            { label: requiredLabel('会员限时'), path: 'delivery_details.quanity', input: { type: 'number', placeholder: '可输入整数1-99999' }, transform: transformNumber },
            { label: requiredLabel('时限周期'), path: 'delivery_details.period', input: { type: 'select', options: consumablePeriodList, autoInsertEmptyOption: false } },
            {
              label: requiredLabel('档位属性'),
              path: 'first_recharge',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { label: '非续订会员', value: 0 },
                ],
              },
            },
            <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">档位配置</h1>,
            { label: requiredLabel('档位说明'), path: 'title_admin', input: { type: 'text' } },
            { label: requiredLabel('生效应用：'), path: 'app_id', input: { type: 'select', options: applicationList.value.filter(i => i.platform === (form.value.store === 'Google Play' ? 2 : form.value.store === 'Apple Store' ? 1 : 3)).map(item => ({
              label: item.app_name,
              value: item.id,
            })), autoInsertEmptyOption: false, disabled: !!form.value.id }, transform: transformNumber },
            { label: requiredLabel('支付面板展示：'), path: 'status', input: { type: 'radio', options: [
              {
                value: 1,
                label: '展示',
              },
              {
                value: 2,
                label: '不展示',
              },
            ] }, transform: transformInteger },
            {
              label: requiredLabel('序号：'), class: `${form.value.status === 1 ? '' : 'hidden'}`,
              path: 'priority', input: { type: 'select', options: numbers, autoInsertEmptyOption: false }, transform: transformNumber,
            },
            { label: <x-title class="flex items-center justify-between">权益说明 <span class="cursor-pointer text-sm text-blue-400" ref={triggerElementRef}>快捷填入</span></x-title>, path: 'description', input: { type: 'text' } },
            { label: '角标文案：', path: 'slogan', input: { type: 'text' } },
            [requiredLabel('展示平台：'), 'show_platform', { type: 'radio', options: [
              {
                value: 'ios',
                label: 'iOS',
              },
              {
                value: 'h5',
                label: 'H5',
              },
            ] }, { class: mc(form.value.store !== 'h5' ? 'hidden' : '') }],
          ] as any}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditUnMemberModal.value}>取消</Button>
        <Button disabled={btnLoading.value} class="btn btn-primary btn-sm" onClick={() => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          onSave()
        }}
        >
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          确定
        </Button>
      </div>
    </>
  )
})
