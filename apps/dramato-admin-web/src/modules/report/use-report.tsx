/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, CreateTableOld, DateTime, Icon, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetReportList, apiUpdateInfringement, apiSafeguardRights } from './report-api'
import { PiracyPlatformOptions, ResourceTypeV2Options, IsInfringementOptions, PiracyStatusOptions, DetectionTypeOptions } from './constant'

export const useCompetitionContent = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    getList,
  }
}
const defaultParams = {
  piracy_series_id: '',
  piracy_platform: 0,
  resource_type_v2: 0,
  is_infringement: 0,
  piracy_status: 0,
  detection_type: 0,
  page_info: {
    page_index: 1,
    page_size: 20,
  },
}

const Form = CreateForm<Api.Report.ListReqParams>()
const params = ref<Api.Report.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<Api.Report.ReportItem>()
const list = ref<Api.Report.ReportItem[]>([])
const loading = ref<boolean>(false)

const getDescByValue = (value: number, options: { label: string, value: number }[]) => {
  return options.find(item => item.value === value)?.label
}

const  columns = [
  { prop: 'id', label: '资源ID', width: 100, fixed: true },
  { prop: 'title', label: '资源名称', minWidth: 160, fixed: true },
  { label: '短剧类型', width: 120, render: (scope: { row: Api.Report.ReportItem }) => {
    const desc = getDescByValue(scope.row.resource_type_v2, ResourceTypeV2Options)
    return (
      desc ? <div class="badge badge-outline">{desc}</div> : '-'
    )
  } },
  { label: '盗版平台', width: 120, render: (scope: { row: Api.Report.ReportItem }) => {
    const desc = getDescByValue(scope.row.piracy_platform, PiracyPlatformOptions)
    return (
      <div class="badge badge-outline">{desc}</div>
    )
  } },
  { label: '盗版链接', minWidth: 120, render: (scope: { row: Api.Report.ReportItem }) => {
    return (
      <a class="link-primary" href={scope.row.url} target="_blank">{scope.row.url}</a>
    )
  } },
  { label: '合作方', render: (scope: { row: Api.Report.ReportItem }) => {
    return (
      scope.row.partner_name ? scope.row.partner_name : '-'
    )
  }, minWidth: 120 },
  { label: '商务', render: (scope: { row: Api.Report.ReportItem }) => {
    return (
      scope.row.business_principal ? scope.row.business_principal : '-'
    )
  }, minWidth: 120 },

  { label: '检测类型', width: 100, render: (scope: { row: Api.Report.ReportItem }) => {
    const desc = getDescByValue(scope.row.detection_type, DetectionTypeOptions)
    return (
      <div class="badge badge-outline">{desc}</div>
    )
  } },
  { label: '是否侵权', width: 100, render: (scope: { row: Api.Report.ReportItem }) => {
    const desc = getDescByValue(scope.row.is_infringement, IsInfringementOptions)
    return (
      desc ? <div class="badge badge-outline">{desc}</div> : '-'
    )
  } },
  { label: '盗版状态', width: 100, render: (scope: { row: Api.Report.ReportItem }) => {
    const desc = getDescByValue(scope.row.piracy_status, PiracyStatusOptions)
    return (
      <div class="badge badge-outline">{desc}</div>
    )
  } },
  {
    label: '入库时间',
    width: 140,
    render: (scope: { row: Api.Report.ReportItem }) => {
      return (
        <DateTime value={scope.row.created * 1000} />
      )
    },
  },
  { label: '操作', width: 100, fixed: 'right', align: 'center', render: (scope: { row: Api.Report.ReportItem }) => {
    if (scope.row.is_infringement === 1 && scope.row.is_safeguard_rights !== 1) {
      const btnLoading = ref(false)
      return (
        <Button class="btn btn-link btn-xs" onClick={() => {
          const hideDeleteDialog = openDialog({
            title: '维权',
            mainClass: 'pb-0 px-5',
            customClass: '!w-[400px]',
            body: () => (
              <x-infringement-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-infringement-body>确认维权</x-infringement-body>
                <x-infringement-footer class="w-full flex justify-end gap-x-[10px]">
                  <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                  <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                    btnLoading.value = true
                    void apiSafeguardRights({
                      piracy_series_id: [scope.row.id],
                    }).then(() => {
                      showSuccessToast('操作成功！')
                      void getList()
                      hideDeleteDialog()
                    }).catch((error: any) => {
                      showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
                    }).finally(() => {
                      btnLoading.value = false
                    })
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                    确定
                  </button>
                </x-infringement-footer>
              </x-infringement-confirm-dialog>
            ),
          })
        }}>
          维权
        </Button>
      )
    } else if (scope.row.is_infringement === 0) {
      return (
        <Button class="btn btn-link btn-xs" onClick={() => {
          const btnLoading = ref(false)
          const isInfringement = ref(true)
          const hideDeleteDialog = openDialog({
            title: '是否侵权',
            mainClass: 'pb-0 px-5',
            customClass: '!w-[400px]',
            body: () => (
              <x-infringement-confirm-dialog class="flex flex-col gap-y-[25px]">
                <select class="select select-bordered select-sm" v-model={isInfringement.value}>
                  <option value={true}>是</option>
                  <option value={false}>否</option>
                </select>
                <x-infringement-footer class="w-full flex justify-end gap-x-[10px]">
                  <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                  <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                    btnLoading.value = true
                    void apiUpdateInfringement({
                      piracy_series_id: [scope.row.id],
                      is_infringement: isInfringement.value ? 1 : 2,
                    }).then(() => {
                      showSuccessToast('操作成功！')
                      void getList()
                      hideDeleteDialog()
                    }).catch((error: any) => {
                      showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
                    }).finally(() => {
                      btnLoading.value = false
                    })
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                    确定
                  </button>
                </x-infringement-footer>
              </x-infringement-confirm-dialog>
            ),
          })
        }}>
          审核
        </Button>
      )
    } else {
      if (scope.row.is_safeguard_rights === 1) {
        return <div class="badge badge-primary">已维权</div>
      } else if (scope.row.is_infringement === 2) {
        return <div class="badge badge-primary">非侵权</div>
      } else {
        return null
      }
    }
  } },
]

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetReportList(params.value)
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_info.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_info.page_size = size
  params.value.page_info.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_info.page_index = 1
  void getList()
}
