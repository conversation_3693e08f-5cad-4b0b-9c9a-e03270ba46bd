import { httpClient } from 'src/lib/http-client'
import { trim } from 'lodash-es'

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

/**
 * @description 检查资源名称或id
 * @param value
 * @returns
 */
export const checkResourceTitleOrId = (value: string) => {
  if (!value) return []
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t'])
  // 如果数组中list每个元素都为数字，且大于 资源id大于等于9900，如果有一个符合，则 返回符合规则的，如果都不符合规则，则返回
  const list = trimSplit(value)
  const isResourceIds = list.filter(item => {
    return /^\d+$/.test(item) && +item >= 9900
  })
  if (isResourceIds.length > 0) {
    return isResourceIds.map(n => +n)
  } else {
    return [trim(value)]
  }
}

const transNumber = (value: number) => {
  if (!value) return 0
  return value
}

export const apiGetReportList = (data: Api.Report.ListReqParams) =>
  httpClient.post<ApiResponse<Api.Report.ReportListResp>>('/piracy/series/search', data, {
    transformRequestData: {
      piracy_series_id: [checkResourceTitleOrId],
      piracy_platform: [transNumber],
      resource_type_v2: [transNumber],
      is_infringement: [transNumber],
      piracy_status: [transNumber],
      detection_type: [transNumber],
    },
  })
// 标记是否侵权
export const apiUpdateInfringement = (data: Api.Report.InfringementReqParams) =>
  httpClient.post<ApiResponse<Api.Report.ReportListResp>>('/piracy/series/is_infringement', data)

// 维权提交
export const apiSafeguardRights = (data: Api.Report.SafeguardRightsReqParams) =>
  httpClient.post<ApiResponse<Api.Report.ReportListResp>>('/piracy/series/is_safeguard_rights', data)
