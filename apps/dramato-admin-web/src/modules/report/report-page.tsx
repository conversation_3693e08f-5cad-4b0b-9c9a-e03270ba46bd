/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Pager, transformNumber } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useCompetitionContent } from './use-report'
import { ElTable, ElTableColumn } from 'element-plus'
import { PiracyPlatformOptions, ResourceTypeV2Options, IsInfringementOptions, PiracyStatusOptions, DetectionTypeOptions } from './constant'
type ReportPageOptions = {
  props: {}
}
export const ReportPage = createComponent<ReportPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange } = useCompetitionContent()

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-report-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>盗版监控</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['资源ID', 'piracy_series_id', { type: 'text', placeholder: '请输入资源ID' }],
              ['盗版平台', 'piracy_platform', { type: 'select', options: PiracyPlatformOptions }, { transform: transformNumber }],
              ['短剧类型', 'resource_type_v2', { type: 'select', options: ResourceTypeV2Options }, { transform: transformNumber }],
              ['是否侵权', 'is_infringement', { type: 'select', options: IsInfringementOptions }, { transform: transformNumber }],
              ['盗版状态', 'piracy_status', { type: 'select', options: PiracyStatusOptions }, { transform: transformNumber }],
              ['检测类型', 'detection_type', { type: 'select', options: DetectionTypeOptions }, { transform: transformNumber }],
            ]}
          />
        ),
        table: () => (
          <ElTable
            v-loading={loading.value}
            data={list.value || []}
          >
            {columns.map(col => {
              const { render, ...rest } = col;

              return (
                <ElTableColumn
                  key={col.prop}
                  {...rest}
                  v-slots={render ? {
                    default: ({ row }: { row: any }) => render({ row }),
                  } : undefined}
                />
              )
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_info.page_index} v-model:size={params.value.page_info.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-report-page>
  )
})

export default ReportPage
