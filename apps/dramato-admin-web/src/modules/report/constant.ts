/**
 * 盗版平台
 */
export const PiracyPlatformOptions = [
  {
    label: 'facebook',
    value: 1,
  },
  {
    label: 'youtube',
    value: 2,
  },
  {
    label: 'dailymotion',
    value: 3,
  },
]

/**
 * 资源类型
 */
export const ResourceTypeV2Options = [
  {
    label: '翻译二轮',
    value: 1,
  },
  {
    label: '本土首发',
    value: 2,
  },
  {
    label: '本土对投',
    value: 3,
  },
  {
    label: '本土二轮',
    value: 4,
  },
  {
    label: '本土自制',
    value: 5,
  },
  {
    label: '翻译首发',
    value: 6,
  },
]
// 是否侵权 1 是 2 否
export const IsInfringementOptions = [
  {
    label: '侵权',
    value: 1,
  },
  {
    label: '非侵权',
    value: 2,
  },
]

// piracy_status 盗版状态 1 在线 2 下线
export const PiracyStatusOptions = [
  {
    label: '在线',
    value: 1,
  },
  {
    label: '下线',
    value: 2,
  },
]

// 检测类型 1 新增 2 历史
export const DetectionTypeOptions = [
  {
    label: '新增',
    value: 1,
  },
  {
    label: '历史',
    value: 2,
  },
]
