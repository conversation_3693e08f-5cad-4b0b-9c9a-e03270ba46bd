declare namespace Api {
  namespace Report {
    interface ListReqParams {
      piracy_series_id: number[] | string
      piracy_platform: number
      resource_type_v2: number
      is_infringement: number
      piracy_status: number
      detection_type: number
      page_info: {
        page_index: number
        page_size: number
      }
    }

    interface ReportListResp {
      list: ReportItem[] // 剧列表
      total: number // 总数
    }

    interface ReportItem {
      id: number
      url: string
      title: string
      series_resource_id: number
      piracy_platform: number
      is_infringement: number
      piracy_status: number
      detection_type: number
      release_round: number
      resource_type: number
      resource_type_v2: number
      partner_name: string
      business_principal: string
      created: number
      updated: number
      is_safeguard_rights: number // 是否已维权
    }

    interface SafeguardRightsReqParams {
      piracy_series_id: number[]
    }

    interface InfringementReqParams {
      piracy_series_id: number[]
      is_infringement: number
    }
  }

}
