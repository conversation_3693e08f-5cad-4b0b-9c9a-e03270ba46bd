declare namespace M {
  interface PushNotificationSearchOptionNew {
    id?: number // 消息id
    list_type: number // 列表类型，必传，1:人工push，2：剧集无关，3：剧集半相关，4：剧集相关
    is_batch_detail?: boolean // 是否查询分批次列表
    push_task_id?: number // push任务id
    timed_start_date_time?: string // 设定开始时间，UTC+0时间
    timed_end_date_time?: string // 设定结束时间，UTC+0时间
    state?: number // 任务状态，1：待发送，20：发送中，40：已完成，60：已停用
    click_rate_pv_lt?: string
    click_rate_uv_lt?: string
    rec_type?: number // push推荐类型，23：签到提醒,19：订阅到期次日，20：订阅到期5天，21：免费部分完播，22：充值用户
    gen_type?: number // 创建类型，1：人工，2：AI
    language?: string // 语言
    prompt_id?: number // 提示词主键id,push_content_llm表主键id
    open_state?: number // 开启状态，1：打开，2：关闭
    exec_start_date_time?: string // 实际开始时间，UTC+0时间
    exec_end_date_time?: string // 实际结束时间，UTC+0时间
    resource_id?: number // 资源id
    series_key?: string // 剧集key
    batch?: string // 统计周期
    page_info: {
      page_index?: number
      page_size?: number// 选填参数
    }
  }

  interface PushStyle {
    push_type: string
    title_prefix: string
    title: string
    sub_title: string
    body: string
    image: string
    small_image: string
    tips: string
    tips_style: string
    media_progress: string
    has_btn1: boolean
    btn1_bg_color: string
    btn1_text: string
    btn1_deep_link: string
    has_btn2: boolean
    btn2_text: string
    btn2_deep_link: string
  }

  interface ActivityItem {
    id?: number
    name?: string
    activity_type?: number
    description?: string
    platform?: number
    lang?: string
    start_time?: string
    end_time?: string
    operator?: string
    activity_status?: number
    display_page?: number
    link?: string
    name?: string
  }

  interface ActivityItemResponse {
    total: number
    items: ActivityItem[],
    page_info: {
      total: number
    }
  }

  interface Limit {
    min: number
    max: number
  }

  interface Tips {
    [key: string]: string
  }

  interface InputField {
    limit: Limit
    tips: string
  }

  interface PushTypeTips {
    [key: string]: string
  }

  interface DeeplinkTips {
    [key: number]: string
  }

  interface Form {
    title_input: InputField
    content_input: InputField
    push_type: {
      tips: PushTypeTips
    }
    deeplink: {
      tips: DeeplinkTips
    }
    btn1: {
      limit: Limit
      tips: string
    }
    btn2: {
      limit: Limit
      tips: string
    }
  }

  interface VarTpl {
    [key: string]: string
  }

  interface PushConfigData {
    form: Form
    var_tpl: VarTpl
  }
  // 1:tile,2:content,3:tips,4:btn1,5:btn2
  enum TranslateType {
    title = 1,
    content = 2,
    tips = 3,
    btn1 = 4,
    btn2 = 5,
  }

  interface Deeplink {
    name: string
    path: string
  }

  interface TranslateItem {
    text: string | boolean
    type: M.TranslateType
    translate: Record<string, string>
  }

  interface CreatePushForm {
    push_task_id?: number
    list_type: number
    resource_scope_type: number
    resource_scope_val: string
    target_user_type: number
    target_user_val: string
    ageing_type: number
    target_app_names_source: string[]
    target_app_names: string
    timed_start_date_time: string
    timed_end_date_time: string
    end_now_add_hours: number
    end_now_add_minute: number
    style_config: {
      push_type: string
      buttonOrProgress: string
      title: string
      body: string
      image: string
      small_image: string
      has_tips: boolean
      tips: string
      tips_style: string
      media_progress: string
      media_progress_type: string
      has_btn1: boolean
      btn1_bg_color: string
      btn1_text: string
      btn1_deep_link: string
      btn1_deep_link_custom?: string
      btn1_deep_link_url: string
      has_btn2: boolean
      btn2_text: string
      btn2_deep_link: string
      btn2_deep_link_custom?: string
      btn2_deep_link_url: string
    }
    translate_list: TranslateItem[]
    rec_type?: number
  }

  interface PreviewReplaceResult {
    language: string
    title: string
    body: string
  }


  interface Config {
    lang_items: {
      [key: string]: string
    }
  }

  interface PushNotificationSearchOptionNew {

    platform?: number
    name?: string
    activity_type?: number
    activity_status?: number
      page_info: {
        page_index?: number
        page_size?: number// 选填参数
      }
    }
}

