<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">

      <el-form ref="formRef" :rules="formRules" :model="formData" label-width="120px">
        <h2>新建活动</h2>
        <div class="mt-4">
          <div class="flex flex-wrap w-[800px]">

            <el-form-item required label="活动名称" class="w-[350px]">
              <el-input v-model="formData.name" placeholder="活动名称" />
            </el-form-item>
            <el-form-item label="活动类型" class="w-[350px]">
              <el-select class="!w-[150px]" v-model="formData.activity_type" placeholder="活动类型">
                <el-option v-for="(item, key) in activityTypeOptions" :key="key" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item required label="应用" class="w-[350px]">
              <el-select class="!w-[150px]" v-model="formData.platform" placeholder="应用">
                <el-option v-for="(item, key) in appTypeOptions" :key="key" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item required label="活动开始时间" class="w-[350px]">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" v-model="formData.start_time" type="datetime"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="用户分层" class="w-[350px]">
              <el-select filterable class="!w-[150px]" multiple clearable v-model="formData.strategy_layer_ids"
                placeholder="用户分层">
                <el-option v-for="(item, key) in list" :key="key" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item required label="活动结束时间" class="w-[350px] items-start">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" v-model="formData.end_time" type="datetime"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="展示页面" class="w-[350px]">
              <el-select filterable class="!w-[150px]" v-model="formData.display_page" placeholder="展示页面">
                <el-option v-for="(item, key) in activityPageOptions" :key="key" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <h2>活动内容配置</h2>
        <div class="w-[800px] mt-4 pt-2">

          <el-button type="primary" class="w-[400px]">评论入口配置</el-button>
          <div class="bg-[rgba(0,0,0,0.3)] rounded-md px-4 py-4">
            <div class="flex flex-wrap">
              <el-form-item label="活动角标" class="w-[350px]">
                <div class=" flex gap-1">
                  <div>
                    <el-input v-model="formData.activity_icon[0]['text']" placeholder="活动角标" />
                  </div>
                  <el-button type="primary" size="mini" @click="openPop('activity_icon')">多语言</el-button>
                </div>
              </el-form-item>
              <el-form-item label="评论入口文案" class="w-[350px] flex">

                <div class=" flex gap-1">
                  <div>
                    <el-input v-model="formData.activity_title[0]['text']" placeholder="评论入口文案" />
                  </div>
                  <el-button type="primary" size="mini" @click="openPop('activity_title')">多语言</el-button>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="flex gap-2">
          <div class=" w-[400px]">
            <div class="w-full">
              <el-button type="primary" class="w-full" @click="addCard">新增卡片</el-button>
            </div>
            <div class="mt-4">
              <div v-for="(item, key) in formData.activity_cards" class="px-3 py-3 !bg-[#eee] border rounded mb-2"
                :key="key">

                <el-form-item label-width="90px" label="标题" class="w-[350px]">
                  <div class=" flex gap-1">
                    <div>
                      <el-input v-model="item['title'][0].text" placeholder="标题" />
                    </div>
                    <el-button type="primary" size="mini" @click="openPop('activity_cards', key, 'title')">多语言</el-button>
                  </div>
                </el-form-item>
                <el-form-item label-width="90px" label="内容" class="w-[350px]">
                  <div class=" flex gap-1">
                    <div>
                      <el-input v-model="item['content'][0].text" placeholder="内容" />
                    </div>
                    <el-button type="primary" size="mini"
                      @click="openPop('activity_cards', key, 'content')">多语言</el-button>
                  </div>
                </el-form-item>
                <el-form-item label-width="90px" label="卡片背景色" class="w-[350px]">
                  <el-input v-model="item['background_color']" placeholder="内容" />
                </el-form-item>
                <el-form-item label-width="90px" label="展示顺序" class="w-[350px]">
                  <el-input v-model.number="item['index']" placeholder="内容" />
                </el-form-item>
                <div class="flex justify-end">
                  <el-button type="danger" size="mini" @click="deleteCard(key, item)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
          <div class=" w-[400px]">
            <div class="w-full">
              <el-button type="primary" class="w-full" @click="addRewardCard">中奖名单卡片</el-button>
            </div>
            <div class="mt-4">
              <div v-for="(item, key) in formData.activity_reward_cards" class="px-3 py-3 !bg-[#eee] border rounded mb-2"
                :key="key">

                <el-form-item label-width="90px" label="标题" class="w-[350px]">
                  <div class=" flex gap-1">
                    <div>
                      <el-input v-model="item['title'][0].text" placeholder="标题" />
                    </div>
                    <el-button type="primary" size="mini"
                      @click="openPop('activity_reward_cards', key, 'title')">多语言</el-button>
                  </div>
                </el-form-item>
                <el-form-item label-width="90px" label="内容" class="w-[350px]">
                  <div class=" flex gap-1">
                    <div>
                      <el-input v-model="item['content'][0].text" placeholder="内容" />
                    </div>
                    <el-button type="primary" size="mini"
                      @click="openPop('activity_reward_cards', key, 'content')">多语言</el-button>
                  </div>
                </el-form-item>
                <el-form-item label-width="90px" label="卡片背景色" class="w-[350px]">
                  <el-input v-model="item['background_color']" placeholder="内容" />
                </el-form-item>
                <el-form-item label-width="90px" label="展示顺序" class="w-[350px]">
                  <el-input v-model.number="item['index']" placeholder="内容" />
                </el-form-item>
                <div class="flex justify-end">
                  <el-button type="danger" size="mini" @click="deleteRewardCard(key, item)">删除</el-button>
                </div>
              </div>
            </div>

          </div>
        </div>
        <div style="margin-top: 32px;">
          <el-form-item label-width="120px" label="活动图片" class="w-[650px]">
            <div class=" flex gap-1">
              <div>
                <el-input v-model="formData.activity_image[0].url" placeholder="活动图片" />
              </div>
              <el-button type="primary" size="mini" @click="openPop('activity_image')">多语言</el-button>
            </div>
          </el-form-item>
          <el-form-item label-width="120px" label="活动底图背景色" class="w-[650px]">
            <div class=" flex gap-1">
              <el-input v-model="formData.background_color" placeholder="活动底图背景色" />
            </div>
          </el-form-item>
        </div>
        <div class="flex justify-end">
          <el-button size="mini" @click="pageBack">取消</el-button>
          <el-button size="mini" type="primary" @click="pageConfirm">确定</el-button>
        </div>
      </el-form>
    </div>
    <el-dialog title="多语言" width="60%" v-model="showDialog">
      <div class="mt-4 max-h-[400px] overflow-y-auto">
        <div class="flex w-full justify-end text-[12px] text-[#666] "
          v-if="currentPopType == 'activity_reward_cards' && currentPopChildType == 'content'">
          <span class="px-[50px]">多行请换行，每行内容：名字/奖品，中间用/分割</span>
        </div>
        <div class="flex gap-1" v-for="(inItem, inKey) in currentLanPopData.lanList" :key="inKey">
          <el-form-item label-width="90px" label="应用语言" class="w-[350px]">
            <el-select class="!w-[150px]" v-model="inItem.lang" placeholder="语言">
              <el-option v-for="(lanItem, lanKey) in formatLansOptions(config.lang_items)" :key="lanKey" :label="lanItem"
                :value="lanKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="90px" label="图标" class="w-[350px]"
            v-if="['activity_image'].includes(currentPopType)">
            <div class="flex justify-center">
              <el-image v-if="inItem.url" style="width: 50px; height: 50px;margin-right: 30px;" :src="inItem.url"
                :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[inItem.url]" :initial-index="4"
                fit="cover" />
              <!-- <el-input v-model="inItem.url" placeholder="图标" /> -->
              <Uploader accept="png,jpg,jpeg" :maxsize="1024 * 1024 * 10"
                class=" border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer" @UploadSuccess="d => {
                  inItem.url = d.temp_path?.includes('https://') ? d.temp_path : 'https://static-v1.mydramawave.com/popup/image/' + d.temp_path
                }" :isImage="true" :multiple="false" uploadUrl="/popup/upload/image">
                <el-button>添加图片</el-button>
                <!-- <span class="size-full flex items-center justify-center">上传图片</span> -->
              </Uploader>
            </div>
          </el-form-item>
          <el-form-item label-width="90px" label="文案内容" class="w-[350px]" v-else>
            <el-input type="textarea" v-model="inItem.text" placeholder="内容" />
          </el-form-item>

          <el-button type="danger" size="mini" @click="deleteLan(inKey)">删除</el-button>
        </div>

      </div>

      <div class="flex justify-center gap-4">
        <el-button type="primary" size="mini" @click="allLanDo">全语言</el-button>
        <el-button type="success" size="mini" @click="addLanDo">新增</el-button>
      </div>

      <div class="flex justify-end gap-4" style="margin-top: 20px">
        <el-button size="mini" @click="cancelPop">取消</el-button>
        <el-button type="primary" size="mini" @click="confirmPop">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
// // import AddForm from './notification-add-form.vue'
// // import Preview from './notification-preview.vue'
import { useNotification } from './use-notification';
import dayjs from 'dayjs';
import { ElMessage, ElDialog } from 'element-plus';
// import { openDialog } from '../../../../../packages/ui/dialog';
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { useRoute, useRouter } from 'vue-router'

import { apiSaveAcitve, apiUpdateAcitve, apiGetAcitve } from './activity-h5-admin-api'
import { formatDate } from '@vueuse/core';
import type { FormInstance, FormRules } from 'element-plus'

import { Uploader } from '../common/uploader/uploader'

const { list, page, pageSize, search } = useUserStrategyLayer()

const {
  appTypeOptions,
  activityTypeOptions,
  activityPageOptions,
  activityStatusOptions, getLangCode, config, getConfig, onSearchActivityList, loading, searchForm, total, form, onDelete, onDownload, initFromData, onReset, isUpdating } = useNotification('manual')

interface titleItem {
  lang: string
  text?: string
  url?: string
}

interface cardItem {
  id?: number
  index?: number
  background_color?: string
  card_type?: number
  title?: titleItem[]
}

const titleItem = {
  lang: '',
  text: '',
  url: ''
}

interface ruleFormItem {

  id?: number | undefined
  name?: string
  activity_type?: number
  platform?: string
  start_time?: string
  end_time?: string
  strategy_layer_ids?: number[]
  display_page?: number
  activity_icon?: titleItem[]
  activity_title?: titleItem[]
  activity_cards?: cardItem[]
  activity_reward_cards?: cardItem[]
  activity_image?: titleItem[]
  delete_activity_cards: number[]
  background_color?: string
  description?: string
  lang?: string
  link?: string
}


const route = useRoute()
const router = useRouter()

interface activityItem {
  index: number,
  background_color: string
  title: titleItem[]
}
const activityItem = {
  index: 1,
  background_color: '#cc3333',
  title: [{ ...titleItem }],
  content: [{ ...titleItem }],
  id: 0,
  card_type: 1
}

const formRef = ref<FormInstance>()

const showDialog = ref(false)
const currentLanPopData = reactive({
  lanList: [] as titleItem[]
})
const currentPopType = ref('')
const currentPopIndex = ref(-1)
const currentPopChildType = ref('')
const formData = reactive<ruleFormItem>({
  id: undefined,
  name: '',
  activity_type: 1,
  platform: '',
  start_time: '',
  end_time: '',
  strategy_layer_ids: [],
  display_page: 1,
  activity_icon: [{ lang: 'en', url: '' }],
  activity_title: [{ lang: 'en', text: '' }],
  activity_cards: [] as cardItem[],
  activity_reward_cards: [] as cardItem[],
  activity_image: [{ lang: 'en', url: '' }],
  delete_activity_cards: [],
  background_color: '',
  description: '',
  lang: '',
  link: ''
})

const formRules = reactive<FormRules<ruleFormItem>>({
  name: [{ required: true, message: '请输入', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择', trigger: 'blur' }],
  start_time: [{ required: true, message: '请选择', trigger: 'blur' }],
  end_time: [{ required: true, message: '请选择', trigger: 'blur' }]
})


const addCard = () => {
  formData.activity_cards.push(JSON.parse(JSON.stringify({ ...activityItem, card_type: 1 })))
}
const addRewardCard = () => {
  console.log(12313)
  if (formData.activity_reward_cards.length == 0) {
    formData.activity_reward_cards.push(JSON.parse(JSON.stringify({ ...activityItem, card_type: 2 })))
  }

}

const formatSubmitLang = () => {
  let arr = []
  for (let i in formData.activity_title) {
    arr.push(formData.activity_title[i]['lang'])
  }
  return arr.join(',')
}

const pageConfirm = () => {
  if (!formData.name) {
    ElMessage.error('请输入名称')
    return false
  }
  if (!formData.platform) {
    ElMessage.error('请选择应用')
    return false
  }
  if (!formData.start_time) {
    ElMessage.error('请选择开始时间')
    return false
  }
  if (!formData.end_time) {
    ElMessage.error('请选择结束时间')
    return false
  }

  formData.lang = formatSubmitLang()
  const submitData = {
    ...formData,
    activity_cards: formData.activity_cards.concat(formData.activity_reward_cards)
  }
  if (!route.params.id) {
    apiSaveAcitve(submitData).then(res => {
      if (res.code == 200) {
        ElMessage.success('保存成功')
        router.back()
      } else {
        ElMessage.error(res.message)
      }
    })
  } else {
    apiUpdateAcitve(submitData).then(res => {
      if (res.code == 200) {
        ElMessage.success('保存成功')
        router.back()
      } else {
        ElMessage.error(res.err_msg)
      }
    })
  }
}

const formatLansOptions = (options: any) => {
  let obj = {}
  for (let i in options) {
    obj[i.split('-')[0]] = options[i]
  }
  return obj
}

const pageBack = () => {
  router.back()
}

const deleteCard = (val: number, item: any) => {
  formData.activity_cards.splice(val, 1)
  formData.delete_activity_cards.push(item.id)
}

const deleteRewardCard = (val: number, item: any) => {
  formData.activity_reward_cards.splice(val, 1)
  formData.delete_activity_cards.push(item.id)
}


const cancelPop = () => {
  showDialog.value = false
}

const confirmPop = () => {
  if (currentPopType.value == 'activity_cards') {

    formData[currentPopType.value][currentPopIndex.value][currentPopChildType.value] = cloneDeep(currentLanPopData.lanList)
    cancelPop()
  } else if (currentPopType.value == 'activity_reward_cards') {
    formData['activity_reward_cards'][0][currentPopChildType.value] = cloneDeep(currentLanPopData.lanList)
    cancelPop()
  } else {

    formData[currentPopType.value] = cloneDeep(currentLanPopData.lanList)
    cancelPop()
  }

}

const addLanDo = () => {
  console.log(config.value.lang_items)
  if (currentLanPopData.lanList.length < Object.keys(config.value.lang_items).length) {
    currentLanPopData.lanList.push(JSON.parse(JSON.stringify(titleItem)))
  }
}

const openPop = (val: string, index?: number, childType?: string) => {
  currentPopType.value = val
  if (val == 'activity_cards' || val == 'activity_reward_cards') {
    currentPopIndex.value = index
    currentPopChildType.value = childType
    currentLanPopData.lanList = cloneDeep(formData[val][index][childType])
    showDialog.value = true
  } else {
    currentPopIndex.value = -1
    currentPopChildType.value = ''
    currentLanPopData.lanList = cloneDeep(formData[val])
    showDialog.value = true
  }
}

const deleteLan = (val: any) => {
  currentLanPopData.lanList.splice(val, 1)
}

const allLanDo = () => {
  let arr = []
  for (let i in config.value.lang_items) {
    if (['activity_image'].includes(currentPopType.value)) {

      arr.push({ lang: i.split('-')[0], url: '' })
    } else {
      arr.push({ lang: i.split('-')[0], text: '' })
    }
  }
  currentLanPopData.lanList = JSON.parse(JSON.stringify(arr))
}

const openDialog = () => {
  showDialog.value = true
  currentLanPopData.lanList = [titleItem]
}

const getPageDetail = () => {
  apiGetAcitve({ id: route.params.id }).then(res => {
    if (res?.data) {
      formData.id = res.data?.id || undefined
      formData.name = res.data?.name || ''
      formData.activity_type = res.data?.activity_type || 1
      formData.platform = res.data?.platform || ''
      formData.start_time = res.data?.start_time || ''
      formData.end_time = res.data?.end_time || ''
      formData.strategy_layer_ids = res.data?.strategy_layer_ids || []
      formData.display_page = res.data?.display_page || [1]
      formData.activity_icon = res.data?.activity_icon || [{ lang: 'en', url: '' }]
      formData.activity_title = res.data?.activity_title || [{ lang: 'en', text: '' }]
      formData.activity_cards = res.data?.activity_cards || [] as titleItem[]
      formData.activity_reward_cards = res.data?.activity_reward_cards || [] as titleItem[]
      formData.activity_image = res.data?.activity_image || [{ lang: 'en', url: '' }]
      formData.background_color = res.data?.background_color || ''
      formData.lang = res.data?.lang || ''


      let cardLists = res.data?.activity_cards
      let normalCards = []
      let rewardCars = []
      for (let i in cardLists) {
        if (cardLists[i]['card_type'] == 2) {
          rewardCars.push(cardLists[i])
        } else {
          normalCards.push(cardLists[i])
        }
      }
      formData.activity_reward_cards = rewardCars
      formData.activity_cards = normalCards



    }
  })
}
const getPageId = () => {
  console.log(route.params.id)
  if (route.params.id) {
    getPageDetail()
  }
}










// ----------------
onMounted(() => {
  console.log('mounted')
  getConfig();
  getPageId()


  page.value = 1
  pageSize.value = 9999
  void search(page.value)
})

</script>

<style scoped></style>
