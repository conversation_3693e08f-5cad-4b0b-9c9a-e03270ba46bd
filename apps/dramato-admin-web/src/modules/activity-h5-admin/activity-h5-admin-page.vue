<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="应用" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.platform" placeholder="应用">
            <el-option class="w-[150px]" v-for="(item, key) in appTypeOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="活动名称" class="w-[250px]">
          <el-input v-model="searchForm.name" placeholder="活动名称" />
        </el-form-item>
        <el-form-item label="活动类型" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.activity_type" placeholder="活动类型">
            <el-option class="w-[150px]" v-for="(item, key) in activityTypeOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布状态" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.activity_status" placeholder="发布状态">
            <el-option class="w-[150px]" v-for="(item, key) in activityStatusOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <el-button type="primary" @click="addNew">新增活动</el-button>
          <!-- <el-button type="danger" @click="onDeleteMuti">删除</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <!-- <el-table-column type="selection" width="55" /> -->
        <!-- <el-table-column prop="push_type" label="AI/人工" width="100">
          <template #default="scope">
            {{ scope.row.push_type === 2 ? 'AI' : '人工' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="id" label="活动ID" width="80" />
        <el-table-column prop="name" label="活动名称" width="200" />
        <el-table-column prop="push_type" label="活动类型" width="100">
          <template #default="scope">
            {{ activityTypeOptions.find(item => scope.row.activity_type === item.value)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="strategy_layer" label="用户分层" width="200" />
        <el-table-column prop="lang" label="语言" width="200">
          <template #default="scope">
            {{ formatLan(scope.row.lang) }}
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="活动开始时间" width="200" />
        <el-table-column prop="end_time" label="活动结束时间" width="200" />
        <el-table-column prop="operator" label="操作人" width="200" />
        <el-table-column prop="activity_status" label="活动状态" width="100">
          <template #default="scope">
            {{ activityStatusOptions.find(item => scope.row.activity_status === item.value)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="display_page" label="展示广告位" width="100">
          <template #default="scope">
            {{ activityTypeOptions.find(item => scope.row.display_page === item.value)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="display_page" label="链接" width="300">
          <template #default="scope">
            <a :href="'https://mydramawave.com/activity/activity-h5?id=' + scope.row.id + '&language=en'"
              target="_blank">https://mydramawave.com/activity/activity-h5?id={{ scope.row.id }}&language=en</a>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-button type="primary" size="mini" @click="handlePreview(scope.row)">查看</el-button>
              <el-button type="primary" size="mini" @click="handleCopy(scope.row)">复制</el-button>
              <el-button type="primary" size="mini" @click="handleEdit(scope.row)">修改</el-button>
              <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
              <el-button type="danger" v-if="scope.row.activity_status == 2" size="mini"
                @click="handleDownDeploy(scope.row)">下架</el-button>
              <el-button type="success" v-else size="mini" @click="handleDeploy(scope.row)">发布</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_info.page_index"
          v-model:page-size="searchForm.page_info.page_size" :total="total" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
// // import AddForm from './notification-add-form.vue'
// // import Preview from './notification-preview.vue'
import { useNotification } from './use-notification';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'
import { apiSaveAcitve, apiOptionActive, apiGetAcitve } from './activity-h5-admin-api'

const {
  appTypeOptions,
  activityTypeOptions,
  activityStatusOptions, getLangCode, config, getConfig, onSearchActivityList, list, loading, searchForm, total, form, onDelete, onDownload, initFromData, onReset, isUpdating } = useNotification('manual')


const route = useRoute()
const router = useRouter()
const formatLan = (data: any) => {
  let arr = [] as string[]
  let lanObj = config.value.lang_items
  data = data.split(',')
  console.log(config.value)
  for (let i in data) {
    for (let j in lanObj) {
      if (j.includes(data[i])) {
        arr.push(lanObj[j])
      }
    }
  }
  return arr.join(',')
}



// 处理操作方法
const handlePreview = (row: any) => {
  router.push('/activity-h5-admin-detail/' + row.id)
}
const handleEdit = (row: any) => {
  router.push('/activity-h5-admin-detail/' + row.id)
}

const handleCopy = (row: any) => {
  ElMessageBox.confirm('确认复制【' + row.name + '】吗？', '提示').then(() => {
    apiGetAcitve({ id: row.id }).then(res => {
      if (res.code == 200) {
        let subData = res.data
        subData['strategy_layer_ids'] = []
        for (let i in subData['strategy_layer_list']) {
          subData['strategy_layer_ids'].push(subData['strategy_layer_list'][i]['id'])
        }
        apiSaveAcitve({ ...subData, id: undefined }).then(res => {
          if (res.code == 200) {
            ElMessage.success('复制成功')
            searchForm.value.page_info.page_index = 1
            onSearchActivityList();
          }
        })
      } else {
        ElMessage.error(res.message)
      }
    })
  })
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除【' + row.name + '】吗？', '提示').then(() => {
    apiOptionActive({ id: row.id, option_type: 3 }).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success('删除成功')
        onSearchActivityList();
      }
    })
  })
}

const onDeleteMuti = () => {
  console.log(multipleSelection.value)
}

const handleDeploy = (row: any) => {

  ElMessageBox.confirm('确认发布【' + row.name + '】吗？', '提示').then(() => {
    apiOptionActive({ id: row.id, option_type: 1 }).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success('发布成功')
        onSearchActivityList();
      }
    })
  })
}

const handleDownDeploy = (row: any) => {

  ElMessageBox.confirm('确认下架【' + row.name + '】吗？', '提示').then(() => {
    apiOptionActive({ id: row.id, option_type: 2 }).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success('下架成功')
        onSearchActivityList();
      }
    })
  })
}



const addNew = () => {
  router.push('/activity-h5-admin-detail')
}













// ----------------
onMounted(() => {
  console.log('mounted')
  getConfig();
  onSearchActivityList();

  let lanList = getLangCode()
  console.log(lanList)
})
const multipleSelection = ref<M.ActivityItem[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchActivityList();
}



const handleSelectionChange = (val: M.ActivityItem[]) => {
  multipleSelection.value = val
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_info.page_index = val
  onSearchActivityList()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_info.page_size = val
  onSearchActivityList()
}

</script>

<style scoped></style>