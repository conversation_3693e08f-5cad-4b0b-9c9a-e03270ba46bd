import { createComponent, useValidator } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { CreateTableOld, openDialog, CreateForm, TableColumnOld, Pager, transformNumber, showFailToast, showSuccessToast } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import dayjs from 'dayjs'
import { lang, statusList } from 'src/lib/constant.ts'
import { RouterLink } from 'vue-router'
import { z } from 'zod'
import { apiGetAppList, apiCreateApp, apiGetAppOptions } from './application.api'
import { set, get } from 'lodash-es'
import { AxiosError } from 'axios'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'

type ApplicationPageOptions = {
  props: {}
}

export const ApplicationPage = createComponent<ApplicationPageOptions>(
  null,
  () => {
    const page = ref<number>(1)
    const pageSize = ref<number>(10)
    const total = ref<number>(0)
    const offset = ref<number>(0)
    const hasMore = ref<boolean>(true)
    const options = ref<M.Application[]>([])
    const searchLoading = ref(false)
    const list = ref<M.Application[]>()
    const formData = ref({
      app_name: '',
      app_key: '',
      page_info: {
        offset: 1,
        size: 10,
      },
    })

    const form = ref<M.Application>({
      app_key: '',
      app_name: '',
      platform: 1,
      supported_language: ['English'],
      status: 2,
    })

    const dialogRef = ref(() => {})
    const Table = CreateTableOld<M.Application>()

    const Form = CreateForm<{
      app_key: string
      app_name: string
      platform: number
      supported_language: string[]
      status: number
    }>()

    const QueryForm = CreateForm<{
      app_name: string
    }>()

    const columns: TableColumnOld<M.Application>[] = [
      ['应用ID', 'id'],
      ['应用包名', 'app_key'],
      ['应用名称', 'app_name'],
      ['平台',
        row => <span>{row.platform === 1 ? 'iOS' : 'Android'}</span>,
      ],
      ['状态',
        row => (
          <div class="space-x-1 flex items-center">
            {row.status === 1
              ? (
                  <>
                    <div class="badge bg-green-600 badge-xs" />
                    <div>可用</div>
                  </>
                )
              : (
                  <>
                    <div class="badge bg-gray-300 badge-xs" />
                    <div>禁用</div>
                  </>
                )}
          </div>
        ),
      ],
      [
        '更新时间',
        row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
      ],
      ['更新人', 'updated_operator_name'],
      [
        <span class="px-3">操作</span>,
        row => {
          return (
            <div class="flex flex-nowrap">
              <button class="btn btn-sm btn-link btn-primary">
                <RouterLink to={`/application/${row.id}`}>
                  编辑
                </RouterLink>
              </button>
            </div>
          )
        },
      ],
    ]

    const createAppFormRules = z.object({
      app_key: z.string().min(1, '请输入包名'),
      app_name: z.string().min(1, '请输入应用名称'),
      platform: z.number().min(1, {
        message: '请选择平台',
      }),
      supported_language: z.string().array().nonempty({
        message: '请选择语音',
      }),
      status: z.number({
        message: '请选择状态',
      }),
    })

    const { error, validateAll } = useValidator(form, createAppFormRules)
    const onReset = () => {
      form.value = {
        app_key: '',
        app_name: '',
        platform: 1,
        supported_language: ['English'],
        status: 2,
      }
    }
    const onClose = () => {
      onReset()
    }

    const onSubmit = async () => {
      if (validateAll()) {
        try {
          const res = await apiCreateApp(form.value)

          if (res.code === 200) {
            showSuccessToast('操作成功！')
          } else {
            showFailToast(res?.message || '服务忙碌，稍后再试')
          }
          page.value = 1
          await getApplicationList()
        } catch (e) {
          const error = e as AxiosError
          showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
        }
      } else {
        console.log(error, 'error')
        return
      }
      onClose()
    }

    const showCreateDialog = () => {
      dialogRef.value = openDialog({
        // beforeClose: () => { onClose() },
        title: () => <div>新建应用</div>,
        body: () => (
          <Form
            class="w-[400px] flex flex-col"
            onSubmit={onSubmit}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            error={error.value}
            items={[
              { label: requiredLabel('应用包名：'), path: 'app_key', input: { type: 'text' } },
              { label: requiredLabel('应用名称：'), path: 'app_name', input: { type: 'text' } },
              { label: requiredLabel('平台：'), path: 'platform', input: { type: 'select', autoInsertEmptyOption: false, options: [
                { label: 'iOS', value: 1 },
                { label: 'Android', value: 2 },
              ] }, transform: transformNumber },
              { label: requiredLabel('支持发行语言：'), path: 'supported_language',
                input: {
                  type: 'multi-select',
                  options: lang,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  disabledItemClass: 'opacity-50 cursor-not-allowed',
                  onClear: () => {
                    form.value.supported_language = ['English']
                  },
                },
              },
              { label: requiredLabel('状态：'), path: 'status', input: { type: 'radio', options: statusList }, transform: transformNumber },
            ]}
          />
        ),
      })
    }

    const asyncFind = async (query: string) => {
      searchLoading.value = true
      if (!query) {
        formData.value.app_name = ''
        page.value = 1
        options.value = []
        await getApplicationList()
        searchLoading.value = false
        return
      }
      const res = await apiGetAppOptions({
        app_name: query,
      })
      searchLoading.value = false
      options.value = res.data?.allData || []
    }

    const getApplicationList = async () => {
      offset.value = pageSize.value * (page.value - 1)
      try {
        const res = await apiGetAppList({
          page_info: {
            offset: offset.value,
            size: pageSize.value,
          },
          app_name: formData.value.app_name,
        })
        list.value = res.data?.list || []
        total.value = res.data?.page_info.total || 0
        hasMore.value = res.data?.page_info.has_more || false
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || '服务忙碌，稍后再试')
      }
    }

    const onPageChange = async (n: number) => {
      page.value = n
      await getApplicationList()
    }

    const onPageSizeChange = async (n: number) => {
      pageSize.value = n
      page.value = 1
      offset.value = 0
      await getApplicationList()
    }

    onMounted(async () => {
      await getApplicationList()
    })

    return () => (
      <div class="space-y-4">
        <NavFormTablePager>{{
          nav: () => (
            <ul>
              <li>应用管理</li>
            </ul>
          ),
          form: () => (
            <QueryForm
              class="w-full flex flex-row"
              // onSubmit={onSubmit}
              // onReset={onReset}
              actions={[]}
              data={form.value}
              onChange={(path, value) => {
                set(form.value, path, value)
              }}
              items={[
                { label: '应用名称：', path: 'app_name', input: { type: 'custom', render: () => {
                  return (
                    <multiselect
                      class="flex-1 min-w-[200px]"
                      modelValue={formData.value}
                      onUpdate:modelValue={async (value: { app_name: string }) => {
                        page.value = 1
                        formData.value.app_name = value.app_name
                        await getApplicationList()
                      }}
                      loading={searchLoading.value}
                      showLabels
                      selectLabel=""
                      options={options.value}
                      multiple={false}
                      close-on-select={true}
                      clear-on-select={false}
                      preserve-search={true}
                      preselect-first={false}
                      allow-empty={true}
                      placeholder="请输入应用名称"
                      label="app_name"
                      track-by="app_key"
                      onSearch-change={(query: string) => asyncFind(query)}
                      v-slots={{
                        noOptions: () => (
                          <div>请输入应用名</div>
                        ),
                        noResult: () => {
                          return <div>暂无数据</div>
                        },
                        selection: (values: M.Application[]) => {
                          return values.length ? <span class="multiselect__single">{ values.length }</span> : ''
                        },
                      }}
                    />
                  )
                } } },
              ]}
            />
          ),
          tableActions: () => (
            <x-table-actions class="flex justify-between items-center">
              <span>应用列表</span>
              <button class="btn btn-primary btn-sm" onClick={() => {
                form.value = {
                  app_key: '',
                  app_name: '',
                  platform: 1,
                  supported_language: ['English'],
                  status: 2,
                }
                showCreateDialog()
              }}
              >新建应用
              </button>
            </x-table-actions>
          ),
          table: () => (
            <Table
              list={list.value || []}
              columns={columns}
            />
          ),
          pager: () => (
            <Pager class="justify-end" v-model:page={page.value} v-model:size={pageSize.value} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
          ),
        }}
        </NavFormTablePager>
      </div>
    )
  },
)

export default ApplicationPage
