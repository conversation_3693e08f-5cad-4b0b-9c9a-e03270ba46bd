import { createComponent, useValidator } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { CreateForm, transformNumber, showFailToast, showSuccessToast, transformInteger } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import dayjs from 'dayjs'
import { lang, statusList } from 'src/lib/constant.ts'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { apiGetAppById, apiUpdateApp } from './application.api'
import { AxiosError } from 'axios'
import { z } from 'zod'
import { set, get } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'

type ApplicationDetailPageOptions = {
  props: {}
}

export const ApplicationDetailPage = createComponent<ApplicationDetailPageOptions>(null, () => {
  const route = useRoute()
  const router = useRouter()
  const applicationDetail = ref<M.Application>({
    app_key: '',
    app_name: '',
    platform: 1,
    supported_language: ['English'],
    status: 1,
    created: 0,
    updated: 0,
    created_operator_name: '',
    updated_operator_name: '',
  })

  const Form = CreateForm<M.Application>()

  const editAppFormRules = z.object({
    app_key: z.string().min(1, '请输入包名'),
    app_name: z.string().min(1, '请输入应用名称'),
    platform: z.number({
      message: '请选择平台',
    }),
    supported_language: z.string().array().nonempty({
      message: '请选择语音',
    }),
    status: z.number({
      message: '请选择状态',
    }),
  })
  const { error, validateAll } = useValidator(applicationDetail, editAppFormRules)

  const save = async () => {
    if (validateAll()) {
      try {
        const res = await apiUpdateApp(applicationDetail.value)
        if (res.data) {
          showSuccessToast('操作成功！')
          await router.replace('/application')
        } else {
          showFailToast('操作失败！')
        }
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
      }
    } else {
      console.log(error, 'error')
      return
    }
  }

  onMounted(async () => {
    const id = route.params.id as string
    try {
      const res = await apiGetAppById(id)
      applicationDetail.value = res.data as M.Application
    } catch (e) {
      const error = e as AxiosError
      showFailToast(error.message || '服务忙碌，稍后再试')
    }
  })

  return () => (
    <div>
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li><RouterLink to="/application">应用管理</RouterLink></li>
            <li>应用详情</li>
          </ul>
        ),
        form: () => (
          <>
            <div class="border-l-[3px] border-l-solid leading-[1] font-semibold border-l-primary pl-2 mt-2 mb-4 text-xl">
              基本信息
            </div>
            <Form
              class="w-[680px] grid grid-cols-3 pb-4"
              onSubmit={save}
              data={applicationDetail.value}
              onChange={(path, value) => {
                set(applicationDetail.value, path, value)
              }}
              error={error.value}
              actions={[(
                <>
                  <button class="btn btn-primary btn-sm" type="submit">提交</button>
                  <button class="btn btn-default btn-sm" onClick={() => router.replace('/application')}>返回</button>
                </>
              )]}
              items={[
                { label: requiredLabel('应用包名：'), path: 'app_key', input: { type: 'text', disabled: true } },
                { label: requiredLabel('应用名称：'), path: 'app_name', input: { type: 'text' } },
                { label: requiredLabel('平台：'), path: 'platform', input: { type: 'select', options: [
                  { label: 'iOS', value: 1 },
                  { label: 'Android', value: 2 },
                ], disabled: true } },
                { label: requiredLabel('支持发行语言：'), path: 'supported_language', input: {
                  type: 'multi-select',
                  options: lang,
                  disabledItemClass: 'opacity-50 cursor-not-allowed',
                  onClear: () => {
                    applicationDetail.value.supported_language = ['English']
                  },
                } },
                { label: requiredLabel('状态：'), path: 'status', input: { type: 'radio', options: statusList }, transform: transformInteger },
                { label: requiredLabel('创建时间：'), path: 'created', input: { type: 'custom',
                  render: () => (
                    <div class="input input-bordered input-sm w-full max-w-xs">{
                      !!applicationDetail.value.created ? dayjs(applicationDetail.value.created * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'
                    }
                    </div>
                  ) } },
                { label: requiredLabel('创建人：'), path: 'created_operator_name', input: { type: 'text', disabled: true } },
                { label: requiredLabel('更新时间：'), path: 'updated', input: { type: 'custom', render: () => (
                  <div class="input input-bordered input-sm w-full max-w-xs">
                    {!!applicationDetail.value.updated ? dayjs(applicationDetail.value.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </div>
                ) } },
                { label: requiredLabel('更新人：'), path: 'updated_operator_name', input: { type: 'text', disabled: true } },
              ]}
            />
          </>
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ApplicationDetailPage
