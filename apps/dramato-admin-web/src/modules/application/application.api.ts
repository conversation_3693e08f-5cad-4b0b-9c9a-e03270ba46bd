import { httpClient } from 'src/lib/http-client'

type PageInfo = {
  offset: number
  size: number
  has_more: boolean
  total: number
}

type AppListParams = {
  page_info: {
    offset: number
    size: number
  }
  app_name: string
}

export const apiGetAppList = (data: AppListParams) =>
  httpClient.post<ApiResponse<{
    list: M.Application[]
    page_info: PageInfo
  }>>('/backend/app/list', data)

export const apiCreateApp = (data: M.Application) =>
  httpClient.post<ApiResponse<boolean>>('/backend/app/create', data)

export const apiUpdateApp = (data: M.Application) =>
  httpClient.post<ApiResponse<boolean>>('/backend/app/update', data)

export const apiGetAppById = (id: string) =>
  httpClient.get<ApiResponse<M.Application>>('/backend/app/detail', { id })

export const apiGetAppOptions = async (data: { app_name: string }) => {
  const rs = await httpClient.post<ApiResponse<{
    list: Array<Required<M.Application>>
    allData: Array<Required<M.Application>>
  }>>('/backend/app/select', data)

  return {
    code: rs.code,
    message: rs.message,
    data: {
      ...rs.data,
      list: rs?.data?.list.filter(i => i.status === 1),
      allData: rs?.data?.list,
    },
  }
}
