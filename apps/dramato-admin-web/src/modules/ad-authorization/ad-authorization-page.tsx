import { createComponent } from '@skynet/shared'
import { onBeforeMount, onUnmounted, ref, nextTick } from 'vue'
import { Button } from '@skynet/ui'
import { ElTabs, ElTabPane } from 'element-plus'

declare global {
  interface Window {
    fbAsyncInit: () => void
    FB: {
      init: (config: FbInitConfig) => void
      login: (
        callback: (response: FbAuthResponse) => void,
        options: FbLoginOptions
      ) => void
      getLoginStatus: (callback: (response: FbAuthResponse) => void) => void
      api: (path: string, params: FbApiParams, callback?: (response: any) => void) => Promise<any>
      Event: {
        subscribe: (event: string, callback: (response?: any) => void) => void
      }
    }
  }
}

interface FbInitConfig {
  appId: string
  cookie?: boolean
  xfbml?: boolean
  version: string
  autoLogAppEvents?: boolean
}

interface FbLoginOptions {
  scope: string
  auth_type?: 'reauthorize'
  config_id?: string
}

interface FbApiParams {
  fields?: string
  access_token?: string
}

interface FbAuthResponse {
  status: 'connected' | 'not_authorized' | 'unknown'
  authResponse?: {
    accessToken: string
    expiresIn: number
    signedRequest: string
    userID: string
  }
}

type Options = {
  props: {
    businessConfigId: string
    onAuthSuccess?: (businessInfo: any) => void
  }
}

let isSDKLoaded = false
let fbInitPromise: Promise<void> | null = null

export const ADAuthorization = createComponent<Options>({
  props: {},
}, props => {
  const fbStatus = ref('')
  const tips = ref('')
  const authResponse = ref('')
  const buttonContainer = ref<HTMLElement>()

  const activeTab = window.location.search.includes('tikTok') ? ref('tikTok') : ref('facebook')

  const loadFacebookSDK = (): Promise<void> => {
    if (fbInitPromise) return fbInitPromise

    fbInitPromise = new Promise<void>(resolve => {
      if (isSDKLoaded) return resolve()

      const existingScript = document.getElementById('facebook-jssdk')
      if (existingScript) {
        existingScript.onload = () => resolve() // 添加已有脚本的加载监听
        return
      }

      const script = document.createElement('script')
      script.src = 'https://connect.facebook.net/en_US/sdk.js'
      script.async = true
      script.defer = true
      script.crossOrigin = 'anonymous'
      script.id = 'facebook-jssdk'
      document.head.prepend(script)

      window.fbAsyncInit = () => {
        window.FB.init({
          appId: '1289684386494277',
          cookie: true,
          xfbml: false,
          version: 'v22.0',
          autoLogAppEvents: true,
        })
        isSDKLoaded = true
        resolve() // 只在这里resolve，不执行状态检查
      }
    })

    return fbInitPromise
  }
  const checkFacebookStatus = async () => {
    await loadFacebookSDK()
    return new Promise<void>(resolve => {
      window.FB.getLoginStatus((response: FbAuthResponse) => {
        statusChangeCallback(response)
        resolve()
      })
    })
  }

  // 全局回调安全绑定
  const bindGlobalCallbacks = () => {
    window.checkLoginState = () => {
      window.FB.getLoginStatus((response: FbAuthResponse) => {
        statusChangeCallback(response)
      })
    }
  }

  // 清除全局回调
  const cleanupGlobalCallbacks = () => {
    delete window.checkLoginState
  }

  const statusChangeCallback = (response: FbAuthResponse) => {
    tips.value = `Status: ${response.status}`
    authResponse.value = 'Thanks for statusChangeCallback, ' + JSON.stringify(response)
    if (response.status === 'connected') {
      getMeInfo()
    } else {
      fbStatus.value = response.status === 'not_authorized'
        ? '请完成授权'
        : '未检测到登录状态'
    }
  }

  const getMeInfo = async () => {
    await window.FB.api('/me', { fields: 'name,email' }, (response: any) => {
      fbStatus.value = 'Thanks for logging in, ' + response.name + '! ' + JSON.stringify(response)
    })
  }

  onBeforeMount(async () => {
    bindGlobalCallbacks()
    await checkFacebookStatus() // 每次进入都检查状态

    nextTick(() => {
      if (buttonContainer.value) {
        window.FB.XFBML.parse(buttonContainer.value)
      }
    })
  })

  onUnmounted(() => {
    cleanupGlobalCallbacks()
    fbStatus.value = ''
    tips.value = ''
  })

  const TtClick = () => {
    const redirect_uri = encodeURIComponent(window.location.href + '?type=tikTok')
    window.location.href = `https://business-api.tiktok.com/portal/auth?app_id=7510077812347240449&state=your_custom_params&redirect_uri=${redirect_uri}`
  }

  return () => (
    <div class="pt-[40px] pl-[30px]">
      {/* 添加唯一容器引用 */}
      <ElTabs v-model={activeTab.value}>
        <ElTabPane label="Facebook授权" name="facebook">
          <div ref={buttonContainer}>
            <div
              class="fb-login-button"
              data-config_id="1189061949616566"
              data-scope="public_profile,email"
              data-onlogin="checkLoginState()"
            >FaceBook授权</div>
          </div>

          <div class="mt-[10px]">
            <p class="text-blue-600 mb-[10px]">{ fbStatus.value }</p>
            <p class="">{authResponse.value}</p>
            <p class="text-gray-500 text-sm mt-[20px]">{ tips.value }</p>
          </div>
        </ElTabPane>
        <ElTabPane label="TikTok授权" name="tikTok">
          <Button class="btn-primary btn btn-sm" onClick={TtClick}>TikTok授权</Button>
        </ElTabPane>
      </ElTabs>
    </div>
  )
})

export default ADAuthorization
