import { ref } from 'vue'

const checked = ref<number[]>([])
const isCheckAll = ref(false)

const checkedAllChange = (value: boolean, list: M.ContentTemplateTableItem[]) => {
  isCheckAll.value = value
  const ids = list.map(row => row.id)
  if (isCheckAll.value) {
    checked.value = ids
  } else {
    checked.value = []
  }
}

const checkboxChange = (value: boolean, id: number, list: M.ContentTemplateTableItem[]) => {
  if (value) {
    if (!checked.value.includes(id)) checked.value.push(id)
    if (list.length === checked.value.length) {
      isCheckAll.value = true
    }
  } else {
    const rowIndex = checked.value.findIndex(rowId => rowId === id)
    if (rowIndex !== -1) {
      checked.value.splice(rowIndex, 1)
      isCheckAll.value = false
    }
  }
}

export const useCheck = () => {
  return {
    checked, isCheckAll, checkedAllChange, checkboxChange,
  }
}
