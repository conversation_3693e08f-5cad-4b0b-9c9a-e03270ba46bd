import { createComponent, mc } from '@skynet/shared'
import { CreateTableOld, TableColumnOld, Pager, showFailToast, Button, openDialog, Checkbox, showSuccessToast, Icon } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { get } from 'lodash-es'
import { AxiosError } from 'axios'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiGetContentTemplateById, apiGetContentTemplateSeriesList, apiContentTemplateSeriesSortChange, apiContentTemplateDelSeries } from './content-template-api'
import { useRoute, RouterLink } from 'vue-router'
import { TemplateSeriesDialog } from './template-series-dialog'
import { useCheck } from './use-check'
import { ContentTemplatePreview } from './content-template-preview'

type TemplateDetailOptions = {
  props: {}
}

export const TemplateDetailPage = createComponent<TemplateDetailOptions>({
  props: {},
}, props => {
  const dialogMainClass = 'h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'
  const { checked, isCheckAll, checkedAllChange, checkboxChange } = useCheck()
  const route = useRoute()
  const contentTemplateId = ref(route.params.id as string)
  const contentTemplate = ref<M.ContentTemplate>()
  const refresh = ref(0)
  const delLoading = ref(false)
  const stateMap: Record<number, string> = {
    '-1': '已下架',
    1: '未上架',
    2: '待上架',
    3: '已上架',
    4: '待下架',
  }

  const sortMap: Record<number, string> = {
    1: '人工排序',
    2: '算法排序',
  }

  const Table = CreateTableOld<M.ContentTemplateTableItem>()
  const list = ref<M.ContentTemplateTableItem[]>([])
  const tableLoading = ref(false)
  const total = ref(0)
  const dialogRef = ref(() => {})

  const onPageSizeChange = async (size: number) => {
    queryForm.value.page_info.page_size = size
    await getList()
  }
  const onPageChange = async (page: number) => {
    queryForm.value.page_info.page_index = page
    await getList()
  }

  const onClose = () => {
    dialogRef.value()
  }

  const onBatchDelete = () => {
    const hideDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>确认删除选中的 {checked.value.length}个短剧 吗？</x-delete-episode-body>
          <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
            <Button class="btn btn-default btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
            <Button class={mc('btn btn-primary btn-sm', delLoading.value ? 'btn-disabled' : '')} disabled={delLoading.value} onClick={async () => {
              try {
                delLoading.value = true
                await apiContentTemplateDelSeries({
                  template_id: +contentTemplateId.value,
                  row_ids: checked.value,
                })
                delLoading.value = false
                hideDeleteDialog()
                refresh.value += 1
                await onQuery()
                showSuccessToast('删除成功')
              } catch (e) {
                delLoading.value = false
                const error = e as AxiosError
                showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
              }
            }}
            >
              {delLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </Button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  }

  const onDeleteTemplateSeries = (d: M.ContentTemplateTableItem) => {
    const hideDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>确认删除 【{d.series_name}】吗？</x-delete-episode-body>
          <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
            <Button class="btn btn-default btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
            <Button class={mc('btn btn-primary btn-sm', delLoading.value ? 'btn-disabled' : '')} disabled={delLoading.value} onClick={async () => {
              try {
                delLoading.value = true
                await apiContentTemplateDelSeries({
                  template_id: +contentTemplateId.value,
                  row_ids: [d.id],
                })
                delLoading.value = false
                hideDeleteDialog()
                refresh.value += 1
                await onQuery()
                showSuccessToast('删除成功')
              } catch (e) {
                const error = e as AxiosError
                showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
              }
            }}
            >
              {delLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </Button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  }

  const changePriority = async (row: M.ContentTemplateTableItem, value: string, oldValue: number) => {
    if (+oldValue === +value) return
    try {
      tableLoading.value = true
      const res = await apiContentTemplateSeriesSortChange({
        template_id: +contentTemplateId.value,
        curr_row_id: row.id,
        to_sort_no: +value,
      })
      if (res.code === 200) {
        showSuccessToast('操作成功！')
        refresh.value += 1
        await onQuery()
      } else {
        row.sort_no = oldValue
        showFailToast(res?.message || '服务忙碌，稍后再试')
      }
    } catch (e) {
      row.sort_no = oldValue
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  }

  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      mainClass: dialogMainClass,
      customClass: '!w-[80vw]',
      // beforeClose: () => { onClose() },
      title: () => (
        <div class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/content-template">内容模板</RouterLink></li>
            <li>{contentTemplate.value?.name}</li>
            <li>添加短剧</li>
          </ul>
        </div>
      ),
      body: () => (
        <TemplateSeriesDialog
          template_id={contentTemplateId.value}
          app_id={queryForm.value.app_id}
          language={contentTemplate.value?.language}
          listing_status={[3]}
          onClose={async () => {
            onClose()
            refresh.value += 1
            await onQuery()
          }}
        />
      ),
    })
  }

  const columns: TableColumnOld<M.ContentTemplateTableItem>[] = [
    [
      () => (
        <Checkbox
          label=""
          disabled={list.value?.length === 0}
          modelValue={isCheckAll.value}
          onUpdate:modelValue={(value: boolean) => {
            checkedAllChange(value, list.value)
          }}
        />
      ),
      row => {
        return (
          <Checkbox
            label=""
            modelValue={checked.value.includes(row.id)}
            onUpdate:modelValue={(value: boolean) => {
              const id = row.id
              checkboxChange(value, id, list.value)
            }}
          />
        )
      },
      { class: 'w-[60px]' },
    ],
    ['序号', row => {
      return (
        <input type="number" maxlength={4} value={row.sort_no}
          class={mc('input input-bordered flex items-center gap-1 h-8 w-[50px]', 'grow')}
          onBlur={(e: FocusEvent) => {
            const target = e.target as HTMLInputElement
            const value = target.value
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            changePriority(row, value, row.sort_no)
          }}
        />
      )
    }, { class: 'w-[70px]' }],
    ['短剧ID', 'series_key', { class: 'w-[120px]' }],
    ['短剧名称', 'series_name', {
      class: 'w-[220px]',
    }],
    ['集数', 'episodes', {
      class: 'w-[100px]',
    }],
    // state: number // 状态： -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架
    ['上架状态', row => (
      <span>
        { row.state === -1 ? '已下架' : stateMap[row.state ?? '-1']}
      </span>
    ), { class: 'w-[120px]' }],
    ['排序类型', row => (
      <span>
        { sortMap[row.sort_type ?? '1']}
      </span>
    ), {
      class: 'w-[100px]',
    }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <button class="btn btn-sm btn-link btn-primary" onClick={() => onDeleteTemplateSeries(row)}>
              删除
            </button>
          </div>
        )
      },
      {
        class: 'w-[100px]',
      },
    ],
  ]

  const queryForm = ref<M.QueryContentTemplateDetails>({
    id: +contentTemplateId.value,
    app_id: undefined,
    is_preview: false,
    page_info: {
      page_index: 1,
      page_size: 10, // 选填参数
    },
  })

  const getList = async () => {
    try {
      tableLoading.value = true
      checked.value = []
      isCheckAll.value = false
      const res = await apiGetContentTemplateSeriesList(queryForm.value)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    } finally {
      tableLoading.value = false
    }
  }

  const onQuery = async () => {
    queryForm.value.page_info.page_index = 1
    await getList()
  }

  const batchAdd = () => {
    showCreateDialog()
  }
  const batchDel = () => {
    onBatchDelete()
  }

  onMounted(async () => {
    try {
      const res = await apiGetContentTemplateById({ id: +contentTemplateId.value })
      contentTemplate.value = res.data?.template_info
      queryForm.value.app_id = res.data?.template_info.app_id
      refresh.value += 1
      await getList()
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li><RouterLink to="/content-template">内容模板</RouterLink></li>
            <li>{contentTemplate.value?.name}</li>
          </ul>
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <span>短剧列表</span>
            {refresh.value > 0 && (
              <div class="space-x-2">
                <Button class="btn btn-sm btn-primary" onClick={() => batchAdd()}>批量添加短剧</Button>
                <Button class="btn btn-sm btn-primary" disabled={checked.value.length === 0} onClick={() => batchDel()}>批量删除</Button>
              </div>
            )}
          </x-table-actions>
        ),
        table: () => (
          <div class="flex">
            <div class="preview">
              <ContentTemplatePreview content_template={contentTemplate.value} refresh={refresh.value} />
            </div>
            <div class="flex overflow-hidden flex-1 justify-center">
              <Table
                list={list.value || []}
                class="w-full tm-table-fix-last-column"
                columns={columns}
                loading={tableLoading.value}
              />
            </div>

          </div>

        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={queryForm.value.page_info.page_index} v-model:size={queryForm.value.page_info.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default TemplateDetailPage
