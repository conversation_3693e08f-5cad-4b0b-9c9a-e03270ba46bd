import { createComponent, mc } from '@skynet/shared'
import { CreateTableOld, CreateForm, transformNumber, TableColumnOld, Pager, showFailToast, Button, openDialog, showSuccessToast, showAlert } from '@skynet/ui'
import { ref, computed, onActivated, watch } from 'vue'
import { set, get } from 'lodash-es'
import { apiGetContentTemplateList, apiDelContentTemplateById, apiGetContentTemplateSeriesList } from './content-template-api'
import { AxiosError } from 'axios'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { TemplateDialog } from './template-dialog'
import { RouterLink } from 'vue-router'
import dayjs from 'dayjs'
import { Icon } from '@skynet/ui/icon/icon'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useContentTemplate } from './use-content-template'
import { useClipboard } from '@vueuse/core'

type ContentTemplateOptions = {
  props: {}
}

export const ContentTemplatePage = createComponent<ContentTemplateOptions>({
  name: 'ContentTemplate',
  props: {},
}, props => {
  const { copy, copied } = useClipboard()
  const { currentContentTemplate } = useContentTemplate()
  const QueryForm = CreateForm<M.QueryTableContent>()
  const Table = CreateTableOld<M.ContentTemplate>()
  const list = ref<M.ContentTemplate[]>([])
  const tableLoading = ref(false)
  const total = ref(0)
  const dialogRef = ref(() => {})
  const dialogMainClass = 'h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'
  const delLoading = ref(false)
  const isPageReady = computed(() => !!queryForm.value.app_id)

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  const onPageSizeChange = async (size: number) => {
    queryForm.value.page_info.page_size = size
    await onQuery()
  }
  const onPageChange = async (index: number) => {
    queryForm.value.page_info.page_index = index
    await getList()
  }
  const onClose = () => {
    dialogRef.value()
  }

  const onQuery = async () => {
    queryForm.value.page_info.page_index = 1
    await getList()
  }

  const handleDelete = (row: M.ContentTemplate) => {
    const hideDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>确认删除【{row.name}】吗</x-delete-episode-body>
          <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
            <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
            <Button class={mc('btn btn-primary btn-sm', delLoading.value ? 'btn-disabled' : '')} disabled={delLoading.value} onClick={async () => {
              try {
                delLoading.value = true
                await apiDelContentTemplateById({
                  template_ids: [row.id!],
                })
                delLoading.value = false
                hideDeleteDialog()
                await onQuery()
                showSuccessToast('删除成功')
              } catch (e) {
                delLoading.value = false
                const error = e as AxiosError
                showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
              }
            }}
            >
              {delLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </Button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  }

  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      mainClass: dialogMainClass,
      title: () => <div>{currentContentTemplate.value?.id ? '编辑模板' : '新建模板'}</div>,
      body: () => (
        <TemplateDialog appList={appOptions.value} onClose={onClose} onAfterSave={async () => {
          list.value = []
          await onQuery()
          onClose()
        }}
        />
      ),
    })
  }
  const columns: TableColumnOld<M.ContentTemplate>[] = [
    ['模板key', 'key', { class: 'w-[160px]' }],
    ['模板名称', 'name', { class: 'w-[180px]' }],
    ['内容类型', row => (
      <div class="space-x-1 flex items-center">{
        row.type === 1 ? '横排专栏' : row.type === 2 ? '竖排专栏' : row.type === 3 ? '榜单' : row.type === 4 ? '3纵列' : '--'
      }
      </div>
    ), { class: 'w-[180px]' }],
    ['语言', 'language', { class: 'w-[120px]' }],
    ['状态',
      row => (
        <div class="space-x-1 flex items-center">
          {row.state === 1
            ? (
                <>
                  <div class="badge bg-green-600 badge-xs" />
                  <div>可用</div>
                </>
              )
            : (
                <>
                  <div class="badge bg-gray-300 badge-xs" />
                  <div>禁用</div>
                </>
              )}
        </div>
      ), { class: 'w-[120px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
      { class: 'w-[180px]' },
    ],
    ['更新人', 'op_user_name', { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <button class="btn btn-sm btn-link btn-primary" onClick={() => {
              currentContentTemplate.value = { ...row }
              showCreateDialog()
            }}
            >
              编辑
            </button>

            {row.content_src !== 2 && (
              <button class="btn btn-sm btn-link btn-primary" onClick={async () => {
                const params = {
                  app_id: row.app_id,
                  id: row.id,
                  is_preview: false,
                  page_info: {
                    page_index: 1,
                    page_size: 10000
                  }
                }
                const res = await apiGetContentTemplateSeriesList(params as M.QueryContentTemplateDetails)
                const ids = res.data?.list.map(row => row.series_key).join(',')
                if (!ids) {
                  showFailToast('暂未配置短剧')
                  return
                }
                void copy(ids)
              }}
              >
                复制短剧ID
              </button>
            )}

            {row.content_src !== 2 && (
              <RouterLink to={`/content-template/${row.id}`}>
                <button class="btn btn-sm btn-link btn-primary">
                  配置短剧
                </button>
              </RouterLink>
            )}

            <button class="btn btn-sm btn-link btn-primary" onClick={() => handleDelete(row)}>
              删除
            </button>
          </div>
        )
      },
      { class: 'w-[320px]' },
    ],
  ]

  const queryForm = ref<M.QueryTableContent>({
    app_id: undefined,
    language: '',
    page_info: {
      page_index: 1,
      page_size: 10, // 选填参数
    },
  })

  const getList = async () => {
    try {
      tableLoading.value = true
      const res = await apiGetContentTemplateList({
        ...queryForm.value,
        app_id: queryForm.value.app_id,
      })
      list.value = res.data?.list as unknown as M.ContentTemplate[] || []
      total.value = res.data?.total || 0
    } finally {
      tableLoading.value = false
    }
  }

  const { appOptions, languageOptions } = useAppAndLangOptions(() => queryForm.value.app_id, {
    onSuccess: getList,
  })

  watch(appOptions, () => {
    if (appOptions.value.length && !queryForm.value.app_id) {
      queryForm.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => queryForm.value.app_id, id => {
    queryForm.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  onActivated(() => {
    if (!queryForm.value.app_id) return
    console.log('onActivated')
    void getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>内容模板</li>
          </ul>
        ),
        form: () => (
          <div class="flex justify-start items-center space-x-2">
            <QueryForm class=" flex flex-row"
              onSubmit={onQuery}
              data={queryForm.value}
              hasAction={false}
              onChange={(path, value) => {
                set(queryForm.value, path, value)
              }}
              items={[
                { label: '应用：', path: 'app_id', input: { type: 'select', options: appOptions.value, autoInsertEmptyOption: true }, transform: transformNumber },
                { label: '语言：', path: 'language', input: { type: 'select', options: languageOptions.value || [], autoInsertEmptyOption: true } },
              ]}
            />
            { isPageReady.value && <Button class="btn mt-2 btn-primary btn-sm" onClick={onQuery}>提交</Button>}
          </div>

        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <span>模板列表</span>
            <Button class="btn btn-sm btn-primary" onClick={() => {
              currentContentTemplate.value = {
                ...currentContentTemplate.value,
                id: undefined,
                key: '',
                updated: undefined,
              }
              showCreateDialog()
            }}
            >新建模板
            </Button>
          </x-table-actions>
        ),
        table: () => (
          <Table
            list={list.value || []}
            class="tm-table-fix-last-column"
            columns={columns}
            loading={tableLoading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={queryForm.value.page_info.page_index} v-model:size={queryForm.value.page_info.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ContentTemplatePage
