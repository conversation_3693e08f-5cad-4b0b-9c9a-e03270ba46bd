import { createComponent } from '@skynet/shared'
import { ref, watch } from 'vue'
import { HorizontalLayout } from '../home-config/layout/horizontal-layout'
import { VerticalLayout } from '../home-config/layout/vertical-layout'
import { BillboardLayout } from '../home-config/layout/billboard-layout'
import { apiGetHomepagePreview } from '../home-config/home-config-api'

type ContentTemplatePreviewOptions = {
  props: {
    content_template: M.ContentTemplate | undefined
    refresh?: number
  }
}
export const ContentTemplatePreview = createComponent<ContentTemplatePreviewOptions>({
  props: {
    content_template: undefined,
    refresh: 0,
  },
}, props => {
  const previewLoading = ref(false)
  const previewItem = ref<M.HomepageUnit>()
  // const el = ref<HTMLElement | null>(null)

  watch(() => [props.refresh], newVal => {
    if (newVal) {
      getTemplatePreview()
    }
  })

  const getTemplatePreview = () => {
    if (!props.content_template) return
    previewLoading.value = true
    void apiGetHomepagePreview({
      app_id: props.content_template.app_id!,
      language: props.content_template.language,
      category_id: props.content_template.id,
      type: props.content_template.type,
    }).then(res => {
      if (!res.data) return
      previewItem.value = res.data.list[0]
    }).finally(() => {
      previewLoading.value = false
    })
  }

  const renderPreview = () => {
    if (!previewItem.value) return
    switch (previewItem.value.type) {
      case 'billboard':
        return (
          <BillboardLayout list={previewItem.value.items} title={previewItem.value.module_name} />
        )
      case 'column_horizontal':
        return (
          <HorizontalLayout list={previewItem.value.items} title={previewItem.value.module_name} />
        )
      case 'column_vertical':
        return (
          <VerticalLayout list={previewItem.value.items} title={previewItem.value.module_name} />
        )
    }
  }

  return () => (
    <div class="mockup-phone scale-80">
      <div class="camera" />
      <div class="display">
        <div class="artboard justify-start artboard-demo phone-5 p-[16px] bg-[#414141] overflow-y-auto">
          {renderPreview()}
          {previewLoading.value ? <x-loading class="text-white py-1 font-bold w-full h-full flex items-center justify-center">加载中...</x-loading> : null}
        </div>
      </div>
    </div>
  )
})
