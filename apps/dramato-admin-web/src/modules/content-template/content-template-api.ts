import { httpClient } from 'src/lib/http-client'

export const apiGetContentTemplateList = (data: {
  app_id?: number
  language: string
  page_info: {
    page_index: number
    page_size: number // 选填参数
  }
}) =>
  httpClient.post<ApiResponse<{
    list: M.ContentTemplate[]
    total: number
  }>>('/content/template/list', data)

export const apiSaveContentTemplate = (data: M.ContentTemplate) =>
  httpClient.post<ApiResponse<{}>>('/content/template/save', data)

export const apiGetContentTemplateById = (params: {
  id: number
}) =>
  httpClient.get<ApiResponse<{
    template_info: M.ContentTemplate
  }>>('/content/template/get', params)

export const apiDelContentTemplateById = (data: {
  template_ids: number[]
}) =>
  httpClient.post<ApiResponse<{}>>('/content/template/delete', data)

export const apiGetContentTemplateSeriesList = (data: M.QueryContentTemplateDetails) =>
  httpClient.post<ApiResponse<{
    list: M.ContentTemplateTableItem[]
    total: number
  }>>('/content/template/content_list', data)

export const apiBatchImportSeries = (data: {
  template_id: number
  series_list: { key: string, list_time: number }[]
}) =>
  httpClient.post<ApiResponse<{}>>('/content/template/series/import', data)

export const apiContentTemplateSeriesSortChange = (data: {
  template_id: number // 榜单id
  curr_row_id: number // 当前操作记录id,获取榜单内容列表 接口返回的记录唯一id
  to_sort_no: number
  to_pre_row_id?: number // 移动目标位置上一条记录id，可选参数，前端能拿到就传
  to_next_row_id?: number // 移动目标位置下一条记录id，，可选参数，前端能拿到就传
}) =>
  httpClient.post<ApiResponse<{}>>('/content/template/series/sort/update', data)

export const apiContentTemplateDelSeries = (data: {
  template_id: number // 榜单id
  row_ids: number[] // 获取榜单内容列表 接口返回的记录唯一id
}) =>
  httpClient.post<ApiResponse<{}>>('/content/template/series/delete', data)
