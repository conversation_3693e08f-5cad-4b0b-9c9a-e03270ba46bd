/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'

const InitContentTemplateOption: M.ContentTemplate = {
  id: undefined, // 榜单id
  key: '', // 榜单key
  name: '', // 榜单名称
  type: undefined, // 内容类型
  desc: '',
  app_id: undefined, // 生效应用id
  language: '', // 语言版本
  content_src: 1, // 内容来源，1：人工榜单，2：算法策略，3：人工+算法
  state: 10, // 生效状态，1：已生效，10：已停用
  updated: undefined,
  op_user_name: '',
}

const currentContentTemplate = ref<M.ContentTemplate>(InitContentTemplateOption)

export const useContentTemplate = () => {
  return {
    InitContentTemplateOption,
    currentContentTemplate,
  }
}
