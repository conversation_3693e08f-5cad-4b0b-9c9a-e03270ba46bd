import { createComponent } from '@skynet/shared'
import dayjs from 'dayjs'
import { showAlert } from '@skynet/ui'
import { Icon } from '@skynet/ui/icon/icon'
import { useClipboard } from '@vueuse/core'
import { watch } from 'vue'
import { useShortDramaStore, type DramaItem } from './series-store'
import free from './img/free.png'
type SeriesOptions = {
  props: {
    item: DramaItem
  }
}
export const Series = createComponent<SeriesOptions>({
  props: {
    item: {} as DramaItem,
  },
}, props => {
  const { copy, copied } = useClipboard()
  const { selectedDramas, addDramas, removeDramas } = useShortDramaStore()
  const toggleDrama = () => {
    if (selectedDramas.value.find(item => item.drama_id === props.item.drama_id)) {
      removeDramas([props.item])
    } else {
      addDramas([props.item])
    }
  }
  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  return () => (
    <div class="w-[400px] flex justify-between flex-col p-4">
      <div class="grid grid-cols-[150px_auto] gap-x-3 w-full">
        <div class="flex gap-2 w-[150px] cursor-pointer relative" onClick={toggleDrama}>
          {props.item.free ? <img src={free} class="absolute left-1 top-1 w-40px h-auto object-cover shadow-sm" /> : null}
          <img src={props.item.cover_url} class="w-[120px] shrink-0 h-auto" />
          <input type="checkbox" checked={!!selectedDramas.value.find(item => item.drama_id === props.item.drama_id)} class="checkbox checkbox-primary checkbox-sm" />
        </div>
        <div class="text-sm text-gray-500 flex flex-col gap-2 truncate">
          <div class="text-lg font-bold text-gray-900 truncate" title={props.item.title}>{props.item.title}</div>
          <div class="flex items-center">
            短剧ID：
            <span class="truncate">{props.item.series_key}</span>
            {props.item.series_key && <Icon class="shrink-0 w-4 h-4 cursor-pointer ml-1" name="material-symbols:content-copy-outline" onClick={() => copy(props.item.series_key)} />}
          </div>
          <div class="truncate" title={props.item.labels}>标签：{props.item.labels}</div>
          <div class="truncate" title={props.item.episodes_number + ''}>集数：{props.item.episodes_number}</div>
          <div class="flex items-center" title={props.item.description}>
            简介：
            <span class="truncate">{props.item.description}</span>
            {props.item.description && <Icon class="shrink-0 w-4 h-4 cursor-pointer ml-1" name="material-symbols:content-copy-outline" onClick={() => copy(props.item.description)} />}
          </div>
          <div class="truncate">上架时间：{+props.item.listing_time > 0 ? dayjs(new Date(props.item.listing_time * 1000)).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>
        </div>
      </div>
    </div>
  )
})
