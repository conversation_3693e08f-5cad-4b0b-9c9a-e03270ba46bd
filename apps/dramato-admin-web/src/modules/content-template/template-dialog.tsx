import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, transformNumber, Button, showSuccessToast, showFailToast, transformInteger } from '@skynet/ui'
import { z } from 'zod'
import { ref, watch } from 'vue'
import { requiredLabel } from 'src/lib/required-label'
import { set, get, cloneDeep } from 'lodash-es'
import { apiSaveContentTemplate } from './content-template-api'
import { AxiosError } from 'axios'
import { Icon } from '@skynet/ui/icon/icon'
import { lang } from 'src/lib/constant'
import { useContentTemplate } from './use-content-template'

type TemplateDialogOptions = {
  props: {
    appList: { value: number, label: string, language: string[] }[]
  }
  emits: {
    close: () => void
    afterSave: () => void
  }
}

const contentTypeOptions: { value: number, label: string }[] = [
  { label: '横排专栏', value: 1 },
  { label: '竖排专栏', value: 2 },
  { label: '榜单', value: 3 },
  { label: '3纵列', value: 4 },
]

export const TemplateDialog = createComponent<TemplateDialogOptions>({
  props: {
    appList: [],
  },
  emits: {
    close: () => {},
    afterSave: () => {},
  },
}, (props, { emit }) => {
  const { currentContentTemplate } = useContentTemplate()
  const loading = ref(false)
  const rules = z.object({
    name: z.string().min(1, {
      message: '请输入榜单名称',
    }),
    desc: z.string().min(1, {
      message: '请输入描述',
    }),
    app_id: z.number().min(1, {
      message: '请选择应用',
    }),
    language: z.string().min(1, {
      message: '请选择语言',
    }),
    content_src: z.number().min(1, {
      message: '请选择内容来源',
    }),
    // show_limit: z.union([
    //   z.number().int().min(1).max(9999), // 必须是 1 到 9999 的整数
    //   z.nullable(z.undefined()), // 允许为空值 (null 或 undefined)
    // ]),
    state: z.number().min(1, {
      message: '请选择状态',
    }),
  })

  const supportLang = ref<{ value: string, label: string }[]>([])

  const { error, validateAll } = useValidator(currentContentTemplate, rules)
  const onSave = async () => {
    if (validateAll()) {
      try {
        loading.value = true
        const params = cloneDeep(currentContentTemplate.value)
        // const show_limit = params.show_limit === undefined ? -1 : params.show_limit
        // params.show_limit = show_limit
        const res = await apiSaveContentTemplate(params)
        if (res.code === 200) {
          emit('afterSave')
          showSuccessToast('操作成功！')
        } else {
          showFailToast(res?.message || '服务忙碌，稍后再试')
        }
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
      } finally {
        loading.value = false
      }
    } else {
      console.log(error, 'error')
      return
    }
  }

  const beforeClose = () => {
    emit('close')
  }

  const Form = CreateForm<M.ContentTemplate>()

  watch(() => currentContentTemplate.value?.app_id, () => {
    if (currentContentTemplate.value?.app_id && props.appList.length > 0) {
      supportLang.value = props.appList.find(app => app.value === currentContentTemplate.value?.app_id)?.language.map(langStr => {
        return {
          value: langStr,
          label: lang.find(item => item.value === langStr)?.label || '',
        }
      }) || []
      if (!supportLang.value.some(row => row.value === currentContentTemplate.value.language)) {
        currentContentTemplate.value.language = ''
      }
    }
  }, {
    immediate: true,
  })

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px]">
        <Form
          class="w-full flex flex-nowrap flex-col"
          data={currentContentTemplate.value}
          onChange={(path, value) => {
            set(currentContentTemplate.value, path, value)
          }}
          hasAction={false}
          error={error.value}
          items={[
            { label: requiredLabel('模板key：'), class: `${currentContentTemplate.value.id ? '' : 'hidden'}`, path: 'key', input: { type: 'text', disabled: !!currentContentTemplate.value.id } },
            { label: requiredLabel('模板名称：'), path: 'name', input: { type: 'text' } },
            { label: requiredLabel('内容类型：'), path: 'type', input: { type: 'select', options: contentTypeOptions }, transform: transformNumber },
            { label: requiredLabel('描述：'), path: 'desc', input: { type: 'text' } },
            { label: requiredLabel('选择生效应用：'), path: 'app_id', input: { type: 'select', options: props.appList, disabled: !!currentContentTemplate.value.id }, transform: transformNumber },
            { label: requiredLabel('语言：'), path: 'language', input: { type: 'select', options: supportLang.value, disabled: !currentContentTemplate.value?.app_id || !!currentContentTemplate.value.id } },
            { label: requiredLabel('内容来源：'), path: 'content_src', input: { type: 'radio', options: [
              {
                value: 1,
                label: '人工模板',
              },
              {
                value: 2,
                label: '算法策略',
              },
              {
                value: 3,
                label: '人工+算法',
              },
            ] }, transform: transformInteger },
            // { label: '展示数量：', path: 'show_limit', input: { type: 'text', oninput: 'this.value=this.value.replace(/[^0-9]/g, \'\')', placeholder: '请输入展示数量1-9999，为空表示不限制' }, transform: transformNumber },
            // { label: '生效版本号：', path: 'lower_version', input: { type: 'text', placeholder: '按大于等于输入版本号生效，为空表示不限制' } },
            { label: requiredLabel('状态：'), path: 'state', input: { type: 'radio', options: [
              {
                value: 1,
                label: '已生效',
              },
              {
                value: 10,
                label: '已停用',
              },
            ] }, transform: transformInteger },

          ]}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn btn-sm btn-default" onClick={() => beforeClose()}>取消</Button>
        <Button class="btn btn-sm btn-primary" disabled={loading.value} onClick={() => onSave()}>
          {loading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          提交
        </Button>
      </div>
    </>
  )
})
