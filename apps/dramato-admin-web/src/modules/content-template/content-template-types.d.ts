declare namespace M {

  interface ContentTemplate {
    id?: number// 内容id
    type?: number// 类目类型，1：横排专栏，2：竖排专栏，3：榜单
    key: string// 模板key
    name: string// 模板名称
    desc: string
    app_id?: number// 生效应用id
    language: string// 语言版本
    content_src: number// 内容来源，1：人工榜单，2：算法策略，3：人工+算法
    state: number// 生效状态，1：已生效，10：已停用
    updated?: number// 更新时间
    op_user_name: string
    content_list_cnt?: number // 模版短剧数量
  }

  interface QueryTableContent {
    app_id?: number
    language: string
    page_info: {
      page_index: number
      page_size: number // 选填参数
    }
  }

  interface ContentTemplateTableItem {
    id: number // 记录唯一id，修改时使用
    sort_no: number // 排序
    series_key: string // 短剧唯一key
    series_name: string // 短剧名称
    cover?: string
    episodes: number // 集数
    state: number // 状态： -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架
    sort_type: number // 排序类型
  }

  interface QueryContentTemplateDetails {
    id: number
    app_id?: number // app_id
    is_preview: boolean // 是否是预览模式，默认false:非预览模式
    page_info: { // 选填参数
      page_index: number
      page_size: number // 选填参数
    }
  }
}
