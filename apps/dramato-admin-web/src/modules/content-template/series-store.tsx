import { ref } from 'vue'

export type DramaItem = {
  drama_id: number
  series_key: string // 剧集唯一标识
  app_id: string // 应用唯一标识
  app_name: string // 应用名称
  title: string // 短剧标题
  free: boolean // 是否免费
  labels: string // 内容标签
  description: string // 简介
  cover_url: string // 封面url
  episodes_number: number// 集数
  language_version_code: string // 语言版本code
  language_version_name: string // 语言版本名称
  start_paying_episodes: number // 开始付费集数
  episodes_price: number // 单集价格
  serialize_status: 1 | 2 // 连载状态 1 未完结 2 已完结
  listing_status: -1 | 1 | 2 | 3 | 4// 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
  is_time_listing: boolean // 是否定时上架 true/false
  listing_time: number // 上架时间
  is_time_removal: boolean // 是否定时下架 true/false
  removal_time: number // 下架时间
  publish_time: number // 发布时间
  created_time: number // 创建时间
  updated_time: number // 更新时间
  operator_id: number// 操作用户id
  operator_name: string// 操作用户名称
  publish_user_id: number// 发布用户id
  publish_user_name: string// 发布用户名称
}
const selectedDramas = ref<DramaItem[]>([])

const addDramas = (data: DramaItem[]) => {
  data.forEach(drama => {
    if (!selectedDramas.value.find(item => item.drama_id === drama.drama_id)) {
      selectedDramas.value.push(drama)
    }
  })
}

const removeDramas = (data: DramaItem[]) => {
  data.forEach(drama => {
    const idx = selectedDramas.value.findIndex(item => item.drama_id === drama.drama_id)
    if (idx > -1) {
      selectedDramas.value.splice(idx, 1)
    }
  })
}

export const useShortDramaStore = () => {
  return {
    selectedDramas,
    addDramas,
    removeDramas,
  }
}
