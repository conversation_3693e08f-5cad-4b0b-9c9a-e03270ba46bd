import { createComponent, fn } from '@skynet/shared'
import { Series } from './series'
import { ref, watch, computed, onMounted } from 'vue'
import { apiDramaQueryList, DramaItem, apiDramaQueryByKeyword, QueryDramaListParams } from 'src/modules/short-drama/short-drama-api'
import { apiBatchImportSeries } from './content-template-api'

import { showAlert, CreateForm, Input, Pager, Button, showSuccessToast, showFailToast } from '@skynet/ui'
import { Icon } from '@skynet/ui/icon/icon'
import { cloneDeep, get, set, throttle } from 'lodash-es'
import { useShortDramaStore } from './series-store'
import { AxiosError } from 'axios'
import { useAppAndLangOptions } from '../options/use-app-options'

type TemplateSeriesDialogOptions = {
  props: {
    app_id?: number
    language?: string
    listing_status?: Array<-1 | 0 | 1 | 2 | 3 | 4>
    template_id: string
  }
  emits: {
    close: () => void
  }
}
export const TemplateSeriesDialog = createComponent<TemplateSeriesDialogOptions>({
  props: {
    app_id: 0,
    language: '',
    listing_status: [3],
    template_id: '',
  },
  emits: {
    close: fn,
  },
}, (props, { emit }) => {
  const { selectedDramas, addDramas, removeDramas } = useShortDramaStore()

  const Form = CreateForm<QueryDramaListParams>()
  const searchLoading = ref(false)
  const queryString = ref<string>('')
  const queryResults = ref<DramaItem[]>([])
  const keywordLoading = ref<boolean>(false)
  const checkAll = ref<boolean>(false)
  const selectedDropdownDrama = ref<DramaItem[]>([])
  const importLoading = ref(false)
  const dramaList = ref<DramaItem[]>([])
  const total = ref(0)
  const dramaParams = ref<QueryDramaListParams>({
    app_id: props.app_id,
    language_version_code: props.language,
    listing_time_start: '',
    updated_time_start: '',
    publish_time_start: '',
    listing_status: [3],
    search_keyword: [],
    sort_info: [],
    page_info: {
      page_index: 1,
      page_size: 30,
    },
  })
  type sortType = '' | 'series_key' | 'publish_time' | 'updated' | 'listing_time'
  const order_column = ref<sortType>('publish_time')
  const order_type = ref('desc')
  const isItemSort = (value: string) => value === order_column.value
  const isItemSortAsc = (value: string) => {
    return value === order_column.value && (order_type.value === 'desc')
  }
  const sortItems = ref([{ value: 'listing_time', label: '上架时间' }, { value: 'updated', label: '更新时间' }, { value: 'publish_time', label: '授权时间' }])
  const toggleSort = (value: sortType) => {
    if (order_column.value === value) {
      order_type.value === 'desc' ? order_type.value = 'asc' : order_type.value === 'asc' ? order_type.value = '' : ''
      if (order_type.value === '') {
        order_column.value = ''
      }
    } else {
      order_column.value = value
      order_type.value = 'desc'
    }
    search()
  }
  const selectAll = () => {
    // 超过100就选中前100个，否则全选
    if (queryResults.value.length > 100) {
      selectedDropdownDrama.value = queryResults.value.slice(0, 100)
    } else {
      selectedDropdownDrama.value = cloneDeep(queryResults.value)
    }
  }

  const search = () => {
    searchLoading.value = true
    dramaList.value = []
    const params = {
      ...dramaParams.value,
    }

    if (order_type.value) {
      params.sort_info = [{
        field: order_column.value,
        asc: order_type.value === 'desc' ? false : true,
      }]
    }
    void apiDramaQueryList(params).then(res => {
      if (!res.data) return
      dramaList.value = res.data.list
      total.value = res.data.total
    }).catch(error => {
      showAlert(error.response.data.message, 'error')
    }).finally(() => {
      searchLoading.value = false
    })
  }

  const toggleCheckAll = (e: Event) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selectedDropdownDrama.value = cloneDeep(queryResults.value)
    } else {
      selectedDropdownDrama.value = []
    }
  }

  const toggleCheckItem = (e: Event, item: DramaItem) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selectedDropdownDrama.value.push(item)
    } else {
      selectedDropdownDrama.value = selectedDropdownDrama.value.filter(i => i.series_key !== item.series_key)
    }
  }

  const searchResult = () => (
    <div class="w-[360px] px-3 py-1 bg-white text-sm text-gray-500">
      <div class="border-b border-gray-200 py-2">
        <div>粘贴ID后，按回车键添加。(多ID值之间请以逗号分隔)</div>
      </div>
      {
        keywordLoading.value
          ? (
              <div class="flex justify-center items-center h-32">
                <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" />
              </div>
            )
          : (
              queryResults.value.length > 0
                ? (
                    <>
                      <div class="flex flex-col justify-start items-start pt-2 max-h-[400px] overflow-y-auto w-full">
                        {/* 只有全量的时候才展示全部 */}
                        {
                          queryString.value === ''
                          && (
                            <label class="flex items-center gap-2 py-1">
                              <input type="checkbox" checked={checkAll.value} onChange={(e: Event) => toggleCheckAll(e)} class="checkbox checkbox-primary checkbox-xs" />
                              <div>全部</div>
                            </label>
                          )
                        }
                        {
                          queryResults.value.map(item => (
                            <label class="max-w-full flex items-center gap-2 py-1">
                              <input type="checkbox" checked={selectedDropdownDrama.value.findIndex(i => i.series_key === item.series_key) !== -1} onChange={(e: Event) => toggleCheckItem(e, item)} class="checkbox checkbox-primary checkbox-xs" />
                              <div class="truncate">{item.series_key + '-' + item.title}</div>
                            </label>
                          ))
                        }
                      </div>
                      <div class="flex flex-row justify-between py-2">
                        <div>
                          {queryResults.value.length > 100 ? '100+' : queryResults.value.length}
                          个匹配结果
                        </div>
                        {queryResults.value.length > 0 && (
                          <div class="link-primary cursor-pointer" onClick={selectAll}>
                            全选
                            {queryResults.value.length > 100 ? '前100项' : ''}
                          </div>
                        )}
                      </div>
                    </>
                  )
                : (
                    <div class="flex justify-center items-center h-32">
                      <p>暂无搜索结果</p>
                    </div>
                  )
            )

      }
    </div>
  )
  const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (queryString.value) {
        const search_keyword = queryString.value.split(/,|，/g).join(',')
        const params = {
          search_keyword,
          app_id: +dramaParams.value.app_id,
          language_version: dramaParams.value.language_version_code,
        }
        void apiDramaQueryByKeyword(params).then(res => {
          if (!res.data) return
          selectedDropdownDrama.value = selectedDropdownDrama.value.concat(res.data.list)
        }).finally(() => {
          keywordLoading.value = false
        })
        queryString.value = ''
      }
    } else if (e.key === 'Backspace') {
      if (selectedDropdownDrama.value.length > 0 && queryString.value === '') {
        selectedDropdownDrama.value.pop()
      }
    }
  }

  const toggleAllCurrentPage = () => {
    if (checkAllCurrentPage.value) {
      removeDramas(dramaList.value)
    } else {
      addDramas(dramaList.value)
    }
  }

  const checkAllCurrentPage = computed(() => {
    return dramaList.value.length > 0 && dramaList.value.every(item => selectedDramas.value.findIndex(i => i.series_key === item.series_key) !== -1)
  })

  watch(() => dramaParams.value.page_info, () => {
    search()
  }, {
    deep: true,
  })

  watch(() => [selectedDropdownDrama.value, queryResults.value], () => {
    if (selectedDropdownDrama.value.length === queryResults.value.length && selectedDropdownDrama.value.length > 0) {
      checkAll.value = true
    } else {
      checkAll.value = false
    }
    dramaParams.value.search_keyword = selectedDropdownDrama.value.map(item => item.series_key)
    const keywords = queryString.value.split(/,|，/g)
    keywords.forEach((item, index) => {
      if (selectedDropdownDrama.value.some(drama => drama.series_key === item)) {
        keywords.splice(index, 1)
      }
    })
    queryString.value = keywords.join(',')
  }, {
    deep: true,
    immediate: true,
  })

  watch(() => [dramaParams.value.app_id, dramaParams.value.language_version_code, queryString.value], throttle(() => {
    if (dramaParams.value.app_id === '' || dramaParams.value.language_version_code === '') {
      console.error('请先选择应用和语言')
      return
    }
    // 以中英文逗号分割
    const search_keyword = queryString.value.split(/,|，/g).join(',')
    const params = {
      search_keyword,
      app_id: dramaParams.value.app_id,
      language_version: dramaParams.value.language_version_code,
    }
    keywordLoading.value = true
    void apiDramaQueryByKeyword(params).then(res => {
      if (!res.data) return
      queryResults.value = res.data.list
    }).finally(() => {
      keywordLoading.value = false
    })
  }, 1000))

  const batchImportSeries = async () => {
    try {
      importLoading.value = true
      const series_list = selectedDramas.value.map(row => {
        return {
          key: row.series_key,
          list_time: row.publish_time,
        }
      })
      const res = await apiBatchImportSeries({
        template_id: +props.template_id,
        series_list: series_list || [],
      })
      if (res.code === 200) {
        showSuccessToast('批量添加短剧成功！')
        emit('close')
      } else {
        showFailToast('短剧添加失败')
      }
      importLoading.value = false
    } catch (e) {
      const error = e as AxiosError
      importLoading.value = false
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  }

  const appId = ref<number>()
  const { languageOptions } = useAppAndLangOptions(() => appId.value, {
    onSuccess: search,
  })
  onMounted(() => {
    appId.value = props.app_id
  })

  return () => (
    <>
      <div class="flex items-center">
        <Form
          class="px-[20px]"
          data={dramaParams.value}
          onChange={(path, value) => {
            set(dramaParams.value, path, value)
          }}
          hasAction={false}
          items={[
            {
              label: '语言',
              path: 'language_version_code',
              input: {
                type: 'select',
                options: languageOptions.value,
              },
            },
            {
              label: '短剧ID/名称',
              path: 'search_keyword',
              input: {
                type: 'custom',
                render: () => (
                  <Input
                    searchable
                    class="input input-bordered input-sm w-[360px] overflow-hidden"
                    inputClass="w-full grow shrink basis-0 p-0 border-none pr-6"
                    popoverWrapperClass="z-popover-in-dialog"
                    limit={2}
                    v-model={queryString.value}
                    results={searchResult}
                    onKeydown={handleKeydown}
                    labels={selectedDropdownDrama.value.map(item => item.title)}
                    onRemove:label={(index: number) => selectedDropdownDrama.value.splice(index, 1)}
                    v-slots={{
                      suffix: () => <Icon class="w-5 h-5" name="material-symbols-light:search" />,
                    }}
                  />
                ),
              },
            },
          ]}
        />
        <div class="space-x-2 mt-2">
          <Button class="btn btn-sm btn-primary" onClick={() => {
            // 点击查询需要还原分页
            // search() 这里不需要search是因为page_info的值已经变了，会触发watch，然后search
            if (queryString.value) {
              const search_keyword = queryString.value.split(/,|，/g).join(',')
              const params = {
                search_keyword,
                app_id: +dramaParams.value.app_id,
                language_version: dramaParams.value.language_version_code,
              }
              void apiDramaQueryByKeyword(params).then(res => {
                if (!res.data) return
                selectedDropdownDrama.value = selectedDropdownDrama.value.concat(res.data.list)
                dramaParams.value.page_info = {
                  page_index: 1,
                  page_size: 30,
                }
              }).finally(() => {
                keywordLoading.value = false
              })
              queryString.value = ''
            } else {
              dramaParams.value.page_info = {
                page_index: 1,
                page_size: 30,
              }
            }
            // 查询要清空上一次选中的搜索结果
            selectedDramas.value = []
          }}
          >搜索
          </Button>
          <Button class="btn btn-sm btn-outline" onClick={() => {
            toggleAllCurrentPage()
          }}
          >{checkAllCurrentPage.value ? '取消全选' : '全选'}当前页短剧
          </Button>
          <span>
            已选中{selectedDramas.value.length}部短剧
          </span>
        </div>
      </div>
      <div class="px-[20px] space-x-2">
        <Button class="btn btn-sm btn-primary" disabled={selectedDramas.value.length === 0 || importLoading.value} onClick={async () => {
          await batchImportSeries()
        }}
        >
          {importLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          批量添加短剧
        </Button>
        <span class="text-sm text-[var(--text-3)]">添加的短剧将按照其ID顺序，在当前榜单的上架短剧序号末尾自动分配一个排序序号</span>
      </div>
      <div class="space-x-1 px-[20px] py-2 flex flex-nowrap items-center overflow-auto">
        <div class="flex-none">排序：</div>
        {
          sortItems.value.map(item => {
            return (
              <div class="cursor-pointer p-2 flex-none" onClick={() => toggleSort(item.value as sortType)}>
                <div class="flex items-center gap-1">
                  <span class={isItemSort(item.value) ? 'font-bold text-gray-800' : 'font-normal'}>{item.label}</span>
                  <div>
                    {
                      order_column.value === item.value
                        ? !isItemSortAsc(item.value) ? <Icon name="ci:sort-descending" /> : <Icon name="ci:sort-ascending" />
                        : ''
                    }
                  </div>
                </div>
              </div>
            )
          })
        }
      </div>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px]">
        <div class="w-full flex-wrap flex gap-4 p-1">
          {
            searchLoading.value
              ? (
                  <div class="w-full flex justify-center items-center h-[200px]">
                    <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" />
                  </div>
                )
              : dramaList.value.length > 0
                ? dramaList.value.map(
                  item => (
                    <Series
                      key={item.series_key}
                      item={item}
                    />
                  ),
                )
                : (
                    <div class="w-full flex justify-center items-center h-[200px]">
                      <span class="text- -400">暂无数据</span>
                    </div>
                  )
          }

        </div>
      </div>
      <div class="w-full px-20 flex justify-end">
        {
          total.value > 0 && (
            <Pager
              total={total.value}
              v-model:page={dramaParams.value.page_info.page_index}
              v-model:size={dramaParams.value.page_info.page_size}
            />
          )
        }
      </div>
    </>
  )
})
