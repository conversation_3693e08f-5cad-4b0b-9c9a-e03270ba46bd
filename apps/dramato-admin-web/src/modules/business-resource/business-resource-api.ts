import { httpClient } from 'src/lib/http-client'
import { sortBy } from 'lodash-es'

const detailTransform = (value: M.IBusinessResourceDetail) => {
  if (value.language_code) {
    value.language_code = [value.language_code as string]
  } else {
    value.language_code = []
  }
  if (value.online_time) {
    value.online_time = +value.online_time * 1000
  } else {
    value.online_time = ''
  }
  if (value.free_start_time) {
    value.free_start_time = +value.free_start_time * 1000
  } else {
    value.free_start_time = ''
  }
  if (value.auth_start_time) {
    value.auth_start_time = +value.auth_start_time * 1000
  } else {
    value.auth_start_time = ''
  }
  if (value.auth_end_time) {
    value.auth_end_time = +value.auth_end_time * 1000
  } else {
    value.auth_end_time = ''
  }

  if (value.list && value.list.length > 0) {
    sortBy(value.list, o => o.serial_number)
  }
  return value
}
// 列表
export const apiGetExternalBusinessResourceList = (data: M.IBusinessResourceParams) => {
  const transTimeStamp = (v: unknown) => {
    return v ? new Date(v as string).getTime() / 1000 : 0
  }
  return httpClient.post<ApiResponse<{
    list: M.IBusinessResource[]
    total: number
  }>>('/business_resource/external/list', data, {
    transformRequestData: {
      create_start_time: [transTimeStamp],
      create_end_time: [transTimeStamp],
    },
  })
}
// 详情
export const apiGetExternalBusinessResourceById = (data: {
  id: number
}) => {
  return httpClient.post<ApiResponse<M.IBusinessResourceDetail>>('/business_resource/external/detail', data, {
    transformResponseData: {
      data: [detailTransform],
    },
  })
}

// 保存
export const apiSaveExternalBusinessResource = (data: M.IBusinessResourceDetail) => {
  const x = (value: string[]) => {
    if (value) {
      if (value.length > 0) {
        return value[0]
      }
    }
    return ''
  }

  const transform = (v: number) => {
    if (v) {
      return v / 1000
    } else {
      return 0
    }
  }
  return httpClient.post<ApiResponse<M.IBusinessResourceDetail>>('/business_resource/external/create_or_update', data, {
    transformRequestData: {
      language_code: [x],
      online_time: [transform],
      free_start_time: [transform],
      auth_start_time: [transform],
      auth_end_time: [transform],
    },
    transformResponseData: {
      data: [detailTransform],
    },
  })
}

// 提交审核
export const apiUpdateExternalBusinessResourceStatus = (data: {
  id: number
  audit_operate: 1 | 2 | number // 审核操作 1 提交审核 2 撤销审核
}) =>
  httpClient.post<ApiResponse<{
    data: M.IBusinessResource
  }>>('/business_resource/external/audit', data)
