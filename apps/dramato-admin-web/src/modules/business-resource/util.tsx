import { get } from 'lodash-es'
import { langKey, langValue } from '../resource/constant'

export const getAllSubtitleLanguage = (list: M.IResourceAssetItem[]) => {
  const colsSet = new Set()
  langKey.some((langCode: string) => {
    for (let i = 0; i < list.length; i++) {
      if (get(list[i], `${langCode}_subtitle`)) {
        colsSet.add(langCode)
      }
    }
  })
  return Array.from(colsSet) as string[]
}

export const checkVideos = (files: File[]) => {
  return Promise.all(files.map(file => {
    return new Promise(resolve => {
      const videoElement = document.createElement('video')
      const videoURL = URL.createObjectURL(file)
      videoElement.src = videoURL
      // 确保视频已加载元数据，否则 videoWidth 和 videoHeight 可能为 0
      videoElement.addEventListener('loadedmetadata', () => {
        const width = videoElement.videoWidth
        const height = videoElement.videoHeight
        console.log(`视频清晰度: ${width}x${height}`)
        const resolution = `${width}x${height}`
        const invalidResolutions = ['1080x1920', '1920x1080', '1216x2160', '2160x1216', '1440x2560', '2560x1440', '2160x3840', '3840x2160']
        resolve({
          fileName: file.name,
          validate: Math.min(width, height) >= 1080 && invalidResolutions.includes(resolution),
        })
      })
    })
  }))
}

export const renderLanguageShow = (languages: string[]) => {
  const showLanguage = languages.map((langCode: string) => {
    return langValue[langKey.findIndex(code => code === langCode)]
  }).join(',')
  return (
    <div>
      {showLanguage}
    </div>
  )
}
