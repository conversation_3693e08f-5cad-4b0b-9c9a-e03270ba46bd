/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { RouterLink, useRoute } from 'vue-router'
import { useBusinessResource } from './use-business-resource'
import { AuditForm } from './components/audit-form'
import { apiGetInnerBusinessResourceById } from './business-resource-audit-api'
import { showFailToast } from '@skynet/ui'
import { onMounted, ref } from 'vue'

type BusinessResourceAuditDetailOptions = {
  props: {}
}
export const BusinessResourceAuditDetail = createComponent<BusinessResourceAuditDetailOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const { currentBusinessResource } = useBusinessResource()
  const loading = ref<boolean>(false)
  const getResourceDetail = async () => {
    try {
      loading.value = true
      const res = await apiGetInnerBusinessResourceById({
        resource_key: route.params.id as string
      })
      currentBusinessResource.value = res.data!
      currentBusinessResource.value.list = currentBusinessResource.value.list || []
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (route.params.id) {
      void getResourceDetail()
    }
  })

  return () => (
    <div class="pt-4">
      <div class="breadcrumbs text-sm">
        <ul>
          <li><RouterLink to="/audit-business-resource">短剧资源上传</RouterLink></li>
          <li>审核短剧资源</li>
        </ul>
      </div>
      <Wrapper>
        <AuditForm />
      </Wrapper>
    </div>

  )
})

export default BusinessResourceAuditDetail
