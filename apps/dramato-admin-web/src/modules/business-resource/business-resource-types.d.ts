declare namespace M {

  interface IBusinessResourceParams {
    title?: string
    audit_status?: 0 | 1 | 2 | 3 | number // 审核状态 0 未提交审核 1待审核 2 通过 3 不通过
    resource_type?: 1 | 2 | number
    create_start_time?: string | number
    create_end_time?: string | number
    language_code?: string
    partner?: string
    page_index: number
    page_size: number
  }

  interface IBusinessResource {
    id?: number
    title: string
    language_code: string
    partner?: string
    cover: string
    audit_status: 0 | 1 | 2 | 3 | number // 审核状态 1待审核 2 通过 3 不通过
    created: number
    updated: number // 更新时间
    updated_user: string
    audit_failed_reason: string
    vertical_cover?: string
    resource_key?: string
    openplatform_principal?: string
  }

  interface IBusinessAsset {
    id: number
    file_path: string
    serial_number: number
    file_name: string
    file_size?: number
  }

  interface IBusinessResourceDetail {
    id?: number
    title?: string
    audit_failed_reason?: string
    description?: string
    language_code?: string | string[]
    resource_type?: 1 | 2 | number
    vertical_cover?: string
    horizontal_cover?: string
    supplement_cover?: string
    psd_cover?: string
    count?: number
    subtitle_language_code?: string | string[]
    unlocked_episodes?: number
    online_time?: number | string
    free_start_time?: number | string
    auth_start_time?: number | string
    auth_end_time?: number | string
    list?: IResourceAssetItem[]
    subtitle_language_code_list?: string[]
    delete_ids_list?: number[]
    //  -1 未提交 0 未提交审核 1待审核 2 通过 3 不通过
    audit_status?: -1 | 0 | 1 | 2 | 3
    resource_key?: string
  }

  interface IResourceAsset {
    serial_number?: number
    path: string
    size?: number
    resolution?: string
    alter_type?: number // 0 不变 1 新增 2 修改 3 删除
  }
  interface IResourceAssetItem {
    serial_number: number
    pure_episode?: IResourceAsset
    origin_episode?: IResourceAsset
    no_audio_episode?: IResourceAsset
    origin_audio?: IResourceAsset
    effect_voice?: IResourceAsset
    bg_voice?: IResourceAsset
    subtitle?: IResourceAsset
    episode?: IResourceAsset
    [record: string]: IResourceAsset
  }
}
