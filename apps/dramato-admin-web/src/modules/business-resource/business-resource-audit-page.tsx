/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, Image, CreateForm, TableColumnOld, Pager, DateTime, Button, showFailToast, transformNumber } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { useRouter } from 'vue-router'
import { apiGetInternalBusinessResourceList } from './business-resource-audit-api'
import { status_list } from './constant'
import { langKey, langValue } from '../resource/constant'
import { useBusinessResource } from './use-business-resource'

type BusinessResourceAuditPageOptions = {
  props: {}
}
export const BusinessResourceAuditPage = createComponent<BusinessResourceAuditPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { resetData } = useBusinessResource()

  const list = ref<M.IBusinessResource[]>([])
  const loading = ref<boolean>(false)
  const Table = CreateTableOld<M.IBusinessResource>()
  const QueryForm = CreateForm<M.IBusinessResourceParams>()
  const total = ref<number>(0)
  const initDefaultParams = {
    title: '',
    resource_type: undefined,
    partner: '',
    audit_status: -1,
    create_start_time: undefined,
    create_end_time: undefined,
    language_code: '',
    page_index: 1,
    page_size: 20,
  }
  const form = ref<M.IBusinessResourceParams>(initDefaultParams)

  const columns: TableColumnOld<M.IBusinessResource>[] = [
    ['短剧名称', 'title', { class: 'w-[160px]' }],
    ['短剧ID', 'resource_key', { class: 'w-[160px]' }],
    ['公司', 'openplatform_principal', { class: 'w-[140px]' }],
    ['人声语言', row => {
      return langValue[langKey.findIndex(key => key === row.language_code)] || ''
    }, { class: 'w-[100px]' }],
    ['封面', row => row.vertical_cover ? <img src={row.vertical_cover} /> : '-', { class: 'w-[130px]' }],
    ['审核状态', row => ['草稿', '待审核', '审核驳回', '审核通过', '已发布'][row.audit_status], { class: 'w-[100px]' }],
    ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : undefined} />, { class: 'w-[160px]' }],
    ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : undefined} />, { class: 'w-[160px]' }],
    ['备注', 'audit_failed_reason', { class: 'w-[160px]' }],
    [<span class="px-3">操作</span>, (row, index) => (
      <div class="flex flex-nowrap justify-center">
        <Button
          class="btn btn-xs btn-link"
          onClick={() => {
            resetData()
            void router.push(`/audit-business-resource-detail/${row.resource_key}`)
          }}
        >
          查看
        </Button>
      </div>
    ), {
      class: 'w-[100px] text-center',
    },
    ],
  ]
  const getList = async () => {
    try {
      loading.value = true
      const res = await apiGetInternalBusinessResourceList(form.value)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      loading.value = false
    }
  }

  const onPageChange = (page: number) => {
    form.value.page_index = page
    void getList()
  }
  const onPageSizeChange = (pageSize: number) => {
    form.value.page_index = 1
    form.value.page_size = pageSize
    void getList()
  }
  const onQuery = () => {
    form.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = initDefaultParams
    void getList()
  }

  void getList()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>短剧资源信息审核列表</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              if (path === 'create_start_time' || path === 'create_end_time') {
                set(form.value, path, value)
              } else {
                set(form.value, path, value)
              }
            }}
            items={[
              { label: '剧名：', path: 'title', input: { type: 'text', placeholder: '请输入剧名' } },
              { label: '语言：', path: 'language_code', input: { type: 'select', options: langKey.map((key, index) => {
                return {
                  value: key,
                  label: langValue[index],
                }
              }), placeholder: '请选择语言' } },
              { label: '审核状态：', path: 'audit_status', input: { type: 'select', options: status_list, autoInsertEmptyOption: false }, transform: transformNumber },
              { label: '创建时间-开始', path: 'create_start_time', input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' } },
              { label: '创建时间-结束', path: 'create_end_time', input: { type: 'datetime', min: new Date(form.value.create_start_time as string).getTime(), rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' } },
              { label: '公司', path: 'openplatform_principal', input: { type: 'text', placeholder: '请输入合作方名称' } },
            ]}
          />
        ),
        table: () => (
          <Table
            class="tm-table-fix-last-column tm-table-fix-first-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={form.value.page_index} v-model:size={form.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default BusinessResourceAuditPage
