/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { set, sortBy } from 'lodash-es'
import { z } from 'zod'
import { apiSaveExternalBusinessResource } from './business-resource-api'
import { showSuccessToast, showFailToast } from '@skynet/ui'

const currentBusinessResource = ref<M.IBusinessResourceDetail>({
  subtitle_language_code_list: [],
  delete_ids_list: [],
  audit_status: -1
})
const step = ref<number>(0)
const saveLoading = ref(false)

const setStep = (n: number) => {
  step.value = n
}

const setCurrentBusinessResource = (path: string, value: string | number | number[] | M.IResourceAssetItem[]) => {
  set(currentBusinessResource.value || {}, path, value)
}

const setAssetToList = (data: M.IResourceAsset[], path: string) => {
  let list = currentBusinessResource.value.list || []
  data.forEach(row => {
    const index = list.findIndex(item => item.serial_number === row.serial_number)
    if (index > -1) {
      list[index][path] = {
        ...row,
        alter_type: 1,
      }
    } else {
      const x = {
        serial_number: row.serial_number!,
        [path]: {
          ...row,
          alter_type: 1,
        },
      }
      list.push(x as M.IResourceAssetItem)
      list = sortBy(list,
        [function (o) { return o.serial_number; }]);
      setCurrentBusinessResource('list', list)
    }
    const delIds = currentBusinessResource.value.delete_ids_list || []
    const delIndex = delIds?.findIndex(id => id === row.serial_number!) || -1
    if (delIndex > -1) delIds.splice(delIndex, 1)
    setCurrentBusinessResource('delete_ids_list', delIds)
  })
}

const formRules = z.object({
  title: z.string().min(1, {
    message: '请输入短剧名称',
  }).max(255, {
    message: '短剧名称太长',
  }),
  description: z.string().min(1, {
    message: '请输入短剧简介',
  }).max(1000, {
    message: '短剧简介太长，请控制在1000个字符',
  }),
  language_code: z.array(z.string().min(1, {
    message: '请选择人声语言',
  })),
  resource_type: z.number().min(1, {
    message: '请选择短剧类别',
  }),
  vertical_cover: z.string().min(1, {
    message: '请上传竖排封面资源',
  }),
  count: z.number().min(1, {
    message: '请输入总集数',
  }),
  unlocked_episodes: z.number().min(1, {
    message: '请输入付费第一集',
  }).refine(e => {
    if (currentBusinessResource.value.count && e >= (currentBusinessResource.value.count || 0)) {
      return false
    }
    return true
  }, {
    message: '必须小于总集数'
  }),
  auth_start_time: z.number().min(1, {
    message: '请选择授权开始时间',
  }),
  auth_end_time: z.number().min(1, {
    message: '请选择授权结束时间',
  }).refine(e => {
    if (currentBusinessResource.value.auth_start_time) {
      if (e < +currentBusinessResource.value.auth_start_time) {
        return false
      }
    }
    return true
  }, {
    message: '授权结束时间必须大于授权开始时间'
  }),
  subtitle_language_code_list: z.array(z.string().min(1, {
    message: '请选择语言',
  })),
  free_start_time: z.any().refine(e => {
    if (currentBusinessResource.value.online_time) {
      if (e < +currentBusinessResource.value.online_time) {
        return false
      }
    }
    return true
  }, {
    message: '最早可免费时间必须大于上线时间'
  })
})

const saveCraft = async (cb?: () => void) => {
  try {
    saveLoading.value = true
    const res = await apiSaveExternalBusinessResource({
      ...currentBusinessResource.value,
    })
    currentBusinessResource.value = res.data || {}
    showSuccessToast('操作成功')
    saveLoading.value = false
    cb && cb()
  } catch (error: any) {
    saveLoading.value = false
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    saveLoading.value = false
  }
}

const resetData = () => {
  currentBusinessResource.value = {}
  setStep(0)
}

export const useBusinessResource = () => {
  return {
    currentBusinessResource,
    setCurrentBusinessResource,
    setStep,
    step,
    setAssetToList,
    formRules,
    saveCraft,
    saveLoading,
    resetData
  }
}
