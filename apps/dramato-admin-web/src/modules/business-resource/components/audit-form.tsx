/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, Button, Icon, DateTime, openDialog, showSuccessToast, showFailToast } from '@skynet/ui'
import { useBusinessResource } from '../use-business-resource'
import { requiredLabel } from 'src/lib/required-label'
import { useRouter } from 'vue-router'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { ResourceTable } from './resource-table'
import { ref } from 'vue'
import { z } from 'zod'
import { set } from 'lodash-es'
import { apiUpdateInnerBusinessResourceStatus } from '../business-resource-audit-api'
import { renderLanguageShow } from '../util.tsx'

type AuditFormOptions = {
  props: {}
}
export const AuditForm = createComponent<AuditFormOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { currentBusinessResource, setCurrentBusinessResource } = useBusinessResource()
  const Form = CreateForm<M.IBusinessResourceDetail>()
  const saveLoading = ref(false)
  const auditLoading = ref(false)

  const cancel = () => {
    void router.replace('/audit-business-resource')
  }

  const onPass = () => {
    const assetsList = currentBusinessResource.value.list || []
    const totalCount = currentBusinessResource.value.count

    // 检查总集数
    if (assetsList.length !== totalCount) {
      showFailToast('资源总集数必须等于设定的总集数')
      return
    }

    // 检查集数是否连续（从1到count）
    const serialNumbers = assetsList.map(item => item.serial_number).sort((a, b) => a - b)
    const hasAllEpisodes = serialNumbers.every((num, index) => num === index + 1)
    if (!hasAllEpisodes) {
      showFailToast('视频集数必须从1集连续到最后一集')
      return
    }
    // 检查每集是否都有视频和字幕 字幕非必填，但是只要有一集字幕，就必须有count数量的字幕
    const subtitleList = assetsList.filter(item => item.subtitle)
    if (subtitleList.length > 0) {
      if (subtitleList.length !== totalCount) {
        showFailToast('字幕集数必须等于总集数')
        return
      }
    }
    const hasAllAssets = assetsList.map(item => item.subtitle).filter(Boolean)
    if (subtitleList.length > 0) {
      if (hasAllAssets.length !== totalCount) {
        showFailToast('字幕集数必须等于总集数')
        return
      }
    }

    const hideDialog = openDialog({
      title: '审核通过',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <div class="text-base text-[var(--text-1)]">通过后素材将自动入库</div>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={auditLoading.value} onClick={async () => {
              auditLoading.value = true
              try {
                await apiUpdateInnerBusinessResourceStatus({
                  resource_key: currentBusinessResource.value.resource_key!,
                  audit_operate_status: 1,
                })
                showSuccessToast('操作成功')
                void router.replace('/audit-business-resource')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                auditLoading.value = false
                hideDialog()
              }
            }}
            >
              {auditLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onReject = () => {
    const RejectForm = CreateForm<{
      audit_failed_reason: string
    }>()
    const rejectForm = ref<{
      audit_failed_reason: string
      audit_failed_reason_en: string
    }>({
      audit_failed_reason: '',
      audit_failed_reason_en: ''
    })
    const formRules = z.object({
      audit_failed_reason: z.string().min(1, {
        message: '请输入审核不通过原因',
      }),
    })
    const { error, validateAll } = useValidator(rejectForm, formRules)

    const hideDialog = openDialog({
      title: '审核不通过',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-text-area>
            <RejectForm
              class="grid w-full m-auto grid-cols-3"
              hasAction={false}
              error={error.value}
              data={rejectForm.value}
              onChange={(path, value: any) => {
                set(rejectForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('中文备注：'),
                  'audit_failed_reason',
                  {
                    type: 'textarea',
                    placeholder: '请输入审核不通过原因',
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-text-area>
            <RejectForm
              class="grid w-full m-auto grid-cols-3"
              hasAction={false}
              error={error.value}
              data={rejectForm.value}
              onChange={(path, value: any) => {
                set(rejectForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('英文备注：'),
                  'audit_failed_reason_en',
                  {
                    type: 'textarea',
                    placeholder: '请输入审核不通过原因',
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={auditLoading.value} onClick={async () => {
              if (!validateAll()) return
              auditLoading.value = true
              try {
                await apiUpdateInnerBusinessResourceStatus({
                  resource_key: currentBusinessResource.value.resource_key!,
                  audit_operate_status: 2,
                  audit_failed_reason: rejectForm.value.audit_failed_reason,
                  audit_failed_reason_en: rejectForm.value.audit_failed_reason_en,
                })
                showSuccessToast('操作成功')
                void router.replace('/audit-business-resource')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                auditLoading.value = false
                hideDialog()
              }
            }}
            >
              {auditLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  return () => (
    <>
      <Form
        class="grid w-full m-auto grid-cols-3"
        hasAction={false}
        data={currentBusinessResource.value}
        items={[
          [
            requiredLabel('审核状态'),
            'audit_status',
            {
              type: 'custom',
              render: () => {
                return <div class="badge badge-lg mt-2">{['草稿', '待审核', '审核驳回', '审核通过', '已发布'][currentBusinessResource.value.audit_status || 0]}</div>
              },
            },
            {
              class: `col-span-3 ${currentBusinessResource.value.audit_status! > 0 ? 'block' : 'hidden'}`,
              hint: () => currentBusinessResource.value.audit_failed_reason ? <div class="ml-4 mt-4 !text-[var(--text-1)] !text-base">驳回原因：{currentBusinessResource.value.audit_failed_reason}</div> : ''
            },
          ],
          [
            requiredLabel('短剧名称：'),
            'title',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg">
                    { currentBusinessResource.value.title }
                  </div>
                )
              },
            },
            {
              class: 'col-span-3',
            },
          ],
          [
            requiredLabel('短剧简介：'),
            'description',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg break-all">
                    { currentBusinessResource.value.description }
                  </div>
                )
              },
            },
            {
              class: 'col-span-3',
            },
          ],
          [
            requiredLabel('人声语言：'),
            'language_code',
            {
              type: 'custom',
              render: () => renderLanguageShow(currentBusinessResource.value.language_code as string[] || []),
            },
            {
              class: 'pointer-events-none',
            },
          ],
          [
            requiredLabel('字幕语言：'),
            'subtitle_language_code',
            {
              type: 'custom',
              render: () => renderLanguageShow(currentBusinessResource.value.subtitle_language_code as string[] || []),
            },
            {
              class: 'pointer-events-none',
            },
          ],
          [
            requiredLabel('短剧类别：'),
            'resource_type',
            {
              type: 'custom',
              render: () => currentBusinessResource.value.resource_type === 1 ? '本土剧' : '翻译剧',
            },
            {
              class: 'pointer-events-none',
            },
          ],
          [
            requiredLabel('总集数：'),
            'count',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg">
                    { currentBusinessResource.value.count }集
                  </div>
                )
              },
            },
          ],
          [
            requiredLabel('付费第1集：'),
            'unlocked_episodes',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg">
                    { currentBusinessResource.value.unlocked_episodes }集
                  </div>
                )
              },
            },
          ],

          [
            '',
            'empty',
            {
              type: 'custom',
              render: () => null,
            },
          ],
          [
            requiredLabel('授权开始时间：'),
            'auth_start_time',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg">
                    <DateTime value={(currentBusinessResource.value?.auth_start_time as number || 0)} />
                  </div>
                )
              },
            },
            {
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],

          [
            requiredLabel('授权结束时间：'),
            'auth_end_time',
            {
              type: 'custom',
              render: () => {
                return (
                  <div class="text-lg">
                    <DateTime value={(currentBusinessResource.value?.auth_end_time as number || 0)} />
                  </div>
                )
              },
            },
            {
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],
          [
            requiredLabel('竖排-封面资源：'),
            'vertical_cover',
            {
              type: 'custom',
              placeholder: '请上传封面资源',
              render: () => {
                return (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        setCurrentBusinessResource('vertical_cover', d.temp_path as string)
                      }}
                      isImage={true}
                    >
                      {
                        currentBusinessResource.value.vertical_cover
                          ? <img src={currentBusinessResource.value.vertical_cover} class="size-full object-contain" />
                          : <span class="size-full flex items-center justify-center">竖排-封面资源</span>
                      }
                    </Uploader>
                  </x-upload-cover>
                )
              },
            },
            {
              class: 'col-span-3 pointer-events-none',
            },
          ],
        ]}
      />
      <ResourceTable isAuditPage={true} />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-default btn-sm" onClick={() => {
          cancel()
        }}
        >返回
        </Button>
        {
          currentBusinessResource.value.audit_status === 1
            ? (
                <>
                  <Button class="btn btn-primary btn-sm" onClick={onReject}>
                    {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                    不通过
                  </Button>
                  <Button class="btn btn-primary btn-sm" onClick={onPass}>
                    {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                    通过
                  </Button>
                </>
              )
            : null
        }

      </div>
    </>
  )
})
