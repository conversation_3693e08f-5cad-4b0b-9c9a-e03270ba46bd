/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, Button, transformDatetime, Icon, transformNumber } from '@skynet/ui'
import { useBusinessResource } from '../use-business-resource'
import { requiredLabel } from 'src/lib/required-label'
import { useRouter } from 'vue-router'
import { langKey, langValue } from 'src/modules/resource/constant'
import { Uploader } from 'src/modules/common/uploader/uploader'
import dayjs from 'dayjs'

type Step1FormOptions = {
  props: {}
}
export const Step1Form = createComponent<Step1FormOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { currentBusinessResource, setCurrentBusinessResource, setStep, formRules, saveCraft, saveLoading } = useBusinessResource()
  const Form = CreateForm<M.IBusinessResourceDetail>()

  const { error, validateAll } = useValidator(currentBusinessResource, formRules)
  console.log(currentBusinessResource.value, 'currentBusinessResource')
  const cancel = () => {
    void router.replace('/business-resource')
  }

  const nextStep = () => {
    if (currentBusinessResource.value.audit_status !== 2 && !validateAll()) return
    setStep(1)
  }

  return () => (
    <>
      <Form
        class="grid w-full m-auto grid-cols-3"
        hasAction={false}
        error={error.value}
        data={currentBusinessResource.value}
        onChange={(path, value: any) => {
          if (['auth_start_time', 'auth_end_time', 'online_time', 'free_start_time'].includes(path) && value) {
            setCurrentBusinessResource(path, new Date(value).getTime())
          } else {
            setCurrentBusinessResource(path, value)
          }
        }}
        items={[
          [
            requiredLabel('审核状态'),
            'audit_status',
            {
              type: 'custom',
              render: () => {
                return <div class="badge badge-lg mt-2">{['草稿', '待审核', '通过', '不通过'][currentBusinessResource.value.audit_status || 0]}</div>
              }
            },
            {
              class: `col-span-3 ${currentBusinessResource.value.audit_status! > 0 ? 'block' : 'hidden' }`,
            },
          ],
          [
            requiredLabel('短剧名称：'),
            'title',
            {
              type: 'text',
              placeholder: '请输入短剧名称',
            },
            {
              class: 'col-span-3',
            },
          ],
          [
            requiredLabel('短剧简介：'),
            'description',
            {
              type: 'textarea',
              placeholder: '请输入短剧简介',
            },
            {
              class: 'col-span-3',
            },
          ],
          [
            requiredLabel('人声语言：'),
            'language_code',
            {
              type: 'multi-select',
              search: true,
              maxlength: 1,
              placeholder: '请选择人声语言',
              options: langKey.map((langCode, index) => {
                return {
                  label: langValue[index],
                  value: langCode,
                }
              }),
            },
          ],
          [
            requiredLabel('字幕语言：'),
            'subtitle_language_code_list',
            {
              type: 'multi-select',
              search: true,
              placeholder: '请选择字幕语言',
              options: langKey.map((langCode, index) => {
                return {
                  label: langValue[index],
                  value: langCode,
                }
              }),
            },
          ],
          [
            requiredLabel('短剧类别：'),
            'resource_type',
            {
              type: 'select',
              placeholder: '请选择短剧类型',
              options: [{
                value: 1,
                label: '本土剧',
              }, {
                value: 2,
                label: '翻译剧',
              }],
            },
            {
              transform: transformNumber,
            },
          ],
          [
            requiredLabel('竖排-封面资源：'),
            'vertical_cover',
            {
              type: 'custom',
              placeholder: '请上传封面资源',
              render: () => {
                return (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 2}
                      dimension={[3,4]}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        setCurrentBusinessResource('vertical_cover', `https://static-v1.mydramawave.com/${d.temp_path}`)
                      }}
                      isImage={true}
                    >
                      {
                        currentBusinessResource.value.vertical_cover
                          ? <img src={currentBusinessResource.value.vertical_cover} class="size-full object-contain" />
                          : <span class="size-full flex items-center justify-center">竖排-封面资源</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制2M, 3:4比例</x-upload-cover-tip>
                  </x-upload-cover>
                )
              },
            },
            {
              class: 'col-span-1',
            },
          ],
          [
            '横排-封面资源(选填)：',
            'horizontal_cover',
            {
              type: 'custom',
              placeholder: '请上传封面资源',
              render: () => {
                return (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 2}
                      dimension={[16, 9]}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        setCurrentBusinessResource('horizontal_cover', `https://static-v1.mydramawave.com/${d.temp_path}`)
                      }}
                      isImage={true}
                    >
                      {
                        currentBusinessResource.value.horizontal_cover
                          ? <img src={currentBusinessResource.value.horizontal_cover} class="size-full object-contain" />
                          : <span class="size-full flex items-center justify-center">横排-封面资源(选填)</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制2M, 16:9比例</x-upload-cover-tip>
                  </x-upload-cover>
                )
              },
            },
            {
              class: 'col-span-1',
            },
          ],
          [
            '补充-封面资源(选填)：',
            'supplement_cover',
            {
              type: 'custom',
              placeholder: '请上传封面资源',
              render: () => {
                return (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        setCurrentBusinessResource('supplement_cover', `https://static-v1.mydramawave.com/${d.temp_path}`)
                      }}
                      isImage={true}
                    >
                      {
                        currentBusinessResource.value.supplement_cover
                          ? <img src={currentBusinessResource.value.supplement_cover} class="size-full object-contain" />
                          : <span class="size-full flex items-center justify-center">补充-封面资源(选填)</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                  </x-upload-cover>
                )
              },
            },
            {
              class: 'col-span-1',
            },
          ],

          [
            'PSD封面资源：',
            'psd_cover',
            {
              type: 'custom',
              placeholder: '请上传封面资源',
              render: () => {
                return (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="psd"
                      maxsize={1024 * 1024 * 500}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        setCurrentBusinessResource('psd_cover', `https://static-v1.mydramawave.com/${d.temp_path}`)
                      }}
                      isImage={true}
                    >
                      {
                        currentBusinessResource.value.psd_cover
                          ? <img src={currentBusinessResource.value.psd_cover} class="size-full object-contain" />
                          : <span class="size-full flex items-center justify-center">上传psd资源</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">psd格式，大小限制500M</x-upload-cover-tip>
                  </x-upload-cover>
                )
              },
            },
            {
              class: 'col-span-3',
            },
          ],
          [
            requiredLabel('总集数：'),
            'count',
            {
              type: 'number',
              placeholder: '请输入总集数',
              suffix: () => <div>集</div>,
            },
            {
              transform: transformNumber,
            },
          ],
          [
            requiredLabel('付费第1集：'),
            'unlocked_episodes',
            {
              type: 'number',
              placeholder: '请输入付费第1集',
              suffix: () => <div>集</div>,
            },
            {
              transform: transformNumber,
            },
          ],

          [
            '',
            'empty',
            {
              type: 'custom',
              render: () => null,
            },
          ],
          [
            '上线时间：',
            'online_time',
            {
              type: 'datetime',
              rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
              displayFormat: 'YYYY-MM-DD HH:mm:ss',
              min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              transform: transformDatetime,
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],
          [
            '最早可免费日期：',
            'free_start_time',
            {
              type: 'datetime',
              rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
              displayFormat: 'YYYY-MM-DD HH:mm:ss',
              min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              transform: transformDatetime,
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],
          [
            requiredLabel('授权开始时间：'),
            'auth_start_time',
            {
              type: 'datetime',
              rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
              displayFormat: 'YYYY-MM-DD HH:mm:ss',
              min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              transform: transformDatetime,
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],

          [
            requiredLabel('授权结束时间：'),
            'auth_end_time',
            {
              type: 'datetime',
              rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
              displayFormat: 'YYYY-MM-DD HH:mm:ss',
              min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              transform: transformDatetime,
              hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
            },
          ],
        ]}
      />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-default btn-sm" onClick={() => {
          cancel()
        }}
        >返回
        </Button>
        {
          [-1, 0, 3].includes(currentBusinessResource.value.audit_status || -1)
            ? (
                <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
                  await saveCraft(() => {
                    void router.replace(`/business-resource-detail/${currentBusinessResource.value.id}`)
                  })
                }}
                >
                  {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  保存草稿
                </Button>
              )
            : null
        }
        <Button class="btn btn-primary btn-sm" onClick={() => {
          nextStep()
        }}
        >下一步
        </Button>
      </div>
    </>
  )
})
