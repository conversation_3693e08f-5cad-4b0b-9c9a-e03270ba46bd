/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { computed, ref, watch } from 'vue'
import { CreateTableOld, TableColumnOld, Button, openDialog, showFailToast, Checkbox } from '@skynet/ui'
import { useBusinessResource } from '../use-business-resource'
import { cloneDeep, get } from 'lodash-es'
import { langKey, langValue } from 'src/modules/resource/constant'
import Editor from 'src/modules/resource/components/editor'
import { requiredLabel } from 'src/lib/required-label'
import { M3u8Player } from 'src/modules/resource/components/m3u8-player'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'

type ResourceTableOptions = {
  props: {
    isAuditPage?: boolean
  }
}

export const ResourceTable = createComponent<ResourceTableOptions>({
  props: {
    isAuditPage: false,
  },
}, props => {
  const { currentBusinessResource, setCurrentBusinessResource } = useBusinessResource()
  const list = ref<M.IResourceAssetItem[]>([])
  const loading = ref<boolean>(false)
  const Table = CreateTableOld<M.IResourceAssetItem>()

  const deleteResource = (num: number) => {
    const delLoading = ref(false)
    const closeDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: (
        <>
          <div>确认删除当前资源？</div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={delLoading.value} onClick={() => {
              delLoading.value = true
              try {
                if (!currentBusinessResource.value.delete_ids_list?.includes(num)) {
                  const delIds = [...currentBusinessResource.value.delete_ids_list || []]
                  delIds?.push(num)
                  setCurrentBusinessResource('delete_ids_list', delIds)
                }
                const index = list.value.findIndex(row => row.serial_number === num)
                list.value.splice(index, 1)
                setCurrentBusinessResource('list', list.value)
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                delLoading.value = false
              }
            }}
            >
              提交
            </Button>
          </div>
        </>

      ),
    })
  }

  const previewVideo = async (row: M.IResourceAssetItem, videoPath?: string, lang?: string) => {
    const currentTime = ref(0)
    // 当前集
    const curRow = ref<M.IResourceAssetItem>(row)
    // 编辑器滚动条位置
    const top = ref(0)
    // 字幕内容
    const code = ref('')
    const videoRef = ref<any>(null)
    // 是否开启字母同步滚动
    const isAsync = ref(true)
    const btnLoading = ref(false)
    const url = ref(videoPath)
    const subtitles = ref<{
      language: string
      type: string
      subtitle: string
    }[]>([])

    if (lang && row.subtitle?.path) {
      code.value = await getSubtitleContent(row.subtitle?.path)
      subtitles.value = [{
        language: lang,
        type: 'srt',
        subtitle: row.subtitle?.path,
      }]
    }

    watch(() => videoRef.value, () => {
      videoRef.value.on('timeupdate', () => {
        currentTime.value = videoRef.value.getCurrentTime() || 0
      })
    })

    openDialog({
      title: () => lang ? `第${curRow.value.serial_number}集 ${langValue[langKey.findIndex(s => s === lang)]}` : `第${curRow.value.serial_number}集`,
      mainClass: 'px-4 !py-0',
      customClass: lang ? '!w-[750px]' : '!w-[400px]',
      body: () => (
        <x-video-subtile-container class="flex flex-col">
          <div class="flex flex-row">
            <x-video-area>
              { url.value
                ? (
                    <M3u8Player
                      currentLanguage={lang}
                      subtitles={subtitles.value}
                      url={url.value}
                      onPlayerReady={(e: any) => {
                        videoRef.value = e
                      }}
                    />
                  )
                : '暂无视频' }
            </x-video-area>
            {lang
              ? (
                  <x-subtitle-area class="flex-1">
                    <Editor
                      customClass="w-full h-[550px]"
                      code={code.value}
                      options={{
                        language: 'plaintext',
                        formatOnPaste: false,
                        tabSize: 2,
                        inDiffEditor: false,
                        readOnly: true,
                        minimap: {
                          enabled: false,
                        } }}
                      isAsync={isAsync.value}
                      currentTime={currentTime.value}
                      onChange={e => {
                        code.value = e
                      }}
                      scrollTop={top.value}
                      onScroll={scrollTop => {
                        top.value = scrollTop
                      }}
                      onVideoProgress={(time: number) => {
                        if (videoRef.value) videoRef.value?.seek(time)
                      }}
                    />
                  </x-subtitle-area>
                )
              : null }
          </div>
          <div class="flex justify-between space-x-4 items-center flex-row w-full mt-4">
            <div class="flex items-center">
              <div class="flex items-center">
                <Button class="btn btn-xs btn-outline" disabled={curRow.value.serial_number === 1 || btnLoading.value} onClick={async () => {
                  if (!currentBusinessResource.value.list) return
                  btnLoading.value = true
                  const curIndex = currentBusinessResource.value.list?.findIndex(item => item.serial_number === curRow.value.serial_number)
                  const item = currentBusinessResource.value.list && currentBusinessResource.value.list[curIndex - 1]
                  curRow.value = item
                  if (curRow.value.subtitle?.path) code.value = await getSubtitleContent(curRow.value.subtitle?.path)
                  url.value = curRow.value.episode?.path
                  btnLoading.value = false
                  if (lang && curRow.value.subtitle?.path) {
                    subtitles.value = [{
                      language: lang,
                      type: 'srt',
                      subtitle: curRow.value.subtitle?.path,
                    }]
                  }
                }}
                >上一集
                </Button>
                <div class="px-4">{ curRow.value.serial_number }</div>
                <Button class="btn btn-xs btn-outline" disabled={(currentBusinessResource.value.list && currentBusinessResource.value.list[currentBusinessResource.value.list.length - 1].serial_number === curRow.value.serial_number) || btnLoading.value} onClick={async () => {
                  if (!currentBusinessResource.value.list) return
                  btnLoading.value = true
                  const curIndex = currentBusinessResource.value.list?.findIndex(item => item.serial_number === curRow.value.serial_number)
                  const item = currentBusinessResource.value.list && currentBusinessResource.value.list[curIndex + 1]
                  curRow.value = item
                  if (curRow.value.subtitle?.path) code.value = await getSubtitleContent(curRow.value.subtitle?.path)
                  url.value = curRow.value.episode?.path
                  btnLoading.value = false
                  if (lang && curRow.value.subtitle?.path) {
                    subtitles.value = [{
                      language: lang,
                      type: 'srt',
                      subtitle: curRow.value.subtitle?.path,
                    }]
                  }
                }}
                >下一集
                </Button>
              </div>
              {lang
                ? (
                    <label class="flex items-center ml-4">
                      <Checkbox
                        label=""
                        modelValue={isAsync.value}
                        onUpdate:modelValue={(value: boolean) => {
                          isAsync.value = value
                        }}
                      />
                      <span>字幕同步滚动</span>
                    </label>
                  )
                : null }
            </div>
          </div>
        </x-video-subtile-container>
      ),
    })
  }

  const preview = (row: M.IResourceAssetItem, mediaPath: string, type: 'video' | 'audio' | 'subtitle') => {
    switch (type) {
      case 'video':
        void previewVideo(row, row?.episode?.path, currentBusinessResource.value.subtitle_language_code ? currentBusinessResource.value.subtitle_language_code[0] : undefined)
        break
      case 'subtitle':
        void previewVideo(row, row.episode?.path, currentBusinessResource.value.subtitle_language_code ? currentBusinessResource.value.subtitle_language_code[0] : undefined)
        break
    }
  }

  const renderPreview = (row: M.IResourceAssetItem, path: string, type: 'video' | 'audio' | 'subtitle') => {
    const mediaPath = get(row, `${path}.path`, '') as string
    return (
      mediaPath
        ? (
            <div class="hover group flex space-x-0 justify-center items-center relative">
              <Button class="btn btn-active btn-xs btn-link" onClick={() => { preview(row, mediaPath, type) }}>查看</Button>
            </div>
          )
        : '-'
    )
  }

  const constantCols = [
    ['集数', 'serial_number', { class: 'w-[60px]' }],
    ['视频', (row: M.IResourceAssetItem) => renderPreview(row, 'episode', 'video'), { class: 'w-[60px] text-center' }],
    ['字幕', (row: M.IResourceAssetItem) => renderPreview(row, 'subtitle', 'subtitle'), { class: 'w-[100px] text-center' }],
  ]
  const columns: any = computed(() => {
    list.value = cloneDeep(currentBusinessResource.value.list) || []
    const cols = currentBusinessResource.value.subtitle_language_code_list || []
    const dyCols = cols.map(langCode => {
      return [`${langValue[langKey.indexOf(langCode)]}字幕`, (row: M.IResourceAssetItem) => renderPreview(row, `${langCode}_subtitle`, 'subtitle'), { class: 'w-[100px] text-center' }]
    })

    return [
      ...constantCols,
      ...dyCols,
    ]
  })

  return () => (
    <>
      <div class="text-[var(--text-2)]">{requiredLabel('提示：上传成片和字幕语言对应字幕需与总集数相同')}</div>
      <Table
        class="min-h-[330px]"
        list={list.value || []}
        columns={columns.value as TableColumnOld<M.IResourceAssetItem>[]}
        loading={loading.value}
      />
    </>

  )
})
