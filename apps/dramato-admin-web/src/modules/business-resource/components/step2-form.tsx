/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, openDialog, Input, showSuccessToast, showToast, showFailToast, Icon } from '@skynet/ui'
import { ref } from 'vue'
import { Uploader, type UploadImage } from 'src/modules/common/uploader/uploader'
import { langKey, langValue } from 'src/modules/resource/constant'
import { requiredLabel } from 'src/lib/required-label'
import { ResourceTable } from './resource-table'
import { useBusinessResource } from '../use-business-resource'
import { checkVideos } from '../util.tsx'
import { apiUpdateExternalBusinessResourceStatus } from '../business-resource-api'
import { useRouter } from 'vue-router'
import { useGlobalLeaveWarning } from 'src/lib/use-leave'
import { showEditBtn } from '../constant.ts'

type Step2FormOptions = {
  props: {}
}

type IVideoInfo = {
  temp_path: string
  duration: number
  path: string
  file_size: number
  serial_number: number
}

export const Step2Form = createComponent<Step2FormOptions>({
  props: {},
}, props => {
  const { stop } = useGlobalLeaveWarning()
  const router = useRouter()
  const { currentBusinessResource, setAssetToList, setStep, saveCraft, saveLoading } = useBusinessResource()
  const auditLoading = ref(false)
  const cdn = 'https://img.tianmai.cn/'
  const tip = ref('')
  const onUploadVideo = () => {
    const promises = ref<Promise<unknown>[]>([])
    const tempFiles: File[] = []
    const hasUploadFiles: UploadImage[] = []
    const uploadType = ref<string>('origin_episode')

    const closeDialog = openDialog({
      title: '上传视频',
      mainClass: '!p-4 !pb-0',
      body: () => (
        <>
          <Input
            type="radio"
            class="tm-radio pb-4"
            options={[
              {
                value: 'origin_episode',
                label: '成片（有字幕有视频）',
              },
              {
                value: 'pure_episode',
                label: '无字幕有音频',
              },
              {
                value: 'no_audio_episode',
                label: '无字幕无音频',
              },
            ]}
            modelValue={uploadType.value}
            onUpdate:modelValue={value => uploadType.value = value as string}
          />
          <Uploader
            accept="mp4,mkv,avi,mov"
            isImage={false}
            ossKeyType="resource"
            multiple
            beforeUpload={async ({ files }) => {
              const responses = await checkVideos(files) as { fileName: string, validate: boolean }[]
              const unValidVideos = responses.filter(res => !res.validate)
              if (unValidVideos.length > 0) {
                showToast({
                  message: `${unValidVideos.map(row => row.fileName).join(',')}, 视频清晰度没有达到1080`,
                  duration: 5000,
                  icon: 'fail',
                })
                return false
              }
              files.forEach(file => tempFiles.push(file))
            }}
            onUploadSuccess={file => {
              if (hasUploadFiles.some(f => f?.file?.name === file.file?.name)) return
              hasUploadFiles.push(file)

              const ps = new Promise(resolve => {
                const video = document.createElement('video')
                video.preload = 'metadata'
                video.src = file.url as string
                video.onloadedmetadata = () => {
                  const width = video.videoWidth
                  const height = video.videoHeight
                  const resolution = `${width}x${height}`
                  const videoInfo = {
                    temp_path: file.temp_path || '',
                    duration: Math.round(video.duration),
                    path: cdn + file.temp_path || '',
                    file_size: file.file?.size || 0,
                    serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                    resolution,
                  }
                  resolve(videoInfo)
                }
              })
              promises.value.push(ps)
            }}
          >
            <x-uploader-wrapper class="w-full flex-col h-[120px] flex justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
              <div>上传视频, 支持MP4, MKV, AVI，MOV格式</div>
              <div>支持1080x1920 ，1216x2160 2160x3840  三种分辨率</div>
              <div>注意：建议以 1、2、3.../ep01、ep02进行命名，方便自动识别集数并排序</div>
            </x-uploader-wrapper>
          </Uploader>
          <Button class="btn btn-primary float-right btn-sm mt-4" disabled={promises.value.length === 0 || tempFiles.length !== promises.value.length} onClick={async () => {
            try {
              const videoInfos = await Promise.all(promises.value) as IVideoInfo[]
              setAssetToList(videoInfos, uploadType.value)
              closeDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '操作失败')
            }
          }}
          >保存
          </Button>
        </>
      ),
    })
  }

  const onUploadSubtitle = () => {
    const language_code = ref('')
    const tempFiles: File[] = []
    const hasUploadFiles = ref<UploadImage[]>([])

    const closeDialog = openDialog({
      title: '上传字幕',
      mainClass: '!p-4 !pb-0',
      body: () => (
        <>
          <div class="flex items-center pb-4 gap-x-2">
            字幕语言:
            <select class="select select-bordered select-sm" value={language_code.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              language_code.value = value
            }}
            >
              {
                langKey.map((n, index) => <option value={n} disabled={!currentBusinessResource.value.subtitle_language_code_list?.includes(n)}>{langValue[index]}</option>)
              }
            </select>
          </div>
          <Uploader
            accept="vtt,srt"
            ossKeyType="resource"
            isImage={false}
            maxsize={1024 * 1024 * 1}
            multiple
            beforeUpload={({ files }) => {
              files.forEach(file => tempFiles.push(file))
            }}
            onUploadSuccess={file => {
              if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
              hasUploadFiles.value.push(file)
            }}
          >
            <x-uploader-wrapper class="w-full h-[120px] flex flex-col gap-y-2 justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
              <p>上传限制：支持 srt 格式</p>
              <p>注意：建议以 1、2、3.../ep01、ep02进行命名，方便自动识别文件名并排序</p>
            </x-uploader-wrapper>
          </Uploader>
          <Button class="btn btn-primary float-right btn-sm mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={() => {
            try {
              if (!language_code.value) {
                showFailToast('请选择上传字幕的语言')
                return
              }
              const subtitleInfos = hasUploadFiles.value.map(file => {
                return {
                  path: cdn + file.temp_path || '',
                  serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                }
              })
              setAssetToList(subtitleInfos, `${language_code.value}_subtitle`)
              closeDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '操作失败')
            }
          }}
          >保存
          </Button>
        </>
      ),
    })
  }

  const onUploadAudio = () => {
    const audio_type = ref<string>('origin_audio')
    const tempFiles: File[] = []
    const hasUploadFiles = ref<UploadImage[]>([])

    const closeDialog = openDialog({
      title: '上传音频',
      mainClass: '!p-4 !pb-0',
      body: () => (
        <>
          <div class="flex items-center pb-4 gap-x-2">
            {requiredLabel('音频类型:')}
            <select class="select select-bordered select-sm" value={audio_type.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              audio_type.value = value
            }}
            >
              <option value="origin_audio">原始音频</option>
              <option value="bg_voice">背景音频</option>
              <option value="effect_voice">特效音频</option>
            </select>
          </div>
          <Uploader
            accept="mp4,mp3,wav"
            ossKeyType="resource"
            isImage={false}
            maxsize={1024 * 1024 * 1024 * 2}
            multiple
            beforeUpload={({ files }) => {
              files.forEach(file => tempFiles.push(file))
            }}
            onUploadSuccess={file => {
              if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
              hasUploadFiles.value.push(file)
            }}
          >
            <x-uploader-wrapper class="w-full h-[120px] flex flex-col gap-y-2 justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
              <p>上传限制：支持 mp4、mp3、wav 格式</p>
              <p>注意：建议以 1、2、3.../ep01、ep02进行命名，方便自动识别文件名并排序</p>
            </x-uploader-wrapper>
          </Uploader>
          <Button class="btn btn-primary float-right btn-sm mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={() => {
            try {
              const subtitleInfos = hasUploadFiles.value.map(file => {
                return {
                  path: cdn + file.temp_path || '',
                  serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                }
              })
              setAssetToList(subtitleInfos, `${audio_type.value}`)
              closeDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '操作失败')
            }
          }}
          >保存
          </Button>
        </>
      ),
    })
  }

  const preStep = () => {
    setStep(0)
  }
  const cols = (currentBusinessResource.value.subtitle_language_code_list || []).map(lang => `${lang}_subtitle`)
  const validateRow = ['origin_episode', ...cols]
  const getMissingNum = (count: number) => {
    const result: number[] = []
    const fakeNumList = new Array(count).fill(0).map((_, i) => i + 1);
    (currentBusinessResource.value.list || []).forEach(row => {
      if ((!validateRow.every(r => row[r] && row[r].path) || !fakeNumList.includes(row.serial_number)) && row.serial_number <= count) {
        result.push(row.serial_number)
      }
    })
    return result
  }

  const getMoreNum = (count: number) => {
    const result: number[] = []
    const fakeNumList = new Array(count).fill(0).map((_, i) => i + 1);
    (currentBusinessResource.value.list || []).map(row => row.serial_number).forEach(n => {
      if (!fakeNumList.includes(n)) {
        result.push(n)
      }
    })
    return result
  }

  /**
   * 只有 原视频 和 选中 的字幕语言 需要必传， 各类音频和 纯净视频 都不是必传哈
   * 各类音频和 纯净视频其他非必传的， 不管是否有传少集， 都可以提交审核
   */
  const validate = () => {
    const count = +(currentBusinessResource.value.count || 0)
    const lessNums = getMissingNum(count)
    const moreNum = getMoreNum(count)
    return {
      lessNums,
      moreNum,
    }
  }

  const nextStep = async () => {
    const { lessNums, moreNum } = validate()
    if (lessNums.length > 0 || moreNum.length > 0) {
      tip.value = `总共${+(currentBusinessResource.value.count || 0)}集，上传${(currentBusinessResource.value.list || []).length}集\n`
      tip.value += lessNums.length > 0 ? `请查看${lessNums.join(',')}集的视频资源、各语言字幕资源是否上传\n` : ''
      tip.value += moreNum.length > 0 ? `多传了第${moreNum.join(',')}集，请删除` : ''
      return
    } else {
      tip.value = ''
    }
    auditLoading.value = true
    await saveCraft()
    try {
      const res = await apiUpdateExternalBusinessResourceStatus({
        id: currentBusinessResource.value.id!,
        audit_operate: 1,
      })
      if (res.code !== 200) {
        showFailToast(res.message || '操作失败')
        return
      }
      showSuccessToast('操作成功')
      stop()
      void router.replace('/business-resource')
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
      auditLoading.value = false
    } finally {
      auditLoading.value = false
    }
  }

  return () => (
    <div class="space-y-4 w-full overflow-hidden">
      {
        showEditBtn.includes(currentBusinessResource.value.audit_status || -1)
          ? (
              <div class="flex space-x-2">
                <Button class="btn btn-sm btn-primary" onClick={onUploadVideo}>导入视频</Button>
                <Button class="btn btn-sm btn-primary" onClick={onUploadSubtitle}>导入字幕</Button>
                <Button class="btn btn-sm btn-primary" onClick={onUploadAudio}>导入音频</Button>
              </div>
            )
          : null
      }
      <pre class="text-red-500">
        { tip.value }
      </pre>
      <ResourceTable />
      <div class="flex justify-start gap-x-2">
        <Button class="btn btn-primary btn-sm" onClick={() => {
          preStep()
        }}
        >上一步
        </Button>
        {
          showEditBtn.includes(currentBusinessResource.value.audit_status || -1)
            ? (
                <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={() => {
                  void saveCraft(() => {
                    stop()
                    void router.replace(`/business-resource-detail/${currentBusinessResource.value.id}`)
                  })
                }}
                >
                  {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  保存草稿
                </Button>
              )
            : null
        }
        {
          showEditBtn.includes(currentBusinessResource.value.audit_status || -1)
            ? (
                <Button class="btn btn-primary btn-sm" disabled={auditLoading.value} onClick={() => {
                  void nextStep()
                }}
                >
                  {auditLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  提交审核
                </Button>
              )
            : null
        }
      </div>
    </div>
  )
})
