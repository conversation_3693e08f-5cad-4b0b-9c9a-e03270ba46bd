import { httpClient } from 'src/lib/http-client'

const detailTransform = (value: M.IBusinessResourceDetail) => {
  if (value.language_code) {
    value.language_code = [value.language_code as string]
  } else {
    value.language_code = []
  }
  if (value.subtitle_language_code) {
    value.subtitle_language_code = [value.subtitle_language_code as string]
  } else {
    value.subtitle_language_code = []
  }
  if (value.online_time) {
    value.online_time = +value.online_time * 1000
  } else {
    value.online_time = ''
  }
  if (value.free_start_time) {
    value.free_start_time = +value.free_start_time * 1000
  } else {
    value.free_start_time = ''
  }
  if (value.auth_start_time) {
    value.auth_start_time = +value.auth_start_time * 1000
  } else {
    value.auth_start_time = ''
  }
  if (value.auth_end_time) {
    value.auth_end_time = +value.auth_end_time * 1000
  } else {
    value.auth_end_time = ''
  }
  return value
}

// 列表
export const apiGetInternalBusinessResourceList = (data: M.IBusinessResourceParams) => {
  const transTimeStamp = (v: unknown) => {
    return v ? new Date(v as string).getTime() / 1000 : 0
  }
  return httpClient.post<ApiResponse<{
    list: M.IBusinessResource[]
    total: number
  }>>('/open_platform_in/resource/list', data, {
    transformRequestData: {
      create_start_time: [transTimeStamp],
      create_end_time: [transTimeStamp],
    },
  })
}
// 详情
export const apiGetInnerBusinessResourceById = (data: {
  resource_key: string
}) => {
  return httpClient.post<ApiResponse<M.IBusinessResourceDetail>>('/open_platform_in/resource/detail', data, {
    transformResponseData: {
      data: [detailTransform],
    },
  })
}

// 提交审核
export const apiUpdateInnerBusinessResourceStatus = (data: {
  resource_key: string
  audit_operate_status: 1 | 2 | number // 审核操作 1 通过 2 不通过
  audit_failed_reason?: string
  audit_failed_reason_en?: string
}) =>
  httpClient.post<ApiResponse<{
    data: M.IBusinessResource
  }>>('/open_platform_in/resource/audit/edit', data)
