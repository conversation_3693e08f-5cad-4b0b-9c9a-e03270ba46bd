/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { RouterLink, useRoute } from 'vue-router'
import { useBusinessResource } from './use-business-resource'
import { Step1Form } from './components/step1-form'
import { Step2Form } from './components/step2-form'
import { apiGetExternalBusinessResourceById } from './business-resource-api'
import { showFailToast } from '@skynet/ui'
import { onMounted, onUnmounted, ref } from 'vue'
import { useGlobalLeaveWarning } from 'src/lib/use-leave';

type BusinessResourceDetailOptions = {
  props: {}
}
export const BusinessResourceDetail = createComponent<BusinessResourceDetailOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const { start } = useGlobalLeaveWarning()
  start()
  const { step, currentBusinessResource } = useBusinessResource()
  const loading = ref<boolean>(false)
  const getResourceDetail = async () => {
    try {
      loading.value = true
      const res = await apiGetExternalBusinessResourceById({
        id: +route.params.id,
      })
      currentBusinessResource.value = res.data!
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (route.params.id) {
      void getResourceDetail()
    }
  })

  onUnmounted(() => {
    stop()
  })

  return () => (
    <div class="pt-4">
      <div class="breadcrumbs text-sm">
        <ul>
          <li><RouterLink to="/business-resource">短剧资源上传</RouterLink></li>
          <li>{route.params.id ? '编辑短剧资源' : '新建短剧资源' }</li>
        </ul>
      </div>
      <Wrapper>
        <div class="flex flex-col">
          <aside class="w-full">
            <ul class="steps w-full">
              <li class="step step-primary">
                <div class="text-base text-[var(--text-1)]">创建基本信息</div>
                <div class="text-xs text-[var(--text-3)]">创建短剧作品信息</div>
              </li>
              <li class={`step ${step.value > 0 ? 'step-primary' : ''}`}>
                <div class="text-base text-[var(--text-1)]">视频、字幕上传</div>
                <div class="text-xs text-[var(--text-3)]">批星上传多个剧集的视频、字幕文件</div>
              </li>
              <li class={`step ${(step.value > 0 && (currentBusinessResource.value.audit_status || 0) > 0) ? 'step-primary' : ''}`}>
                <div class="text-base text-[var(--text-1)]">审核状态</div>
                <div class="text-xs text-[var(--text-3)]">
                  { ['未提交', '待审核, 暂无法修改', '审核通过，无法修改', '审核不通过'][currentBusinessResource.value.audit_status!] || '未提交'}
                </div>
              </li>
            </ul>
          </aside>
        </div>
      </Wrapper>
      {
        currentBusinessResource.value?.audit_failed_reason
          ? (
              <Wrapper>
                <span class="text-red-500">审核不通过原因：{currentBusinessResource.value.audit_failed_reason}</span>
              </Wrapper>
            )
          : null
      }
      <Wrapper>
        {
          step.value === 0 ? <Step1Form /> : <Step2Form />
        }
      </Wrapper>
    </div>

  )
})

export default BusinessResourceDetail
