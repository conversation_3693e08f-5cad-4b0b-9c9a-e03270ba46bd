declare namespace M {
  namespace AdSwitch {
    interface Params {
      series_resource_id: number
      serial_number: number
      language: string
    }
    interface listData {
      list: listItem[]
      total: number
    }
    interface listItem {
      task_id: number
      material_name: string
      resource_id: number
      series_key: string
      audio_type: number
      task_type: number
      series_langs: string[]
      target_langs: string[]
      task_type: number
      task_status: number
      material_confirmed: number
      operator: string
      created_dt: string
    }
    interface createItem {
      resource_id: number
      series_key: string
      audio_type: number
      series_langs: string[]
      task_type: number
      target_langs: string[]
      material_ids: number[]
    }
    interface detailTarget {
      lang: string
      material: string
      orig_duration: number
      duration: number
    }
    interface detailData {
      resource_id: number
      task_id: number
      audio_type: number
      series_langs: string[]
      material_id: number
      material_name: string
      target: detailTarget[]
      task_type: number
      task_status: number
      material_confirmed: number
    }

    interface Response {
      code?: number
      message?: string
      data?: any
    }
  }
}
