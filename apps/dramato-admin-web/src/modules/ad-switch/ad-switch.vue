<template>
  <div class="ad-switch">
    <div class="ad-switch-item-title py-2">
      <span>特殊广告开关</span>
    </div>
    <div class="bg-base-100 rounded-lg px-4 pt-4 pb-1 mt-4" >
      <ElTable  :data="tableData"  max-height="68vh" :loading="loading">
        <ElTableColumn prop="platform" label="端">
          <template #default="scope">
            <span>{{ platformOp[scope.row.platform] }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="version" label="版本号" />
        <ElTableColumn prop="updated_name" label="操作人" />
        <ElTableColumn prop="updated" label="操作时间" >
          <template #default="scope">
            <span>{{ formatDate(scope.row.updated) }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="100">
          <template #default="scope">
            <ElButton type="primary" size="small" @click="handleEdit(scope.row)">编辑</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>
    <ElDialog v-model="editPop" :title="editPopTitle" width="500px">
      <ElForm>
        <ElFormItem label="版本号">
          <ElInput v-model="formData.version" :placeholder="editPopPlaceholder" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="editPop = false">取消</ElButton>
        <ElButton type="primary" @click="handleSave">保存</ElButton>
      </template>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElTable, ElTableColumn, ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElMessage, ElLoading } from 'element-plus'
  import { apiGetList, apiSaveData } from './ad-switch-api'
  import dayjs from 'dayjs'

  const tableData = ref([])
  const editPop = ref(false)
  const formData = ref({
    version: '',
    platform: 1,
    id: 0,
  })
  const loading = ref(false)
  const editPopTitle = ref('')
  const editPopPlaceholder = ref('')
  const platformOp = {
    1: 'IOS',
    2: '安卓',
  }

  const getList = () => {
    loading.value = true
    apiGetList().then((res) => {
      tableData.value = res.data
    }).finally(() => {
      loading.value = false
    })
  }

  const formatDate = (str: number) => {
    if (!str) return ''
    return dayjs(str*1000).format('YYYY-MM-DD HH:mm:ss')
  }

  const handleEdit = (row) => {
    editPop.value = true
    editPopTitle.value = platformOp[row.platform] + '特殊广告屏蔽'
    editPopPlaceholder.value = '请输入' + platformOp[row.platform] + '版本号， <此版本号展示广告'
    formData.value = {
      version: row.version,
      platform: row.platform,
      id: row.id,
    }
  }

  const handleSave = () => {
    if (!formData.value.version) {
      ElMessage.error('请输入版本号')
      return
    }

    // 验证版本号格式
    const versionPattern = /^\d+\.\d+\.\d+$/
    if (!versionPattern.test(formData.value.version)) {
      ElMessage.error('版本号格式错误，请使用 x.x.xx 格式')
      return
    }

    const data = {
      id: formData.value.id,
      version: formData.value.version,
      platform: formData.value.platform,
    }
    const loading = ElLoading.service({
      lock: true,
      text: '保存中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiSaveData(data).then((res) => {
      editPop.value = false
      ElMessage.success('保存成功')
      getList()
    }).finally(() => {
      loading.close()
    })
  }

  onMounted(() => {
    getList()
  })

</script>
