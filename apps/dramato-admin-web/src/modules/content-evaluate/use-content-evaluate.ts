import { ref } from 'vue'
import { apiEvaluate, apiGetEchartsData, apiStartOrStopPitch, getContentEvaluateDetail, getContentEvaluateList } from './content-evaluate-api'
import { ElMessage } from 'element-plus'
import { apiGetResourcePublishDetail } from '../resource-publish/resource-publish-api'
import { apiSeriesResourceList } from '../resource/resource-api'

const ratingOptions: {
  label: string
  value: string
}[] = [{
  label: 'S',
  value: 'S',
}, {
  label: 'A',
  value: 'A',
}, {
  label: 'B+',
  value: 'B+',
}, {
  label: 'B',
  value: 'B',
}, {
  label: 'C+',
  value: 'C+',
}, {
  label: 'C',
  value: 'C',
}, {
  label: 'C-',
  value: 'C-',
}]

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<M.ContentEvaluate[]>([])
const loading = ref(true)
const listLoading = ref(false)

export const useContentEvaluateStore = () => {
  const evaluateDetail = ref<M.ContentEvaluateDetail>()
  const resourceDetail = ref<M.IResourcePublishDetail>()
  const episodeList = ref<M.IResourceDrama[]>([])

  const chartData = ref<M.EChartsData>()
  const form = ref<M.QueryContentEvaluate>({
    resource_id_or_title_list: '',
    resource_type_v2: undefined,
    deploy_status: undefined,
  })
  const evaluateForm = ref<{
    rating: string
    reason: string
  }>({
    rating: '',
    reason: '',
  })

  const resetForm = () => {
    form.value = {
      resource_id_or_title_list: '',
      resource_type_v2: undefined,
      deploy_status: undefined,
    }
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0
    tableData.value = []
    void submit()
  }

  const submit = async (page_index?: number) => {
    const params = {
      page_index: page_index || currentPage.value,
      page_size: pageSize.value,
      ...form.value,
    }
    listLoading.value = true
    const res = await getContentEvaluateList(params)
    if (res.data) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
    listLoading.value = false
  }

  const getEvaluateDetail = async (series_resource_id: number) => {
    const res = await getContentEvaluateDetail({ series_resource_id })
    if (res.data) {
      evaluateDetail.value = res.data
    }
  }

  const evaluate = async (id: number) => {
    const rate = evaluateForm.value.rating
    if (!rate) {
      ElMessage.error('请选择评分')
      return
    }
    if (!evaluateForm.value.reason) {
      ElMessage.error('请输入评估原因')
      return
    }
    const res = await apiEvaluate({
      series_resource_id: id,
      rating: rate,
      reason: evaluateForm.value.reason,
    })
    if (res.code === 200) {
      ElMessage.success('评估成功')
      void submit()
      void getEvaluateDetail(id)
      void getChartData(id)
    }
  }

  const getResourceDetail = async (id: number) => {
    const res = await apiGetResourcePublishDetail({ id })
    if (res.data) {
      resourceDetail.value = res.data
    }
  }

  const getEpisodeList = async (id: number) => {
    loading.value = true
    const res = await apiSeriesResourceList({
      page_index: 1,
      page_size: 300,
      series_resource_id: id,
    })
    episodeList.value = res?.data?.list_v2 || []
    loading.value = false
  }

  const getChartData = async (id: number) => {
    const res = await apiGetEchartsData({ series_resource_id: id })
    if (res.data) {
      chartData.value = res.data
    }
  }

  const startOrStopPitch = async (row: M.ContentEvaluate, status: number) => {
    const res = await apiStartOrStopPitch({
      series_resource_id_list: [row.series_resource_id],
      ope_status: status,
    })
    if (res.code === 200) {
      ElMessage.success('操作成功')
      void submit()
    } else {
      ElMessage.error('操作失败')
    }
  }

  return {
    form,
    resetForm,
    submit,
    getEvaluateDetail,
    currentPage,
    pageSize,
    total,
    evaluate,
    evaluateForm,
    getResourceDetail,
    evaluateDetail,
    resourceDetail,
    getChartData,
    chartData,
    tableData,
    startOrStopPitch,
    getEpisodeList,
    episodeList,
    ratingOptions,
    loading,
    listLoading,
  }
}
