import { httpClient } from 'src/lib/http-client'
import { checkResourceTitleOrId } from 'src/modules/resource-publish/util'

export const getContentEvaluateList = (params: M.QueryContentEvaluate & {
  page_index: number
  page_size: number
}) => {
  return httpClient.post<ApiResponse<{
    list: M.ContentEvaluate[]
    total: number
  }>>('/resource_assess/list', params, {
    transformRequestData: {
      resource_id_or_title_list: [checkResourceTitleOrId],
    },
  })
}

export const getContentEvaluateDetail = (params: { series_resource_id: number }) => {
  return httpClient.post<ApiResponse<M.ContentEvaluateDetail>>('/resource_assess/detail', params)
}

export const apiEvaluate = (params: {
  series_resource_id: number
  rating: string
  reason: string
}) => {
  return httpClient.post<ApiResponse<null>>('/resource_assess/edit', params)
}

export const apiGetEchartsData = (params: { series_resource_id: number }) => {
  return httpClient.post<ApiResponse<M.EChartsData>>('/resource_assess/chart/line', params)
}

export const apiStartOrStopPitch = (params: {
  series_resource_id_list: number[]
  ope_status: number // 1 启动 2 中止
}) => {
  return httpClient.post<ApiResponse<null>>('/resource_assess/climbing/status/edit', params)
}
