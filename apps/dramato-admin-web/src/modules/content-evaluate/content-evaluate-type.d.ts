declare namespace M {
  interface QueryContentEvaluate {
    resource_id_or_title_list?: string | string[]
    resource_type_v2?: number | undefined // 合作形式 1 翻译剧非首发 2 本土首发 3 本土对投 4 本土二轮采买 5 本土自制 6 翻译剧首发
    deploy_status?: number | undefined // 1 已入库未发布 2 已发布未投放 3 爬坡中 4 爬坡完成 5 投放中
  }
  interface ContentEvaluate {
    series_resource_id: number
    title: string
    contractor: string
    online_time: string
    rating: string
    revenue: number
    revenue_rating: string // 收入评估结果
    open_roles: string
    created: number
    deploy_status_list: number[]
    resource_type_v2: number
  }
  interface ContentEvaluateDetail {
    rating: string
    reason: string
  }

  interface EChartsItem {
    episode_list: {
      serial_number: number
      play_uv: number
    }[]
    series_resource_id: number
    series_resource_title: string
    all_revenue: number
    series_key: string
    play_uv_sum: number
    listing_time: number
    unlocked_episodes: number
    statistics_start_time: number
    statistics_end_time: number
  }

  interface EChartsData {
    list: EChartsItem[]
  }
}
