/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQuery } from '@skynet/shared'
import { Icon, usePopover } from '@skynet/ui'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import * as echarts from 'echarts'
import { RouterLink, useRoute } from 'vue-router'
import { useContentEvaluateStore } from './use-content-evaluate'
import { Wrapper } from 'src/layouts/wrapper'
import { ElMessage } from 'element-plus'
import { M3u8Player } from 'src/modules/resource/components/m3u8-player'
import { useOptions } from './use-options'
import { langKeyForCol, langValue } from '../resource/constant'

export const ContentEvaluateDetailPage = createComponent(null, () => {
  const videoRef = ref<HTMLVideoElement>()
  const { getEvaluateDetail, evaluate, evaluateForm, getResourceDetail, evaluateDetail, resourceDetail, getChartData, chartData, getEpisodeList, episodeList, ratingOptions, loading } = useContentEvaluateStore()
  const { id } = useRoute().params
  const currentPage = ref(1)
  const currentEpisodeUrl = ref<string>()
  const currentEpisode = ref<number>()
  const chartRef = ref<HTMLDivElement>()
  const is_evaluate = getQuery('is_evaluate', 0)
  const subtitleLang = ref('')
  const showSubtitle = ref(true)

  const multiSubtitleChange = (row?: M.IResourceDrama) => {
    if (!row) return []
    return langKeyForCol.map((key, index) => {
      return {
        language: key,
        type: 'normal',
        subtitle: row[`${langKeyForCol[index]}_subtitle_path`],
      }
    }).filter(item => {
      return item.subtitle && item.subtitle.indexOf('http') === 0
    })
  }

  const subtitles = ref<{
    language: string
    type: string
    subtitle: string
  }[]>([])

  watchEffect(() => {
    const episodeRow = episodeList.value?.[0] || {}
    currentEpisodeUrl.value = episodeRow.origin_path !== '' ? episodeRow.origin_path : episodeRow.pure_path
    currentEpisode.value = episodeRow.serial_number
  })

  watch(() => currentEpisode.value, () => {
    const episodeRow = episodeList.value.find(row => row.serial_number === currentEpisode.value)
    if (!episodeRow) return
    subtitles.value = multiSubtitleChange(episodeRow) || []
    if (subtitleLang.value === '') {
      if (episodeRow['zh-TW_subtitle_path']) {
        subtitleLang.value = 'zh-TW'
      } else if (episodeRow['en_subtitle_path']) {
        subtitleLang.value = 'en'
      } else {
        const keys = Object.keys(episodeRow)
        keys.forEach(key => {
          if (`${key}_subtitle_path` && `${key}_subtitle_path`.indexOf('http')) {
            subtitleLang.value = key
          }
        })
      }
    }
  })

  const hasEvaluated = computed(() => !!evaluateDetail.value?.rating || is_evaluate === 1)

  onMounted(async () => {
    void getResourceDetail(Number(id)).catch(() => {
      ElMessage.error('获取资源详情失败')
    })
    await getEvaluateDetail(Number(id)).catch(() => {
      ElMessage.error('获取评估详情失败')
    })
    await getEpisodeList(Number(id)).catch(() => {
      ElMessage.error('获取剧集列表失败')
    })
    if (hasEvaluated.value) {
      void getChartData(Number(id)).catch(() => {
        ElMessage.error('获取图表数据失败')
      })
    }
  })

  watch(() => chartRef.value && chartData.value, () => {
    if (chartRef.value && chartData.value) {
      initChart()
    }
  })

  const initChart = () => {
    const { getOptions } = useOptions(chartData.value)
    const chart = echarts.init(chartRef.value)
    chart.setOption(getOptions())
  }

  const triggerElementRef = ref()

  usePopover({
    triggerElement: triggerElementRef,
    content: () => (
      <x-tips>
        S: 预估收入大于300万美金<br />
        A: 预估收入大于100万美金，小于300万美金<br />
        B+: 预估收入大于50万美金，小于100万美金<br />
        B: 预估收入大于10万美金，小于50万美金<br />
        C: 预估收入小于10万美金
      </x-tips>
    ),
    placement: 'bottom-start',
    class: 'overflow-visible',
    offset: 10,
    arrowVisible: false,
    triggerType: 'hover',
    wrapperClass: 'p-2 bg-white border-[1px] border-[#eee] border-[solid] rounded-xs',
  })
  return () => (
    <x-content-evaluate-detail-page class="block">
      <Wrapper class="!gap-y-4">
        <section class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/content-evaluate">内容评估</RouterLink></li>
            <li>详情</li>
          </ul>
        </section>
        <div class="space-y-4">
          <div class="my-2 flex w-full items-center gap-1 text-base font-bold">
            <Icon name="uis:web-section-alt" />
            基本信息
          </div>
          <div class="flex flex-col justify-start gap-8">
            <div class="flex w-full gap-4">
              {resourceDetail.value?.vertical_cover ? <img src={resourceDetail.value?.vertical_cover} alt="封面" class="w-[130px] h-[174px] object-contain rounded-lg" /> : null}
              <div class="space-y-2 flex-1">
                <div class="flex w-full items-start gap-2">
                  <span class="whitespace-nowrap">资源名称：</span>
                  <span>{resourceDetail.value?.title}</span>
                </div>
                <div class="flex items-start gap-2">
                  <span class="whitespace-nowrap">资源简介：</span>
                  <span>{resourceDetail.value?.description}</span>
                </div>
              </div>
            </div>

            <x-player-area class="flex flex-1 items-start justify-start ">
              <x-player class="relative block">
                {
                  currentEpisodeUrl.value ? (
                    <M3u8Player
                      subtitles={subtitles.value}
                      showSubtitle={showSubtitle.value}
                      currentLanguage={subtitleLang.value}
                      url={currentEpisodeUrl.value ?? ''}
                      onPlayerReady={(e: unknown) => {
                        videoRef.value = e as HTMLVideoElement
                        (videoRef.value as any)?.on('ended', () => {
                          if (Math.max.apply([...episodeList.value.map(item => item.serial_number), currentEpisode.value]) === currentEpisode.value) {
                            return
                          }
                          const nextEpisode = episodeList.value.find(row => row.serial_number === (currentEpisode.value || 0) + 1)
                          if (nextEpisode) {
                            currentEpisodeUrl.value = nextEpisode.origin_path !== '' ? nextEpisode.origin_path : nextEpisode.pure_path
                            currentEpisode.value = nextEpisode.serial_number
                            currentPage.value = Math.floor(((nextEpisode.serial_number - 1) / 50)) + 1
                          }
                        })
                      }}
                    />
                  ) : (
                    <div class="flex h-[550px] w-[309px] items-center justify-center">
                      <span class="text-[var(--text-3)]">{ loading.value ? '加载中……' : '无视频' }</span>
                    </div>
                  )
                }

              </x-player>
              <x-episode class="flex w-[400px] flex-col gap-2">
                <x-episode-subitle-switch class="flex w-[300px] items-center px-4">
                  <span>字幕开关：</span>
                  <el-switch
                    v-model={showSubtitle.value}
                    v-slots={{
                      'active-action': () => <span class="custom-active-action">开</span>,
                      'inactive-action': () => <span class="custom-inactive-action">关</span>,
                    }} />
                </x-episode-subitle-switch>
                <x-episode-subtitle class="flex w-[220px] items-center px-4">
                  <span>字幕选择：</span>
                  <el-select
                    class="flex-1"
                    v-model={subtitleLang.value}
                    disabled={!showSubtitle.value}
                    placeholder="请选择字幕语言"
                  >
                    {
                      subtitles.value.map(s => {
                        return <el-option value={s.language} label={langValue[langKeyForCol.findIndex(o => o === s.language)]}>{langValue[langKeyForCol.findIndex(o => o === s.language)]}</el-option>
                      })
                    }
                  </el-select>
                </x-episode-subtitle>
                <x-episode-tabs class="flex gap-2 px-4 py-2">
                  {
                    // 将数组长度按照1-50, 51-100, 101-150, 151-200...划分成一个索引数组，最后不足的自动补齐，然后遍历索引数组，生成tab
                    Array.from({ length: Math.ceil((resourceDetail.value?.count ?? 0) / 50) }, (_, index) => index + 1).map(item => (
                      <x-episode-tab onClick={() => currentPage.value = item} key={item} class={`flex cursor-pointer items-center justify-center ${currentPage.value === item ? 'border-b-2 border-b-primary' : ''}`}>{`${(item - 1) * 50 + 1}-${item * 50}`}</x-episode-tab>
                    ))
                  }
                </x-episode-tabs>
                <x-episode-list class="grid grid-cols-6 gap-2 rounded-lg p-3">
                  {
                    // 根据当前页展示对应的数据
                    episodeList.value?.filter(episode => {
                      // 当前页面显示的集数范围
                      const pageStart = (currentPage.value - 1) * 50 + 1
                      const pageEnd = currentPage.value * 50

                      // 预告片（serial_number < 1）总是显示在第一页
                      if (episode.serial_number < 1) {
                        return currentPage.value === 1
                      }

                      // 正片和花絮按照集数范围过滤
                      return episode.serial_number >= pageStart && episode.serial_number <= pageEnd
                    }).map((episode, index) => (
                      <x-episode-item
                        key={episode.serial_number}
                        onClick={() => {
                          if (currentEpisode.value === episode.serial_number) return
                          currentEpisodeUrl.value = episode.origin_path !== '' ? episode.origin_path : episode.pure_path
                          currentEpisode.value = episode.serial_number
                        }} class={`relative flex cursor-pointer items-center justify-center whitespace-nowrap rounded-lg bg-[#f0f2f5] p-2 ${currentEpisode.value === episode.serial_number ? '!bg-primary text-white' : ''}`}>
                        {currentEpisode.value === episode.serial_number ? <Icon name="solar:soundwave-outline" /> : (episode.serial_number < 1 ? '预告' : episode.serial_number > (resourceDetail.value?.count ?? 0) ? `花絮${episode.serial_number - (resourceDetail.value?.count ?? 0)}` : episode.serial_number)}
                        {(resourceDetail.value?.unlocked_episodes ?? 0) <= episode.serial_number ? <Icon class="absolute right-1 top-1 text-xs" name="solar:lock-outline" /> : null}
                      </x-episode-item>
                    ))
                  }
                </x-episode-list>
              </x-episode>
            </x-player-area>
          </div>
        </div>
        {
          hasEvaluated.value ? (
            <x-echarts-area class="block">
              <div class="h-[600px] w-full" ref={chartRef} />
            </x-echarts-area>
          ) : null
        }
        {
          evaluateForm.value && !hasEvaluated.value ? (
            <x-evaluate-area class="flex flex-col gap-2">
              <div class="flex items-center gap-2">
                <span class="flex  items-center whitespace-nowrap">
                  资源评估：
                </span>
                <el-select v-model={evaluateForm.value.rating} class="!w-[150px]" placeholder="请选择资源评估">
                  {
                    ratingOptions.map(option => <el-option value={option.value} label={option.label}>{option.label}</el-option>)
                  }
                </el-select>
              </div>
              <div class="flex items-start gap-2">
                <span class="whitespace-nowrap">评估原因：</span>
                <el-input v-model={evaluateForm.value.reason} maxlength={500} rows={4} show-word-limit type="textarea" placeholder="必填：评分说明，限制500字符" class="!w-[500px]" />
              </div>
              <el-button class="w-[80px]" type="primary" onClick={() => evaluate(Number(id))}>评估</el-button>
              <span class="mt-2 text-sm text-[var(--text-3)]">
                S: 预估收入大于300万美金<br />
                A: 预估收入大于100万美金，小于300万美金<br />
                B+: 预估收入大于50万美金，小于100万美金<br />
                B: 预估收入大于10万美金，小于50万美金<br />
                C+: 预估收入大于5万美金，小于10万美金<br />
                C：预估收入大于1万美金，小于5万美金<br />
                C-：预估收入大于0美金，小于1万美金<br />
                <br />
                提交完评估，可以查看实际数据
                <br />
                每一次评估都会计入个人历史数据，请谨慎对待
              </span>
            </x-evaluate-area>
          ) : null
        }
      </Wrapper>
    </x-content-evaluate-detail-page>
  )
})

export default ContentEvaluateDetailPage
