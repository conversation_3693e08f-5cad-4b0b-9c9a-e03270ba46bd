import * as echarts from 'echarts'
import dayjs from 'dayjs'

import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
const lineColors = ['#3B82F6', '#FACC15', '#D50B95', '#14B8A6', '#F59E0B', '#FF5555']

const formatRevenue = (revenue: number) => {
  if (revenue >= 2000000) {
    return '200万+'
  } else if (revenue >= 1000000) {
    return '100万+'
  } else if (revenue >= 500000) {
    return '50万+'
  }
  return null
}

const getLegendName = (item: M.EChartsItem) => {
  const arr = []
  if (item.all_revenue && formatRevenue(item.all_revenue)) {
    arr.push(`收入: ${formatRevenue(item.all_revenue)}`)
  }
  if (item.listing_time) {
    const listingTime = dayjs(`${item.listing_time.toString().slice(0, 4)}-${item.listing_time.toString().slice(4, 6)}-${item.listing_time.toString().slice(6)}`, 'YYYY-MM-DD').unix()
    arr.push(`上线天数: ${dayjs().utc().diff(listingTime * 1000, 'day')}天`)
  }
  return item.series_resource_title + (arr.length > 0 ? ` (${arr.join(' ')})` : '')
}

const getSeries = (chartData: M.EChartsData) => {
  return chartData.list.map((item, index) => {
    return {
      name: getLegendName(item),
      type: 'line',
      data: item.episode_list?.map(episode => [episode.serial_number, ((episode.play_uv / item.play_uv_sum) * 100).toFixed(2)]) ?? [],
      smooth: true,
      color: lineColors[index],
      areaStyle: {
        color: getGradientColor(lineColors[index]), // 动态计算渐变色
      },
      markPoint: {
        data: [{
          coord: [item.unlocked_episodes, (item.episode_list?.[item.unlocked_episodes - (item.episode_list?.[0].serial_number === 1 ? 1 : 0)]?.play_uv / item.play_uv_sum * 100).toFixed(2)],
          label: {
            show: true,
            formatter: `第${item.unlocked_episodes}集，开始收费`,
            color: '#000',
            fontSize: 12,
            fontWeight: 'bold',
            fontFamily: 'Arial',
            fontStyle: 'normal',
            fontColor: '#000',
            fontOpacity: 1,
            offset: [55, 0],
          },
          symbol: 'pin',
          symbolSize: 25,
        }],
      },
    }
  })
}

// 将 hex 颜色转换为 rgba 格式
function hexToRgba(hex: string, alpha: number) {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

function getGradientColor(color: string) {
  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: hexToRgba(color, 0.8), // 半透明
    },
    {
      offset: 1,
      color: hexToRgba(color, 0), // 完全透明
    },
  ])
}

export const useOptions = (chartData?: M.EChartsData) => {
  if (!chartData) {
    return {
      getOptions: () => ({}),
    }
  }
  const maxLength = Math.max(...chartData?.list.map(item => item.episode_list?.length ?? 0) ?? [])
  const legendData = chartData?.list.map(item => getLegendName(item)) ?? []

  const getOptions = () => {
    return {
      title: {
        text: '播放章节留存',
      },
      xAxis: {
        type: 'category',
        name: '集数',
        boundaryGap: false,
        // 找到长度最大的数组作为x轴
        data: Array.from({ length: maxLength }, (_, index) => index),
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E7EAEE',
          },
        },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        name: '播放率（%）',
      },
      series: getSeries(chartData),
      legend: {
        data: legendData,
        bottom: 0,
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '10%',
      },
      tooltip: {
        trigger: 'axis',
      },
    }
  }

  return {
    getOptions,
  }
}
