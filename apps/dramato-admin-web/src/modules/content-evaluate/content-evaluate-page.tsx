import { createComponent, on } from '@skynet/shared'
import { useContentEvaluateStore } from './use-content-evaluate'
import router from 'src/router'
import { onMounted } from 'vue'
import { useMenu } from '../menu/use-menu'

export const ContentEvaluatePage = createComponent({
  name: 'ContentEvaluate',
}, () => {
  const { form, resetForm, submit, currentPage, pageSize, total, tableData, startOrStopPitch, listLoading } = useContentEvaluateStore()
  const { getUserRoles } = useMenu()
  const getContentType = (type: number) => {
    return type === 1 ? '翻译剧非首发' : type === 2 ? '本土首发' : type === 3 ? '本土对投' : type === 4 ? '本土二轮采买' : type === 5 ? '本土自制' : type === 6 ? '翻译剧首发' : '--'
  }
  const hasPermission = (permissionId: number) => {
    const roles = getUserRoles()
    return roles.includes(permissionId)
  }
  onMounted(() => {
    void submit()
  })
  return () => (
    <x-content-evaluate-page class="block">
      <div class="breadcrumbs px-4 pt-8 text-sm">
        <ul>
          <li>内容评估</li>
        </ul>
      </div>
      <el-form label-width="95px" inline class="my-4 rounded-lg bg-white py-4 shadow">
        <el-form-item label="资源名称/ID">
          <el-input v-model={form.value.resource_id_or_title_list} />
        </el-form-item>
        <el-form-item label="资源类型">
          <el-select v-model={form.value.resource_type_v2} placeholder="请选择" clearable style="width: 160px">
            <el-option label="翻译剧非首发" value={1} />
            <el-option label="本土首发" value={2} />
            <el-option label="本土对投" value={3} />
            <el-option label="本土二轮采买" value={4} />
            <el-option label="本土自制" value={5} />
            <el-option label="翻译剧首发" value={6} />
          </el-select>
        </el-form-item>
        <el-form-item label="剧状态">
          <el-select v-model={form.value.deploy_status} placeholder="请选择" clearable style="width: 160px">
            <el-option label="未发布" value={1} />
            <el-option label="已发布" value={2} />
            <el-option label="投放中" value={5} />
            {/* <el-option label="待爬坡" value={6} />
            <el-option label="爬坡启动" value={7} />
            <el-option label="爬坡中" value={3} />
            <el-option label="爬坡完成" value={4} />
            <el-option label="爬坡中止" value={8} />
            <el-option label="爬坡淘汰" value={9} /> */}
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" onClick={() => submit()}>搜索</el-button>
          <el-button onClick={resetForm}>重置</el-button>
        </el-form-item>
      </el-form>
      <x-table-container class="mt-4 block rounded-lg bg-white py-2 shadow">
        <el-table v-loading={listLoading.value} data={tableData.value}>
          <el-table-column label="资源类型" prop="resource_type_v2">
            {
              (scope: { row: M.ContentEvaluate }) => {
                return <span>{getContentType(scope.row.resource_type_v2)}</span>
              }
            }
          </el-table-column>
          <el-table-column label="资源名称" prop="title" />
          <el-table-column label="承制方" prop="contractor" />
          <el-table-column label="状态" prop="state">
            {
              (scope: { row: M.ContentEvaluate }) => {
                // return <span>{scope.row?.deploy_status_list?.map(item => item === 1 ? '未发布' : item === 2 ? '已发布' : item === 3 ? '爬坡中' : item === 4 ? '爬坡完成' : item === 5 ? '投放中' : item === 6 ? '待爬坡' : item === 7 ? '爬坡启动' : item === 8 ? '爬坡中止' : item === 9 ? '爬坡淘汰' : '--').join('、')}</span>
                return (
                  <span>{scope.row?.deploy_status_list?.filter(item => {
                    return ![3, 4, 6, 7, 8, 9, 0].includes(item)
                  }).map(item => item === 1 ? '未发布' : item === 2 ? '已发布' : item === 5 ? '投放中' : '--').join('、')}</span>
                )
              }
            }
          </el-table-column>
          <el-table-column label="上线时间" prop="online_time">
            {
              (scope: { row: M.ContentEvaluate }) => {
                // scope.row.online_time格式20250321，转换为2025-03-21
                return <span>{scope.row.online_time ? String(scope.row.online_time).replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') : '--'}</span>
              }
            }
          </el-table-column>
          <el-table-column label="自评结果" prop="rating" />
          <el-table-column label="收入结果" prop="revenue_rating">
            {
              (scope: { row: M.ContentEvaluate }) => {
                return <span>{scope.row.revenue_rating ? scope.row.revenue_rating : '请先评价'}</span>
              }
            }
          </el-table-column>
          <el-table-column label="操作" prop="action" width="200" fixed="right" align="center">
            {
              (scope: { row: M.ContentEvaluate }) => {
                return (
                  <div>
                    <el-button type="text" onClick={() => {
                      void router.push(`/content-evaluate/${scope.row.series_resource_id}?is_evaluate=${scope.row.rating ? 1 : 0}`)
                    }}>{scope.row.rating ? '查看' : '评估'}</el-button>
                    {/* {
                      hasPermission(34) && scope.row.deploy_status_list.includes(6) ? (
                        <el-button type="text" onClick={() => startOrStopPitch(scope.row, 1)}>启动爬坡</el-button>
                      ) : hasPermission(34) && scope.row.deploy_status_list.includes(3) ? (
                        <el-button type="text" onClick={() => startOrStopPitch(scope.row, 2)}>中止爬坡</el-button>
                      ) : null
                    } */}
                  </div>
                )
              }
            }
          </el-table-column>
        </el-table>
        <el-pagination
          class="p-2"
          v-model:current-page={currentPage.value}
          v-model:page-size={pageSize.value}
          page-sizes={[10, 20, 50, 100]}
          layout="total, sizes, prev, pager, next, jumper"
          total={total.value}
          onSizeChange={() => submit()}
          onCurrentChange={() => submit()} />
      </x-table-container>
    </x-content-evaluate-page>
  )
})

export default ContentEvaluatePage
