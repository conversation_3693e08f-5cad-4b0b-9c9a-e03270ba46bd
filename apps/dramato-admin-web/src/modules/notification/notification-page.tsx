import { createComponent } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { watch } from 'vue'
import { set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { useNotification } from './use-notification'
import { NotificationForm } from './notification-form'
import { useAppAndLangOptions } from '../options/use-app-options'
import { apiGetInfoPushDetail } from './notification-api'
import dayjs from 'dayjs'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const NotificationPage = createComponent(null, () => {
  const {
    onSearchNotifications,
    total,
    searchForm,
    onReset,
    applicationList,
    currentNotification,
    InitPushNotificationOption,
    closeEditPushNotificationModal,
    list,
    onPageChange,
    onPageSizeChange,
    onPreview,
    loading,
  } = useNotification()

  const Form = CreateForm<M.PushNotificationSearchOption>()
  const Table = CreateTableOld<M.PushNotification>()

  const { appOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: onSearchNotifications,
  })

  watch(appOptions, options => {
    if (options.length > 0 && !searchForm.value.app_id) {
      applicationList.value = options
    }
  })

  const columns: TableColumnOld<M.PushNotification>[] = [
    ['通知ID', 'id', { class: 'w-[60px]' }],
    ['标题', 'title', { class: 'w-[200px]' }],
    ['内容', 'content', { class: 'w-[350px]' }],
    ['应用名称', 'target_app_names', { class: 'w-[150px]' }],
    ['开始时间', row => <DateTime value={(row.start_ts || 0) * 1000} />, { class: 'w-[200px]' }],
    ['结束时间', row => <DateTime value={(row.end_ts || 0) * 1000} />, { class: 'w-[200px]' }],
    ['指定发送时间', row => <DateTime value={(row.timed_ts as number || 0) * 1000} />, { class: 'w-[200px]' }],
    ['状态', row => (
      <div class="space-x-1 flex items-center">
        {row.state === 40
          ? (
              <>
                <div class="badge bg-green-600 badge-xs" />
                <div>已完成</div>
              </>
            )
          : row.state === 60
            ? (
                <>
                  <div class="badge bg-red-600 badge-xs" />
                  <div>已停用</div>
                </>
              )
            : (
                <>
                  <div class="badge bg-gray-300 badge-xs" />
                  <div>{row.state === 1 ? '待发送' : '发送中'}</div>
                </>
              )}
      </div>
    ), { class: 'w-[120px]' }],
    ['发送条数', 'send_cnt', { class: 'w-[80px]' }],
    ['创建时间', row => <DateTime value={(row.created || 0) * 1000} />, { class: 'w-[200px]' }],
    ['创建人', 'creator', { class: 'w-[150px]' }],
    ['更新时间', row => <DateTime value={(row.updated || 0) * 1000} />, { class: 'w-[200px]' }],
    ['更新人', 'updator', { class: 'w-[150px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-link btn-sm" onClick={async () => {
            const rs = await apiGetInfoPushDetail({ id: row.id || 0 })
            currentNotification.value = {
              ...rs.data?.info,
              timed_ts: dayjs(+(rs.data?.info?.timed_ts || '') * 1000).format('YYYY-MM-DD HH:mm:ss'),
            }
            onPreview()
          }}
          >
            预览
          </Button>
          <Button class="btn btn-link btn-sm" onClick={async () => {
            const rs = await apiGetInfoPushDetail({ id: row.id || 0 })
            currentNotification.value = {
              ...rs.data?.info,
              target_app_ids: (rs.data?.info?.target_app_ids as string || '').split(',').map(Number),
              timed_ts: dayjs(+(rs.data?.info?.timed_ts || '') * 1000).format('YYYY-MM-DD HH:mm:ss'),
            }
            if (!applicationList.value || applicationList.value.length <= 0) {
              applicationList.value = appOptions.value || []
            }
            closeEditPushNotificationModal.value = openDialog({
              title: '编辑通知',
              body: <NotificationForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[800px]',
            })
          }}
          >编辑
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/info-push">推送通知</RouterLink></li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearchNotifications(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              [
                '应用',
                'app_id',
                {
                  type: 'select',
                  class: 'w-[240px]',
                  options: appOptions.value,
                },
                { transform: transformNumber },
              ],
              [
                '状态',
                'state',
                {
                  type: 'select',
                  class: 'w-[240px]',
                  options: [
                    { label: '待发送', value: 1 },
                    { label: '发送中', value: 20 },
                    { label: '已完成', value: 40 },
                  ],
                },
                { transform: transformNumber },
              ],
              ['内容检索', 'search_val', { type: 'text', placeholder: '请输入内容检索', class: 'w-[240px]' }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="w-full flex justify-between items-center">
            <span>通知列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => {
                currentNotification.value = {
                  ...InitPushNotificationOption,
                }

                if (!applicationList.value || applicationList.value.length <= 0) {
                  applicationList.value = appOptions.value || []
                }

                closeEditPushNotificationModal.value = openDialog({
                  title: '新建通知',
                  body: <NotificationForm />,
                  mainClass: dialogMainClass,
                  customClass: '!w-[800px]',
                })
              }}
              >新建通知
              </Button>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default NotificationPage
