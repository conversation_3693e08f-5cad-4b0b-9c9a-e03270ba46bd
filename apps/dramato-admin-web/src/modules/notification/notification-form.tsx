import { createComponent, mc, useValidator } from '@skynet/shared'
import { useNotification } from './use-notification'
import { Button, CreateForm, openDialog, transformDatetime, transformInteger, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader'
import dayjs from 'dayjs'
import { ref } from 'vue'
import { apiCheckDeeplink } from './notification-api'

export const NotificationForm = createComponent(null, props => {
  const {
    currentNotification,
    applicationList,
    onEdit,
    onCreate,
    closeEditPushNotificationModal,
    isUpdating,
  } = useNotification()

  const isUploading = ref(false)
  const isUploadedFail = ref(false)

  const isHorizontalUploading = ref(false)
  const isHorizontalUploadedFail = ref(false)
  const Form = CreateForm<M.PushNotification>()
  const formRules = z.object({
    title: z.string().min(1, '请输入标题').max(100, '最多100个字符'),
    content: z.string().min(1, '请输入通知内容'),
    link: z.string().min(1, '请输入链接'),
    image_url: z.string().min(1, '请上传封面').optional(),
    priority: z.number().min(1, '请选择优先级'),
    target_app_ids: z.array(z.number()).min(1, '请选择生效应用'),
    ageing_type: z.number().min(1, '请选择发送时间'),
    timed_ts: z.string().min(1, '请指定时间发送时间'),
    target_user_type: z.number().min(1, '请选择发送用户'),
    user_identify_val: z.string().min(1, '请输入用户id'),
    android_notify_channel: z.string().min(1, '请选择Android通知渠道'),
    notify_btn_text: z.string().min(1, '请输入通知按钮文字'),
    notify_btn_bg_color: z.string().min(1, '').regex(/^#[0-9a-fA-F]{6}$/).optional(),
    notify_btn2_text: z.string().min(1, '请输入提示按钮文字'),
    tips_style: z.string().min(1, '请输入'),
  })

  const { error, validateAll } = useValidator(currentNotification, formRules)

  const hasAndroidPlatform = () => {
    return applicationList.value?.filter(i => (currentNotification.value?.target_app_ids as number[])?.includes(i.value)).find(i => i.platform === 2)
  }

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'timed_ts' && value === 'Invalid Date') {
              set(currentNotification.value || {}, path, undefined)
              return
            }
            set(currentNotification.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('标题'),
              'title',
              {
                type: 'textarea',
                maxlength: 100,
                placeholder: '请输入标题，最大支持100字符',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('通知内容'),
              'content',
              {
                type: 'textarea',
                maxlength: 200,
                placeholder: '请输入标题，最大支持200字符',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '样式类型',
              'content_type',
              {
                type: 'select',
                options: [
                  // {
                  //   label: '纯文本',
                  //   value: 1,
                  // },
                  // {
                  //   label: '小图',
                  //   value: 2,
                  // },
                  // {
                  //   label: '大图',
                  //   value: 3,
                  // },
                  {
                    label: '左图右文（仅付费版IOS有效）',
                    value: 4,
                  },
                  // {
                  //   label: '大图+小图',
                  //   value: 6
                  // }
                ],
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('deeplink'),
              'link',
              {
                type: 'textarea',
                maxlength: 1000,
                placeholder: '请输入标题，最大支持1000字符',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '图片',
              'image_url',
              {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      beforeUpload={() => {
                        if (isUpdating.value) {
                          return Promise.resolve(false)
                        }
                        isUploading.value = true
                        return Promise.resolve(isUploading.value)
                      }}
                      onUploadSuccess={d => {
                        currentNotification.value.image_url = d.temp_path
                        isUploading.value = false
                        isUploadedFail.value = false
                      }}
                      onUploadFailed={() => {
                        console.log('onUploadFailed')

                        isUploading.value = false
                        isUploadedFail.value = true
                      }}
                      isImage={true}
                      multiple={false}
                      uploadUrl="/push/upload/image"
                    >
                      {
                        currentNotification.value.image_url && !isUploading.value
                          ? <img src={currentNotification.value.image_url.includes('https://') ? currentNotification.value.image_url : 'https://static-v1.mydramawave.com/push/task/image/notify/' + currentNotification.value.image_url} class="size-full object-cover" />
                          : (
                              <span class="size-full flex items-center justify-center">{
                                isUploading.value
                                  ? (
                                      <div class="flex flex-col gap-y-2 items-center">
                                        <span class="loading loading-spinner size-4" />
                                        <span>上传中</span>
                                      </div>
                                    )
                                  : '上传封面'
                              }
                              </span>
                            )
                      }
                    </Uploader>
                  </x-upload-cover>

                ),
              },
              {
                class: 'col-span-1',
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    { isUploadedFail.value && <span class="text-red-500">上传失败, 请重试</span>}
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      1.支持 png、jpg、jpeg 格式图片，大小限制 10M 以内
                    </x-tip>
                    <x-tip>
                      2.建议图片尺寸iOS：351*182；Android：351*223；两端同时发送可参考iOS 建议尺寸
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              '横图',
              'horizontal_image_url',
              {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      beforeUpload={() => {
                        if (isHorizontalUploading.value) {
                          return Promise.resolve(false)
                        }
                        isHorizontalUploading.value = true
                        return Promise.resolve(isHorizontalUploading.value)
                      }}
                      onUploadSuccess={d => {
                        currentNotification.value.horizontal_image_url = d.temp_path
                        isHorizontalUploading.value = false
                        isHorizontalUploadedFail.value = false
                      }}
                      onUploadFailed={() => {
                        console.log('onUploadFailed')

                        isHorizontalUploading.value = false
                        isHorizontalUploadedFail.value = true
                      }}
                      isImage={true}
                      multiple={false}
                      uploadUrl="/push/upload/image"
                    >
                      {
                        currentNotification.value.horizontal_image_url && !isHorizontalUploading.value
                          ? <img src={currentNotification.value.horizontal_image_url.includes('https://') ? currentNotification.value.horizontal_image_url : 'https://static-v1.mydramawave.com/push/task/image/notify/' + currentNotification.value.horizontal_image_url} class="size-full object-cover" />
                          : (
                              <span class="size-full flex items-center justify-center">{
                                isHorizontalUploading.value
                                  ? (
                                      <div class="flex flex-col gap-y-2 items-center">
                                        <span class="loading loading-spinner size-4" />
                                        <span>上传中</span>
                                      </div>
                                    )
                                  : '上传封面'
                              }
                              </span>
                            )
                      }
                    </Uploader>
                  </x-upload-cover>

                ),
              },
              {
                class: 'col-span-1',
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    { isHorizontalUploadedFail.value && <span class="text-red-500">上传失败, 请重试</span>}
                    <x-tip>
                      提示：
                    </x-tip>
                    <x-tip>
                      1.支持 png、jpg、jpeg 格式图片，大小限制 10M 以内
                    </x-tip>
                    <x-tip>
                      {/* 2.建议图片尺寸iOS：351*182；Android：351*223；两端同时发送可参考iOS 建议尺寸 */}
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('优先级'),
              'priority',
              {
                type: 'select',
                options: [
                  {
                    label: '紧急',
                    value: 1,
                  },
                  {
                    label: '高',
                    value: 2,
                  },
                  {
                    label: '中',
                    value: 3,
                  },
                  {
                    label: '低',
                    value: 4,
                  },
                  {
                    label: '无',
                    value: 5,
                  },
                ],
                autoInsertEmptyOption: false,
              },
              {
                transform: transformInteger,
                class: 'col-span-1',
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示文案：{[
                        '',
                        '紧急：发出提示音，并以浮动通知的形式显示',
                        '高：发出提示音',
                        '中：不发出声音',
                        '低：不发出提示音，且不会显示在状态栏中',
                        '无：不发出提示音，且不会显示在状态栏或通知栏中',
                      ][currentNotification.value.priority || 0]}
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('选择生效应用'),
              'target_app_ids',
              {
                type: 'multi-select',
                options: applicationList.value,
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('发送时间'),
              'ageing_type',
              {
                type: 'radio',
                options: [
                  {
                    label: '立即发送',
                    value: 1,
                  },
                  {
                    label: '定时发送',
                    value: 2,
                  },
                ],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('指定时间'),
              'timed_ts',
              {
                type: 'datetime',
                min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformDatetime,
                class: mc('col-span-1', +(currentNotification.value.ageing_type || '') !== 2 ? 'hidden' : ''),
                hint: (
                  <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                    <x-tip>
                      提示：这里设定的是(UTC-08:00)北京时间
                    </x-tip>
                  </x-upload-cover-tip>
                ),
              },
            ],
            [
              requiredLabel('发送用户'),
              'target_user_type',
              {
                type: 'radio',
                options: [
                  {
                    label: '全部用户',
                    value: 1,
                  },
                  {
                    label: '指定用户',
                    value: 2,
                  },
                  {
                    label: '用户特征',
                    value: 3,
                  },
                ],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('用户id'),
              'user_identify_val',
              {
                type: 'textarea',
                placeholder: 'id列表,逗号分割',
              },
              {
                class: mc('col-span-1', +(currentNotification.value.target_user_type || '') !== 2 ? 'hidden' : ''),
              },
            ],
            [
              [
                '用户日龄开始天数',
                'last_n_day_register_start',
                {
                  type: 'number',
                },
                {
                  transform: transformNumber,
                  class: mc('col-span-1', +(currentNotification.value.target_user_type || '') !== 3 ? 'hidden' : ''),
                },
              ],
              [
                '用户日龄结束天数',
                'last_n_day_register_end',
                {
                  type: 'number',
                },
                {
                  transform: transformNumber,
                  class: mc('col-span-1', +(currentNotification.value.target_user_type || '') !== 3 ? 'hidden' : ''),
                },
              ],
            ],
            [
              requiredLabel('Android通知渠道'),
              'android_notify_channel',
              {
                type: 'radio',
                options: [
                  {
                    label: '短剧通知',
                    value: 'Drama',
                  },
                  {
                    label: '运营通知',
                    value: 'Operation',
                  },
                ],
              },
              {
                class: mc(
                  'col-span-1',
                  hasAndroidPlatform() ? '' : 'hidden',
                ),
              },
            ],
            [
              '是否有按钮',
              'has_notify_btn',
              {
                type: 'radio',
                options: [
                  {
                    label: '是',
                    value: true,
                  },
                  {
                    label: '否',
                    value: false,
                  },
                ],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('按钮文案'),
              'notify_btn_text',
              {
                type: 'text',
              },
              {
                class: mc('col-span-1', !!currentNotification.value.has_notify_btn ? '' : 'hidden'),
              },
            ],
            [
              '按钮背景色',
              'notify_btn_bg_color',
              {
                type: 'color',
              },
              {
                // transform: [
                //   (r: string) => r ? r.trim() : '',
                //   (r: string) => r ? r.trim() : '',
                // ],
                // hint: () => currentNotification.value.notify_btn_bg_color && currentNotification.value.notify_btn_bg_color.match(/^#[0-9a-fA-F]{6}$/)
                //   ? (
                //       <div class="rounded-sm !w-4 !h-4 mt-2" style={{
                //         background: currentNotification.value.notify_btn_bg_color,
                //       }}
                //       />
                //     )
                //   : null,
                class: mc('col-span-1', !!currentNotification.value.has_notify_btn ? '' : 'hidden'),
              },
            ],
            [
              'tips',
              'tips',
              {
                type: 'textarea',
                placeholder: '请输入tips',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('tips样式'),
              'tips_style',
              {
                type: 'select',
                options: [
                  {
                    label: 'purple',
                    value: 'purple',
                  },
                  {
                    label: 'blue',
                    value: 'blue',
                  },
                  {
                    label: 'green',
                    value: 'green',
                  },
                  {
                    label: 'orange',
                    value: 'orange',
                  },
                  {
                    label: 'red',
                    value: 'red',
                  },
                  {
                    label: 'cyan',
                    value: 'cyan',
                  },
                ],
              },
              {
                class: mc('col-span-1', !!currentNotification.value.tips ? '' : 'hidden'),
              },
            ],
            [
              '是否有提示按钮',
              'has_notify_btn2',
              {
                type: 'radio',
                options: [
                  {
                    label: '是',
                    value: true,
                  },
                  {
                    label: '否',
                    value: false,
                  },
                ],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('提示按钮文案'),
              'notify_btn2_text',
              {
                type: 'text',
              },
              {
                class: mc('col-span-1', !!currentNotification.value.has_notify_btn2 ? '' : 'hidden'),
              },
            ],
            [
              '提示按钮跳转链接',
              'notify_btn2_deep_link',
              {
                type: 'text',
              },
              {
                class: mc('col-span-1', !!currentNotification.value.has_notify_btn2 ? '' : 'hidden'),
              },
            ],
          ]}
          data={currentNotification.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditPushNotificationModal.value}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} disabled={isUpdating.value} onClick={() => {
          const exclude: string[] = []
          if (+(currentNotification.value.ageing_type || '') !== 2) {
            exclude.push('timed_ts')
          }
          if (+(currentNotification.value.target_user_type || '') !== 2) {
            exclude.push('user_identify_val')
          }
          if (!hasAndroidPlatform()) {
            exclude.push('android_notify_channel')
          }
          if (!currentNotification.value.has_notify_btn) {
            exclude.push('notify_btn_text')
          }
          if (!currentNotification.value.tips) {
            exclude.push('tips_style')
          }
          if (!currentNotification.value.has_notify_btn2) {
            exclude.push('notify_btn2_text')
          }

          if (!currentNotification.value.notify_btn_bg_color) {
            exclude.push('notify_btn_bg_color')
          }
          if (!validateAll({ exclude })) {
            console.log('validateAll', error.value)

            return
          }
          apiCheckDeeplink({
            target_app_ids: (currentNotification.value.target_app_ids as number[])?.join(','), // 生效应用id,用逗号分割
            link: currentNotification.value.link!,
          }).then(() => {
            !currentNotification.value?.id ? void onCreate() : void onEdit()
          }).catch(() => {
            const close = openDialog({
              customClass: 'pb-0',
              title: '提示',
              body: (
                <x-tips>
                  <x-tips-body class="flex-1 flex flex-col overflow-y-auto px-1">
                    deeplink 对应短剧已下线，请确认是否保存
                  </x-tips-body>
                  <x-tips-footer class="flex justify-end gap-x-2">
                    <Button class="btn btn-sm btn-default" onClick={() => close()}>取消</Button>
                    <Button class="btn btn-sm btn-primary" onClick={() => {
                      !currentNotification.value?.id ? void onCreate() : void onEdit()
                      close()
                    }}
                    >确认
                    </Button>
                  </x-tips-footer>
                </x-tips>
              ),
            })
          })
        }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
