import { createComponent, mc } from '@skynet/shared'
import { ref } from 'vue'
import { useNotification } from './use-notification'

export const NotificationPreview = createComponent({
  props: {},
}, props => {
  const { currentNotification } = useNotification()
  const currentState = ref<'initial' | 'expanded'>('initial')

  const onTabClick = (state: 'initial' | 'expanded') => {
    currentState.value = state
  }

  return () => (
    <x-preview-push-notification class="flex flex-col gap-y-3">
      <div role="tablist" class="tabs tabs-boxed">
        <a role="tab" class={mc('tab', currentState.value === 'initial' ? 'tab-active' : '')} onClick={() => onTabClick('initial')}>初始状态</a>
        <a role="tab" class={mc('tab', currentState.value === 'expanded' ? 'tab-active' : '')} onClick={() => onTabClick('expanded')}>展开时图</a>
      </div>
      <x-preview-warp class="w-full h-[400px]">
        <x-preview-content class={mc('w-full flex flex-row items-end gap-x-2 bg-gray-500 text-white p-2 rounded-xl', currentState.value === 'initial' ? '' : 'hidden')}>
          <x-preview-left class="flex-1 flex flex-col overflow-hidden">
            <x-preview-title class="text-xl truncate max-w-full">
              {currentNotification.value?.title}
            </x-preview-title>
            <x-preview-desc class="line-clamp-2 max-w-full break-words">
              {currentNotification.value?.content}
            </x-preview-desc>
          </x-preview-left>
          <img src={currentNotification.value?.image_url?.includes('https://') ? currentNotification.value.image_url : 'https://static-v1.mydramawave.com/push/task/image/notify/' + currentNotification.value.image_url} alt="" class="w-10 h-10 object-cover mb-2" />
        </x-preview-content>
        {/** 展开时图 */}
        <x-preview-content class={mc('w-full py-2  flex flex-col flex-1 gap-y-2 bg-gray-500 text-white px-2 rounded-xl overflow-y-auto', currentState.value === 'expanded' ? '' : 'hidden')}>
          <x-preview-left class="flex flex-col">
            <x-preview-title class="text-xl max-w-full break-words">
              {currentNotification.value?.title}
            </x-preview-title>
            <x-preview-desc class="max-w-full break-words line-clamp-2">
              {currentNotification.value?.content}
            </x-preview-desc>
          </x-preview-left>
          <img src={currentNotification.value?.image_url?.includes('https://') ? currentNotification.value.image_url : 'https://static-v1.mydramawave.com/banner/cover/' + currentNotification.value.image_url} alt="" class="w-full h-fit max-h-[300px] object-cover" />
        </x-preview-content>
      </x-preview-warp>

    </x-preview-push-notification>

  )
})
