
import { httpClient } from 'src/lib/http-client'

export const apiGetInfoPushList = (data: M.PushNotificationSearchOption) =>
  httpClient.post<ApiResponse<M.BannerListRequestResponse>>('/push/task/list', data)

export const apiEditInfoPush = (data: M.PushNotification) =>
  httpClient.post<ApiResponse<null>>('/push/task/save', data)

export const apiGetInfoPushDetail = (data: { id: number }) =>
  httpClient.post<ApiResponse<{
    info: M.PushNotification
  }>>('/push/task/info', data)

export const apiCheckDeeplink = (data: {
  target_app_ids: string // 生效应用id,用逗号分割
  link: string
}) =>
  httpClient.post<ApiResponse<null>>('/content/template/link/check', data)
