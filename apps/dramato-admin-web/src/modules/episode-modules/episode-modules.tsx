/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { watch } from 'vue'
import { Button, Checkbox, DateTime, openDialog, showAlert, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { cloneDeep, set } from 'lodash-es'
import { useEpisodeModules } from './use-episode-modules'
import { useEpisodeTabs } from '../episode-tabs/use-episode-tabs'
import { useEpisodeTags } from '../episode-tags/use-episode-tags'
import { apiDeleteEpisodeModules, apiUpdateEpisodeModulesStatus } from './episode-modules-api'
import { useAppAndLangOptions } from '../options/use-app-options'
import { showModeOptions } from './show-mode-options'

type EpisodeTagsOptions = {
  props: {}
}
export const EpisodeTags = createComponent<EpisodeTagsOptions>({
  props: {},
}, props => {
  const { Form, params, Table, list, loading, search, checkedItems, batchDelete, batchUp, batchDown, showTagsFormDialog } = useEpisodeModules()
  const { tabParams } = useEpisodeTabs()
  const { getTags } = useEpisodeTags()
  const { appOptions, languageOptions } = useAppAndLangOptions(() => params.value.app_id, { onSuccess: search })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !params.value.app_id) {
      params.value.app_id = appOptions.value[0].value
      tabParams.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => params.value.app_id, id => {
    tabParams.value.app_id = id
    params.value.language = id ? (languageOptions.value[0]?.value) : ''
    tabParams.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  watch(() => params.value.language, language => {
    tabParams.value.language = language
    void getTags(language!)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>模块管理</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              void search()
            }}
            onSubmit={() => search()}
            data={params.value}
            items={[
              ['应用', 'app_id', { type: 'select', class: 'w-[240px]', autoInsertEmptyOption: false, options: appOptions.value }, { transform: transformNumber }],
              ['语言', 'language', { type: 'select', autoInsertEmptyOption: false, options: languageOptions.value }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <x-actions class="ml-auto flex gap-x-2">
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchUp}>批量上架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDown}>批量下架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '新建模块')}>新建模块</Button>
            </x-actions>
          </div>
        ),
        table: () => (
          <Table class="tm-table-fix-last-column" loading={loading.value} list={list.value} columns={[
            ['', row => (
              <Checkbox
                label=""
                modelValue={!!checkedItems.value.find(i => i.id === row.id)}
                onUpdate:modelValue={(v: boolean) => {
                  const idx = checkedItems.value.findIndex(i => i.id === row.id)
                  if (idx > -1) {
                    checkedItems.value.splice(idx, 1)
                  } else {
                    checkedItems.value.push(row)
                  }
                }}
              />
            ), { class: 'w-[40px]' }],
            ['关联tab名称', 'tab_name', { class: 'w-[80px]' }],
            ['关联Tab下模块排序', 'module_index', { class: 'w-[120px]' }],
            ['模块名称', 'module_name', { class: 'w-[100px]' }],
            ['展示模块名称文字', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { value: 0, label: '否' },
                { value: 1, label: '是' },
              ].find(item => item.value === row.show_title)?.label ?? ''}
              </span>
            ), { class: 'w-[180px]' }],
            ['模块展示形式', row => (
              <span class="badge badge-outline whitespace-nowrap">{showModeOptions.find(item => item.value === row.show_mode)?.label ?? ''}
              </span>
            ), { class: 'w-[140px]' }],
            ['模块数据源', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { value: 1, label: '算法规则' },
                { value: 2, label: '人工配置' },
                { value: 3, label: '标签配置' },
              ].find(item => item.value === row.content_type)?.label ?? ''}
              </span>
            ), { class: 'w-[150px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.operator_name, { class: 'w-[100px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 0 },
              ].find(item => item.value === row.listing_status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.id) return
                      void apiUpdateEpisodeModulesStatus({
                        ids: [row.id],
                        is_up: row.listing_status !== 1 ? 1 : 2,
                      })
                        .then(() => {
                          void search()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    {row.listing_status !== 1 ? '上架' : '下架'}
                  </Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '修改模块')}>修改</Button>
                  <Button
                    class={mc('btn btn-outline btn-xs', row.listing_status !== 1 ? '' : 'hidden')}
                    onClick={() => {
                      const showTipsDialog = () => {
                        const hideDialog = openDialog({
                          title: '',
                          mainClass: 'pb-0 px-5',
                          body: (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-status-body>是否确认删除tab【{row.module_name}】?</x-status-body>
                              <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" onClick={() => {
                                  void apiDeleteEpisodeModules({
                                    ids: `[${row.id}]`,
                                  })
                                    .then(() => {
                                      void search()
                                    })
                                    .catch((error: any) => {
                                      showAlert(error.response.data.message, 'error')
                                    })
                                  hideDialog()
                                }}
                                >确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }

                      showTipsDialog()
                    }}
                  >
                    删除
                  </Button>
                </div>
              ), {
                class: 'w-[200px]',
              },
            ],
          ]}
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeTags
