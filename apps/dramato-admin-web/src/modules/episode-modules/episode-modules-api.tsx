import { httpClient } from 'src/lib/http-client'

export const apiGetEpisodeModulesList = (data: M.EpisodeModules.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeModules.List.Response>>('/hometab/module/list', data)

export const apiUpdateEpisodeModulesStatus = (data: M.EpisodeModules.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/module/listing_status', data)

export const apiSaveEpisodeModules = (data: M.EpisodeModules.Save.Request) =>
  httpClient.post<ApiResponse>('/hometab/module/save', data)

export const apiDeleteEpisodeModules = (d: M.EpisodeModules.Delete.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/module/delete', d)
