/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiDeleteEpisodeModules, apiGetEpisodeModulesList, apiSaveEpisodeModules, apiUpdateEpisodeModulesStatus } from './episode-modules-api'
import { EpisodeModulesForm } from './episode-modules-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeModules = () => {
  return {
    tags, Form, params, Table, list, loading, search, changeTab, module, checkedItems, batchDelete, batchUp, batchDown,
    showTagsFormDialog, hideTagsFormDialog, onSave,
  }
}

const Form = CreateForm<M.EpisodeModules.List.Request>()
const params = ref<M.EpisodeModules.List.Request>({})

const Table = CreateTableOld<M.EpisodeModules.List.Item>()
const list = ref<M.EpisodeModules.List.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([
//   {
//     language_code: 'zh-CN',
//     label_id: 29,
//     content: '婚前恋爱',
//   },
])

const voidModule: M.EpisodeModules.List.Item = {
  id: 0, // 可选，>0是编辑， =0 是create
  platform: 1, // 平台 1-IOS 2-Android
  language: 'en', // 语言类型
  tab_name: '', // tab名称

  home_tab_info_id: 1, // 模块关联的tab_id
  module_name: '', // 名称
  module_index: 1, // 排序
  module_key: '', // 模块业务名称
  show_mode: 'column_horizontal', // 展示模式，column_horizontal 单排流，column_vertical 双排流，column_vertical_three 三排流
  content_type: 1, // 内容类型 1、算法规则，2、人工配置
  content_key: 'featured', // 算法规则（featured，popular_choice，holdback），人工配置（拉取内容key接口）
  show_title: 1, // 1 展示， 0 隐藏

}
const module = ref<M.EpisodeModules.List.Item>({ ...voidModule })

const checkedItems = ref<M.EpisodeModules.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiGetEpisodeModulesList({
    ...params.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
}

const changeTab = (item: Partial<M.EpisodeModules.List.Item>) => {
  module.value = Object.assign({}, voidModule, item)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeModules.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeModulesForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const onSave = (d: M.EpisodeModules.List.Item) => {
  void apiSaveEpisodeModules({
    app_id: params.value.app_id,
    language: params.value.language,
    item: { ...d, listing_status: 0 },
  }).then(d => {
    console.log('>>> d')

    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.message || '保存失败', 'error')
  })
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架tab【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeModulesStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认下架tab【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeModulesStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 2,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDelete = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认删除tab【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiDeleteEpisodeModules({
                ids: '[' + checkedItems.value.map(item => item.id).join(',') + ']',
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}
