declare namespace M {
  namespace EpisodeModules {
    namespace List {
      interface Request {
        app_id?: number
        language?: string
      }

      interface Item {
        id: number
        app_id?: number // 应用 ID
        app_key?: string // 应用 key
        platform: number // 平台 1-IOS 2-Android
        language: string // 语言类型
        home_tab_info_id: number // 模块关联的tab_id
        tab_name: string // tab名称
        module_name: string // 名称
        module_index: number // 排序
        module_key: string // 模块业务名称
        show_mode: string // 展示模式，column_horizontal 单排流，column_vertical 双排流，column_vertical_three 三排流
        content_type: number // 内容类型 1、算法规则，2、人工配置
        content_key: string // 算法规则（featured，popular_choice，holdback），人工配置（拉取内容key接口）
        show_title: number // 1 展示， 0 隐藏
        listing_status?: number // 状态 1 上架 2 下架
        operator_name?: string // 修改人
        created?: number // 创建时间
        updated?: number // 更新时间
      }

      interface Response {
        list: Item[]
      }
    }

    namespace Save {
      interface Request extends List.Request {
        item: Omit<List.Item, 'id', 'platform', 'home_tab_info_id', 'module_name', 'module_index', 'module_key', 'show_mode', 'content_type', 'content_key', 'show_title'>
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        is_up: number // 是否上架  1 上架 2 下架
      }
    }

    // TODO
    namespace GetSeriesCountRequest {
      interface Request extends List.Request {
        label_ids: string
      }

      interface Response {
        count: number
      }
    }

    namespace SortTab {
      interface Request extends List.Request {
        curr_row_id: number
        to_sort_no: number // 目标位置
        to_pre_row_id: number // 目标位置的前一个元素
        to_next_row_id: number // 目标位置的后一个元素
      }
    }

    namespace PreviewTab {
      interface Request extends List.Request {}

      interface Response {
        tab_list: {
          tab_name: string
          tab_key: string
        }[]
      }
    }

    namespace ContentPreviewTab {
      interface Request extends List.Request {
        home_tab_info_id: number
      }

    }

    namespace Delete {
      interface Request extends List.Request {
        ids: string
      }
    }
  }
}
