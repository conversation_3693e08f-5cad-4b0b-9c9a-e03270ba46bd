import { createComponent, useValidator } from '@skynet/shared'
import { <PERSON>ton, CreateForm, transformInteger } from '@skynet/ui'
// import { useBanner } from './use-banner.tsx'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useEpisodeModules } from './use-episode-modules.tsx'
import { useEpisodeTabs } from '../episode-tabs/use-episode-tabs.tsx'
import { onMounted, ref, watch } from 'vue'
import { showModeOptions } from './show-mode-options.ts'
import { useEpisodeTags } from '../episode-tags/use-episode-tags.tsx'
import { manualContentKey } from './options.ts'

export const EpisodeModulesForm = createComponent(null, () => {
  const { module, hideTagsFormDialog, onSave } = useEpisodeModules()
  const { search, list } = useEpisodeTabs()
  const { tags } = useEpisodeTags()
  const Form = CreateForm<M.Banner>()

  const formRules = z.object({
    module_name: z.string().min(1, '请输入关联tab名称').max(50, '最多50个字符'),
    module_index: z.number().min(1, '请输入关联Tab下模块排序'),
    module_key: z.string().min(1, '请输入模块key'),
  })

  const { error, validateAll } = useValidator(module, formRules)

  const algorithmContentKey = [
    { value: 'featured', label: 'featured' },
    { value: 'popular_choice', label: 'popular_choice' },
    { value: 'holdback', label: 'holdback' },
    { value: 'reward', label: 'reward' },
    { value: 'exclusive', label: 'exclusive' },
  ]

  const contentKey = ref<typeof algorithmContentKey | typeof manualContentKey>([])

  watch(
    () => module.value.content_type,
    () => {
      if (module.value.content_type === 1) {
        contentKey.value = algorithmContentKey
        return
      } else if (module.value.content_type === 2) {
        contentKey.value = manualContentKey
      } else {
        if (algorithmContentKey.some(item => item.value === module.value.content_key) || manualContentKey.some(item => item.value === module.value.content_key)) {
          module.value.content_key = ''
        }
      }
    },
    { deep: true, immediate: true },
  )

  onMounted(() => {
    void search()
  })

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1 flex-1"
          data={module.value}
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'content_key' && module.value.content_type === 3) {
              const ids = (value as string || '').split(',').map(Number)
              const labels = tags.value.filter(item => ids.includes(item.label_id))
              set(module.value || {}, 'content_key', labels.map(item => item.content).join(','))
            }
            set(module.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('关联Tab'),
              path: 'home_tab_info_id',
              transform: transformInteger,
              input: {
                type: 'select',
                options: list.value.map((l, index) => ({
                  value: l.id,
                  label: l.tab_name,
                })),
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('关联Tab下模块排序'),
              path: 'module_index',
              transform: transformInteger,
              input: {
                type: 'text',
                placeholder: '请输入关联Tab下模块排序',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块名称*（页面展示名称）'),
              path: 'module_name',
              input: {
                type: 'text',
                placeholder: '请输入模块名称',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块业务名称*（英文/不要空格/不要emoji）'),
              path: 'module_key',
              input: {
                type: 'text',
                placeholder: '请输入模块业务名称',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块名称文字是否展示（是/否）'),
              path: 'show_title',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 0, label: '否' },
                  { value: 1, label: '是' },
                ],
              },
            },
            {
              label: requiredLabel('模块展示形式（单排流、双排流、三排流）'),
              path: 'show_mode',
              input: {
                type: 'select',
                options: showModeOptions,
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块数据源类型（人工配置、算法规则）'),
              path: 'content_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: [{
                  value: 1,
                  label: '算法规则',
                }, {
                  value: 2,
                  label: '人工配置',
                }, {
                  value: 3,
                  label: '标签配置',
                }],
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'select',
                options: contentKey.value,
                autoInsertEmptyOption: false,
              },
              class: `col-span-3 ${module.value.content_type === 3 ? 'hidden' : ''}`,
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: tags.value.map(tag => ({
                  value: tag.label_id,
                  label: tag.content,
                })),
              },
              transform: [
                (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
              ] as const,
              class: `col-span-3 ${module.value.content_type !== 3 ? 'h-0 overflow-hidden' : 'pb-40'}`,
            },
          ]}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(module.value)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
