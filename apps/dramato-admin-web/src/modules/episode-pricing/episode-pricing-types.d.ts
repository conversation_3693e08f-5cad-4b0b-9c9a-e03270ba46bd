declare namespace M {
  namespace EpisodePricing {
    interface ListItem {
      id: number
      id: number
      operation?: number // 操作类型(-1:删除 0:新建 1:上下架，2:修改)
      status?: number  // 1上架 0下架
      name?: string
      weight?: number
      series_count?: number
      price_params?: Country[]
      white_list?: number[]
      package_ids?: number[]
      update_name?: string
      update_time?: number | string
    }

    namespace List {
      interface Params {
        name?: string
        status?: number // 状态 1 线上 2 未上架
      }

      interface Response {
        list: ListItem[]
        page_info: PageInfo2
      }
    }

    namespace UpdateState {
      // 定义 price_items 类型
      interface PriceItem {
        price: number;
        episodes_scale: number;
      }

      // 定义 price_items 类型
      interface PriceParam {
        code?: string
        name?: string
        type?: number // 1 国家 2 地区
        state?: boolean // true 常规； false分段
        price?: number | string // 常规金币数
        price_items?: PriceItem[]; // 分段金币数
      }
      interface Params {
        id: number
        operation?: number // 操作类型(-1:删除 0:新建 1:上下架，2:修改)
        status?: number  // 1上架 0下架
        name?: string;
        weight?: number;
        series_count?: number;
        price_params?: Country[];
        white_list?: number[];
        package_ids?: number[];
      }
    }

    namespace Save {
      // interface EpisodeSeries {
      //   drama_id: number
      //   series_key: string // 剧集id
      //   title: string // 剧名
      //   listing_time: number // 上架时间
      //   episodes_number: number // 集数
      //   start_paying_episodes: number // 开始付费集数
      //   count: number // 总集数
      //   episodes_price: number// 单集价格
      //   episodes_adjust_price: number// 调整价格
      // }
      interface priceListItem {
        episodes_scale: number
        price: number
      }
      interface price_params_list {
        code?: string
        name?: string
        type?: number // 1 国家 2 地区
        state: boolean // true 常规； false分段
        price?: number | string // 常规金币数
        price_items?: priceListItem[] // 分段金币数
      }
      interface Params {
        id?: string // id 非新增操作时必传
        name?: string
        status?: number // 状态(-1:删除 0:未上架 1:已上架)
        price_params?: Country[]// 国家地区配置
        weight?: number // 定价权重
        series_count?: number // 剧集数量
        white_list?: string // 测试白名单用户id
        package_ids?: string // 剧包id
      }
    }

    interface Country {
      code?: string
      name?: string
      type?: number // 1 国家 2 地区
      state?: boolean // true 常规； false分段
      price?: number | string // 常规金币数
      price_items?: priceListItem[] // 分段金币数
    }
  }
}
