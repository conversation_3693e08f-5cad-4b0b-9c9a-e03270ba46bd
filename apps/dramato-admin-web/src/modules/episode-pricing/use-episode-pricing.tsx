import { ref } from 'vue'
import { map, set, cloneDeep, cloneDeepWith } from 'lodash-es'
import { showAlert, openDialog, CreateForm, CreateTableOld } from '@skynet/ui'
import { useRouter } from 'vue-router'
import { apiGetCountryList, apiEpisodePriceFetch, apiUpdateEpisodePriceStatus, apiGetEpisodePriceList } from './episode-pricing-api'
import { UpdateEpisodePricingPage } from './update-episode-pricing-page'
const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'
export const useEpisodePricing = () => {
  return {
    resetFormData, countryRegions, getCountryRegions, episodePricing, isUpdating, getApiSaveEpisodePrice, isFetchUpdating, getApiEpisodePriceFetch, showTagsFormDialog, selected_price_params_list,
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
  }
}

const resetFormData = () => { }


const Form = CreateForm<M.EpisodePricing.List.Params>()
const params = ref<M.EpisodePricing.List.Params>({})

const Table = CreateTableOld<M.EpisodePricing.ListItem>()
const list = ref<M.EpisodePricing.ListItem[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(0)

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetEpisodePriceList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value
    }
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  page.value = _page
  return list.value
}

const countryRegions = ref<M.EpisodePricing.Country[]>([])
const getCountryRegions = () => {
  void apiGetCountryList().then(res => {
    const countryData = res.data?.country_region_list ?? []
    const countryDatas = map(countryData, item => {
      return {
        ...item,
        state: true,
        price: '',
        price_items: [{ price: '', episodes_scale: '' }, { price: '', episodes_scale: '' }, { price: '', episodes_scale: '' }],
      }
    })
    set(episodePricing.value || {}, 'price_params', countryDatas)
    countryRegions.value = countryDatas
    console.log(countryRegions.value)
  })
}

// const episodePricing = ref<M.EpisodePricing.Save.Params>({ price_factor: 100 })
const episodePricing = ref<M.EpisodePricing.UpdateState.Params>()

const isUpdating = ref(false)
const router = useRouter()
const isFetchUpdating = ref(false)
const getApiEpisodePriceFetch = () => {
  isFetchUpdating.value = true
  void apiEpisodePriceFetch({
    package_ids: episodePricing.value?.package_ids || []
  })
    .then(rs => {
      episodePricing.value!.series_count = rs?.data?.series_count || 0
    })
    .catch(error => {
      showAlert(error.response.data.message || '数量获取失败', 'error')
    })
    .finally(() => {
      isFetchUpdating.value = false
    })
}
const getApiSaveEpisodePrice = () => {
  const priceParamsClone = cloneDeep(episodePricing.value?.price_params || [])
  priceParamsClone.map((i: any) => {
    if (i.state) {
      i.price = i.price * 100
    }
    if (!i.state) {
      i.price_items.map((j: any) => { j.price = j.price * 100 })
    }
  })
  isUpdating.value = true
  void apiUpdateEpisodePriceStatus({
    id: episodePricing.value?.id ? episodePricing.value?.id : 0,
    operation: episodePricing.value?.id ? 2 : 0,
    status: 0,
    ...episodePricing.value,
    price_params: priceParamsClone
  })
    .then(rs => {
      showAlert('保存成功')
      hideTagsFormDialog.value && hideTagsFormDialog.value()
      search(1)
    })
    .catch(error => {
      console.log(error);
      showAlert(error.response.data.ret_msg || '保存失败', 'error')
    })
    .finally(() => {
      isUpdating.value = false
    })
}

const selected_price_params_list = ref<M.EpisodePricing.Country[]>([]) // 选中的下拉项
const changeTab = (item: M.EpisodePricing.UpdateState.Params, title: string) => {
  episodePricing.value = item
  if (title === '新建定价') {
    episodePricing.value = { id: 0 }
    set(episodePricing.value || {}, 'price_params', cloneDeep(countryRegions.value))
    selected_price_params_list.value = []
  } else {
    selected_price_params_list.value = item.price_params!
    selected_price_params_list.value.map((i) => {
      if (i.state) {
        i.price_items = [{ price: '', episodes_scale: '' }, { price: '', episodes_scale: '' }, { price: '', episodes_scale: '' }]
        i.price = Math.floor((i.price as number) / 100)
      }
      if (!i.state) {
        i.price = ''
        i.price_items.map((j: any) => { j.price = Math.floor((j.price as number) / 100) })
      }
    })
    const params_list = replaceWithMap(cloneDeep(countryRegions.value), selected_price_params_list.value)
    set(episodePricing.value || {}, 'price_params', params_list)
  }
}
const replaceWithMap = (arr1: any, arr2: any) => {
  const idMap = new Map();
  // 将 array2 中的元素以 code 为键存储在 Map 中
  arr2.forEach((item: any) => {
    idMap.set(item.code, item);
  });
  // 遍历 array1，根据 code 从 Map 中查找替换元素
  const result = arr1.map((item: any) => {
    return idMap.has(item.code) ? idMap.get(item.code) : item;
  });
  return result;
}
const hideTagsFormDialog = ref()
const showTagsFormDialog = (d: M.EpisodePricing.UpdateState.Params, title: string) => {
  changeTab(d, title)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <UpdateEpisodePricingPage />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}