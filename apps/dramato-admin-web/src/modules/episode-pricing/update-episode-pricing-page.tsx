import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformNumber, transformNumberArray } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useEpisodePricing } from './use-episode-pricing'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
import { useRouter } from 'vue-router'

export const UpdateEpisodePricingPage = createComponent(null, props => {
  const router = useRouter()
  const { countryRegions, getCountryRegions, episodePricing, isUpdating, getApiSaveEpisodePrice, isFetchUpdating, getApiEpisodePriceFetch, selected_price_params_list } = useEpisodePricing()

  const Form = CreateForm<M.EpisodePricing.UpdateState.Params>()

  const formRules = z.object({
    name: z.string().min(1, '请输入定价组名称').max(100, '最多100个字符'),
    weight: z.number().min(1, '请输入定价权重').max(999, '支持输入1~999的正整数'),
    package_ids: z.array(z.number()).min(1, '请选择剧包'),
  })

  const { error, validateAll } = useValidator(episodePricing, formRules)

  onMounted(() => {
    if (countryRegions.value.length == 0) { getCountryRegions() }

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(1, [1, 2, 3, 4, 5])
  })
  const seriesPackageStore = useSeriesPackageStore()

  const toggleCheckItem = (e: Event, item: M.EpisodePricing.UpdateState.PriceParam) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selected_price_params_list.value.push(item)
    } else {
      const newArray1 = selected_price_params_list.value.filter(i => i.code !== item.code)
      selected_price_params_list.value = JSON.parse(JSON.stringify(newArray1))
    }
  }
  return () => (
    <>
      <div class="flex flex-col gap-y-[25px] px-[20px]">
        <Form
          class="grid grid-cols-1 gap-y-3"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(episodePricing.value || {}, path, value)
          }}
          items={[
            () => (
              <h1 class="flex items-center pl-[10px] text-2xl font-medium">
                <span class="mr-2 flex size-6 items-center justify-center rounded-[50%] bg-red-400 text-[16px] text-white">1</span>配置</h1>
            ),
            [[
              requiredLabel('定价组名称'),
              'name',
              {
                type: 'textarea',
                maxlength: 100,
                placeholder: '请输入定价组名称，最大支持100字符',
              },
              {
                hint: () => <span>例如：美国澳洲热门剧</span>,
                class: 'flex-1',
              },
            ],
            {
              label: '定价组ID',
              path: 'id',
              input: {
                type: 'text',
                disabled: true,
              },
              class: mc('w-[240px]', episodePricing.value?.id ? '' : 'hidden'),
            }],
            [
              requiredLabel('剧包ID'),
              'package_ids',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: seriesPackageStore.list.value.map(n => {
                  return { value: n.id || 0, label: `${n.id}/${n.package_name}` }
                }),
              },
            ],
            () => (
              <div class="flex items-center">
                <Button class="btn btn-sm w-[150px] border-gray-500 flex items-center" onClick={() => {
                  if (episodePricing.value?.package_ids) {
                    getApiEpisodePriceFetch()
                  } else {
                    showAlert('请先选剧包ID！', 'error')
                  }
                }}
                >
                  {isFetchUpdating.value && <span class="loading loading-spinner size-4" />}
                  估计覆盖短剧数
                </Button>
                <div class={mc('ml-7', !episodePricing.value?.series_count && 'hidden')}>{episodePricing.value?.series_count}个</div>
              </div>
            ),
            [
              <div class="flex flex-col"><span>白名单</span><small class="text-gray-400">输入用户ID，用英文逗号隔开；</small><small class="text-gray-400">从的T0-T4中，第一个选中的生效</small></div>,
              'white_list',
              {
                type: 'textarea',
                placeholder: '请输入',
              },
              {
                transform: transformNumberArray,
              },
            ],
            () => (
              <h1 class="flex items-center pl-[10px] text-2xl font-medium">
                <span class="mr-2 flex size-6 items-center justify-center rounded-[50%] bg-yellow-500 text-[16px] text-white">2</span>定价</h1>
            ),
            [
              <small class="ml-2 text-gray-400">每集所需单价，建议为5的倍数；分段式总和需为100%</small>,
              'price_params',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <>
                      <div>
                        {
                          (r.value || []).map((item: M.EpisodePricing.UpdateState.PriceParam) => (
                            <div class="mt-3 flex items-center">
                              <label class="flex max-w-full items-center gap-2 py-1">
                                <input type="checkbox" checked={selected_price_params_list.value.findIndex(i => i.code === item.code) !== -1} onChange={(e: Event) => toggleCheckItem(e, item)} />
                                <div class="truncate">{item.code}</div>
                              </label>
                              {item.state ? (
                                <div class="flex items-center">
                                  <div class="input input-sm input-bordered ml-5 flex w-[300px]">
                                    <input type="number" min="0" v-model={item.price} placeholder="正整数" class="w-full" />
                                    <span class="inline-block w-[40px]">金币</span>
                                  </div>
                                  <Button
                                    class="btn btn-xs btn-link"
                                    onClick={() => {
                                      item.state = !item.state
                                    }}
                                  >
                                    分段式定义
                                  </Button>
                                </div>
                              ) : (
                                <div class="ml-5 w-[600px]">
                                  <div class="flex w-full">
                                    {
                                      (item.price_items || []).map((i: M.EpisodePricing.Save.priceListItem, index: number) => (
                                        <div class="flex  w-full items-center">
                                          <div class="flex flex-col items-center">
                                            <div class="mx-4 ml-5 flex w-[180px] items-center justify-end">
                                              <div class="flex-1 text-center">{index == 0 ? '付费集前' : index == 1 ? '中' : '后'}</div>
                                              <div class="input input-sm input-bordered mb-2 flex w-[90px]">
                                                <input type="number" min="0" max="99" v-model={i.episodes_scale} disabled={index === item.price_items!.length - 1}
                                                  onInput={(e: Event) => {
                                                    if (index < item.price_items!.length - 1) {
                                                      let sum = 0
                                                      // 计算前两个 input 的和
                                                      for (let i = 0; i < item.price_items!.length - 1; i++) {
                                                        const value = item.price_items![i].episodes_scale || 0
                                                        sum += value
                                                      }
                                                      // 计算最后一个 input 的值
                                                      item.price_items![2].episodes_scale = 100 - sum
                                                    }
                                                  }}
                                                  placeholder="正整数" class="w-full" />
                                                <span class="inline-block w-[20px]">%</span>
                                              </div>
                                            </div>
                                            <div class="input input-sm input-bordered mx-4 ml-5 flex w-[180px]">
                                              <input type="number" min="0" v-model={i.price} placeholder="正整数" class="w-full" />
                                              <span class="inline-block w-[40px]">金币</span>
                                            </div>
                                          </div>
                                        </div>
                                      ))
                                    }
                                  </div>
                                  <Button
                                    class="btn btn-xs btn-link"
                                    onClick={() => { item.state = !item.state }}
                                  >
                                    恢复常规式
                                  </Button>
                                </div>
                              )}
                            </div>
                          ))
                        }
                      </div>
                    </>
                  )
                },
              },
            ],
            [
              <span class="flex items-center">{requiredLabel('定价权重')} <small class="ml-2 text-gray-400">越大优先级越大，最大999</small></span>,
              'weight',
              {
                type: 'number',
                placeholder: '1~999的正整数',
                min: 1,
                max: 999,
              },
              { transform: transformNumber },
            ],
          ]}
          data={episodePricing.value as M.EpisodePricing.UpdateState.Params}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px] pb-3">
        <Button class="btn  btn-sm" onClick={() => {
          void router.push('/episode-pricing')
        }}
        >取消
        </Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value}
          onClick={() => {
            const exclude: string[] = []
            if (!validateAll({ exclude })) {
              return
            }
            console.log(selected_price_params_list.value)

            selected_price_params_list.value.map(i => {
              if (i.state) { delete i.price_items }
              if (!i.state) { delete i.price }
            })
            episodePricing.value!.price_params = selected_price_params_list.value
            console.log(episodePricing.value)
            getApiSaveEpisodePrice()
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '保存'}
        </Button>
      </div>
    </>
  )
})

export default UpdateEpisodePricingPage
