import { httpClient } from 'src/lib/http-client'

interface apiGetEpisodePriceListParams extends M.EpisodePricing.List.Params {
  page_info: {
    offset: number
    size: number
  }
}
export const apiGetEpisodePriceList = (data: apiGetEpisodePriceListParams) =>
  httpClient.post<ApiResponse<M.EpisodePricing.List.Response>>('/episode-price/list', data)

export const apiUpdateEpisodePriceStatus = (data: M.EpisodePricing.UpdateState.Params) =>
  httpClient.post<ApiResponse<boolean>>('/episode-price/upset', data)

export const apiEpisodePriceFetch = (data: { package_ids: number[] }) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/episode-price/fetch', data)

export const apiGetCountryList = () =>
  httpClient.get<ApiResponse<{ country_region_list: M.EpisodePricing.Country[] }>>('/series-region-price/country', {})
