/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted } from 'vue'
import { Button, openDialog, Pager, showAlert, transformInteger } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useEpisodePricing } from './use-episode-pricing'
import { set, cloneDeep } from 'lodash-es'
import { apiUpdateEpisodePriceStatus } from './episode-pricing-api'
type EpisodePricingPageOptions = {
  props: {}
}
export const EpisodePricingPage = createComponent<EpisodePricingPageOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    resetFormData,
    countryRegions,
    getCountryRegions,
    showTagsFormDialog,
  } = useEpisodePricing()

  onMounted(() => {
    void search(1)
    getCountryRegions()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>剧集定价</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              resetFormData()
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              // ['定价组ID', 'name', { type: 'text' }],
              ['筛选条件', 'condition_type', {
                type: 'select',
                options: [{ label: 'ID', value: 1 }, { label: '名称', value: 2 }, { label: '地区', value: 3 }, { label: '资源ID', value: 4 }, { label: '状态', value: 5 }],
              },
              {
                transform: transformInteger,
                class: 'w-[200px]',
              },
              ],
              ['文本内容', 'condition_value', { type: 'text' }, { hint: '当筛选条件为‘状态’时，文本内容：未上线输入0；上线输入1' }],

            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            剧集定价列表
            <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({ id: 0 }, '新建定价')}>新建定价</Button>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['定价组ID', 'id', { class: 'w-[100px]' }],
            ['组名称', 'name', { class: 'w-[200px]' }],
            ['权重', 'weight', { class: 'w-[80px]' }],
            ['状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '未上架', value: 0 },
                { label: '线上', value: 1 },
                { label: '未上架', value: 2 },
              ].find(item => item.value === row.status)?.label ?? row.status}
              </span>
            ), { class: 'w-[100px]' }],
            ['覆盖剧数（估计）', 'series_count', { class: 'w-[200px]' }],
            ['定价地区', row => (
              <span>
                {(row!.price_params || []).map(item => item.code).join('、')}
              </span>
            ), { class: 'w-[200px]' }],
            ['修改时间', 'update_time', { class: 'w-[200px]' }],
            ['修改人', 'update_name', { class: 'w-[200px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button
                  class="btn btn-outline btn-xs"
                  onClick={() => {
                    if (!row.id) return
                    void apiUpdateEpisodePriceStatus({
                      id: row.id,
                      operation: 1,
                      status: row.status === 1 ? 0 : 1,
                    })
                      .then(() => {
                        void search(page.value)
                      })
                      .catch((error: any) => {
                        showAlert(error.response.data.message, 'error')
                      })
                  }}
                >
                  {row.status === 1 ? '下架' : '上架'}
                </Button>
                <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '编辑定价')}>编辑</Button>
                <Button
                  class={mc('btn btn-outline btn-xs', row.status === 1 ? 'hidden' : '')}
                  onClick={() => {
                    const showTipsDialog = () => {
                      const hideDialog = openDialog({
                        title: '',
                        mainClass: 'pb-0 px-5',
                        body: (
                          <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                            <x-status-body>是否确认删除定价组【{row.name}】?</x-status-body>
                            <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                              <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                              <button class="btn btn-primary btn-sm" onClick={() => {
                                void apiUpdateEpisodePriceStatus({
                                  id: row.id,
                                  operation: -1,
                                })
                                  .then(() => {
                                    void search(page.value)
                                  })
                                  .catch((error: any) => {
                                    showAlert(error.response.data.message, 'error')
                                  })
                                hideDialog()
                              }}
                              >确定
                              </button>
                            </x-status-footer>
                          </x-status-confirm-dialog>
                        ),
                      })
                    }

                    showTipsDialog()
                  }}
                >
                  删除
                </Button>
              </div>
            ), {
              class: 'w-[200px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default EpisodePricingPage
