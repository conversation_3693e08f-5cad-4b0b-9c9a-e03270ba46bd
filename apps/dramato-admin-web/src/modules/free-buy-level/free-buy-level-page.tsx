/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, openDialog, Pager, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiGetAppOptions } from '../application/application.api'
import { RechargeLevelForm } from './free-buy-form'
import { useRechargeLevelPage } from './use-free-buy-level'

const dialogMainClass = 'flex flex-col flex-auto pb-0'

type TopUpLevelPageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.FreeBuyLevel[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.FreeBuyLevel) => void
    remove: (id: M.FreeBuyLevel) => void
  }
}
export const TopUpLevelPage = createComponent<TopUpLevelPageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.FreeBuyLevel) => {},
    remove: (item: M.FreeBuyLevel) => {},
  },
}, (props, { emit }) => {
  const {
    initTopUpLevel,
    searchForm,
    list,
    originalList,
    onSearch,
    onReset,
    applicationList,
    currentLevel,
    closeEditRechargeLevelModal,
    onEditRechargeLevelPriority,
    // onEditRechargeLevelStatus,
    total,
    page,
    onPageChange,
    onPageSizeChange,
  } = useRechargeLevelPage()

  const Form = CreateForm<M.FreeBuyLevelSearchParams>()
  const Table = CreateTableOld<M.FreeBuyLevel>()
  const currentPriority = ref(1)

  const columns: TableColumnOld<M.FreeBuyLevel>[] = [
    [
      '',
      row => {
        const id = row.id as number
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={props.checkedItem.map(i => i.id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.id).includes(id)) {
                  const found = originalList.value.find(i => i.id === id)
                  if (!found) return
                  emit('add', found)
                }
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['应用名称', row => applicationList.value.find(app => +(app.id || '') === +(row?.app_id || ''))?.app_name, { class: 'w-[200px]' }],
    ['序号', row => (
      <label class={mc('h-8', row.status === 1 ? '' : 'hidden')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          onInput={(e: Event) => {
            currentPriority.value = Number((e.target as HTMLInputElement).value) || 0
            void onEditRechargeLevelPriority({
              id: row.id || 0,
              priority: currentPriority.value,
            })
          }}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ),
    { class: mc('w-[100px]', props.hasPriority ? '' : 'hidden') },
    ],
    ['现价/金币数', 'coins', { class: 'w-[100px]' }],
    ['售卖观影券数', 'amount', { class: 'w-[100px]' }],
    ['赠送观影券数', 'amount_bonus', { class: 'w-[100px]' }],
    ['状态',
      row => (
        <div class="space-x-1 flex items-center">
          {row.status === 1
            ? (
                <>
                  <div class="badge bg-green-600 badge-xs" />
                  <div>展示</div>
                </>
              )
            : (
                <>
                  <div class="badge bg-gray-300 badge-xs" />
                  <div>不展示</div>
                </>
              )}
        </div>
      ),
      { class: 'w-[100px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm') : '-',
      { class: 'w-[150px]' },
    ],
    ['更新人', 'update_user', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap">
          {/* <Button class="btn btn-link btn-sm" onClick={() => onEditRechargeLevelStatus(row)}>{row.status === 1 ? '禁用' : '启用'}
          </Button> */}
          <Button class="btn btn-link btn-sm" onClick={() => {
            currentLevel.value = { ...row }
            closeEditRechargeLevelModal.value = openDialog({
              title: '编辑观影券兑换档位',
              body: <RechargeLevelForm />,
              mainClass: dialogMainClass,
            })
          }}
          >编辑
          </Button>
        </div>
      ),
      { class: mc('w-[80px]', props.hasActions ? '' : 'hidden') },
    ],
  ]

  onMounted(() => {
    void apiGetAppOptions({ app_name: '' }).then(res => {
      if (!res.data) return
      applicationList.value = (res.data.list || []).filter((i: any) => i.app_name.includes('Freereels') || i.app_name.includes('FreeReels'))
      searchForm.value.app_id = +`${applicationList.value.filter(i => i.platform === props.platform)[0]?.id || ''}`
      set(searchForm.value, 'store', props.platform === 1 ? 'Apple Store' : 'Google Play')
      onSearch(true)
    })
  })

  watch(
    () => searchForm.value.app_id,
    () => {
      const platform = applicationList.value.find(item => item.id === +(searchForm.value.app_id || ''))?.platform
      if (platform === 1) {
        set(searchForm.value, 'store', 'Apple Store')
      } else if (platform === 2) {
        set(searchForm.value, 'store', 'Google Play')
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  return () => (
    <NavFormTablePager>
      {{
        nav: () => props.hasNav
          ? (
              <ul>
                <li>观影券兑换档位管理</li>
              </ul>
            )
          : null,
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              {
                label: () => (
                  <div>应用： <span class="text-sm">上架商店：{ searchForm.value.store }</span></div>),
                path: 'app_id',
                input: {
                  type: 'select',
                  class: 'w-[300px]',
                  autoInsertEmptyOption: false,
                  options: applicationList.value.map(item => ({
                    label: item.app_name,
                    value: item.id,
                  })),
                  // disabled: props.appIdSelectDisabled,
                },
                transform: transformNumber,
              },
              { label: '档位名称', path: 'title',
                input: { type: 'text' } },
              { label: '状态',
                path: 'status',
                transform: transformNumber,
                input: { type: 'select', options: [
                  {
                    label: '展示',
                    value: 1,
                  },
                  {
                    label: '不展示',
                    value: 2,
                  },
                ] } },
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="w-full flex justify-between items-center">
                  <span>观影券兑换档位列表</span>
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    currentLevel.value = {
                      ...initTopUpLevel,
                      app_id: +searchForm.value.app_id,
                    }

                    closeEditRechargeLevelModal.value = openDialog({
                      title: '新建观影券兑换档位',
                      body: <RechargeLevelForm />,
                      mainClass: dialogMainClass,
                    })
                  }}
                  >新建观影券兑换档位
                  </Button>
                </x-table-actions>
              )
            : null
        ),
        table: () => <Table list={list.value} columns={columns} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={page.value}
                v-model:size={searchForm.value.page_size}
                total={total.value}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default TopUpLevelPage
