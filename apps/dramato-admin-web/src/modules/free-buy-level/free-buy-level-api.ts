
import { httpClient } from 'src/lib/http-client'

export const apiGetRechargeLevels = (data: M.FreeBuyLevelSearchParams) =>
  httpClient.post<ApiResponse<{
    items: M.FreeBuyLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/exchange/product/list', data)

export const apiCreateRechargeLevel = (data: M.FreeBuyLevel) =>
  httpClient.post<ApiResponse<{
    items: M.FreeBuyLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/exchange/product/save', data)

export const apiEditRechargeLevel = (data: M.FreeBuyLevel) =>
  httpClient.post<ApiResponse<{
    items: M.FreeBuyLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/exchange/product/save', data)

export const apiUpdateRechargeLevelStatus = (data: { id: number, status: number }) =>
  httpClient.post<ApiResponse<null>>('/exchange/product/status', data)

export const apiUpdateRechargeLevelPriority = (data: { id: number, priority: number }) =>
  httpClient.post<ApiResponse<null>>('/exchange/product/sort', data)
