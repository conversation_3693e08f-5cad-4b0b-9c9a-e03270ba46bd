import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger, transformNumber, transformNumber2 } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useRechargeLevelPage } from './use-free-buy-level'

export const RechargeLevelForm = createComponent(null, () => {
  const { currentLevel, applicationList, onEdit, onCreate, closeEditRechargeLevelModal } = useRechargeLevelPage()
  const Form = CreateForm<M.FreeBuyLevel>()

  const formRules = z.object({
    title: z.string().min(1, '请输入档位名称').max(40, '最多40个字符'),
    app_id: z.number({ message: '请选择生效应用' }),
    priority: z.number({ message: '请选择序号' }).max(12, '最大12').min(1, '最小1'),
    coins: z.number({ message: '请输入现价' }),
  })

  const { error, validateAll } = useValidator(currentLevel, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <h1 class="font-medium border-l-[2px] border-l-solid border-l-primary pl-[10px]">基础信息</h1>
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentLevel.value || {}, path, value)
          }}
          items={[
            // currentLevel.value.id ? { label: '档位ID：', path: 'id', input: { type: 'text', disabled: true } } : { label: '', path: '', input: { type: 'custom', render: () => <></> } },
            {
              label: requiredLabel('档位名称'),
              path: 'title',
              input: {
                type: 'text',
                maxlength: 40,
                placeholder: '请输入档位名称，1-40个字符',
              },
            },
            {
              label: requiredLabel('选择生效应用'),
              path: 'app_id',
              input: {
                type: 'select',
                options: applicationList.value.map(item => ({
                  label: item.app_name,
                  value: item.id,
                })),
                autoInsertEmptyOption: false,
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber,
            },
            {
              label: requiredLabel('支付面板展示'),
              path: 'status',
              transform: transformInteger,
              input: { type: 'radio',
                options: [
                  {
                    label: '展示',
                    value: 1,
                  },
                  {
                    label: '不展示',
                    value: 2,
                  },
                ],
              },
            },
            {
              label: requiredLabel('序号'),
              path: 'priority',
              input: {
                type: 'select',
                options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(i => ({
                  label: '' + i,
                  value: i,
                })),
              },
              transform: transformInteger,
              class: currentLevel.value?.status === 2 ? 'hidden' : '',
            },
            {
              label: requiredLabel('金币'),
              path: 'coins',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber2,
            },
            {
              label: requiredLabel('观影券数'),
              path: 'amount',
              input: {
                type: 'number',
                suffix: <>个</>,
              },
              transform: transformNumber,
            },
            {
              label: '赠送观影券数',
              path: 'amount_bonus',
              input: {
                type: 'number',
                suffix: <>个</>,
              },
              transform: transformNumber,
            },
          ]}
          data={currentLevel.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditRechargeLevelModal.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          let exclude: string[] = []
          if (currentLevel.value.status === 2) {
            exclude = ['priority']
          }
          // if (currentLevel.value.store === 'Google Play' && Number(currentLevel.value.status)) {
          //   exclude = ['apple_store.apple_id', 'apple_store.product_id']
          // }
          // if (currentLevel.value.store === 'Apple Store' && Number(currentLevel.value.status)) {
          //   exclude = ['google_play.google_play_id']
          // }

          // if (currentLevel.value.status !== 1) {
          //   exclude = ['google_play.google_play_id', 'apple_store.apple_id', 'apple_store.product_id']
          // }
          if (!validateAll({ exclude })) {
            return
          }
          !currentLevel.value?.id ? void onCreate() : void onEdit()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
