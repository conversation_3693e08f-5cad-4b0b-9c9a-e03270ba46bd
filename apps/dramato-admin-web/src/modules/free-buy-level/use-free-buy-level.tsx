/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { showFailToast, showSuccessToast } from '@skynet/ui'
import { apiCreateRechargeLevel, apiEditRechargeLevel, apiGetRechargeLevels, apiUpdateRechargeLevelPriority, apiUpdateRechargeLevelStatus } from './free-buy-level-api'
const searchForm = ref<M.FreeBuyLevelSearchParams>({
  status: 0,
  page_size: 10,
  next: '',
  app_id: +'',
})
const applicationList = ref<Array<Required<M.Application>>>([])
const list = ref<M.FreeBuyLevel[]>([])
const originalList = ref<M.FreeBuyLevel[]>([])
const total = ref<number>(0)
const page = ref(1)
const closeEditRechargeLevelModal = ref(() => {})

const initTopUpLevel = {
  store: 'Google Play',
  first_recharge: 0,
  status: 1,
  delivery_details: {},
  google_play: {},
  apple_store: {},
  currency: 'USD',
}

const currentLevel = ref<M.FreeBuyLevel>(initTopUpLevel)

const getList = async () => {
  const rs = await apiGetRechargeLevels(searchForm.value)
  originalList.value = rs.data?.items || []
  list.value = (originalList.value).map(i => ({
    ...i,
    coins: +(i?.coins || 0),
  }),
  )
  total.value = rs.data?.total || 0
  searchForm.value.next = rs.data?.page_info.next || ''
}

const switchLevel = () => ({
  ...currentLevel.value,
  product_type: 'ticket',
  coins: currentLevel.value?.coins || 0,
})

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_size = 10
    searchForm.value.next = ''
  }
  void getList()
}

const onPageChange = (n: number) => {
  page.value = n
  searchForm.value.next = n - 1 > 0 ? `${(n - 1) * searchForm.value.page_size}` : ''
  onSearch()
}

const onPageSizeChange = (n: number) => {
  searchForm.value.page_size = n
  searchForm.value.next = ''
  onSearch()
}

const onReset = () => {
  page.value = 1
  searchForm.value = {
    status: 0,
    page_size: 10,
    next: '',
    app_id: +`${applicationList.value[0]?.id || ''}`,
    store: '',
  }
  onSearch(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  closeEditRechargeLevelModal.value && closeEditRechargeLevelModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  searchForm.value.next = page.value - 1 > 0 ? `${(page.value - 1) * searchForm.value.page_size}` : ''
  onSearch()
}
// Recharge

const onEditRechargeLevelStatus = async (d: M.FreeBuyLevel) => {
  try {
    await apiUpdateRechargeLevelStatus({
      id: d?.id || 0,
      status: d?.status === 1 ? 2 : 1,
    })
    onEditSuccess()
    showSuccessToast(`${d?.status === 1 ? '禁用' : '启用'}成功`)
  } catch (error: any) {
    showFailToast(error.response.data.message || `${d?.status === 1 ? '禁用' : '启用'}失败`)
  }
}

const onEditRechargeLevelPriority = async (d: M.FreeBuyLevel) => {
  try {
    const priority = d?.priority || 0
    if (priority < 1) {
      showFailToast('序号不能小于1')
      return
    }
    if (priority > 12) {
      showFailToast('序号不能大于12')
      return
    }
    await apiUpdateRechargeLevelPriority({
      id: d?.id || 0,
      priority,
    })
    onEditSuccess()
    showSuccessToast('编辑成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '更新序号失败')
  }
}

const onCreate = async () => {
  try {
    await apiCreateRechargeLevel(switchLevel())
    showSuccessToast('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    showFailToast(error.response.data.message || '创建失败')
  }
}

const onEdit = async () => {
  try {
    await apiEditRechargeLevel(switchLevel())
    showSuccessToast('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    showFailToast(error.response.data.message || '编辑失败')
  }
}

export const useRechargeLevelPage = () => {
  return {
    searchForm,
    list,
    originalList,
    total,
    page,
    applicationList,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    currentLevel,
    onCreate,
    onEdit,
    closeEditRechargeLevelModal,
    initTopUpLevel,
    onEditRechargeLevelStatus,
    onEditRechargeLevelPriority,
  }
}
