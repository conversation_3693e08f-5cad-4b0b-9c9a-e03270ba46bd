declare namespace M {
  interface FreeBuyLevelSearchParams {
    page_size: number // 每页数据
    next: string // 下一页数据偏移量 使用返回的next
    app_id: number // 查询的APP ID
    title?: string // 档位名称
    store?: string // 上架的商店
    status?: number // 上架状态 //状态：0.所有 1-启用，2-禁用
  }

  interface FreeBuyLevel {
    id?: number
    app_id?: number
    product_type?: string // 选填, 默认 ticket
    title?: string
    description?: ''
    coins?: number
    amount?: number // 单位是 分
    amount_bonus?: number // 单位是 分
    priority?: number
    status?: number // 1:正常  2:禁用
    deleted?: number
    create_user?: string
    update_user?: string
    created?: number
    updated?: number
    coins?: number
  }
}
