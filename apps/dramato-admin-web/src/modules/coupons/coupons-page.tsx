import { bindLoading, createComponent, useValidator } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, openDialog, Pager, showToast, transformDatetime, transformNumber, transformTimestamp } from '@skynet/ui'
import { cloneDeep, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { apiCreateCoupon, apiDeleteCoupon, apiGetCouponsList, apiUpdateCoupon } from './coupons-api'
import dayjs from 'dayjs'
import { z } from 'zod'

export const CouponsPage = createComponent(null, props => {
  const QueryCouponsForm = CreateForm<M.QueryCoupons>()
  const CouponForm = CreateForm<M.Coupons>()
  const Table = CreateTableOld<M.Coupons>()
  const defaultQueryCouponsForm = {
    id: undefined,
    name: '',
    effect_method: '',
    page_info: {
      offset: 0,
      size: 10,
    },
  }

  const page = ref<number>(1)

  const defaultCouponForm = {
    id: undefined,
    name: '',
    effect_method: 'date',
    start_time: undefined,
    expire_days: 1,
  }

  const couponsRules = z.object({
    name: z.string().min(1, '兑换券名称不能为空'),
    effect_method: z.enum(['date', 'dramaList', 'receive']),
    start_time: z.number().min(0, '可兑换开始日期不能为空'),
    expire_days: z.number().min(1, '有效期不能为空'),
  })

  const queryCouponsForm = ref<M.QueryCoupons>(cloneDeep(defaultQueryCouponsForm))
  const couponsForm = ref<M.Coupons>(cloneDeep(defaultCouponForm))
  const total = ref<number>(0)
  const list = ref<M.Coupons[]>([])
  const columns: TableColumnOld<M.Coupons>[] = [
    ['ID', 'id', { class: 'w-[60px]' }],
    ['兑换券名称', 'name', { class: 'w-[160px]' }],
    ['兑换方式', row => row.effect_method === 'date' ? '指定日期' : row.effect_method === 'dramaList' ? '自短剧上架开始' : '自领取当天开始', { class: 'w-[160px]' }],
    ['可兑换开始日期', row => row.start_time ? dayjs.unix(row.start_time).format('YYYY-MM-DD') : row.effect_method === 'dramaList' ? '按上架日期' : row.effect_method === 'receive' ? '按领取日期' : '--', { class: 'w-[160px]' }],
    ['有效期（天）', 'expire_days', { class: 'w-[160px]' }],
    ['更新时间', row => row.updated ? dayjs.unix(row.updated).format('YYYY-MM-DD HH:mm:ss') : '--', { class: 'w-[160px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap gap-x-2">
            <button class="btn btn-outline btn-xs" onClick={() => {
              couponsForm.value = row
              openCouponDialog(true)
            }}
            >
              查看
            </button>
            <button class="btn btn-outline btn-xs" onClick={() => openDeleteConfirmDialog(row.id)}>
              删除
            </button>
          </div>
        )
      },
      { class: 'w-[200px]' },
    ],
  ]
  const loading = ref<boolean>(false)
  const dialogLoading = ref<boolean>(false)

  const { error, validateAll } = useValidator(couponsForm, couponsRules)

  const getList = async () => {
    const params = cloneDeep({ ...queryCouponsForm.value, page_info: {
      offset: (page.value - 1) * queryCouponsForm.value.page_info.size,
      size: queryCouponsForm.value.page_info.size,
    } })
    const res = await bindLoading(apiGetCouponsList(params), loading)
    if (!res.data) return
    list.value = res.data.list
    total.value = res.data.page_info.total
  }

  let closeCouponsDialog: () => void
  let closeDeleteConfirmDialog: () => void

  const createCoupon = async () => {
    const exclude: string[] = []
    if (couponsForm.value.effect_method !== 'date') {
      exclude.push('start_time')
    }
    if (validateAll({ exclude })) {
      const res = await bindLoading(apiCreateCoupon(couponsForm.value), dialogLoading)
      if (res.code === 200) {
        showToast('创建成功')
        closeCouponsDialog()
        void getList()
      }
    }
  }

  const updateCoupon = async () => {
    const exclude: string[] = []
    if (couponsForm.value.effect_method !== 'date') {
      exclude.push('start_time')
    }
    if (validateAll({ exclude })) {
      if (!couponsForm.value.id) return
      const res = await bindLoading(apiUpdateCoupon({ id: couponsForm.value.id, name: couponsForm.value.name }), dialogLoading)
      if (res.code === 200) {
        showToast('更新成功')
        closeCouponsDialog()
        void getList()
      }
    }
  }

  const deleteCoupon = async (id: number | undefined) => {
    if (!id) return
    const res = await bindLoading(apiDeleteCoupon(id), dialogLoading)
    if (res.code === 200) {
      showToast('删除成功')
      closeDeleteConfirmDialog()
      if (list.value.length === 1) {
        page.value = page.value - 1 > 0 ? page.value - 1 : 1
      }
      void getList()
    }
  }

  const openDeleteConfirmDialog = (id: number | undefined) => {
    if (!id) return
    closeDeleteConfirmDialog = openDialog({
      title: '删除兑换券',
      body: () => (
        <div>
          <div>请先确保此兑换券已不在线上使用，删除后可能会影响线上领取</div>
          <div class="flex justify-end gap-x-2 mt-4">
            <Button class="btn  btn-sm" onClick={() => closeDeleteConfirmDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" disabled={dialogLoading.value} onClick={() => deleteCoupon(id)}>删除</Button>
          </div>
        </div>
      ),
    })
  }

  const openCouponDialog = (isView: boolean = false) => {
    closeCouponsDialog = openDialog({
      title: isView ? '查看兑换券' : '新建兑换券',
      body: () => (
        <x-coupons-form>
          <CouponForm
            class="flex flex-col gap-y-4"
            data={couponsForm.value}
            hasAction={false}
            onChange={(path, value) => {
              set(couponsForm.value, path, value)
            }}
            error={error.value}
            items={
              [
                { label: '兑换券ID', path: 'id', input: { type: 'custom', render: () => isView ? <x-coupons-id>{couponsForm.value.id}</x-coupons-id> : <x-coupons-id>To be generated</x-coupons-id> } },
                { label: '兑换券名称', path: 'name', input: { type: 'text', placeholder: '仅用于辨识，不对外展示' } },
                { label: '兑换方式', path: 'effect_method', input: { type: 'radio', options: [{ label: '指定日期', value: 'date', disabled: isView }, { label: '自短剧上架开始', value: 'dramaList', disabled: isView }, { label: '自领取当天开始', value: 'receive', disabled: isView }], disabled: isView } },
                couponsForm.value.effect_method === 'date' ? { label: '可兑换-开始日期（UTC时间）', path: 'start_time', input: { type: 'date', disabled: isView, min: dayjs().add(1, 'day').format('YYYY-MM-DD'), displayFormat: 'YYYY-MM-DD' }, transform: transformTimestamp } : null,
                { label: () => (
                  <x-label class="flex flex-col">
                    有效期（天）
                    <x-tooltip class="text-sm text-gray-500">*可兑换开始后几天内有效（1-30天）</x-tooltip>
                  </x-label>
                ), path: 'expire_days', input: { type: 'select', options: new Array(30).fill(0).map((_, index) => ({ label: `${index + 1}天`, value: index + 1 })), disabled: isView, autoInsertEmptyOption: false }, transform: transformNumber },
              ]
            }
          />
          <div class="flex justify-end gap-x-2">
            <Button class="btn  btn-sm" onClick={() => closeCouponsDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" disabled={dialogLoading.value} onClick={isView ? updateCoupon : createCoupon}>保存</Button>
          </div>
        </x-coupons-form>
      ),
      beforeClose: () => {
        couponsForm.value = cloneDeep(defaultCouponForm)
      },
    })
  }

  onMounted(() => {
    void getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>兑换券仓库</li>
          </ul>
        ),
        form: () => (
          <div class="flex justify-start items-center space-x-2">
            <QueryCouponsForm class=" flex flex-row"
              onSubmit={getList}
              data={queryCouponsForm.value}
              onChange={(path, value) => {
                set(queryCouponsForm.value, path, value)
              }}
              onReset={() => {
                queryCouponsForm.value = cloneDeep(defaultQueryCouponsForm)
                void getList()
              }}
              items={[
                { label: '兑换券ID', path: 'id', input: { type: 'text' }, transform: transformNumber },
                { label: '兑换券名称', path: 'name', input: { type: 'text' } },
                { label: '兑换方式', path: 'effect_method', input: { type: 'select', options: [{ label: '指定日期', value: 'date' }, { label: '自短剧上架开始', value: 'dramaList' }, { label: '自领取当天开始', value: 'receive' }] } },
              ]}
            />
          </div>

        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <span>兑换券列表<span class="text-gray-500 ml-2">*兑换券属性不可修改（已领取的不可变），若需调整，请新建兑换券再下发给后续领取的用户</span></span>
            <Button class="btn btn-sm btn-primary" onClick={() => openCouponDialog()}>新建兑换券</Button>
          </x-table-actions>
        ),
        table: () => (
          <Table
            list={list.value || []}
            class="tm-table-fix-last-column"
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={page.value} v-model:size={queryCouponsForm.value.page_info.size} total={total.value} onUpdate:page={getList} onUpdate:size={() => {
            page.value = 1
            void getList()
          }}
          />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default CouponsPage
