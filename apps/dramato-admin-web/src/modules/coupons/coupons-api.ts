import { httpClient } from 'src/lib/http-client'

export const apiGetCouponsList = (data: M.QueryCoupons) =>
  httpClient.post<ApiResponse<{
    list: M.Coupons[]
    page_info: {
      offset: number
      total: number
      size: number
    }
  }>>('/coupon/list', data)

export const apiCreateCoupon = (data: M.Coupons) => httpClient.post<ApiResponse>('/coupon/save', data)

export const apiUpdateCoupon = (data: {
  id: number
  name: string
}) => httpClient.post<ApiResponse>('/coupon/update', data)

export const apiDeleteCoupon = (id: number) => httpClient.post<ApiResponse>('/coupon/delete', { id })
