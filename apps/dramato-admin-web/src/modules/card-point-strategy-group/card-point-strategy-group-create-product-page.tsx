/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, CreateTableOld, DateTime, DialogFooter, Icon, openDialog, TableColumnOld } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'
import { useAppAndLangOptions } from '../options/use-app-options'
import { listingStatusList } from '../short-drama/short-drama-api'
import ShortDrama from '../short-drama/short-drama-page'
import { apiCreateCardPointStrategyGroup, apiEditCardPointStrategyGroup } from './card-point-strategy-group-api'
import { useCardPointStrategyGroupStore } from './card-point-strategy-group-store'

type CardPointStrategyGroupCreateProductPageOptions = {
  props: {}
}

export const CardPointStrategyGroupCreateProductPage = createComponent<CardPointStrategyGroupCreateProductPageOptions>({
  props: {},
}, props => {
  const { formData, Form, validateStepTwo, validateStepOne, stepOneError, stepTwoError } = useCardPointStrategyGroupStore()

  const curTab = ref([0, 1].includes(formData.value?.user_platform || 0) ? 'ios_config' : 'android_config')

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const appId = ref(0)

  const { appOptions } = useAppAndLangOptions(() => undefined, {
    onSuccess: () => {
      appId.value = appOptions.value?.find(i => i.label === ([0, 1].includes(formData.value?.user_platform || 0) ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'))?.value || 0
    },
  })

  const Table = CreateTableOld<M.Drama>()
  const checkedEpisodeItem = ref<M.ApplicationDramaItem[]>([])

  const iosEpisodeColumns: TableColumnOld<M.Drama>[] = [
    ['剧集ID', 'drama_id', { class: 'w-[120px]' }],
    ['剧名', 'title', { class: 'w-[320px]' }],
    ['上架状态', row => listingStatusList.find(item => item.code === row.listing_status)?.name, { class: 'w-[80px]' }],
    ['上架时间', row => <DateTime value={row?.listing_time ? row?.listing_time * 1000 : 0} />, { class: 'w-[150px]' }],
    ['集数', 'episodes_number', { class: 'w-[120px]' }],
    ['短剧类型', row => ['', '本土', '翻译'][+(row.resource_type || 0)], { class: 'w-[80px]' }],
    ['原起始付费集', 'start_paying_episodes', { class: 'w-[120px]' }],
    ['现起始付费集', (row, idx) => (
      <input
        class="input input-bordered flex items-center gap-1 h-8 w-[120px]"
        value={row?.new_start_paying_episodes}
        type="number"
        min="2"
        onInput={e => {
          const new_start_paying_episodes = (e.target as HTMLInputElement).value || ''
          formData.value.episode_config[curTab.value].drama_list[idx].new_start_paying_episodes = +new_start_paying_episodes
        }}
      />
    ), { class: 'w-[160px]' }],
    ['单集定价', 'episodes_price', { class: 'w-[120px]' }],
    ['生效端', row => ['', 'ios', 'android'][+(row?.app_platform || 0)], { class: 'w-[80px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.episode_config[curTab.value].drama_list) {
                return
              }
              (formData.value.episode_config[curTab.value].drama_list || []).splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]

  const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[1300px] h-[80vh] overflow-hidden px-4'

  const showIosImportDramaDialog = () => {
    if (formData.value.episode_config[curTab.value]?.drama_list) {
      checkedEpisodeItem.value = [...formData.value.episode_config[curTab.value].drama_list] as any
    }
    const closeDialog = openDialog({
      title: '导入指定生效短剧',
      mainClass: dialogMainClass,
      body: () => (
        <x-import-recharge-level class="flex-1 flex flex-col overflow-hidden gxp-y-4">
          <x-episode-list class="flex-1  overflow-y-auto">
            <ShortDrama
              hasNav={false}
              hasActions={false}
              appId={appId.value}
              checkedItems={checkedEpisodeItem.value}
              onAdd={item => {
                checkedEpisodeItem.value?.push({ ...item, drama_id: item.drama_id })
              }}
              onRemove={item => {
                checkedEpisodeItem.value = checkedEpisodeItem.value?.filter(i => i.drama_id !== item.drama_id)
              }}
            />
          </x-episode-list>
          <footer class="w-full flex justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.episode_config) {
                formData.value.episode_config = {
                  ios_config: {
                    drama_list: [],
                  },
                  android_config: {
                    drama_list: [],
                  },
                }
              }
              if (!formData.value.episode_config[curTab.value]) {
                formData.value.episode_config[curTab.value] = {
                  drama_list: [],
                }
              }
              if (!formData.value.episode_config[curTab.value].drama_list) {
                formData.value.episode_config[curTab.value].drama_list = []
              }
              formData.value.episode_config[curTab.value].drama_list = checkedEpisodeItem.value.map(i => ({
                drama_id: i.drama_id || 0,
                series_key: i.series_key || '',
                app_platform: i.app_platform, // 1 IOS 2 Android
                title: i.title || '',
                listing_status: i.listing_status || 1, // 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
                listing_time: i.listing_time || 0,
                episodes_number: i.episodes_number || 0,
                start_paying_episodes: i.start_paying_episodes || 0,
                new_start_paying_episodes: formData.value.episode_config[curTab.value].drama_list.find(j => j.drama_id === i.drama_id)?.new_start_paying_episodes || 2,
                episodes_price: i.episodes_price || 0,
                resource_type: i.resource_type,
              })) as any
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1300px overflow-hidden',
      beforeClose: () => {
        checkedEpisodeItem.value = []
      },
    })
  }

  const checkEpisodes = () => {
    const iosDramas = (formData.value.episode_config?.ios_config?.drama_list || []).map(i => ({ ...i, platform: 1 }))
    const androidDramas = (formData.value.episode_config?.android_config?.drama_list || []).map(i => ({ ...i, platform: 2 }))

    const errorDramas = [...iosDramas, ...androidDramas].filter(j => {
      return j.new_start_paying_episodes < 2 || j.new_start_paying_episodes > j.episodes_number
    })

    console.log('errorDramas', errorDramas)

    if (!!errorDramas && errorDramas.length > 0) {
      openDialog({
        title: '指定生效短剧配置错误',
        body: () => (
          <div>
            {errorDramas.map(i => <div>{i.platform === 1 ? 'iOS' : 'Android'}平台，短剧{i.drama_id}的现起始付费集数{i.new_start_paying_episodes}不在2~{i.episodes_number}之间</div>)}
          </div>
        ),
      })
    }

    return !!errorDramas && errorDramas.length > 0
  }

  const onSubmit = async () => {
    if (!validateStepOne() || !validateStepTwo()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepTwoError.value)}
          </div>
        ),
      })
      return
    }

    if (formData.value.import_type === 1 && checkEpisodes()) {
      return
    }

    const requestApi = formData.value.id ? apiEditCardPointStrategyGroup : apiCreateCardPointStrategyGroup
    const response = await requestApi({ ...formData.value })
    if (response.code !== 200) return
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                void router.push('/card-point-strategy-group')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  const resetTaskFormData = () => {
    formData.value = {
      ...formData.value,
      episode_config: {
        ios_config: {
          drama_list: [],
        },
        android_config: {
          drama_list: [],
        },
      },
    }
  }

  const inputRef = ref<HTMLInputElement>()

  const batchImport = () => {
    inputRef.value && inputRef.value?.click()
  }

  const handleFileSelect = (event: any) => {
    const file = event.target.files[0]
    const reader = new FileReader()
    reader.onload = async (e: any) => {
      const data = new Uint8Array(e.target.result)
      const XLSX = await import('xlsx')
      const workbook = XLSX.read(data, { type: 'array' })
      // 获取第一个工作表的名称
      const firstSheetName = workbook.SheetNames[0]
      // 获取第一个工作表的数据
      const worksheet = workbook.Sheets[firstSheetName]
      // 将工作表的数据转换为 JSON 格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet)
      console.log(jsonData)
      if (!formData.value.episode_config[curTab.value]) {
        formData.value.episode_config[curTab.value] = {}
      }
      if (!formData.value.episode_config[curTab.value].drama_list) {
        formData.value.episode_config[curTab.value].drama_list = []
      }
      // currentJDRank.value.series_info = (jsonData as M.Drama[])
      formData.value.episode_config[curTab.value].drama_list.push(...(jsonData as M.Drama[]))
      // currentJDRank.value.series_ids = currentJDRank.value.series_info.map(item => item.series_id).join(',')
    }
    reader.readAsArrayBuffer(file)
  }

  const isUploadingExcel = ref(false)
  const isUploadedExcelFail = ref(false)

  return () => (
    <div class="flex flex-col p-8 flex-1 overflow-hidden">
      <div role="tablist" class="tabs tab-sm tabs-boxed bg-white gap-x-2 flex mb-4">
        {
          [
            [0, 1].includes(formData.value.user_platform || 0) ? 'ios_config' : '',
            [0, 2].includes(formData.value.user_platform || 0) ? 'android_config' : '',
          ].filter(i => !!i).map(item => (
            <span
              role="tab"
              onClick={() => {
                curTab.value = item
                checkedEpisodeItem.value = []
                console.log('appOptions.value', appOptions.value, item)
                const key = item === 'ios_config' ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'
                appId.value = appOptions.value?.find(i => i.label === key)?.value || 0
                console.log(appId.value)
              }}
              class={mc('tab bg-gray-300 text-black', curTab.value === item ? 'tab-active' : '')}
            >
              {{ ios_config: 'iOS配置', android_config: 'Android配置' }[item]}
            </span>
          ))
        }
        <div class="flex-1 flex justify-end">
          {/* <Button class="btn btn-outline  btn-sm justify-self-end" disabled={refreshLoading.value} onClick={onRefresh}>刷新</Button> */}
        </div>

      </div>
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepTwoError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          resetTaskFormData()
        }}
        onSubmit={onSubmit}
        items={[
          formData.value.import_type === 0 && [requiredLabel('指定生效短剧'), `episode_config.${curTab.value}.drama_list`, {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="border border-1 border-solid p-4 rounded-lg">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showIosImportDramaDialog()}>导入配置
                      </Button>
                      {/* <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => batchImport()}>批量导入
                      </Button>
                      <input ref={inputRef} type="file" accept=".xlsx" name="file" class="btn btn-sm btn-outline w-[80px] hidden" onChange={handleFileSelect} /> */}
                    </div>
                    <div class="flex items-center space-x-2">
                      现起始付费集：
                      <input
                        class="input input-bordered flex items-center gap-1 h-8 w-[120px]"
                        value={formData.value.episode_config[curTab.value].new_start_paying_episodes}
                        type="number"
                        min="2"
                        onInput={e => {
                          const new_start_paying_episodes = (e.target as HTMLInputElement).value || ''
                          if (!formData.value.episode_config) {
                            return
                          }
                          formData.value.episode_config[curTab.value].new_start_paying_episodes = +new_start_paying_episodes
                        }}
                      />
                      <Button class="btn btn-primary btn-sm" disabled={formData.value.episode_config[curTab.value].new_start_paying_episodes < 2} onClick={() => {
                        formData.value.episode_config[curTab.value].drama_list.forEach(item => {
                          item.new_start_paying_episodes = formData.value.episode_config[curTab.value].new_start_paying_episodes
                        })
                      }}
                      >确认批量修改
                      </Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <Table
                    list={formData.value.episode_config[curTab.value].drama_list || []}
                    columns={iosEpisodeColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
          formData.value.import_type === 1 && [
            requiredLabel('上传指定生效短剧'),
            curTab.value === 'ios_config' ? 'episode_config.ios_url' : 'episode_config.android_url',
            {
              type: 'custom',
              render: () => (
                <x-upload-cover class="grid gap-y-2">
                  <Uploader
                    accept="xlsx"
                    maxsize={1024 * 1024 * 10}
                    class="w-full h-[100px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                    beforeUpload={() => {
                      if (isUploadingExcel.value) {
                        return Promise.resolve(false)
                      }
                      isUploadingExcel.value = true
                      return Promise.resolve(isUploadingExcel.value)
                    }}
                    onUploadSuccess={d => {
                      formData.value.episode_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url'] = d.temp_path
                      isUploadingExcel.value = false
                      isUploadedExcelFail.value = false
                    }}
                    onUploadFailed={() => {
                      isUploadingExcel.value = false
                      isUploadedExcelFail.value = true
                    }}
                    isImage={true}
                    multiple={false}
                    uploadUrl="/episodestrategy/import"
                    showFileList
                  >
                    <span class="size-full flex items-center justify-center">{
                      isUploadingExcel.value
                        ? (
                            <div class="flex flex-col gap-y-2 items-center">
                              <span class="loading loading-spinner size-4" />
                              <span>上传中</span>
                            </div>
                          )
                        : '上传文件'
                    }
                    </span>
                  </Uploader>
                </x-upload-cover>

              ),
            },
            {
              class: 'col-span-1',
              hint: (
                <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                  { formData.value.episode_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']
                  && (
                    <span class="flex items-center gap-x-2">
                      <a href={'https://static-v1.mydramawave.com/' + formData.value.episode_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']} target="_blank">{formData.value.episode_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']}</a>
                      <Icon name="ant-design:delete-filled" class="cursor-pointer" onClick={() => {
                        formData.value.episode_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url'] = ''
                      }}
                      />
                    </span>
                  )}
                  { isUploadedExcelFail.value && <span class="text-red-500">上传失败, 请重试</span>}
                  <x-tip>
                    提示：
                  </x-tip>
                  <x-tip>
                    支持 xlsx 格式，大小限制 10M 以内
                  </x-tip>
                </x-upload-cover-tip>
              ),
            },
          ],
        ]}
        actions={() => (
          <div class="flex justify-between items-center gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/card-point-strategy-group/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit" disabled={isViewMode.value}>保存</Button>
          </div>
        )}
      />

    </div>
  )
})
export default CardPointStrategyGroupCreateProductPage
