import { createComponent } from '@skynet/shared'
import { Button, Empty, transformInteger, transformN<PERSON>ber, DialogFooter, openDialog } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetStrategyGroupAdChannelList } from 'src/modules/strategy-group/strategy-group-api'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { apiCreateCardPointStrategyGroup, apiEditCardPointStrategyGroup } from './card-point-strategy-group-api'
import { useCardPointStrategyGroupStore } from './card-point-strategy-group-store'

type StrategyGroupCreateTargetPageOptions = {
  props: {}
}
export const StrategyGroupCreateTargetPage = createComponent<StrategyGroupCreateTargetPageOptions>({
  props: {},
}, props => {
  const { formData, Form, stepOneError, validateStepOne } = useCardPointStrategyGroupStore()
  const route = useRoute()
  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const isViewMode = computed(() => route.query.mode === 'view')
  const adChannelList = ref<Array<{ name: string, code: string }>>([])
  onMounted(async () => {
    const response = await apiGetStrategyGroupAdChannelList()
    if (!response) return
    adChannelList.value = response.data?.list ?? []
  })
  const router = useRouter()
  const onSubmit = async () => {
    if (!validateStepOne()) return
    if (formData.value.special_type === 1) {
      const requestApi = formData.value.id ? apiEditCardPointStrategyGroup : apiCreateCardPointStrategyGroup
      const response = await requestApi({ ...formData.value })
      if (response.code !== 200) return
      setTimeout(() => {
        void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
          const closeDialog = openDialog({
            title: '提示',
            body: (
              <div>保存成功
                <DialogFooter okText="返回列表页" onOk={() => {
                  closeDialog()
                  void router.push('/card-point-strategy-group')
                }} cancelText="留在当前页" onCancel={() => closeDialog()}
                />
              </div>
            ),
          })
        })
      })
    } else {
      void router.push({ path: './product', query: { ...route.query, id: formData.value.id } })
    }
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  console.log('formData', formData.value)

  const items = computed(() => [
    [
      [requiredLabel('策略组名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['策略组id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ],
    [requiredLabel('是否针对指定非保护剧生效'), 'special_type', {
      type: 'radio', options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    }, { class: 'w-[400px]' }],
    [requiredLabel('导入类型'), 'import_type', {
      type: 'radio', options: [
        { label: '筛选导入', value: 0, disabled: formData.value.id },
        { label: '批量导入', value: 1, disabled: formData.value.id },
      ],
    }, { class: formData.value.special_type === 1 ? 'hidden' : '' }],
    [
      [requiredLabel('是否AB实验'), 'is_abtest', {
        type: 'radio', options: [
          { label: '是', value: 1, disabled: isViewMode.value },
          { label: '否', value: 0, disabled: isViewMode.value },
        ],
      }],
    ],
    <h2 class="col-span-2 mb-4 font-bold text-lg"> {requiredLabel('目标用户群')} </h2>,
    [
      [<span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>, 'user_platform', {
        type: 'radio',
        options: [
          { label: '全部', value: 0, disabled: isViewMode.value },
          { label: 'iOS', value: 1, disabled: isViewMode.value },
          { label: 'Android', value: 2, disabled: isViewMode.value },
        ],
      }, { errorVisible: false }],
      (formData.value.is_abtest === 0
        ? ([
            '分层画像',
            'strategy_layer_ids',
            {
              type: 'multi-select',
              search: true,
              popoverWrapperClass: 'z-popover-in-dialog',
              options: list.value.map((n, index) => {
                return { value: n.id, label: `${n.id}/${n.name}` }
              }),
              // maxlength: 1,
              class: 'w-[400px]',
            },
          ]) : null),
    ],
    <h2 class="col-span-2 font-bold text-lg"> 任务计划 </h2>,
    [
      [
        requiredLabel(<span>任务优先级<small class="text-gray-500 pl-1">数字越大越高</small></span>),
        'priority',
        {
          type: 'number',
          disabled: isViewMode.value,
        },
        { transform: transformInteger },
      ],
      [
        requiredLabel('策略持续时间'),
        'duration',
        {
          type: 'number',
          placeholder: '填 -1 表示不限',
          suffix: '天',
          min: -1,
          disabled: isViewMode.value,
        },
        { transform: transformNumber, class: 'w-[12em]' },
      ],
    ],
  ] as FormOptions<FormData>['props']['items'])

  const formError = computed(() => {
    return {
      ...stepOneError.value,
    }
  })

  const resetUserFormData = () => {
    formData.value = {
      ...formData.value,
      name: '',
      is_abtest: 1,
      priority: 0,
      special_type: 0,
      user_platform: 0, // 用户平台 0 all 1 IOS 2 Android
      strategy_layer_ids: [],
      duration: -1, // 持续时间 -1 表示不限
    }
  }
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        error={formError.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            {formData.value.special_type === 0
              ? <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
              : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default StrategyGroupCreateTargetPage
