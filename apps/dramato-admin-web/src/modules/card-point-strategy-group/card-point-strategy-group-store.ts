import { useValidator } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.CardPointStrategyGroup>()
const defaultFormData = () => {
  return {
    name: '',
    is_abtest: 1,
    priority: 0,
    user_platform: 0, // 用户平台 0 all 1 IOS 2 Android
    strategy_layer_ids: [],
    duration: -1, // 持续时间 -1 表示不限
    import_type: 0,
    special_type: 0,
    episode_config: {
      ios_config: {
        drama_list: [],
      },
      android_config: {
        drama_list: [],
      },
    },
  } as M.CardPointStrategyGroup
}
const formData = ref<M.CardPointStrategyGroup>(defaultFormData())
const activeUserType = ref<'latest_days' | 'start_and_end'>('latest_days')
const needShowSaveDragDialog = ref(true)

const resetFormData = () => {
  formData.value = defaultFormData()
}

const stepOneRules = z.object({
  name: z.string().min(1),
  user_platform: z.number(),
  priority: z.number().min(0, '必填'),
  duration: z.number().min(-1, '必填'),
})

const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)

const validateStepOne = () => {
  const valid = _validateStepOne({ exclude: [] })
  return valid
}

const stepTwoRules = z.object({
  episode_config: z.object({
    ios_url: z.string().min(1),
    android_url: z.string().min(1),
    ios_config: z.object({
      drama_list: z.array(z.object({
        drama_id: z.number(),
      })).min(1, { message: '必填' }),
    }).optional(),
    android_config: z.object({
      drama_list: z.array(z.object({
        drama_id: z.number(),
      })).min(1, { message: '必填' }),
    }).optional(),
  }),
})

const { error: stepTwoError, validateAll: _validateStepTwo } = useValidator(formData, stepTwoRules)

const validateStepTwo = () => {
  const exclude = [
    formData.value.user_platform === 1 && 'episode_config.android_config',
    formData.value.user_platform === 2 && 'episode_config.ios_config',
    formData.value.import_type === 1 && ['episode_config.ios_config', 'episode_config.android_config'],
    formData.value.import_type === 0 && ['episode_config.ios_url', 'episode_config.android_url'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepTwo({ exclude })
  return valid
}
// const userProfileList = ref<Api.CardPointStrategyGroupUserProfile.Item[]>([])
const fetchingUserProfileList = ref(false)

const platformMap: Record<number, string> = {
  0: 'All',
  1: 'IOS',
  2: 'Android',
}
export const useCardPointStrategyGroupStore = () => {
  return {
    platformMap,
    formData,
    resetFormData,
    Form,
    stepOneError,
    stepTwoError,
    validateStepOne,
    validateStepTwo,
    activeUserType,
    needShowSaveDragDialog,
    fetchingUserProfileList,
  }
}
