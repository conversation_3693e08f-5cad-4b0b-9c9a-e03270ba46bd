import { r, redirect } from '@skynet/shared'

export const cardPointStrategyGroupRoutes = [
  r('card-point-strategy-group', '付费卡点策略组', null, [
    r('', '付费卡点策略组', () => import('src/modules/card-point-strategy-group/card-point-strategy-group-page.tsx')),
    r('create', '新建付费卡点策略组', () => import('src/modules/card-point-strategy-group/card-point-strategy-group-create-layout.tsx'), [
      redirect('', 'target'),
      r('target', '新建付费卡点策略组 - 目标条件', () => import('src/modules/card-point-strategy-group/card-point-strategy-group-create-target-page.tsx')),
      r('product', '新建付费卡点策略组 - 商品配置', () => import('src/modules/card-point-strategy-group/card-point-strategy-group-create-product-page.tsx')),
    ]),
  ]),
]
