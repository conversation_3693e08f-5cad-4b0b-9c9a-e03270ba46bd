declare namespace M {
  type Drama = {
    drama_id: number
    series_key: string
    app_platform: string // 1 IOS 2 Android
    title: string
    listing_status: number // 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
    listing_time: number
    episodes_number: number
    start_paying_episodes: number
    new_start_paying_episodes: number
    episodes_price: string
    resource_type: number // 1 付费 2 免费
  }

  type CardPointStrategyGroup = {
    id?: string
    name?: string
    status?: number // 状态 1 线上 3 未上架
    is_abtest?: 0 | 1
    priority?: number
    list_time?: number
    user_platform?: 0 | 1 | 2 | number
    strategy_layer_ids?: number[]
    duration?: number
    auto_up?: 0 | 1
    import_type?: number
    episode_config?: {
      ios_url?: string
      android_url?: string
      ios_config?: {
        drama_list?: Drama[]
      }
      android_config?: {
        drama_list?: Drama[]
      }
    }
    created?: number
    created_operator_id?: string
    created_operator_name?: string
    updated?: number
    updated_operator_id?: string
    updated_operator_name?: string
    deleted?: number
    resource_type?: 1 | 2 | number
    isEdit?: boolean
    special_type?: 0 | 1 | number
  }

  type Tab = 'ios_config' | 'android_config'

}
declare namespace Api {
  namespace CardPointStrategyGroup {
    namespace Request {
      interface List {
        id?: string
        name?: string
        //  1 线上 3 未上架
        status?: 1 | 3 | number
        // 0: 全部, 1: 否, 2: 是
        is_abtest?: 0 | 1 | 2 | number
        // ASC: 从小到大, DESC: 从大到小
        sort_desc?: string
        sort?: string
        sort_type?: string
        page_info: {
          page_size: number
          page_index: number
        }
      }
    }

    namespace Response {
      type List = ApiResponse<{ list: M.CardPointStrategyGroup[], total: number }>
    }
  }
}
