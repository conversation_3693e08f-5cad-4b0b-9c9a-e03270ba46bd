import { httpClient } from 'src/lib/http-client'

export const apiListCardPointStrategyGroup = (params: Api.CardPointStrategyGroup.Request.List) => {
  return httpClient.post<Api.CardPointStrategyGroup.Response.List>('/episodestrategy/list', params)
}

export const apiDeleteCardPointStrategyGroup = (params: { id: string }) => {
  return httpClient.post<Api.CardPointStrategyGroup.Response.List>('/episodestrategy/delete', params)
}

export const apiUpdateCardPointStrategyGroupPriority = (params: { id: string, priority: number }) => {
  return httpClient.post<Api.CardPointStrategyGroup.Response.List>('/episodestrategy/set-priority', params)
}

export const apiCreateCardPointStrategyGroup = (params: Partial<M.CardPointStrategyGroup>) => {
  return httpClient.post<ApiResponse<M.CardPointStrategyGroup>>('/episodestrategy/create', params)
}

export const apiEditCardPointStrategyGroup = (params: Partial<M.CardPointStrategyGroup>) => {
  return httpClient.post<ApiResponse<M.CardPointStrategyGroup>>('/episodestrategy/update', params)
}

export const apiDeleteCardPointStrategyGroupDetails = (params: { id: string }) => {
  return httpClient.post<ApiResponse<M.CardPointStrategyGroup>>('/episodestrategy/delete', params)
}

export const apiGetCardPointStrategyGroupDetails = (params: { id: string }) => {
  return httpClient.post<ApiResponse<M.CardPointStrategyGroup>>('/episodestrategy/detail', params)
}

/** operation 状态 1 线上 3 未上架  */
export const apiUpdateCardPointStrategyGroupState = (params: { id: string, status: number }) => {
  return httpClient.post<unknown>('/episodestrategy/set-state', params)
}
