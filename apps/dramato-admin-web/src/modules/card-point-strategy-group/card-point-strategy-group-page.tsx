/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, on } from '@skynet/shared'
import { Button, CreateForm, CreateTableOld, DateTime, Icon, openDialog, Pager, showAlert, TableColumnOld, transformInteger } from '@skynet/ui'
import { set, omit } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref, watch } from 'vue'
import { RouterLink, useRouter } from 'vue-router'

import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { apiCreateCardPointStrategyGroup, apiDeleteCardPointStrategyGroup, apiGetCardPointStrategyGroupDetails, apiListCardPointStrategyGroup, apiUpdateCardPointStrategyGroupPriority, apiUpdateCardPointStrategyGroupState } from './card-point-strategy-group-api'
import { useCardPointStrategyGroupStore } from './card-point-strategy-group-store'

export const CardPointStrategyGroupPage = createComponent(null, (props, { emit }) => {
  type ListParams = Omit<Api.CardPointStrategyGroup.Request.List, 'page_info'>
  const Form = CreateForm<ListParams>()
  const listParams = ref<ListParams>({})
  const Table = CreateTableOld<M.CardPointStrategyGroup>()
  const list = ref<M.CardPointStrategyGroup[]>([])
  const router = useRouter()
  const currentPriority = ref<number>(0)

  const columns: TableColumnOld<M.CardPointStrategyGroup>[] = [
    ['策略组ID', 'id', { class: 'w-[100px]' }],
    ['策略组名称', 'name', { class: 'w-[200px]' }],
    ['优先级', row => (
      <x-input class="flex flex-row items-center gap-x-2">
        <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
          <input
            type="number"
            class={mc('grow w-100px')}
            value={row.priority}
            disabled={!row.isEdit}
            // ant-design:edit-filled
            onFocus={() => {
              currentPriority.value = +(row.priority || '')
            }}
            onInput={(e: Event) => {
              currentPriority.value = +((e.target as HTMLInputElement).value || '')
            }}
            onKeydown={(e: KeyboardEvent) => {
              if (e.key !== 'Enter') {
                return
              }
              if (currentPriority.value === row.priority) {
                return
              }
              void apiUpdateCardPointStrategyGroupPriority({
                id: row.id + '' || '',
                priority: currentPriority.value,
              }).then(() => {
                void search(page.value)
              })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
            }}
            onBlur={() => {
              if (currentPriority.value === row.priority) {
                return
              }
              void apiUpdateCardPointStrategyGroupPriority({
                id: row.id + '' || '',
                priority: currentPriority.value,
              }).then(() => {
                void search(page.value)
              })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
            }}
          />
        </label>
        <Icon name="ant-design:edit-filled" class="size-4 cursor-pointer" onClick={() => {
          row.isEdit = true
        }}
        />
      </x-input>
    ), { class: 'w-[200px]' }],
    ['是否AB实验', row => (
      <>
        {row.is_abtest === 1 ? '是' : '否'}
      </>
    ), { class: 'w-[100px]' }],
    ['上架状态', row => (
      <span class={`badge badge-outline whitespace-nowrap text-[var(${row.status === 1 ? '--green-6' : '--text-6'}]`}>{[
        { label: '启用', value: 1 },
        { label: '未启用', value: 3 },
      ].find(item => item.value === row.status)?.label ?? row.status}
      </span>
    ), { class: 'w-[120px]' }],
    ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
    ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
    [<span class="px-3">操作</span>, row => (
      <x-hide-when-in-dialog class="flex gap-x-2">
        <Button
          class={mc('btn btn-outline btn-xs', [2, 4].includes(row.status || 0) ? 'hidden' : '')}
          onClick={() => {
            if (!row.id) return
            void apiUpdateCardPointStrategyGroupState({
              id: row.id,
              status: row.status === 3 ? 1 : 3,
            })
              .then(() => {
                void search(page.value)
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
          }}
        >
          {row.status === 3 ? '启用' : '下架'}
        </Button>
        <RouterLink to={`/card-point-strategy-group/create?id=${row.id}`} class={mc('btn btn-outline btn-xs', [2, 3].includes(row.status || 0) ? '' : 'hidden')}>修改</RouterLink>
        <RouterLink to={`/card-point-strategy-group/create?id=${row.id}&mode=view`} class={mc('btn btn-outline btn-xs', row.status === 1 ? '' : 'hidden')}>查看</RouterLink>
        <Button
          class="btn btn-outline btn-xs"
          onClick={() => {
            const hideDeleteDialog = openDialog({
              title: '删除',
              mainClass: 'pb-0 px-5',
              body: (
                <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-delete-episode-body>确认删除 策略【{row.name}】吗？</x-delete-episode-body>
                  <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void apiDeleteCardPointStrategyGroup({
                        id: row.id || '',
                      })
                        .then(() => {
                          void search(page.value)
                          hideDeleteDialog()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                    >确定
                    </button>
                  </x-delete-episode-footer>
                </x-delete-episode-confirm-dialog>
              ),
            })
          }}
        >
          删除
        </Button>
        <Button class="btn-outline btn btn-xs" onClick={() => copyBtn(row)}>
          复制
        </Button>
      </x-hide-when-in-dialog>
    ), {
      class: 'w-[225px] hide-when-in-dialog',
    },
    ]]

  const { resetFormData } = useCardPointStrategyGroupStore()

  onMounted(() => {
    void search(1)
    on('refresh:strategyGroup', () => {
      void search(1)
    })
  })
  const loading = ref<boolean>(false)
  const search = async (_page?: number) => {
    _page = _page || page.value + 1
    loading.value = true
    if (listParams.value.sort_desc) {
      listParams.value.sort = listParams.value.sort_desc.split('-')[0]
      listParams.value.sort_type = listParams.value.sort_desc.split('-')[1]
    }
    const res = await apiListCardPointStrategyGroup({ ...listParams.value, page_info: { page_index: _page, page_size: pageSize.value } })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
    page.value = _page
  }

  const copyBtn = async (row: M.CardPointStrategyGroup) => {
    try {
      const rs = await apiGetCardPointStrategyGroupDetails({ id: row.id || '' })
      if (!rs.data) {
        return
      }
      let d = rs.data
      d = {
        ...omit(d, ['id', 'status']),
        name: d.name + '副本',
      }
      await apiCreateCardPointStrategyGroup(d)
      showAlert('复制成功')
      void search(page.value)
    } catch (error: any) {
      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败', 'error')
    }
  }
  watch(() => listParams.value.sort, () => {
    void search(1)
  }, { deep: true })
  const page = ref<number>(1)
  const pageSize = ref<number>(20)
  const total = ref<number>(1)

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
  })
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>策略组列表</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { }
              page.value = 1
              pageSize.value = 20
              void search(1)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              ['策略ID', 'id', { type: 'text' }],
              ['策略名称', 'name', { type: 'text' }],
              ['状态', 'status', {
                type: 'select',
                options: [
                  { label: '已启用', value: 1 },
                  { label: '未启用', value: 3 },
                ],
              }, { transform: transformInteger }],
              ['是否AB实验', 'is_abtest', {
                type: 'select',
                options: [
                  { label: '否', value: 0 },
                  { label: '是', value: 1 },
                ],
              }, { transform: transformInteger }],
              ['排序', 'sort_desc', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-desc' },
                  { label: '创建时间：从旧到新', value: 'created-asc' },
                  { label: 'ID：从大到小', value: 'id-desc' },
                  { label: 'ID：从小到大', value: 'id-asc' },
                  { label: '优先级：从大到小', value: 'priority-desc' },
                  { label: '优先级：从小到大', value: 'priority-asc' },
                ],
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-2">
              付费卡点策略
            </div>
            <x-hide-when-in-dialog>
              <Button class="btn-primary btn btn-sm" onClick={
                () => {
                  resetFormData()
                  void router.push('/card-point-strategy-group/create')
                }
              }
              >新建策略组
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default CardPointStrategyGroupPage
