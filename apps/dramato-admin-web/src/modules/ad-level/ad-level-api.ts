import { httpClient } from 'src/lib/http-client'

export const apiGetAdLevelList = (data: M.AdLevel.params) =>
  httpClient.post<ApiResponse<M.AdLevel.ListResponse>>('/ads/value/level/list', data)

export const apiCreateAdLevel = (data: M.AdLevel.CreateAdLevel) =>
  httpClient.post<ApiResponse<boolean>>('/ads/value/level/save', data)

export const apiEditAdLevel = (data: M.AdLevel.CreateAdLevel) =>
  httpClient.post<ApiResponse<boolean>>('/ads/value/level/update', data)
