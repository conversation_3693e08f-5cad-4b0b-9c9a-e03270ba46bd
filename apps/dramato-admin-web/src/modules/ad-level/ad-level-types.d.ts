declare namespace M {
  namespace AdLevel {
    interface params {
      page_info?: {
        offset?: number
        size?: number// 选填参数
      }
      country?: string
      platform?: string
      level?: number
      // countryList?: string[]
    }

    interface Level extends CreateAdLevel {
      created: number
      updated: number
      updated_operator_name: string
    }

    interface ListResponse {
      list: ListItem[]
      page_info: PageInfo2
    }

    interface CreateAdLevel {
      id?: number
      country?: string
      platform?: string
      level?: number
      start?: number
      end?: number
      // countryList?: string[]
    }
  }
}
