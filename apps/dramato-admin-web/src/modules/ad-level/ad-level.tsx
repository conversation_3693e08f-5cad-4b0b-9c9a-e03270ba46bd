import { createComponent } from '@skynet/shared'
import { <PERSON><PERSON>, Pager, transformNumber } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { useAdLevel } from './use-ad-level'
type AdLevelOptions = {
  props: {}
}
export const AdLevel = createComponent<AdLevelOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    onCreateBtnClick,
    onEditBtnClick,
  } = useAdLevel()

  onMounted(() => {
    void search(1)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>广告价值档位</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { platform: 'ios' }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['平台', 'platform', { type: 'select', options: [{ label: 'ios', value: 'ios' }, { label: 'android', value: 'android' }], autoInsertEmptyOption: false }],
              ['档位', 'level', { type: 'number' }, { transform: transformNumber }],
              [
                '国家组',
                'country',
                {
                  type: 'text',
                  // popoverWrapperClass: 'z-popover-in-dialog',
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            广告价值档位
            <Button class="btn-primary btn btn-sm" onClick={onCreateBtnClick}>新增档位</Button>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['国家组', 'country', { class: 'w-[200px]' }],
            ['档位', 'level', { class: 'w-[100px]' }],
            ['广告价值区间', r => (
              <x-price class="flex flex-col gap-2">
                {r.start}-{r.end}
              </x-price>
            ), { class: 'w-[200px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
              </div>
            ), {
              class: 'w-[40px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default AdLevel
