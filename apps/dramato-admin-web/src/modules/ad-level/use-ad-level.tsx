/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateAdLevel, apiEditAdLevel, apiGetAdLevelList } from './ad-level-api'
import { AdLevelForm } from './ad-level-form'

export const useAdLevel = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    showADDialog,
    closeAdDialog,
    currentAd,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
  }
}

const Form = CreateForm<M.AdLevel.params>()
const params = ref<M.AdLevel.params>({
  platform: 'ios',
})

const Table = CreateTableOld<M.AdLevel.Level>()
const list = ref<M.AdLevel.Level[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)

const currentAd = ref<M.AdLevel.CreateAdLevel>({})

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetAdLevelList({
    ...params.value,
    // country: params.value.countryList && params.value.countryList.length > 0 ? params.value.countryList?.join('-') : undefined,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value } })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  page.value = _page
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeAdDialog = ref()

const showADDialog = () => {
  closeAdDialog.value = openDialog({
    title: currentAd.value.id ? '编辑档位' : '新建档位',
    body: () => <AdLevelForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentAd.value = {
    // countryList: [],
    platform: params.value.platform,
  }
  showADDialog()
}

const onEditBtnClick = (r: M.AdLevel.Level) => {
  currentAd.value = {
    ...r,
    platform: params.value.platform,
    // countryList: r.country?.split('-'),
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeAdDialog.value && closeAdDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search(page.value)
}

const switchRequestParams = () => ({
  ...currentAd.value,
  // country: currentAd.value.countryList?.join('-'),
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateAdLevel(switchRequestParams())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditAdLevel(switchRequestParams())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}
