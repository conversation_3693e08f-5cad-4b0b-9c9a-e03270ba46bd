import { createComponent, mc } from '@skynet/shared'
import { Button, Empty, transformInteger } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetStrategyGroupAdChannelList } from 'src/modules/strategy-group/strategy-group-api'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { useCommercialStrategyStore } from './commercial-strategy-store'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'

type CommercialStrategyTargetPageOptions = {
  props: {}
}
export const CommercialStrategyTargetPage = createComponent<CommercialStrategyTargetPageOptions>({
  props: {},
}, props => {
  const { formData, Form, stepOneError, validateStepOne } = useCommercialStrategyStore()
  const route = useRoute()
  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const isViewMode = computed(() => route.query.mode === 'view')
  const adChannelList = ref<Array<{ name: string, code: string }>>([])
  onMounted(async () => {
    const response = await apiGetStrategyGroupAdChannelList()
    if (!response) return
    adChannelList.value = response.data?.list ?? []
  })
  const router = useRouter()

  const onSubmit = () => {
    if (!validateStepOne()) return console.log(stepOneError.value)
    void router.push({ path: './ads', query: { ...route.query, id: formData.value.id } })
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])
  })

  const seriesPackageStore = useSeriesPackageStore()

  const items = computed(() => [
    [
      [requiredLabel('策略组名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['策略组id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ],
    [requiredLabel('是否AB实验'), 'is_abtest', {
      type: 'radio', options: [
        { label: '是', value: 1, disabled: isViewMode.value },
        { label: '否', value: 0, disabled: isViewMode.value },
      ],
    }],
    [
      requiredLabel(<span>任务优先级<small class="pl-1 text-gray-500">数字越大越高</small></span>),
      'priority',
      {
        type: 'number',
        disabled: isViewMode.value,
      },
      { transform: transformInteger },
    ],
    <h2 class="col-span-2 mb-4 text-lg font-bold"> {requiredLabel('目标用户群')} </h2>,
    [
      [<span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>, 'user_platform', {
        type: 'radio',
        options: [
          { label: '全部', value: '', disabled: isViewMode.value },
          { label: 'iOS', value: 'ios', disabled: isViewMode.value },
          { label: 'Android', value: 'android', disabled: isViewMode.value },
        ],
      }, { errorVisible: false }],
      [
        '分层画像',
        'strategy_layer_ids',
        {
          type: 'multi-select',
          search: true,
          popoverWrapperClass: 'z-popover-in-dialog',
          options: list.value.map((n, index) => {
            return { value: n.id, label: `${n.id}/${n.name}` }
          }),
          // maxlength: 1,
          disabled: isViewMode.value,
          class: 'w-[400px]',
        },
      ],
    ],
    <h2 class="col-span-2 text-lg font-bold"> 目标剧集 </h2>,
    [
      '剧包ID',
      'series_package_ids',
      {
        type: 'multi-select',
        search: true,
        popoverWrapperClass: 'z-popover-in-dialog',
        options: seriesPackageStore.list.value.map((n, index) => {
          return { value: n.id, label: `${n.id}/${n.package_name}` }
        }),
        disabled: isViewMode.value,
      },
      {
        class: mc('col-span-1'),
      },
    ],
    [
      requiredLabel('导入剧是否全部免费（无卡点）'),
      'is_free',
      {
        type: 'radio',
        options: [
          { label: '是', value: 1, disabled: !formData.value.series_package_ids || formData.value.series_package_ids.length === 0 },
          { label: '否', value: 0, disabled: !formData.value.series_package_ids || formData.value.series_package_ids.length === 0 },
        ],
      },
      { class: 'w-[400px]' },
    ],
    formData.value.is_free === 0 && [
      requiredLabel('是否修改卡点集'),
      'is_series_strategy',
      {
        type: 'radio',
        options: [
          { label: '是', value: 1, disabled: !formData.value.series_package_ids || formData.value.series_package_ids.length === 0 },
          { label: '否', value: 0, disabled: !formData.value.series_package_ids || formData.value.series_package_ids.length === 0 },
        ],
      },
      { class: 'w-[400px]' },
    ],
    formData.value.is_free === 0 && formData.value.is_series_strategy === 1 && [
      requiredLabel('卡点模式'),
      'series_strategy.strategy_type',
      {
        type: 'radio',
        options: [
          { label: '常规前移', value: 'normalForward' },
          // { label: '指定前移', value: 'normalForward' },
          { label: '常规后移', value: 'normalBack' },
          { label: '指定后移到最后', value: 'appointBack' },
        ],
      },
      {
        hint: () => (
          <x-hint>
            提醒文案：
            常规前移：按顺序从原始卡点集往前移；卡点前移不支持第0-1集，最前只能从第二集开始卡点；
            {'常规后移：按顺序从原始卡点集往后移；若卡点集>总集数，则默认取原始卡点；'}
            指定后移到最后：指定后移至最后第几集，集数=1则从最后一集卡点；
            {'如果指定后移卡点结果值比原始卡点小，默认取原始卡点；卡点集>总集数则默认取原始卡点'}
          </x-hint>
        ),
      },
      // { class: 'w-[400px]' },
    ],
    formData.value.is_free === 0 && formData.value.is_series_strategy === 1 && [
      requiredLabel('集数'),
      'series_strategy.episodes_number',
      {
        type: 'number',
        disabled: isViewMode.value,
      },
      { transform: transformInteger },
    ],
    formData.value?.is_free === 0 && [
      requiredLabel('承接模式/支持面板'),
      'panel_type', {
        type: 'radio', options: [
        // { label: '2合1面板优先', value: 0, disabled: isViewMode.value },
        // { label: '2合1广告优先', value: 1, disabled: isViewMode.value },
          { label: '3合1面板优先', value: 2, disabled: isViewMode.value },
          { label: '3合1广告优先', value: 3, disabled: isViewMode.value },
        ],
      }],
    formData.value?.is_free === 0 && [
      requiredLabel('支付面板个性化'),
      'panel_config.custom_panel_config',
      {
        type: 'checkbox-group',
        options: [
          { label: '订阅区展示', value: 'show_membership', disabled: isViewMode.value },
          { label: '充值区展示', value: 'show_recharge', disabled: isViewMode.value },
          { label: '激励广告展示', value: 'show_ad', disabled: isViewMode.value },
        ],
        disabled: isViewMode.value,
      },
    ],
    formData.value?.is_free === 0 && ['激励广告按钮', 'panel_config.highlight_ad', {
      type: 'radio',
      options: [
        { label: '默认样式（非强化）', value: false, disabled: isViewMode.value },
        { label: '强化展示', value: true, disabled: isViewMode.value },
      ],
      disabled: isViewMode.value,
    }],
    ['是否VIP PRO策略', 'is_pro', {
      type: 'radio',
      options: [
        { label: '是', value: 1, disabled: isViewMode.value },
        { label: '否', value: 0, disabled: isViewMode.value },
      ],
      disabled: isViewMode.value,
    }],
    <h2 class="col-span-2 text-lg font-bold"> 第三方支付配 </h2>,
    ['是否启用第三方', 'h5_pay', {
      type: 'radio',
      options: [
        { label: '是', value: 1, disabled: isViewMode.value },
        { label: '否', value: 0, disabled: isViewMode.value },
      ],
      disabled: isViewMode.value,
    }],
  ] as FormOptions<FormData>['props']['items'])

  const formError = computed(() => {
    return {
      ...stepOneError.value,
    }
  })

  const resetUserFormData = () => {
    formData.value = {
      ...formData.value,
      name: '',
      is_abtest: 1,
      priority: 0,
      user_platform: '', // 用户平台 0 all 1 IOS 2 Android
      strategy_layer_ids: [],
      special_type: 1,
      series_strategy: {},
      series_package_ids: [],
      series_package_ids_desc: '',
      is_series_strategy: 0,
      is_free: 0,
      panel_type: 0, // 面板类型。0-2合1面板优先，1-2合1广告优先，2-3合1面板优先，3-3合1广告优先
      panel_config: {
        hide_membership: false, // 是否隐藏订阅
        hide_recharge: false, // 是否隐藏充值
        hide_ad: false, // 是否隐藏广告
        highlight_ad: false, // 是否高亮广告
      }, // 展示元素
    }
  }
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        error={formError.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          if (path === 'is_free') {
            formData.value.is_series_strategy = value === 1 ? 0 : 1
          }
          if (path === 'series_package_ids' && (value as number[]).length === 0) {
            formData.value.is_series_strategy = 0
            formData.value.is_free = 0
          }
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default CommercialStrategyTargetPage
