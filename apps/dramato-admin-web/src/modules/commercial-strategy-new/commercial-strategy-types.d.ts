declare namespace M {

  namespace CommercialStrategy {
    type AdsStrategy = {
      id: number
      name: string
    }

    type IapStrategy = {
      id: string
      name: string
    }

    type Details = {
      isEdit?: boolean
      id?: number
      name?: string
      // scene?: string
      strategy_layer_names?: string
      strategy_layer_name_items?: Array<{
        id: number
        name: string
      }>
      priority?: number
      panel_type?: number // 面板类型。0-2合1面板优先，1-2合1广告优先，2-3合1面板优先，3-3合1广告优先
      panel_config?: {
        hide_membership: boolean // 是否隐藏订阅
        hide_recharge: boolean // 是否隐藏充值
        hide_ad: boolean // 是否隐藏广告
        highlight_ad: boolean // 是否高亮广告
        custom_panel_config?: string[]
      }// 展示元素
      special_type?: number
      is_series_strategy?: number
      series_package_ids?: number[] // 剧包id
      series_package_ids_desc?: string
      is_abtest?: 0 | 1
      is_free?: 0 | 1
      series_strategy?: {
        strategy_type?: string // moveForward-前移，moveBack-后移
        // forward_type?: string// normal-常规，appoint-指定
        episodes_number?: number // 集数

      }// 剧集卡点
      status?: number // 状态 1 线上 3 未上架
      created?: number
      created_operator_name?: string
      updated?: number
      updated_operator_name?: string
      ads_strategy_group?: AdsStrategy[]
      iap_strategy_group?: IapStrategy[]
      user_platform?: string
      strategy_layer_ids?: number[]
      product_config?: M.NSStrategyGroup.ProductConfig
      is_pro?: number // 是否是pro策略: 0-不是pro策略，1-是pro策略
      h5_pay?: number // 是否启用第三方支付，0-不启用，1-启用
      h5_product_config?: {
        recharge_option: M.StrategyGroup.LevelOption[] // 充值档位方案
        member_vip_option: M.StrategyGroup.LevelOption[] // 订阅方案
      }
    }

    type Tab = 'ios_config' | 'android_config'
  }

}
declare namespace Api {
  namespace CommercialStrategy {
    namespace Request {
      interface List {
        id?: number
        name?: string
        //  1 线上 3 未上架
        status?: 1 | 3 | number
        // 0: 全部, 1: 否, 2: 是
        is_abtest?: 0 | 1 | 2 | number
        series_package_id?: string
        // ASC: 从小到大, DESC: 从大到小
        sort_desc?: string
        scene?: string // IAP、IAA
        sort?: {
          // created?: string
          [key in string]?: string
        }
        strategy_layer_id?: number
        list_type?: number
        page_info: {
          offset: number
          size: number
        }
      }
    }

    namespace Response {
      type List = ApiResponse<{ list: M.CommercialStrategy[], page_info: { offset: number, size: number, total: number } }>
    }
  }
}
