/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, on } from '@skynet/shared'
import { Button, Checkbox, CreateTableOld, DateTime, Icon, openDialog, Pager, showAlert, TableColumnOld, transformInteger } from '@skynet/ui'
import { set, omit, cloneDeep } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'

import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { apiBatchCreateCommercialStrategy, apiBatchDeleteCommercialStrategy, apiBatchGetCommercialStrategyDetails, apiCreateCommercialStrategy, apiDeleteCommercialStrategy, apiGetCommercialStrategyDetails, apiListCommercialStrategy, apiUpdateCommercialStrategyPriority, apiUpdateCommercialStrategyState } from './commercial-strategy-api'
import { useCommercialStrategyStore } from './commercial-strategy-store'
import { useSeriesPackageAddFormStore } from '../series-package-new/use-series-package-add-form-store'
import SeriesPackageAddForm from '../series-package-new/series-package-add-form'
import { dialogMainClass } from '../series-package-new/series-package-page'
import { apiListSeriesPackage } from '../series-package-new/series-package-api'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
import { get_k_sso_token } from 'src/lib/device-id'
import qs from 'qs'
import BatchCopyForm from './batch-copy-form'
import dayjs from 'dayjs'

export const CommercialStrategyPage = createComponent(null, (props, { emit }) => {
  const {
    list,
    SearchForm: Form,
    page,
    pageSize,
    total,
    listParams,
  } = useCommercialStrategyStore()
  const Table = CreateTableOld<M.CommercialStrategy.Details>()
  const router = useRouter()
  const currentPriority = ref<{ [key: number]: number }>({})
  const checkedItems = ref<number[]>([])

  const isAll = () => {
    return list.value.every(item => checkedItems.value.includes(item.id as number))
  }

  const columns: TableColumnOld<M.CommercialStrategy.Details>[] = [
    [
      () => (
        <Checkbox
          label=""
          modelValue={isAll()}
          onUpdate:modelValue={(value: unknown) => {
            if (value) {
              checkedItems.value = list.value.map(item => item.id as number)
            } else {
              checkedItems.value = []
            }
          }}
        />
      ),
      row => {
        const id = row.id as number
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={checkedItems.value.includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!checkedItems.value.includes(id)) {
                  checkedItems.value.push(id)
                }
              } else {
                const rowIndex = checkedItems.value.findIndex(i => i === id)
                if (rowIndex !== -1) {
                  checkedItems.value.splice(rowIndex, 1)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]') },
    ],
    ['策略组ID', 'id', { class: 'w-[100px]' }],
    ['策略组名称', 'name', { class: 'w-[200px]' }],
    ['用户分层', row => (
      <x-links class="flex flex-wrap gap-2">
        {
          row?.strategy_layer_name_items && row?.strategy_layer_name_items?.length > 0 ? row.strategy_layer_name_items?.map(i =>
            <a target="_blank" class="link link-primary" href={`/user-strategy-layer/edit?id=${i.id}`}>{i.name}</a>,
          ) : '-空-'
        }
      </x-links>
    ), { class: 'w-[200px]' }],
    ['剧包ID', row => (
      <x-badge class="flex flex-wrap gap-1">{
        row.series_package_ids?.map(idstr => (
          <button class="link link-primary" onClick={async () => {
            const { formData, curStep, closeAddDialog } = useSeriesPackageAddFormStore()
            const res = await apiListSeriesPackage({ id: +idstr, page_info: { page_index: 1, page_size: 10 } })
            curStep.value = 1
            if (!res.data?.list) {
              return showAlert('未找到剧包信息')
            }
            formData.value = cloneDeep(res.data.list[0])
            formData.value.needDisabled = true
            delete formData.value.state
            delete formData.value.created
            delete formData.value.updated
            delete formData.value.created_operator_name
            delete formData.value.updated_operator_name
            delete formData.value.created_operator_id
            delete formData.value.updated_operator_id
            delete formData.value.deleted
            console.log('formData.value', formData.value)

            closeAddDialog.value = openDialog({
              title: '查看剧包',
              body: () => <SeriesPackageAddForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[1000px] overflow-hidden',
            })
          }}>{idstr}</button>
        ))
      }</x-badge>
    ), { class: 'w-[200px]' }],
    ['广告策略组ID', row => !(row.ads_strategy_group && row.ads_strategy_group[0]) ? '-空-' : <a target="_blank" class="link link-primary" href={`/ad-strategy-group/iap/create/target?id=${row.ads_strategy_group && row.ads_strategy_group[0] ? row.ads_strategy_group[0].id : ''}`}>{row.ads_strategy_group && row.ads_strategy_group[0].id ? row.ads_strategy_group[0].id : '-空-'}</a>, { class: 'w-[120px]' }],
    ['IAP策略组ID', row => !(row.iap_strategy_group && row.iap_strategy_group[0]) ? '-空-' : <a target="_blank" class="link link-primary" href={`/strategy-group/create/target?id=${row.iap_strategy_group && row.iap_strategy_group[0] ? row.iap_strategy_group[0].id : ''}`}>{row.iap_strategy_group && row.iap_strategy_group[0].id ? row.iap_strategy_group[0].id : '-空-'}</a>, { class: 'w-[100px]' }],
    ['优先级', row => (
      <x-input class="flex flex-row items-center gap-x-2">
        <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
          <input
            type="number"
            class={mc('grow w-100px')}
            value={row.priority}
            disabled={!row.isEdit}
            // ant-design:edit-filled
            onFocus={() => {
              currentPriority.value[row.id || 0] = +(row.priority || '')
            }}
            onInput={(e: Event) => {
              currentPriority.value[row.id || 0] = +((e.target as HTMLInputElement).value || '')
            }}
            onKeydown={(e: KeyboardEvent) => {
              if (e.key !== 'Enter') {
                return
              }
              if (currentPriority.value[row.id || 0] === row.priority) {
                return
              }
              void apiUpdateCommercialStrategyPriority({
                id: row.id || 0,
                priority: currentPriority.value[row.id || 0],
              }).then(() => {
                row.priority = currentPriority.value[row.id || 0]
                row.isEdit = false
              })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
            }}
            onBlur={() => {
              if (currentPriority.value[row.id || 0] === row.priority) {
                return
              }
              void apiUpdateCommercialStrategyPriority({
                id: row.id || 0,
                priority: currentPriority.value[row.id || 0],
              }).then(() => {
                row.priority = currentPriority.value[row.id || 0]
                row.isEdit = false
              })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
            }}
          />
        </label>
        <Icon name="ant-design:edit-filled" class="size-4 cursor-pointer" onClick={() => {
          row.isEdit = true
        }}
        />
      </x-input>
    ), { class: 'w-[200px]' }],
    ['导入剧是否全部免费（无卡点）', row => ['否', '是'][row.is_free || 0], { class: 'w-[200px]' }],
    ['是否修改卡点集', row => ['否', '是'][Object.keys(row?.series_strategy || {}).length > 0 ? 1 : 0], { class: 'w-[100px]' }],
    ['面板样式', row => (
      <span class="text-[var( badge badge-outline">{[
        { label: '2合1面板优先', value: 0 },
        { label: '2合1广告优先', value: 1 },
        { label: '3合1面板优先', value: 2 },
        { label: '3合1广告优先', value: 3 },
      ].find(item => item.value === row.panel_type)?.label ?? row.panel_type}
      </span>
    ), { class: 'w-[180px]' }],
    ['是否VIP PRO策略', row => (
      <>
        {row.is_pro === 1 ? '是' : '否'}
      </>
    ), { class: 'w-[120px]' }],
    ['是否启用第三方支付配置', row => (
      <>
        {row.h5_pay === 1 ? '是' : '否'}
      </>
    ), { class: 'w-[160px]' }],
    ['是否AB实验', row => (
      <>
        {row.is_abtest === 1 ? '是' : '否'}
      </>
    ), { class: 'w-[100px]' }],
    ['上架状态', row => (
      <span class={`text-[var( badge badge-outline whitespace-nowrap${row.status === 1 ? '--green-6' : '--text-6'}]`}>{[
        { label: '启用', value: 1 },
        { label: '未启用', value: 3 },
      ].find(item => item.value === row.status)?.label ?? row.status}
      </span>
    ), { class: 'w-[120px]' }],
    ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
    ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
    [<span class="px-3">操作</span>, row => (
      <x-hide-when-in-dialog class="flex gap-x-2">
        <Button
          class={mc('btn btn-warning btn-xs text-white', [2, 4].includes(row.status || 0) ? 'hidden' : '')}
          onClick={() => {
            if (!row.id) return
            void apiUpdateCommercialStrategyState({
              id: row.id,
              operation: row.status === 3 ? 1 : 2,
            })
              .then(() => {
                void search(page.value)
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
          }}
        >
          {row.status === 3 ? '启用' : '下架'}
        </Button>
        <RouterLink to={`/commercial-strategy/create/target?id=${row.id}&status=${row.status}`} class={mc('btn btn-primary btn-xs', [2, 3].includes(row.status || 0) ? '' : '')}>修改</RouterLink>
        <Button class="btn btn-success btn-xs text-white" onClick={() => copyBtn(row)}>
          复制
        </Button>
        <Button
          class="btn btn-error btn-xs text-white"
          onClick={() => {
            const hideDeleteDialog = openDialog({
              title: '删除',
              mainClass: 'pb-0 px-5',
              body: (
                <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-delete-episode-body>确认删除 策略【{row.name}】吗？</x-delete-episode-body>
                  <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void apiDeleteCommercialStrategy({
                        id: row.id || 0,
                      })
                        .then(() => {
                          void search(page.value)
                          hideDeleteDialog()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                    >确定
                    </button>
                  </x-delete-episode-footer>
                </x-delete-episode-confirm-dialog>
              ),
            })
          }}
        >
          删除
        </Button>
        <RouterLink to={`/commercial-strategy/create/target?id=${row.id}&mode=view`} class={mc('btn btn-xs', row.status === 1 ? '' : '')}>查看</RouterLink>
      </x-hide-when-in-dialog>
    ), {
      class: 'w-[280px] hide-when-in-dialog',
    },
    ]]

  const { resetFormData } = useCommercialStrategyStore()

  const loading = ref<boolean>(false)
  const search = async (_page?: number) => {
    _page = _page || page.value + 1
    loading.value = true
    checkedItems.value = []
    if (listParams.value.sort_desc) {
      const key = listParams.value.sort_desc.split('-')[0]
      const v = listParams.value.sort_desc.split('-')[1]
      listParams.value.sort = {}
      listParams.value.sort[key] = v
    }
    const res = await apiListCommercialStrategy({ ...listParams.value, page_info: { offset: (_page - 1) * pageSize.value, size: pageSize.value } })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
    total.value = res.data?.page_info.total || 0
    page.value = _page
  }

  const copyBtn = async (row: M.CommercialStrategy.Details) => {
    try {
      const rs = await apiGetCommercialStrategyDetails({ id: row.id || 0 })
      if (!rs.data) {
        return
      }
      let d = rs.data as M.CommercialStrategy.Details
      d = {
        ...omit(d, ['id', 'status']),
        name: d.name + '副本',
      }
      await apiCreateCommercialStrategy(d)
      showAlert('复制成功')
      void search(1)
    } catch (error: any) {
      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败', 'error')
    }
  }

  const onBatchCopy = async () => {
    try {
      const rs = await apiBatchGetCommercialStrategyDetails({ ids: checkedItems.value })
      await apiBatchCreateCommercialStrategy({ strategy_list: rs.data?.strategy_list || [] })
      showAlert('复制成功')
      void search(1)
    } catch (error: any) {
      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败', 'error')
    }
  }

  const exportExcel = async () => {
    const sort = {}
    const result = JSON.parse(JSON.stringify({ ...listParams.value }))

    try {
      const queryString = qs.stringify({ ...result, sort, ids: checkedItems.value.join(',') }).toString()
      // const queryString = encodeURIComponent({ ...listParams.value, sort})

      // 发送请求到服务器
      const response = await fetch(`${import.meta.env.VITE_DRAMA_API_URL}/commercial-strategy/export?${queryString}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Device: 'Web',
          Token: get_k_sso_token() || '',
        },
      })
      // 检查响应是否成功
      if (!response.ok) {
        showAlert('下载失败', 'error')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        showAlert('下载的文件为空', 'error')
        return
      }
      // 创建一个下载链接并触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `商业化策略${dayjs().format('MMDD')}.xlsx` // 设置下载的文件名
      document.body.appendChild(link)
      link.click() // 自动点击链接以开始下载
      document.body.removeChild(link) // 清理链接
      URL.revokeObjectURL(link.href) // 释放内存
    } catch (error) {
      showAlert('下载失败', 'error')
    }
  }

  const originCheckedItems = ref<number[]>([])

  const onBatchModify = () => {
    const hide = openDialog({
      title: '批量修改',
      body: () => (
        <BatchCopyForm
          ids={checkedItems.value}
          onBatchClear={async () => {
            originCheckedItems.value = [...checkedItems.value]
            await search(page.value)
            checkedItems.value = [...originCheckedItems.value]
          }}
          onCancel={() => hide()}
          onOk={async () => {
            originCheckedItems.value = [...checkedItems.value]
            await search(page.value)
            checkedItems.value = [...originCheckedItems.value]
            hide()
          }} />
      ),
      mainClass: `${dialogMainClass} !w-full`,
      customClass: '!w-[1100px] overflow-hidden',
    })
  }

  const onBatchDelete = () => {
    const hideDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body: (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>确认删除 策略【{checkedItems.value.join(',')}】吗？</x-delete-episode-body>
          <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiBatchDeleteCommercialStrategy(checkedItems.value)
                .then(() => {
                  void search(page.value)
                  hideDeleteDialog()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
            }}
            >确定
            </button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  }

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  const seriesPackageStore = useSeriesPackageStore()

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])
  })

  onMounted(() => {
    void search(1)
  })

  const onListTypeChange = (list_type: number) => {
    listParams.value.list_type = list_type
    void search(1)
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class={mc('space-y-2')}>
            <ul>
              <li>策略组列表</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs w-1/2 bg-slate-200">
              <x-tab class={mc('tab', listParams.value.list_type === 0 ? 'tab-active' : '')} onClick={() => onListTypeChange(0)}>全部策略组</x-tab>
              <x-tab class={mc('tab', listParams.value.list_type === 1 ? 'tab-active' : '')} onClick={() => onListTypeChange(1)}>非剧维度策略组</x-tab>
              <x-tab class={mc('tab', listParams.value.list_type === 2 ? 'tab-active' : '')} onClick={() => onListTypeChange(2)}>剧维度策略组</x-tab>
              <x-tab class={mc('tab', listParams.value.list_type === 3 ? 'tab-active' : '')} onClick={() => onListTypeChange(3)}>实验策略组</x-tab>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { }
              page.value = 1
              pageSize.value = 20
              void search(1)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              ['策略组ID', 'id', { type: 'text' }, { transform: transformInteger }],
              ['策略组名称', 'name', { type: 'text' }],
              ['状态', 'status', {
                type: 'select',
                options: [
                  { label: '已启用', value: 1 },
                  { label: '未启用', value: 3 },
                ],
              }, { transform: transformInteger }],
              [
                '排序',
                'sort_desc',
                {
                  type: 'select',
                  options: [
                    { label: '创建时间：从新到旧', value: 'created-desc' },
                    { label: '创建时间：从旧到新', value: 'created-asc' },
                    { label: 'ID：从大到小', value: 'id-desc' },
                    { label: 'ID：从小到大', value: 'id-asc' },
                    { label: '优先级：从大到小', value: 'priority-desc' },
                    { label: '优先级：从小到大', value: 'priority-asc' },
                  ],
                },
              ],
              [
                '分层画像',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
              [
                '是否AB实验',
                'is_abtest',
                {
                  type: 'select',
                  options: [
                    { label: '否', value: 0 },
                    { label: '是', value: 1 },
                  ],
                }, { transform: transformInteger },
              ],
              [
                '剧包ID',
                'series_package_id',
                {
                  type: 'multi-select',
                  search: true,
                  options: seriesPackageStore.list.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.package_name}` }
                  }),
                },
                {
                  class: mc('col-span-1'),
                  transform: [
                    (raw?: string) => raw ? raw.split(',').map((n: string) => +(n.trim())) : [],
                    (display: number[]): string => display ? display.join(',') : '',
                  ] as const,
                },
              ],
            ] as any}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              商业化策略
            </div>
            <x-hide-when-in-dialog class="flex items-center gap-2">
              <Button
                class="btn btn-error btn-sm text-white"
                disabled={checkedItems.value.length === 0}
                onClick={onBatchDelete}
              >
                批量删除
              </Button>
              <Button
                class="btn btn-warning btn-sm text-white"
                disabled={checkedItems.value.length === 0}
                onClick={onBatchModify}
              >
                批量修改
              </Button>
              <Button
                class="btn btn-success btn-sm text-white"
                disabled={checkedItems.value.length === 0}
                onClick={onBatchCopy}
              >
                批量复制
              </Button>
              <Button
                class="btn btn-sm"
                onClick={exportExcel}
              >
                导出Excel
              </Button>
              <Button
                class="btn btn-primary btn-sm"
                onClick={
                  () => {
                    resetFormData()
                    void router.push('/commercial-strategy/create/target')
                  }
                }
              >
                新建策略组
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default CommercialStrategyPage
