/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc } from '@skynet/shared'
import { Button, CheckboxGroup, CreateForm, CreateTableOld, Money, openDialog, RadioGroup, showAlert, TableColumnOld } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { cloneDeep, isEqual, set } from 'lodash-es'
import { memberAttr } from 'src/lib/constant'
import { computed, onMounted, ref } from 'vue'
import MemberPage from '../member/member-page'
import RechargeItemPage from '../recharge-item-management/recharge-item-page'
import RechargeLevelPage from '../recharge-level/recharge-level-page'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
import StrategyGroupAdPage from '../strategy-group-ad/strategy-group-ad-page'
import { apiBatchGetProduct, apiGetStrategyGroupDetails } from '../strategy-group/strategy-group-api'
import StrategyGroupPage from '../strategy-group/strategy-group-page'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { apiBatchEditCommercialStrategy, apiBatchGetCommercialStrategyDetails } from './commercial-strategy-api'

type BatchCopyFormOptions = {
  props: {
    ids: number[]
  }
  emits: {
    cancel: Fn
    ok: Fn
    batchClear: Fn
  }
}

const Form = CreateForm<M.CommercialStrategy.Details>()

export const BatchCopyForm = createComponent<BatchCopyFormOptions>({
  props: {
    ids: [],
  },
  emits: {
    cancel: fn,
    ok: fn,
    batchClear: fn,
  },
}, (props, { emit }) => {
  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const seriesPackageStore = useSeriesPackageStore()

  const formData = ref<M.CommercialStrategy.Details>({
    series_package_ids: [],
    strategy_layer_ids: [],
    ads_strategy_group: [],
    iap_strategy_group: [],
    product_config: {
      ios_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
      android_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
    },
    h5_product_config: {
      recharge_option: [],
      member_vip_option: [],
    },
  })

  const originFormData = ref<M.CommercialStrategy.Details>({
    series_package_ids: [],
    strategy_layer_ids: [],
    ads_strategy_group: [],
    iap_strategy_group: [],
    product_config: {
      ios_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
      android_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
    },
    h5_product_config: {
      recharge_option: [],
      member_vip_option: [],
    },
  })

  const needModify = computed(() => {
    if (props.ids.length === 1) {
      return !(isEqual(originFormData.value.series_package_ids, formData.value.series_package_ids)
        && isEqual(originFormData.value.strategy_layer_ids, formData.value.strategy_layer_ids)
        && isEqual(originFormData.value.ads_strategy_group, formData.value.ads_strategy_group)
        && isEqual(originFormData.value.iap_strategy_group, formData.value.iap_strategy_group)
        && isEqual(originFormData.value.product_config, formData.value.product_config)
        && isEqual(originFormData.value.h5_product_config?.member_vip_option, formData.value.h5_product_config?.member_vip_option)
        && isEqual(originFormData.value.h5_product_config?.recharge_option, formData.value.h5_product_config?.recharge_option))
    } else {
      if (formData.value.series_package_ids && formData.value.series_package_ids?.length > 0) {
        return true
      }
      if (formData.value.strategy_layer_ids && formData.value.strategy_layer_ids?.length > 0) {
        return true
      }
      if (formData.value.ads_strategy_group && formData.value.ads_strategy_group?.length > 0) {
        return true
      }
      if (formData.value.iap_strategy_group && formData.value.iap_strategy_group?.length > 0) {
        return true
      }
      if (!isEqual(originFormData.value.product_config, formData.value.product_config)) {
        return true
      }

      if (!isEqual(originFormData.value.h5_product_config, formData.value.h5_product_config)) {
        return true
      }

      return false
    }
  })

  const curTab = ref<'ios_config' | 'android_config' | 'h5_config'>(['ios', ''].includes(formData.value?.user_platform || '') ? 'ios_config' : 'android_config')
  const iapStrategyGroupTable = CreateTableOld<M.CommercialStrategy.IapStrategy>()
  const checkedIapStrategyGroup = ref<M.CommercialStrategy.IapStrategy[]>([])
  const LevelOptionTable = CreateTableOld<M.NSStrategyGroup.LevelOption>()
  const checkedRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedMemberItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedRechargeItemItem = ref<M.NSStrategyGroup.LevelOption[]>([])

  const iapStrategyGroupTableColumns: TableColumnOld<M.CommercialStrategy.IapStrategy>[] = [
    ['策略ID', row => <a class="link link-primary" target="_blank" href={`/strategy-group/create/target?id=${row.id}`}>{row.id}</a>, { class: 'w-[120px]' }],
    ['策略名称', 'name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            onClick={() => {
              showImportIapStrategy({ curr_row_id: row.id || '' })
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              if (!formData.value.iap_strategy_group) {
                return
              }
              formData.value.iap_strategy_group.splice(idx, 1)
              formData.value.product_config = {
                ios_config: {
                  recharge_option: [],
                  member_vip_option: [],
                  item_package_option: {},
                },
                android_config: {
                  recharge_option: [],
                  member_vip_option: [],
                  item_package_option: {},
                },
              }
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[60px]' },
    ],
  ]

  const rechargeColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['展示赠送%', (row, idx) => (
      <RadioGroup
        modelValue={row?.show_bonus}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].show_bonus = v as number
        }}
        options={[
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ]}
      />
    ), { class: 'w-[200px]' }],
    ['自定义输入', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[100px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['应用设备', 'platform', { class: 'w-[120px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充档位' : '再充值') : '常规档位', { class: 'w-[200px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i }))}
      />
    ), { class: 'w-[200px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            onClick={() => {
              showImportRechargeLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  return
                }
                formData.value.h5_product_config.recharge_option.splice(idx, 1)
                return
              }

              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const memberColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}

          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['应用设备', 'platform', { class: 'w-[100px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i }))}
      />
    ), { class: 'w-[200px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充' : '再订阅') : '常规档位', { class: 'w-[200px]' }],
    ['提示文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.tips}
        disabled
        onInput={e => {
          const tips = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].tips = tips
        }}
      />
    ), { class: 'w-[320px]' }],
    ['角标文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[320px]' }],
    ['权益说明', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.description}
        disabled
        onInput={e => {
          const description = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].description = description
        }}
      />
    ), { class: 'w-[320px]' }],
    ['原价/元', row => <Money modalValue={row.price} />, { class: 'w-[100px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    // ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    // ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"

            onClick={() => {
              showImportMemberLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  return
                }
                formData.value.h5_product_config.member_vip_option.splice(idx, 1)
                return
              }
              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const packItemTypeList = [
    { label: '解锁礼包', value: 1 },
  ]

  const rechargeItemColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['道具ID', 'id', { class: 'w-[70px]' }],
    ['道具说明', 'description', { class: 'w-[200px]' }],
    ['类型', row => {
      return packItemTypeList.find(item => item.value === row.package_type)?.label
    }, { class: 'w-[160px]' }],
    ['解锁集数', 'unlock_num', { class: 'w-[100px]' }],
    ['折扣', row => {
      return row.discount_rate + '%'
    }, { class: 'w-[100px]' }],
    ['默认充值档位（档位ID/价格/金币/赠送）', row => {
      return row.default_product && row.default_product.id + '/' + (row.default_product.price / 100).toFixed(2) + '/' + row.default_product.delivery_details.quanity + '/' + row.default_product.delivery_details.bonus
    }, { class: 'w-[230px]' }],
    ['更多充值档位（数）', row => {
      return row.product_ids?.length
    }, { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"

            onClick={() => {
              showImportRechargeItemDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"

            onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = undefined
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const showImportIapStrategy = (d?: { curr_row_id?: string, type?: 'recharge_option' | 'item_package_option' | 'member_vip_option' }) => {
    if (!d?.curr_row_id && !d?.type && formData.value.iap_strategy_group && formData.value.iap_strategy_group.length > 0) {
      checkedIapStrategyGroup.value = formData.value.iap_strategy_group.map(i => ({ id: i.id, name: i.name }))
    }
    if (d?.curr_row_id) {
      checkedIapStrategyGroup.value = [{ id: d?.curr_row_id, name: '' }]
    }

    const hide = openDialog({
      title: '导入IAP策略组',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <StrategyGroupPage
            showCheckbox
            showColumns={['id', 'name', 'status']}
            checkedItem={checkedIapStrategyGroup.value.map(i => i.id)}
            onAdd={item => {
              checkedIapStrategyGroup.value = []
              checkedIapStrategyGroup.value?.push({ id: item.id || '', name: item.name })
            }}
            onRemove={item => {
              checkedIapStrategyGroup.value = checkedIapStrategyGroup.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.iap_strategy_group || !formData.value.iap_strategy_group.length) {
                formData.value.iap_strategy_group = []
              }
              if (d?.curr_row_id) {
                const list = formData.value.iap_strategy_group || []
                const index = list.findIndex(i => i.id === d?.curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.iap_strategy_group || [])[index] = { ...checkedIapStrategyGroup.value[0] }
                hide()
                return
              }
              if (!d?.type) {
                formData.value.iap_strategy_group = [
                  ...(checkedIapStrategyGroup.value || []),
                ]
              }
              void apiGetStrategyGroupDetails({ id: checkedIapStrategyGroup.value[0].id }).then(rs => {
                if (!rs.data?.product_config) {
                  return
                }
                rs.data?.product_config?.ios_config?.item_package_option?.product_items?.forEach(element => {
                  element.props = element.props || []
                })
                rs.data?.product_config?.android_config?.item_package_option?.product_items?.forEach(element => {
                  element.props = element.props || []
                })
                if (rs.data.product_config.ios_config && rs.data.product_config.ios_config.item_package_option && rs.data.product_config.ios_config.item_package_option.default_product) {
                  rs.data.product_config.ios_config.item_package_option.default_product.props = rs.data.product_config?.ios_config.item_package_option?.default_product.props || []
                }
                if (rs.data.product_config.android_config.item_package_option && rs.data.product_config.android_config.item_package_option.default_product) {
                  rs.data.product_config.android_config.item_package_option.default_product.props = rs.data.product_config.android_config.item_package_option.default_product.props || []
                }
                if (!formData.value.product_config) {
                  formData.value.product_config = {
                    ios_config: {
                      recharge_option: [],
                      member_vip_option: [],
                      item_package_option: {},
                    },
                    android_config: {
                      recharge_option: [],
                      member_vip_option: [],
                      item_package_option: {},
                    },
                  }
                }
                if (!formData.value.product_config[curTab.value]) {
                  formData.value.product_config[curTab.value] = {
                    recharge_option: [],
                    member_vip_option: [],
                    item_package_option: {},
                  }
                }
                if (d?.type === 'recharge_option' || d?.type === 'member_vip_option') {
                  formData.value.product_config.android_config[d.type] = (rs.data?.product_config.android_config[d.type] || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() }))
                  formData.value.product_config.ios_config[d.type] = (rs.data?.product_config.ios_config[d.type] || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() }))
                  return
                }
                if (d?.type === 'item_package_option') {
                  formData.value.product_config.android_config[d.type] = rs.data?.product_config.android_config.item_package_option || {}
                  formData.value.product_config.ios_config[d.type] = rs.data?.product_config.ios_config.item_package_option || {}
                  return
                }
                formData.value.product_config = {
                  ios_config: {
                    recharge_option: (rs.data?.product_config?.ios_config?.recharge_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 充值档位方案
                    item_package_option: rs.data?.product_config?.ios_config?.item_package_option || {}, // 充值道具方案
                    member_vip_option: (rs.data?.product_config?.ios_config?.member_vip_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 订阅方案
                  },
                  android_config: {
                    recharge_option: (rs.data?.product_config?.android_config?.recharge_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 充值档位方案
                    item_package_option: rs.data?.product_config?.android_config?.item_package_option || {}, // 充值道具方案
                    member_vip_option: (rs.data?.product_config?.android_config?.member_vip_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 订阅方案
                  },
                }
                console.log('formData.value.product_config', formData.value.product_config)
              })
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedIapStrategyGroup.value = []
      },
    })
  }

  const showImportRechargeLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) {
        formData.value.h5_product_config = {
          recharge_option: [],
          member_vip_option: [],
        }
      }
      checkedRechargeItem.value = [...(formData.value.h5_product_config?.recharge_option || [])]
      if (curr_row_id) {
        checkedRechargeItem.value = formData.value.h5_product_config.recharge_option.filter(item => item.id === curr_row_id)
      }
    } else {
      if (!curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option) {
        checkedRechargeItem.value = [...formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option]
      }
      if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option) {
        checkedRechargeItem.value = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option.filter(item => item.id === curr_row_id)
      }
    }
    const hide = openDialog({
      title: '导入充值档位方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeLevelPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'h5_config' ? 3 : curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItem.value}
            onAdd={item => {
              if (curr_row_id) {
                checkedRechargeItem.value = []
              }
              checkedRechargeItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItem.value = checkedRechargeItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  formData.value.h5_product_config = {
                    recharge_option: [],
                    member_vip_option: [],
                  }
                }

                if (curr_row_id) {
                  const list = formData.value?.h5_product_config?.recharge_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.h5_product_config.recharge_option || [])[index] = { ...checkedRechargeItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.h5_product_config.recharge_option = [
                  ...(checkedRechargeItem.value || []),
                ]
                hide()
                return
              }
              if (!formData.value.product_config) return
              if (curr_row_id) {
                const list = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || []
                const index = list.findIndex(i => i.id === curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || [])[index] = { ...checkedRechargeItem.value[0], priority }
                hide()
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option = [
                ...(checkedRechargeItem.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedRechargeItem.value = []
      },
    })
  }

  const showImportRechargeItemDialog = (curr_row_id?: number, priority?: number) => {
    if (formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      checkedRechargeItemItem.value = [formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option || {}]
    } else {
      checkedRechargeItemItem.value = []
    }
    const hide = openDialog({
      title: '导入充值道具方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeItemPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItemItem.value as any}
            onAdd={item => {
              checkedRechargeItemItem.value = []
              checkedRechargeItemItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItemItem.value = checkedRechargeItemItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!checkedRechargeItemItem.value.product_items) {
                checkedRechargeItemItem.value.product_items = []
              }
              checkedRechargeItemItem.value.product_items?.forEach(element => {
                element.props = element.props || []
              })
              if (checkedRechargeItemItem.value.default_product) {
                checkedRechargeItemItem.value.default_product.props = checkedRechargeItemItem.value.default_product.props || []
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = (checkedRechargeItemItem.value || [])[0] || undefined
              hide()
              console.log('formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option', formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedRechargeItemItem.value = []
      },
    })
  }

  const showImportMemberLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) {
        formData.value.h5_product_config = {
          recharge_option: [],
          member_vip_option: [],
        }
      }
      checkedMemberItem.value = [...(formData.value.h5_product_config?.member_vip_option || [])]
      if (curr_row_id) {
        checkedMemberItem.value = formData.value.h5_product_config.member_vip_option.filter(item => item.id === curr_row_id)
      }
    } else {
      if (formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option) {
        checkedMemberItem.value = [...formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option]
      }
      if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option) {
        checkedMemberItem.value = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option.filter(item => item.id === curr_row_id)
      }
    }

    const hide = openDialog({
      title: '导入订阅方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <MemberPage
            hasNav={false}
            hasCheckItem
            hasActions={false}
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'h5_config' ? 3 : curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedMemberItem.value as any}
            onAdd={item => {
              if (curr_row_id) {
                checkedMemberItem.value = []
              }
              checkedMemberItem.value?.push({ ...item, product_id: item?.id?.toString(), props: [] } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedMemberItem.value = checkedMemberItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (curTab.value !== 'h5_config' && !formData.value.product_config) {
                return
              }

              if (curTab.value === 'h5_config' && !formData.value.h5_product_config) {
                return
              }

              if (curTab.value === 'h5_config') {
                if (curr_row_id) {
                  const list = formData.value?.h5_product_config?.member_vip_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.h5_product_config.member_vip_option || [])[index] = { ...checkedMemberItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.h5_product_config.member_vip_option = [
                  ...(checkedMemberItem.value || []),
                ]
                hide()
              } else {
                if (curr_row_id) {
                  const list = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option || [])[index] = { ...checkedMemberItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option = [
                  ...(checkedMemberItem.value || []),
                ]
                hide()
              }
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedMemberItem.value = []
      },
    })
  }

  const currentRechargeList = computed(() => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) return []

      if (!formData.value.h5_product_config.recharge_option) return []

      return formData.value.h5_product_config.recharge_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
    }

    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option?.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const currentMemberList = computed(() => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) return []

      if (!formData.value.h5_product_config.member_vip_option) return []

      return formData.value.h5_product_config.member_vip_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
    }

    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option?.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const currentRechargeItemList = computed(() => {
    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab] || !formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      return []
    }
    if (Object.keys(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option).length === 0) {
      return []
    }
    return [formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option]
  })

  const refreshLoading = ref(false)
  const iosMemberIds = computed(() => formData.value.product_config?.ios_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidMemberIds = computed(() => formData.value.product_config?.android_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const iosRechargeIds = computed(() => formData.value.product_config?.ios_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidRechargeIds = computed(() => formData.value.product_config?.android_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])

  const updateProductItem = (item: M.RechargeLevel, config: 'ios_config' | 'android_config', type: 'recharge_option' | 'member_vip_option') => {
    if (!formData.value.product_config) return
    const targetIndex = formData.value.product_config[config][type]?.findIndex((i: any) => i.id === item.id)

    if (targetIndex > -1) {
      formData.value.product_config[config][type][targetIndex] = {
        priority: formData.value.product_config[config][type][targetIndex].priority,
        title: item.title,
        discount_price: item.discount_price,
        delivery_details: item.delivery_details,
        currency: item.currency,
        price: item.price!,
        first_recharge: item.first_recharge!,
        product_id: item.id + '',
        id: item.id,
      }
    }
  }

  const onRefresh = () => {
    if (refreshLoading.value) return
    refreshLoading.value = true
    void apiBatchGetProduct([
      ...iosRechargeIds.value,
      ...iosMemberIds.value,
      ...androidRechargeIds.value,
      ...androidMemberIds.value,
    ]).then(response => {
      if (!response.data) return
      response.data.items.forEach(item => {
        if (item.id && iosRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'recharge_option')
        }
        if (item.id && iosMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'member_vip_option')
        }
        if (item.id && androidRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'recharge_option')
        }
        if (item.id && androidMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'member_vip_option')
        }
      })
    }).finally(() => {
      refreshLoading.value = false
    })
  }

  const adStrategyGroupTable = CreateTableOld<M.CommercialStrategy.AdsStrategy>()
  const checkedAdStrategyGroup = ref<M.CommercialStrategy.AdsStrategy[]>([])
  const adStrategyGroupTableColumns: TableColumnOld<M.CommercialStrategy.AdsStrategy>[] = [
    ['策略ID', row => <a class="link link-primary" target="_blank" href={`/ad-strategy-group/iap/create/target?id=${row.id}`}>{row.id}</a>, { class: 'w-[120px]' }],
    ['策略名称', 'name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            onClick={() => {
              showImportAdStrategy(row.id)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              if (!formData.value.ads_strategy_group) {
                return
              }
              formData.value.ads_strategy_group.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[60px]' },
    ],
  ]

  const showImportAdStrategy = (curr_row_id?: number) => {
    if (formData.value.ads_strategy_group && formData.value.ads_strategy_group.length > 0) {
      checkedAdStrategyGroup.value = formData.value.ads_strategy_group.map(i => ({ id: i.id, name: i.name }))
    }
    if (curr_row_id) {
      checkedAdStrategyGroup.value = [{ id: curr_row_id, name: '' }]
    }

    const hide = openDialog({
      title: '导入广告策略组',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <StrategyGroupAdPage
            showCheckbox
            showColumns={['id', 'name', 'status']}
            checkedItem={checkedAdStrategyGroup.value.map(i => i.id)}
            onAdd={item => {
              checkedAdStrategyGroup.value = []
              checkedAdStrategyGroup.value?.push({ id: Number(item.id), name: item.name })
            }}
            onRemove={item => {
              checkedAdStrategyGroup.value = checkedAdStrategyGroup.value?.filter(i => i.id !== Number(item.id))
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.ads_strategy_group || !formData.value.ads_strategy_group.length) {
                formData.value.ads_strategy_group = []
              }
              formData.value.ads_strategy_group = [
                ...(checkedAdStrategyGroup.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedAdStrategyGroup.value = []
      },
    })
  }

  const onConfirm = () => {
    const next = async () => {
      try {
        const rs = await apiBatchGetCommercialStrategyDetails({ ids: props.ids })
        rs.data?.strategy_list.forEach(item => {
          if (props.ids.length === 1) {
            item.series_package_ids = formData.value.series_package_ids || []
            item.strategy_layer_ids = formData.value.strategy_layer_ids || []
            item.ads_strategy_group = formData.value.ads_strategy_group || []
            item.iap_strategy_group = formData.value.iap_strategy_group || []
            item.product_config = formData.value.product_config
            item.h5_product_config = formData.value.h5_product_config
          } else {
            if (formData.value.series_package_ids && formData.value.series_package_ids?.length > 0) {
              item.series_package_ids = formData.value.series_package_ids
            }
            if (formData.value.strategy_layer_ids && formData.value.strategy_layer_ids?.length > 0) {
              item.strategy_layer_ids = formData.value.strategy_layer_ids
            }
            if (formData.value.ads_strategy_group && formData.value.ads_strategy_group?.length > 0) {
              item.ads_strategy_group = formData.value.ads_strategy_group
            }
            if (formData.value.iap_strategy_group && formData.value.iap_strategy_group?.length > 0) {
              item.iap_strategy_group = formData.value.iap_strategy_group
            }
            Object.keys(item.product_config?.android_config || {}).forEach(key => {
              const r = formData.value.product_config?.android_config[key]
              if (!((key !== 'item_package_option' && r.length <= 0) || (key === 'item_package_option' && Object.keys(r).length <= 0))) {
                if (!isEqual(item.product_config?.android_config[key], formData.value.product_config?.android_config[key])) {
                  item.product_config.android_config[key] = formData.value.product_config?.android_config[key]
                }
              }
            })
            Object.keys(item.product_config?.ios_config || {}).forEach(key => {
              const r = formData.value.product_config?.ios_config[key]
              if (!((key !== 'item_package_option' && r.length <= 0) || (key === 'item_package_option' && Object.keys(r).length <= 0))) {
                if (!isEqual(item.product_config?.ios_config[key], formData.value.product_config?.ios_config[key])) {
                  item.product_config.ios_config[key] = formData.value.product_config?.ios_config[key]
                }
              }
            })
            if (!isEqual(item.h5_product_config, formData.value.h5_product_config)) {
              item.h5_product_config = formData.value.h5_product_config
            }
          }
        })
        await apiBatchEditCommercialStrategy({ strategy_list: rs.data?.strategy_list || [] })
        emit('ok')
        showAlert('修改成功')
      } catch (error: any) {
        showAlert(error.response.data.message || error.response.data.err_msg || '修改失败', 'error')
      }
    }

    const closeDialog = openDialog({
      body: (
        <div>
          确认修改策略组？修改后将替换原先生效中配置，请仔细检查后点击确认。
          <div class="flex justify-end gap-x-2 px-[20px]">
            <Button class="btn  btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              void next()
              closeDialog()
            }}
            >确定
            </Button>
          </div>
        </div>
      ),
    })
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])
  })

  onMounted(() => {
    if (props.ids.length > 1) {
      return
    }

    void apiBatchGetCommercialStrategyDetails({ ids: props.ids }).then(res => {
      formData.value = res.data?.strategy_list[0] as M.CommercialStrategy.Details
      originFormData.value = cloneDeep(res.data?.strategy_list[0] as M.CommercialStrategy.Details)
    })
  })

  const originFormList = ref<M.CommercialStrategy.Details[]>([])

  onMounted(async () => {
    const rs = await apiBatchGetCommercialStrategyDetails({ ids: props.ids })
    originFormList.value = rs.data?.strategy_list as M.CommercialStrategy.Details[]
  })

  const onReset = async () => {
    await apiBatchEditCommercialStrategy({ strategy_list: originFormList.value || [] })
    showAlert('重置成功')
    formData.value = cloneDeep(originFormData.value)
    emit('batchClear')
  }

  return () => (
    <x-batch-copy-form class="flex h-4/5 flex-col overflow-hidden px-4">
      <Form class="flex flex-1 flex-col !flex-nowrap gap-4 overflow-y-auto"
        data={formData.value}
        items={[
          [
            () => (
              <div class="flex items-center gap-2">剧包ID<Button class="btn btn-error btn-sm text-white" onClick={() => {
                const closeDialog = openDialog({
                  body: (
                    <div>
                      确认清空策略组对应的剧包ID？清空后将替换原先生效中配置，请仔细检查后点击确认。
                      <div class="flex justify-end gap-x-2 px-[20px]">
                        <Button class="btn  btn-sm" onClick={() => closeDialog()}>取消</Button>
                        <Button class="btn btn-primary btn-sm" onClick={async () => {
                          const rs = await apiBatchGetCommercialStrategyDetails({ ids: props.ids })
                          const isAllEmpty = rs.data?.strategy_list.every(item => !item.series_package_ids?.length)
                          if (isAllEmpty) {
                            showAlert('策略组无配置内容，清空无效', 'error')
                            closeDialog()
                            return
                          }
                          rs.data?.strategy_list.forEach(item => item.series_package_ids = [])
                          await apiBatchEditCommercialStrategy({ strategy_list: rs.data?.strategy_list || [] })
                          // originFormList.value = rs.data?.strategy_list || []
                          closeDialog()
                          showAlert('批量清空成功')
                          emit('batchClear')
                          formData.value.series_package_ids = []
                        }}
                        >确定
                        </Button>
                      </div>
                    </div>
                  ),
                })
              }}>批量清空</Button></div>
            ),
            'series_package_ids',
            {
              type: 'multi-select',
              search: true,
              popoverWrapperClass: 'z-popover-in-dialog',
              options: seriesPackageStore.list.value.map((n, index) => {
                return { value: n.id, label: `${n.id}/${n.package_name}` }
              }),
            },
          ],
          [
            '分层画像',
            'strategy_layer_ids',
            {
              type: 'multi-select',
              search: true,
              popoverWrapperClass: 'z-popover-in-dialog',
              options: list.value.map((n, index) => {
                return { value: n.id, label: `${n.id}/${n.name}` }
              }),
            },
          ],
          [
            '广告方案',
            `ads_strategy_group`,
            {
              type: 'custom',
              render: () => {
                return (
                  <section class="border-1 rounded-lg border border-solid p-4">
                    <x-table-actions class="flex items-center justify-between">
                      <div class="space-x-2">
                        <Button class="btn btn-primary btn-sm" onClick={() => showImportAdStrategy()}>导入配置</Button>
                        <Button class="btn btn-error btn-sm text-white" onClick={() => {
                          const closeDialog = openDialog({
                            body: (
                              <div>
                                确认清空策略组对应的广告方案配置？清空后将替换原先生效中配置，请仔细检查后点击确认。
                                <div class="flex justify-end gap-x-2 px-[20px]">
                                  <Button class="btn  btn-sm" onClick={() => closeDialog()}>取消</Button>
                                  <Button class="btn btn-primary btn-sm" onClick={async () => {
                                    const rs = await apiBatchGetCommercialStrategyDetails({ ids: props.ids })
                                    const isAllEmpty = rs.data?.strategy_list.every(item => !item.ads_strategy_group?.length)
                                    if (isAllEmpty) {
                                      showAlert('策略组无配置内容，清空无效', 'error')
                                      closeDialog()
                                      return
                                    }
                                    rs.data?.strategy_list.forEach(item => item.ads_strategy_group = [])
                                    await apiBatchEditCommercialStrategy({ strategy_list: rs.data?.strategy_list || [] })
                                    // originFormList.value = rs.data?.strategy_list || []
                                    closeDialog()
                                    showAlert('批量清空成功')
                                    emit('batchClear')
                                    formData.value.ads_strategy_group = []
                                  }}
                                  >确定
                                  </Button>
                                </div>
                              </div>
                            ),
                          })
                        }}>批量清空</Button>
                      </div>
                    </x-table-actions>
                    <hr class="my-4" />
                    <adStrategyGroupTable
                      list={formData.value.ads_strategy_group}
                      columns={adStrategyGroupTableColumns}
                      class="tm-table-fix-last-column"
                    />
                  </section>
                )
              },
            }, { class: 'w-full' }],
          [
            'IAP方案',
             `iap_strategy_group`,
             {
               type: 'custom',
               render: () => {
                 return (
                   <section class="border-1 rounded-lg border border-solid p-4">
                     <x-table-actions class="flex items-center justify-between">
                       <div class="space-x-2">
                         <Button class="btn btn-primary btn-sm" onClick={() => showImportIapStrategy()}>导入配置</Button>
                         {/* <Button class="btn btn-error btn-sm" onClick={() => {
                           formData.value.iap_strategy_group = []
                           formData.value.product_config = {
                             ios_config: {
                               recharge_option: [],
                               member_vip_option: [],
                               item_package_option: {},
                             },
                             android_config: {
                               recharge_option: [],
                               member_vip_option: [],
                               item_package_option: {},
                             },
                           }
                         }}>批量清空</Button> */}
                       </div>
                     </x-table-actions>
                     <hr class="my-4" />
                     <iapStrategyGroupTable
                       list={formData.value.iap_strategy_group}
                       columns={iapStrategyGroupTableColumns}
                       class="tm-table-fix-last-column"
                     />
                   </section>
                 )
               },
             },
             { class: 'w-full' },
          ],
          <div role="tablist" class="tab-sm tabs-boxed tabs mb-4 flex gap-x-2 bg-white">
            {
              [
                'ios_config',
                'android_config',
                props.ids.length > 1 || formData.value.h5_pay === 1 ? 'h5_config' : undefined,
              ].filter(i => !!i).map(item => (
                <span
                  role="tab"
                  onClick={() => {
                    curTab.value = item
                    // checkedEpisodeItem.value = []
                    // console.log('appOptions.value', appOptions.value, item)
                    // const key = item === 'ios_config' ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'
                    // appId.value = appOptions.value?.find(i => i.label === key)?.value || 0
                    // console.log(appId.value)
                  }}
                  class={mc('tab bg-gray-300 text-black', curTab.value === item ? 'tab-active' : '')}
                >
                  {{ ios_config: 'iOS配置', android_config: 'Android配置', h5_config: 'H5渠道配置' }[item]}
                </span>
              ))
            }
            <div class="flex flex-1 justify-end">
              <Button class="btn btn-outline  btn-sm justify-self-end" disabled={refreshLoading.value} onClick={onRefresh}>刷新</Button>
            </div>
          </div>,
          ['充值档位方案-高亮显示只能勾选一个档位', `product_config.${curTab.value}.recharge_option`, {
            type: 'custom',
            render: () => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" onClick={() => showImportRechargeLevelDialog()}>导入配置</Button>
                      {curTab.value !== 'h5_config' && <Button class="btn btn-primary btn-sm" onClick={() => showImportIapStrategy({ type: 'recharge_option' })}>导入模板</Button>}
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <LevelOptionTable
                    list={currentRechargeList.value}
                    columns={rechargeColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
          curTab.value !== 'h5_config' && [
            '道具（充值）方案-仅支持配置1个。',
            `product_config.${curTab.value}.item_package_option`, {
              type: 'custom',
              render: () => {
                return (
                  <section class="border-1 rounded-lg border border-solid p-4">
                    <x-table-actions class="flex items-center justify-between">
                      <div class="space-x-2">
                        <Button class="btn btn-primary btn-sm" onClick={() => showImportRechargeItemDialog()}>导入配置</Button>
                        <Button class="btn btn-primary btn-sm" onClick={() => showImportIapStrategy({ type: 'item_package_option' })}>导入模板</Button>
                      </div>
                    </x-table-actions>
                    <hr class="my-4" />
                    <LevelOptionTable
                      list={currentRechargeItemList.value}
                      columns={rechargeItemColumns}
                      class="tm-table-fix-last-column"
                    />
                  </section>
                )
              },
            },
            {
              class: 'w-full',
              hint: '带道具时，订阅方案建议不超过2个，以避免支付面板UI内容太多。非必填，若不需要展示，请删除为空',
            },
          ],
          ['订阅方案-高亮显示只能勾选一个档位', `product_config.${curTab.value}.member_vip_option`, {
            type: 'custom',
            render: () => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" onClick={() => showImportMemberLevelDialog()}>导入配置</Button>
                      {curTab.value !== 'h5_config' && <Button class="btn btn-primary btn-sm" onClick={() => showImportIapStrategy({ type: 'member_vip_option' })}>导入模板</Button>}
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <LevelOptionTable
                    list={currentMemberList.value}
                    columns={memberColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
        ] as any[]}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actionClass="col-span-2"
        actions={[]}

      />
      <x-footer class="btn-group flex w-full items-center justify-end gap-3">
        <Button class="btn btn-outline btn-sm" onClick={onReset}>重置</Button>
        <Button class="btn btn-outline btn-sm" onClick={() => emit('cancel')}>
          取消
        </Button>
        <Button disabled={!needModify.value} class="btn btn-primary btn-sm" onClick={onConfirm}>
          确定
        </Button>
      </x-footer>
    </x-batch-copy-form>
  )
})

export default BatchCopyForm
