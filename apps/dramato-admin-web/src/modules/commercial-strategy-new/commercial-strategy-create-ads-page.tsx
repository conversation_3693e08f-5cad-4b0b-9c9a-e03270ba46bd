import { createComponent } from '@skynet/shared'
import { Button, Empty, openDialog, TableColumnOld, CreateTableOld } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCommercialStrategyStore } from './commercial-strategy-store'
import StrategyGroupAdPage from '../strategy-group-ad/strategy-group-ad-page'

type StrategyGroupCreateTargetPageOptions = {
  props: {}
}
export const StrategyGroupCreateTargetPage = createComponent<StrategyGroupCreateTargetPageOptions>({
  props: {},
}, props => {
  const { formData, Form } = useCommercialStrategyStore()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const router = useRouter()
  const onSubmit = () => {
    void router.push({ path: './product', query: { ...route.query, id: formData.value.id } })
  }

  const adStrategyGroupTable = CreateTableOld<M.CommercialStrategy.AdsStrategy>()
  const checkedAdStrategyGroup = ref<M.CommercialStrategy.AdsStrategy[]>([])

  const adStrategyGroupTableColumns: TableColumnOld<M.CommercialStrategy.AdsStrategy>[] = [
    ['策略ID', row => <a class="link link-primary" target="_blank" href={`/ad-strategy-group/iap/create/target?id=${row.id}`}>{row.id}</a>, { class: 'w-[120px]' }],
    ['策略名称', 'name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportAdStrategy(row.id)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.ads_strategy_group) {
                return
              }
              formData.value.ads_strategy_group.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[60px]' },
    ],
  ]

  const showImportAdStrategy = (curr_row_id?: number) => {
    if (formData.value.ads_strategy_group && formData.value.ads_strategy_group.length > 0) {
      checkedAdStrategyGroup.value = formData.value.ads_strategy_group.map(i => ({ id: i.id, name: i.name }))
    }
    if (curr_row_id) {
      checkedAdStrategyGroup.value = [{ id: curr_row_id, name: '' }]
    }

    const hide = openDialog({
      title: '导入广告策略组',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <StrategyGroupAdPage
            showCheckbox
            showColumns={['id', 'name', 'status']}
            checkedItem={checkedAdStrategyGroup.value.map(i => i.id)}
            onAdd={item => {
              checkedAdStrategyGroup.value = []
              checkedAdStrategyGroup.value?.push({ id: Number(item.id), name: item.name })
            }}
            onRemove={item => {
              checkedAdStrategyGroup.value = checkedAdStrategyGroup.value?.filter(i => i.id !== Number(item.id))
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.ads_strategy_group || !formData.value.ads_strategy_group.length) {
                formData.value.ads_strategy_group = []
              }
              formData.value.ads_strategy_group = [
                ...(checkedAdStrategyGroup.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedAdStrategyGroup.value = []
      },
    })
  }

  const items = computed(() => [
    [
      '广告方案',
      `ads_strategy_group`,
      {
        type: 'custom',
        render: ({ item, value }) => {
          return (
            <section class="border-1 rounded-lg border border-solid p-4">
              <x-table-actions class="flex items-center justify-between">
                <div class="space-x-2">
                  <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportAdStrategy()}>导入配置</Button>
                </div>
              </x-table-actions>
              <hr class="my-4" />
              <adStrategyGroupTable
                list={formData.value.ads_strategy_group}
                columns={adStrategyGroupTableColumns}
                class="tm-table-fix-last-column"
              />
            </section>
          )
        },
      }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const resetUserFormData = () => {
    formData.value = {
      ...formData.value,
      panel_type: 0, // 面板类型。0-2合1面板优先，1-2合1广告优先，2-3合1面板优先，3-3合1广告优先
      panel_config: {
        hide_membership: false, // 是否隐藏订阅
        hide_recharge: false, // 是否隐藏充值
        hide_ad: false, // 是否隐藏广告
        highlight_ad: false, // 是否高亮广告
      }, // 展示元素
      ads_strategy_group: [], // 广告方案
    }
  }
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/commercial-strategy/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default StrategyGroupCreateTargetPage
