/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQueriesWithParser, mc, queryParsers } from '@skynet/shared'
import { Button, CheckboxGroup, CreateTableOld, DialogFooter, Money, openDialog, RadioGroup, TableColumnOld } from '@skynet/ui'
import { set } from 'lodash-es'
import { memberAttr } from 'src/lib/constant'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MemberPage from '../member/member-page'
import { useAppAndLangOptions } from '../options/use-app-options'
import RechargeItemPage from '../recharge-item-management/recharge-item-page'
import RechargeLevelPage from '../recharge-level/recharge-level-page'
import { apiBatchGetProduct, apiGetStrategyGroupDetails } from '../strategy-group/strategy-group-api'
import StrategyGroupPage from '../strategy-group/strategy-group-page'
import { apiCreateCommercialStrategy, apiEditCommercialStrategy } from './commercial-strategy-api'
import { useCommercialStrategyStore } from './commercial-strategy-store'

type CommercialStrategyCreateProductPageOptions = {
  props: {}
}

export const CommercialStrategyCreateProductPage = createComponent<CommercialStrategyCreateProductPageOptions>({
  props: {},
}, props => {
  const { formData, Form, validateStepThree, validateStepOne, stepOneError, stepThreeError } = useCommercialStrategyStore()
  const curTab = ref(['ios', ''].includes(formData.value?.user_platform || '') ? 'ios_config' : 'android_config')

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const appId = ref(0)

  const { appOptions } = useAppAndLangOptions(() => undefined, {
    onSuccess: () => {
      appId.value = appOptions.value?.find(i => i.label === (['ios', ''].includes(formData.value?.user_platform || '') ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'))?.value || 0
    },
  })

  const iapStrategyGroupTable = CreateTableOld<M.CommercialStrategy.IapStrategy>()
  const checkedIapStrategyGroup = ref<M.CommercialStrategy.IapStrategy[]>([])
  const LevelOptionTable = CreateTableOld<M.NSStrategyGroup.LevelOption>()
  const checkedRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedMemberItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedRechargeItemItem = ref<M.NSStrategyGroup.LevelOption[]>([])

  const iapStrategyGroupTableColumns: TableColumnOld<M.CommercialStrategy.IapStrategy>[] = [
    ['策略ID', row => <a class="link link-primary" target="_blank" href={`/strategy-group/create/target?id=${row.id}`}>{row.id}</a>, { class: 'w-[120px]' }],
    ['策略名称', 'name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportIapStrategy(row.id)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.iap_strategy_group) {
                return
              }
              formData.value.iap_strategy_group.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[60px]' },
    ],
  ]

  const rechargeColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          disabled={isViewMode.value}
          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (curTab.value === 'h5_config') {
              if (!formData.value.h5_product_config) {
                formData.value.h5_product_config = {
                  recharge_option: [],
                  member_vip_option: [],
                }
              }
              formData.value.h5_product_config.recharge_option[idx].priority = priority
              return
            }
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['展示赠送%', (row, idx) => (
      <RadioGroup
        modelValue={row?.show_bonus}
        onUpdate:modelValue={v => {
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.recharge_option[idx].show_bonus = v as number
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].show_bonus = v as number
        }}
        options={[
          { label: '是', value: 1, disabled: isViewMode.value },
          { label: '否', value: 0, disabled: isViewMode.value },
        ]}
      />
    ), { class: 'w-[200px]' }],
    ['自定义输入', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[100px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.recharge_option[idx].slogan = slogan
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['应用设备', 'platform', { class: 'w-[120px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充档位' : '再充值') : '常规档位', { class: 'w-[200px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.recharge_option.forEach(v => {
              v.props = []
            })
            formData.value.h5_product_config.recharge_option[idx].props = (v || []) as string[]
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i, disabled: isViewMode.value }))}
      />
    ), { class: 'w-[200px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class={mc('btn btn-outline btn-xs', curTab.value === 'h5_config' ? 'hidden' : '')}
            disabled={isViewMode.value}
            onClick={() => {
              showImportRechargeLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  return
                }
                formData.value.h5_product_config.recharge_option.splice(idx, 1)
                return
              }

              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const memberColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          disabled={isViewMode.value}
          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (curTab.value === 'h5_config') {
              if (!formData.value.h5_product_config) {
                formData.value.h5_product_config = {
                  recharge_option: [],
                  member_vip_option: [],
                }
              }
              formData.value.h5_product_config.member_vip_option[idx].priority = priority
              return
            }
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['应用设备', 'platform', { class: 'w-[100px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.member_vip_option.forEach(v => {
              v.props = []
            })
            formData.value.h5_product_config.member_vip_option[idx].props = (v || []) as string[]
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i, disabled: isViewMode.value }))}
      />
    ), { class: 'w-[200px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充' : '再订阅') : '常规档位', { class: 'w-[200px]' }],
    ['提示文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.tips}
        disabled
        onInput={e => {
          const tips = (e.target as HTMLInputElement).value || ''
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.member_vip_option[idx].tips = tips
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].tips = tips
        }}
      />
    ), { class: 'w-[320px]' }],
    ['角标文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.member_vip_option[idx].slogan = slogan
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[320px]' }],
    ['权益说明', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.description}
        disabled
        onInput={e => {
          const description = (e.target as HTMLInputElement).value || ''
          if (curTab.value === 'h5_config') {
            if (!formData.value.h5_product_config) {
              formData.value.h5_product_config = {
                recharge_option: [],
                member_vip_option: [],
              }
            }
            formData.value.h5_product_config.member_vip_option[idx].description = description
            return
          }
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].description = description
        }}
      />
    ), { class: 'w-[320px]' }],
    ['原价/元', row => <Money modalValue={row.price} />, { class: 'w-[100px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    // ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    // ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class={mc('btn btn-outline btn-xs', curTab.value === 'h5_config' ? 'hidden' : '')}
            disabled={isViewMode.value}
            onClick={() => {
              showImportMemberLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  return
                }
                formData.value.h5_product_config.member_vip_option.splice(idx, 1)
                return
              }

              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const packItemTypeList = [
    { label: '解锁礼包', value: 1 },
    { label: '整剧打包解锁', value: 5 },
  ]

  const rechargeItemColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['道具ID', 'id', { class: 'w-[70px]' }],
    ['道具说明', 'description', { class: 'w-[200px]' }],
    ['类型', row => {
      return packItemTypeList.find(item => item.value === row.package_type)?.label
    }, { class: 'w-[160px]' }],
    ['解锁集数', row => {
      return row.package_type == 1 ? row.unlock_num : '全剧'
    }, { class: 'w-[100px]' }],
    ['折扣', row => {
      return row.discount_rate + '%'
    }, { class: 'w-[100px]' }],
    ['默认充值档位（档位ID/价格/金币+赠送）', row => {
      return row.package_type == 1 ? row.default_product && row.default_product.id + ' / $' + (row.default_product.price / 100).toFixed(2) + ' / ' + row.default_product.delivery_details.quanity + ' + ' + (row.default_product.delivery_details.bonus || 0) : row.product_items && row.product_items[0].id + ' / $' + (row.product_items[0].price / 100).toFixed(2) + ' / ' + row.product_items[0].delivery_details.quanity + ' + ' + (row.product_items[0].delivery_details.bonus || 0)
    }, { class: 'w-[230px]' }],
    ['更多充值档位（数）', row => {
      return row.package_type == 1 ? row.product_ids?.length : row.episode_products?.length
    }, { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportRechargeItemDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = undefined
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const showImportRechargeLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) {
        formData.value.h5_product_config = {
          recharge_option: [],
          member_vip_option: [],
        }
      }
      checkedRechargeItem.value = [...(formData.value.h5_product_config?.recharge_option || [])]
      if (curr_row_id) {
        checkedRechargeItem.value = formData.value.h5_product_config.recharge_option.filter(item => item.id === curr_row_id)
      }
    } else {
      if (!curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option) {
        checkedRechargeItem.value = [...formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option]
      }
      if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option) {
        checkedRechargeItem.value = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].recharge_option.filter(item => item.id === curr_row_id)
      }
    }
    const hide = openDialog({
      title: '导入充值档位方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeLevelPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'h5_config' ? 3 : curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItem.value}
            onAdd={item => {
              if (curr_row_id) {
                checkedRechargeItem.value = []
              }
              checkedRechargeItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItem.value = checkedRechargeItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (curTab.value === 'h5_config') {
                if (!formData.value.h5_product_config) {
                  formData.value.h5_product_config = {
                    recharge_option: [],
                    member_vip_option: [],
                  }
                }

                if (curr_row_id) {
                  const list = formData.value?.h5_product_config?.recharge_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.h5_product_config.recharge_option || [])[index] = { ...checkedRechargeItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.h5_product_config.recharge_option = [
                  ...(checkedRechargeItem.value || []),
                ]
                console.log(
                  'formData.value.h5_product_config.recharge_option',
                  formData.value.h5_product_config.recharge_option,
                )

                hide()
                return
              }
              if (!formData.value.product_config) return
              if (curr_row_id) {
                const list = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || []
                const index = list.findIndex(i => i.id === curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || [])[index] = { ...checkedRechargeItem.value[0], priority }
                hide()
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option = [
                ...(checkedRechargeItem.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedRechargeItem.value = []
      },
    })
  }

  const showImportIapStrategy = (curr_row_id?: string) => {
    if (formData.value.iap_strategy_group && formData.value.iap_strategy_group.length > 0) {
      checkedIapStrategyGroup.value = formData.value.iap_strategy_group.map(i => ({ id: i.id, name: i.name }))
    }
    if (curr_row_id) {
      checkedIapStrategyGroup.value = [{ id: curr_row_id, name: '' }]
    }

    const hide = openDialog({
      title: '导入IAP策略组',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <StrategyGroupPage
            showCheckbox
            showColumns={['id', 'name', 'status']}
            checkedItem={checkedIapStrategyGroup.value.map(i => i.id)}
            onAdd={item => {
              checkedIapStrategyGroup.value = []
              checkedIapStrategyGroup.value?.push({ id: item.id || '', name: item.name })
            }}
            onRemove={item => {
              checkedIapStrategyGroup.value = checkedIapStrategyGroup.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.iap_strategy_group || !formData.value.iap_strategy_group.length) {
                formData.value.iap_strategy_group = []
              }
              if (curr_row_id) {
                const list = formData.value.iap_strategy_group || []
                const index = list.findIndex(i => i.id === curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.iap_strategy_group || [])[index] = { ...checkedIapStrategyGroup.value[0] }
                hide()
                return
              }
              formData.value.iap_strategy_group = [
                ...(checkedIapStrategyGroup.value || []),
              ]
              void apiGetStrategyGroupDetails({ id: checkedIapStrategyGroup.value[0].id }).then(rs => {
                if (!rs.data?.product_config) {
                  return
                }
                rs.data?.product_config?.ios_config?.item_package_option?.product_items?.forEach(element => {
                  element.props = element.props || []
                })
                rs.data?.product_config?.android_config?.item_package_option?.product_items?.forEach(element => {
                  element.props = element.props || []
                })
                if (rs.data.product_config.ios_config && rs.data.product_config.ios_config.item_package_option && rs.data.product_config.ios_config.item_package_option.default_product) {
                  rs.data.product_config.ios_config.item_package_option.default_product.props = rs.data.product_config?.ios_config.item_package_option?.default_product.props || []
                }
                if (rs.data.product_config.android_config.item_package_option && rs.data.product_config.android_config.item_package_option.default_product) {
                  rs.data.product_config.android_config.item_package_option.default_product.props = rs.data.product_config.android_config.item_package_option.default_product.props || []
                }
                formData.value.product_config = {
                  ios_config: {
                    recharge_option: (rs.data?.product_config?.ios_config?.recharge_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 充值档位方案
                    item_package_option: rs.data?.product_config?.ios_config?.item_package_option || {}, // 充值道具方案
                    member_vip_option: (rs.data?.product_config?.ios_config?.member_vip_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 订阅方案
                  },
                  android_config: {
                    recharge_option: (rs.data?.product_config?.android_config?.recharge_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 充值档位方案
                    item_package_option: rs.data?.product_config?.android_config?.item_package_option || {}, // 充值道具方案
                    member_vip_option: (rs.data?.product_config?.android_config?.member_vip_option || []).map(i => ({ ...i, props: [], product_id: i?.id?.toString() })), // 订阅方案
                  },
                }
                console.log('formData.value.product_config', formData.value.product_config)
              })
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedIapStrategyGroup.value = []
      },
    })
  }

  const showImportRechargeItemDialog = (curr_row_id?: number, priority?: number) => {
    if (formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      checkedRechargeItemItem.value = [formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option || {}]
    } else {
      checkedRechargeItemItem.value = []
    }

    const hide = openDialog({
      title: '导入充值道具方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeItemPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItemItem.value as any}
            onAdd={item => {
              checkedRechargeItemItem.value = []
              checkedRechargeItemItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItemItem.value = checkedRechargeItemItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.product_config) return
              if (!checkedRechargeItemItem.value.product_items) {
                checkedRechargeItemItem.value.product_items = []
              }
              checkedRechargeItemItem.value.product_items?.forEach(element => {
                element.props = element.props || []
              })
              if (checkedRechargeItemItem.value.default_product) {
                checkedRechargeItemItem.value.default_product.props = checkedRechargeItemItem.value.default_product.props || []
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = (checkedRechargeItemItem.value || [])[0] || undefined
              hide()
              console.log('formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option', formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedRechargeItemItem.value = []
      },
    })
  }

  const showImportMemberLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) {
        formData.value.h5_product_config = {
          recharge_option: [],
          member_vip_option: [],
        }
      }
      checkedMemberItem.value = [...(formData.value.h5_product_config?.member_vip_option || [])]
      if (curr_row_id) {
        checkedMemberItem.value = formData.value.h5_product_config.member_vip_option.filter(item => item.id === curr_row_id)
      }
    } else {
      if (formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option) {
        checkedMemberItem.value = [...formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option]
      }
      if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option) {
        checkedMemberItem.value = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option.filter(item => item.id === curr_row_id)
      }
    }

    const hide = openDialog({
      title: '导入订阅方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <MemberPage
            hasNav={false}
            hasCheckItem
            hasActions={false}
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'h5_config' ? 3 : curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedMemberItem.value as any}
            onAdd={item => {
              if (curr_row_id) {
                checkedMemberItem.value = []
              }
              checkedMemberItem.value?.push({ ...item, product_id: item?.id?.toString(), props: [] } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedMemberItem.value = checkedMemberItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (curTab.value !== 'h5_config' && !formData.value.product_config) {
                return
              }

              if (curTab.value === 'h5_config' && !formData.value.h5_product_config) {
                return
              }

              if (curTab.value === 'h5_config') {
                if (curr_row_id) {
                  const list = formData.value?.h5_product_config?.member_vip_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.h5_product_config.member_vip_option || [])[index] = { ...checkedMemberItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.h5_product_config.member_vip_option = [
                  ...(checkedMemberItem.value || []),
                ]
                hide()
              } else {
                if (curr_row_id) {
                  const list = formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option || []
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option || [])[index] = { ...checkedMemberItem.value[0], priority }
                  hide()
                  return
                }
                formData.value.product_config[curTab.value as M.StrategyGroup.Tab].member_vip_option = [
                  ...(checkedMemberItem.value || []),
                ]
                hide()
              }
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-1200px',
      beforeClose: () => {
        checkedMemberItem.value = []
      },
    })
  }

  const currentRechargeList = computed(() => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) return []

      if (!formData.value.h5_product_config.recharge_option) return []

      return formData.value.h5_product_config.recharge_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
    }

    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option?.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const currentMemberList = computed(() => {
    if (curTab.value === 'h5_config') {
      if (!formData.value.h5_product_config) return []

      if (!formData.value.h5_product_config.member_vip_option) return []

      return formData.value.h5_product_config.member_vip_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
    }

    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option?.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const currentRechargeItemList = computed(() => {
    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab] || !formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      return []
    }
    if (Object.keys(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option).length === 0) {
      return []
    }
    return [formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option]
  })

  const updateProductItem = (item: M.RechargeLevel, config: 'ios_config' | 'android_config', type: 'recharge_option' | 'member_vip_option') => {
    if (!formData.value.product_config) return
    const targetIndex = formData.value.product_config[config][type]?.findIndex((i: any) => i.id === item.id)

    if (targetIndex > -1) {
      formData.value.product_config[config][type][targetIndex] = {
        priority: formData.value.product_config[config][type][targetIndex].priority,
        title: item.title,
        discount_price: item.discount_price,
        delivery_details: item.delivery_details,
        currency: item.currency,
        price: item.price!,
        first_recharge: item.first_recharge!,
        product_id: item.id + '',
        id: item.id,
      }
    }
  }

  const refreshLoading = ref(false)
  const iosMemberIds = computed(() => formData.value.product_config?.ios_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidMemberIds = computed(() => formData.value.product_config?.android_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const iosRechargeIds = computed(() => formData.value.product_config?.ios_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidRechargeIds = computed(() => formData.value.product_config?.android_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])

  const onRefresh = () => {
    if (refreshLoading.value) return
    refreshLoading.value = true
    void apiBatchGetProduct([
      ...iosRechargeIds.value,
      ...iosMemberIds.value,
      ...androidRechargeIds.value,
      ...androidMemberIds.value,
    ]).then(response => {
      if (!response.data) return
      response.data.items.forEach(item => {
        if (item.id && iosRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'recharge_option')
        }
        if (item.id && iosMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'member_vip_option')
        }
        if (item.id && androidRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'recharge_option')
        }
        if (item.id && androidMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'member_vip_option')
        }
      })
    }).finally(() => {
      refreshLoading.value = false
    })
  }

  const { status } = getQueriesWithParser({ status: queryParsers.string('') })

  const onSubmit = async () => {
    if (!validateStepOne() || !validateStepThree()) {
      console.log('formData.value', formData.value)

      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepThreeError.value)}
          </div>
        ),
      })
      return
    }

    const next = async () => {
      const requestApi = formData.value.id ? apiEditCommercialStrategy : apiCreateCommercialStrategy
      const response = await requestApi({ ...formData.value, series_strategy: formData.value.is_series_strategy === 1 ? formData.value.series_strategy : undefined })
      if (response.code !== 200) return
      formData.value.id = response.data?.id || 0
      setTimeout(() => {
        void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
          const closeDialog = openDialog({
            title: '提示',
            body: (
              <div>保存成功
                <DialogFooter okText="返回列表页" onOk={() => {
                  closeDialog()
                  void router.push('/commercial-strategy')
                }} cancelText="留在当前页" onCancel={() => closeDialog()}
                />
              </div>
            ),
          })
        })
      })
    }

    if (+status === 1) {
      const closeDialog = openDialog({
        // title: '删除广告',
        body: (
          <div>
            确认修改后直接上线生效?
            <div class="flex justify-end gap-x-2 px-[20px]">
              <Button class="btn  btn-sm" onClick={() => closeDialog()}>取消</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                void next()
                closeDialog()
              }}
              >确定
              </Button>
            </div>
          </div>
        ),
      })
      return
    }

    await next()
  }

  const resetTaskFormData = () => {
    formData.value = {
      ...formData.value,
      iap_strategy_group: [],
      product_config: {
        ios_config: {
          recharge_option: [],
          member_vip_option: [],
          item_package_option: {},
        },
        android_config: {
          recharge_option: [],
          member_vip_option: [],
          item_package_option: {},
        },
      },
    }
  }

  return () => (
    <div class="flex flex-1 flex-col overflow-hidden p-8">
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepThreeError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          resetTaskFormData()
        }}
        onSubmit={onSubmit}
        items={[
          [
            'IAP方案',
             `iap_strategy_group`,
             {
               type: 'custom',
               render: () => {
                 return (
                   <section class="border-1 rounded-lg border border-solid p-4">
                     <x-table-actions class="flex items-center justify-between">
                       <div class="space-x-2">
                         <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportIapStrategy()}>导入配置</Button>
                       </div>
                     </x-table-actions>
                     <hr class="my-4" />
                     <iapStrategyGroupTable
                       list={formData.value.iap_strategy_group}
                       columns={iapStrategyGroupTableColumns}
                       class="tm-table-fix-last-column"
                     />
                   </section>
                 )
               },
             },
             { class: 'w-full' },
          ],
          <div role="tablist" class="tab-sm tabs-boxed tabs mb-4 flex gap-x-2 bg-white">
            {
              [
                ['', 'ios'].includes(formData.value.user_platform || '') ? 'ios_config' : '',
                ['', 'android'].includes(formData.value.user_platform || '') ? 'android_config' : '',
                formData.value.h5_pay === 1 ? 'h5_config' : '',
              ].filter(i => !!i).map(item => (
                <span
                  role="tab"
                  onClick={() => {
                    curTab.value = item
                    console.log('appOptions.value', appOptions.value, item)
                    let key = item === 'ios_config' ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'
                    if (item === 'h5_config') {
                      key = 'Dramawave_H5'
                    }
                    appId.value = appOptions.value?.find(i => i.label === key)?.value || 0
                    console.log(appId.value)
                  }}
                  class={mc('tab bg-gray-300 text-black', curTab.value === item ? 'tab-active' : '')}
                >
                  {{ ios_config: 'iOS配置', android_config: 'Android配置', h5_config: 'H5渠道配置' }[item]}
                </span>
              ))
            }
            <div class="flex flex-1 justify-end">
              <Button class="btn btn-outline  btn-sm justify-self-end" disabled={refreshLoading.value || isViewMode.value} onClick={onRefresh}>刷新</Button>
            </div>
          </div>,
          ['充值档位方案-高亮显示只能勾选一个档位', `product_config.${curTab.value}.recharge_option`, {
            type: 'custom',
            render: () => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportRechargeLevelDialog()}>导入配置</Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <LevelOptionTable
                    list={currentRechargeList.value}
                    columns={rechargeColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
          curTab.value === 'h5_config' ? null : [
            '道具（充值）方案-仅支持配置1个。',
            `product_config.${curTab.value}.item_package_option`, {
              type: 'custom',
              render: () => {
                return (
                  <section class="border-1 rounded-lg border border-solid p-4">
                    <x-table-actions class="flex items-center justify-between">
                      <div class="space-x-2">
                        <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportRechargeItemDialog()}>导入配置</Button>
                      </div>
                    </x-table-actions>
                    <hr class="my-4" />
                    <LevelOptionTable
                      list={currentRechargeItemList.value}
                      columns={rechargeItemColumns}
                      class="tm-table-fix-last-column"
                    />
                  </section>
                )
              },
            },
            {
              class: 'w-full',
              hint: '带道具时，订阅方案建议不超过2个，以避免支付面板UI内容太多。非必填，若不需要展示，请删除为空',
            },
          ],
          ['订阅方案-高亮显示只能勾选一个档位', `product_config.${curTab.value}.member_vip_option`, {
            type: 'custom',
            render: () => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportMemberLevelDialog()}>导入配置</Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <LevelOptionTable
                    list={currentMemberList.value}
                    columns={memberColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
        ] as any[]}
        actions={() => (
          <div class="flex items-center justify-between gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/commercial-strategy/create/ads`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit" disabled={isViewMode.value}>保存</Button>
          </div>
        )}
      />

    </div>
  )
})
export default CommercialStrategyCreateProductPage
