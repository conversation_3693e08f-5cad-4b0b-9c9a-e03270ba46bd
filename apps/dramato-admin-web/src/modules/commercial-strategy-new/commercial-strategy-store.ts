import { useValidator } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.CommercialStrategy.Details>()
const defaultFormData = () => {
  return {
    name: '',
    is_abtest: 0,
    priority: 0,
    user_platform: '', // 用户平台 0 all 1 IOS 2 Android
    strategy_layer_ids: [],
    special_type: 1,
    is_free: 0,
    series_strategy: {
      strategy_type: 'normalBack',
      episodes_number: 1,
    },
    series_package_ids: [],
    series_package_ids_desc: '',
    is_series_strategy: 0,
    product_config: {
      ios_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
      android_config: {
        recharge_option: [],
        member_vip_option: [],
        item_package_option: {},
      },
    },
    h5_pay: 0,
    h5_product_config: {
      recharge_option: [],
      member_vip_option: [],
    },
    panel_type: 2, // 面板类型。0-2合1面板优先，1-2合1广告优先，2-3合1面板优先，3-3合1广告优先
    panel_config: {
      hide_membership: false, // 是否隐藏订阅
      hide_recharge: false, // 是否隐藏充值
      hide_ad: false, // 是否隐藏广告
      highlight_ad: false, // 是否高亮广告
    }, // 展示元素
    ads_strategy_group: [], // 广告方案
    iap_strategy_group: [],
    is_pro: 0, // vip
  } as M.CommercialStrategy.Details
}
const formData = ref<M.CommercialStrategy.Details>(defaultFormData())
const activeUserType = ref<'latest_days' | 'start_and_end'>('latest_days')
const needShowSaveDragDialog = ref(true)

const resetFormData = () => {
  formData.value = defaultFormData()
}

const stepOneRules = z.object({
  name: z.string().min(1),
  priority: z.number().min(0, '必填'),
  strategy_layer_ids: z.array(z.number()).min(1, '必填'),
  series_strategy: z.object({
    strategy_type: z.string().min(1, '必填'),
    episodes_number: z.number().min(1, '必填').max(9999, '最多9999集'),
  }),
  panel_config: z.object({
    custom_panel_config: z.array(z.string()).min(1, '必填'),
  }),
})

const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)

const validateStepOne = () => {
  const exclude: string[] = []
  console.log('location.pathname', location.pathname)

  if (formData.value.is_series_strategy === 0) {
    exclude.push('series_strategy')
  }
  if (formData.value.is_abtest === 1) {
    exclude.push('strategy_layer_ids')
  }
  if (formData.value?.is_free !== 0) {
    exclude.push('panel_config')
  }
  const valid = _validateStepOne({ exclude })
  return valid
}

const stepThreeRules = z.object({
  // product_config: z.object({
  //   ios_config: z.object({
  //     recharge_option: z.array(z.object({
  //       id: z.number(),
  //     })).min(1, { message: '必填' }),
  //     member_vip_option: z.array(z.object({
  //       id: z.number(),
  //     })).min(1, { message: '必填' }),
  //   }).optional(),
  //   android_config: z.object({
  //     recharge_option: z.array(z.object({
  //       id: z.number(),
  //     })).min(1, { message: '必填' }),
  //     member_vip_option: z.array(z.object({
  //       id: z.number(),
  //     })).min(1, { message: '必填' }),
  //   }).optional(),
  // }),
})

const { error: stepThreeError, validateAll: _validateStepThree } = useValidator(formData, stepThreeRules)

const validateStepThree = () => {
  const exclude = [].filter(Boolean).flat() as string[]
  const valid = _validateStepThree({ exclude })
  return valid
}
// const userProfileList = ref<Api.CommercialStrategyUserProfile.Item[]>([])
const fetchingUserProfileList = ref(false)

const platformMap: Record<number, string> = {
  0: 'All',
  1: 'IOS',
  2: 'Android',
}

const list = ref<M.CommercialStrategy.Details[]>([])
type ListParams = Omit<Api.CommercialStrategy.Request.List, 'page_info'>
const SearchForm = CreateForm<ListParams>()
const page = ref<number>(1)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const listParams = ref<ListParams>({
  list_type: 0,
})
export const useCommercialStrategyStore = () => {
  return {
    platformMap,
    formData,
    resetFormData,
    Form,
    stepOneError,
    stepThreeError,
    validateStepOne,
    validateStepThree,
    activeUserType,
    needShowSaveDragDialog,
    fetchingUserProfileList,
    list,
    SearchForm,
    page,
    pageSize,
    total,
    listParams,
  }
}
