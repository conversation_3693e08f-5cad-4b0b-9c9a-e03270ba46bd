import { omit } from 'lodash-es'
import { httpClient } from 'src/lib/http-client'

export const apiListCommercialStrategy = (params: Api.CommercialStrategy.Request.List) => {
  return httpClient.post<Api.CommercialStrategy.Response.List>('/commercial-strategy/list', params)
}

export const apiDeleteCommercialStrategy = (params: { id: number }) => {
  return httpClient.post<Api.CommercialStrategy.Response.List>('/commercial-strategy/delete', { ids: [params.id] })
}

export const apiBatchDeleteCommercialStrategy = (ids: number[]) => {
  return httpClient.post<Api.CommercialStrategy.Response.List>('/commercial-strategy/delete', { ids })
}

export const apiUpdateCommercialStrategyPriority = (params: { id: number, priority: number }) => {
  return httpClient.post<Api.CommercialStrategy.Response.List>('/commercial-strategy/set-priority', params)
}

export const apiCreateCommercialStrategy = (params: Partial<M.CommercialStrategy.Details>) => {
  const panel_config = {
    ...params.panel_config,
    hide_ad: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_ad'),
    hide_recharge: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_recharge'),
    hide_membership: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_membership'),
  }
  // delete params?.panel_config?.custom_panel_config
  return httpClient.post<ApiResponse<{ id: number }>>('/commercial-strategy/save', {
    ...params,
    panel_config,
    series_strategy: params.is_free === 0 && params.is_series_strategy === 1 ? params.series_strategy : null,
  })
}

//
export const apiEditCommercialStrategy = (params: Partial<M.CommercialStrategy.Details>) => {
  const panel_config = {
    ...params.panel_config,
    hide_ad: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_ad'),
    hide_recharge: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_recharge'),
    hide_membership: !params?.panel_config?.custom_panel_config?.find((v: string) => v === 'show_membership'),
  }
  // delete params?.panel_config?.custom_panel_config
  return httpClient.post<ApiResponse<{ id: number }>>('/commercial-strategy/save', {
    ...params,
    panel_config,
    series_strategy: params.is_free === 0 && params.is_series_strategy === 1 ? params.series_strategy : null,
  })
}

export const apiDeleteCommercialStrategyDetails = (params: { id: number }) => {
  return httpClient.post<ApiResponse<boolean>>('/commercial-strategy/delete', { ids: [params.id] })
}

export const apiBatchCreateCommercialStrategy = (params: { strategy_list: M.CommercialStrategy.Details[] }) => {
  return httpClient.post<ApiResponse<{ id: number }>>('/commercial-strategy/batch/save', {
    strategy_list: params.strategy_list.map(d => ({
      ...omit(d, ['id', 'status']),
      name: d.name + '副本',
    }))
    ,
  })
}

export const apiBatchEditCommercialStrategy = (params: { strategy_list: M.CommercialStrategy.Details[] }) => {
  return httpClient.post<ApiResponse<{ id: number }>>('/commercial-strategy/batch/save', {
    strategy_list: params.strategy_list.map(d => ({
      ...omit(d, ['status']),
    }))
    ,
  })
}

export const apiBatchGetCommercialStrategyDetails = async (params: { ids: number[] }) => {
  return httpClient.get<ApiResponse<{
    strategy_list: M.CommercialStrategy.Details[]
  }>>('/commercial-strategy/batch/detail', { ids: params.ids.join(',') })
}

export const apiGetCommercialStrategyDetails = async (params: { id: number }) => {
  const rs = await httpClient.get<ApiResponse<M.CommercialStrategy.Details>>('/commercial-strategy/detail', params)
  const custom_panel_config = []
  if (!rs.data?.panel_config?.hide_ad) {
    custom_panel_config.push('show_ad')
  }
  if (!rs.data?.panel_config?.hide_recharge) {
    custom_panel_config.push('show_recharge')
  }
  if (!rs.data?.panel_config?.hide_membership) {
    custom_panel_config.push('show_membership')
  }
  const data = {
    ...rs.data,
    is_series_strategy: Object.keys(rs.data?.series_strategy || {}).length > 0 ? 1 : 0,
    panel_config: {
      ...rs.data?.panel_config,
      custom_panel_config,
    },
  }
  return { ...rs, data }
}

/** //操作 1 上架 2 下架 3 冻结 4 解除冻结  */
export const apiUpdateCommercialStrategyState = (params: { id: number, operation: number }) => {
  return httpClient.post<unknown>('/commercial-strategy/status/update', params)
}
