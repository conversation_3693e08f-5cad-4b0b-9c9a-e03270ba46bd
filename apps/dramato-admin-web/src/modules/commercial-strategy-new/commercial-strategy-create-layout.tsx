import { createComponent, getQueriesWithParser, queryParsers } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { computed, FunctionalComponent, onUnmounted, ref, watch } from 'vue'
import { RouterLink, RouterView, useRoute } from 'vue-router'

import router from 'src/router'
import { useCommercialStrategyStore } from './commercial-strategy-store'
import { apiGetCommercialStrategyDetails } from './commercial-strategy-api'
type CommercialStrategyCreatePageOptions = {
  props: {}
}

const Item: FunctionalComponent<{ number: number }, { click: (e: Event) => void }> = (props, { slots, emit }) => (
  <MergeClass tag="x-item" baseClass="flex items-center gap-2" onClick={e => emit('click', e)}>
    <span class="flex size-8 items-center justify-center rounded-full bg-blue-500 text-white
      [.router-link-exact-active_&]:bg-red-600"
    >
      {props.number}
    </span>

    {slots.default?.()}
  </MergeClass>
)
export const CommercialStrategyCreateLayout = createComponent<CommercialStrategyCreatePageOptions>({
  props: {},
}, props => {
  const { formData, validateStepOne } = useCommercialStrategyStore()
  const isDataReady = ref<boolean>(false)

  watch(() => formData.value, () => {
    if (isDataReady.value) {
      void router.push({ path: router.currentRoute.value.path, query: { ...router.currentRoute.value.query, has_edit: 'true' } })
    }
  }, {
    deep: true,
  })

  const { id } = getQueriesWithParser({ id: queryParsers.string('') })
  const route = useRoute()

  watch(() => route.query.id,
    async v => {
      if (v) {
        isDataReady.value = false
        const res = await apiGetCommercialStrategyDetails({ id })
        if (!res.data) return
        formData.value = res.data
        setTimeout(() => {
          isDataReady.value = true
        })
      } else {
        isDataReady.value = true
        // formData.value.scene = route.path.includes('iaa') ? 'IAA' : 'IAP'
      }
    }, { immediate: true })

  const navList = computed(() => [
    ['目标条件', 'target'],
    ['广告方案', 'ads'],
    ['商品配置', 'product'],
  ])

  const onClickNav = (i: number) => (e: Event) => {
    if (i === 0) return
    if (validateStepOne()) {
      e.stopPropagation()
      e.preventDefault()
    }
    if (formData.value.special_type === 1) {
      e.stopPropagation()
      e.preventDefault()
    }
  }

  onUnmounted(() => {
    isDataReady.value = false
  })

  return () => (
    <x-strategy-group-create-page class="my-4 block">
      <header class="sticky left-0 top-top-bar z-up flex items-center justify-start gap-4 border-b bg-white p-4">
        <div class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/commercial-strategy">策略组</RouterLink></li>
            <li>{route.query.id ? '编辑策略组' : '新建策略组'}</li>
          </ul>
        </div>
      </header>
      <div class="relative flex flex-1 items-start justify-start divide-x overflow-hidden">
        <aside class="sticky left-0 top-0 flex min-h-40 w-64 shrink-0
          grow-0 items-start justify-center bg-white py-20"
        >
          <x-list class="block">
            {navList.value.map(([name, path], i) => (
              [
                i !== 0 && <hr class="ml-4 h-10 w-0 border-y-0 border-l-0 border-r border-solid border-gray-500" />,
                <RouterLink to={{ path, query: { ...route.query } }}>
                  <Item number={i + 1} onClick={onClickNav(i)}>
                    {name}
                  </Item>
                </RouterLink>]
            )).flat()}
          </x-list>
        </aside>
        <main class="grow-1  shrink-1 flex-1 overflow-hidden bg-white">
          <RouterView />
        </main>
      </div>

    </x-strategy-group-create-page>
  )
})

export default CommercialStrategyCreateLayout
