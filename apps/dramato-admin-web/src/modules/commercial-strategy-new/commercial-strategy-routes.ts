import { r, redirect } from '@skynet/shared'

export const commercialStrategyGroupRoutes = [
  r('commercial-strategy', '商业化策略组', null, [
    r('', '商业化策略组', () => import('src/modules/commercial-strategy-new/commercial-strategy-page.tsx')),
    r('create', '新建商业化策略组', () => import('src/modules/commercial-strategy-new/commercial-strategy-create-layout.tsx'), [
      redirect('', 'target'),
      r('target', '新建商业化策略组 - 目标条件', () => import('src/modules/commercial-strategy-new/commercial-strategy-create-target-page.tsx')),
      r('ads', '新建商业化策略组 - 广告方案', () => import('src/modules/commercial-strategy-new/commercial-strategy-create-ads-page')),
      r('product', '新建商业化策略组 - 商品配置', () => import('src/modules/commercial-strategy-new/commercial-strategy-create-product-page.tsx')),
    ]),
  ]),
]
