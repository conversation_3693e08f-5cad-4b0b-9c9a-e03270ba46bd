import { createComponent } from '@skynet/shared'
import { useEpisodeDataClimb } from './use-episode-data-climb'
import { onMounted } from 'vue'
import { useMenu } from '../menu/use-menu'
import { Button, DateTime, Icon, openDialog } from '@skynet/ui'

export const ClimbDataItemPage = createComponent({
  name: 'ClimbDataItem',
}, () => {
  const { form, resetForm, submit, currentPage, pageSize, total, tableData, startOrStopPitch, listLoading, climbingStatusOptions, query, btnLoading } = useEpisodeDataClimb()
  const { getUserRoles } = useMenu()

  const hasPermission = (permissionId: number) => {
    const roles = getUserRoles()
    return roles.includes(permissionId)
  }

  onMounted(() => {
    void submit()
  })
  return () => (
    <x-content-evaluate-page class="block">
      <div class="breadcrumbs px-4 pt-8 text-sm">
        <ul>
          <li>剧集爬坡管理</li>
        </ul>
      </div>
      <el-form label-width="80px" inline class="my-4 rounded-lg bg-white py-4 shadow">
        <el-form-item label="剧名/ID">
          <el-input v-model={form.value.series_key_or_title_list} />
        </el-form-item>
        <el-form-item label="爬坡状态">
          <el-select v-model={form.value.climbing_status} placeholder="请选择" style="width: 160px">
            <el-option label="全部" value={-1} />
            {
              climbingStatusOptions.map(item => (
                <el-option label={item.label} value={item.value} />
              ))
            }
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" onClick={() => query()}>搜索</el-button>
          <el-button onClick={resetForm}>重置</el-button>
        </el-form-item>
      </el-form>
      <x-table-container class="mt-4 block rounded-lg bg-white py-2 shadow">
        <el-table v-loading={listLoading.value} data={tableData.value}>
          <el-table-column label="短剧名称" prop="title" />
          <el-table-column label="状态" prop="state">
            {
              (scope: { row: M.ClimbDataItem }) => {
                return (
                  <span>
                    {
                      climbingStatusOptions.find(item => item.value === scope.row.climbing_status)?.label
                    }
                  </span>
                )
              }
            }
          </el-table-column>
          <el-table-column label="上线时间" prop="online_time">
            {
              (scope: { row: M.ClimbDataItem }) => {
                // scope.row.online_time格式20250321，转换为2025-03-21
                return <span>{scope.row.online_time ? <DateTime value={scope.row.online_time * 1000} /> : '--'}</span>
              }
            }
          </el-table-column>
          { hasPermission(34) ? (
            <el-table-column label="操作" prop="action" width="200" fixed="right" align="center">
              {
                (scope: { row: M.ClimbDataItem }) => {
                  return (
                    <div>
                      {
                        [0].includes(scope.row.climbing_status) ? (
                          <el-button type="text" onClick={() => {
                            const hideDeleteDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: () => (
                                <x-climb-data-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-climb-data-body>确认启动爬坡吗？</x-climb-data-body>
                                  <x-climb-data-footer class="flex w-full justify-end gap-x-[10px]">
                                    <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                                      void startOrStopPitch(scope.row, 1, hideDeleteDialog)
                                    }}
                                    >
                                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                      确定
                                    </Button>
                                  </x-climb-data-footer>
                                </x-climb-data-confirm-dialog>
                              ),
                            })
                          }}>启动爬坡</el-button>
                        ) : [1, 2, 6].includes(scope.row.climbing_status) ? (
                          <el-button type="text" disabled={scope.row.climbing_status === 1 || scope.row.climbing_status === 6} onClick={() => {
                            const hideDeleteDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: () => (
                                <x-climb-data-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-climb-data-body>确认终止爬坡吗？</x-climb-data-body>
                                  <x-climb-data-footer class="flex w-full justify-end gap-x-[10px]">
                                    <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                                      void startOrStopPitch(scope.row, 2, hideDeleteDialog)
                                    }}
                                    >
                                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                      确定
                                    </Button>
                                  </x-climb-data-footer>
                                </x-climb-data-confirm-dialog>
                              ),
                            })
                          }}>终止爬坡</el-button>
                        ) : [3, 4, 5].includes(scope.row.climbing_status) ? (
                          <el-button type="text" onClick={() => {
                            const hideDeleteDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: () => (
                                <x-climb-data-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-climb-data-body>确认重新爬坡吗？</x-climb-data-body>
                                  <x-climb-data-footer class="flex w-full justify-end gap-x-[10px]">
                                    <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || scope.row.climbing_status === 4} onClick={() => {
                                      void startOrStopPitch(scope.row, 3, hideDeleteDialog)
                                    }}
                                    >
                                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                      确定
                                    </Button>
                                  </x-climb-data-footer>
                                </x-climb-data-confirm-dialog>
                              ),
                            })
                          }}>重新爬坡</el-button>
                        ) : null
                      }
                    </div>
                  )
                }
              }
            </el-table-column>
          ) : null}
        </el-table>
        <el-pagination
          class="p-2"
          v-model:current-page={currentPage.value}
          v-model:page-size={pageSize.value}
          page-sizes={[10, 20, 50, 100]}
          layout="total, sizes, prev, pager, next, jumper"
          total={total.value}
          onSizeChange={() => submit()}
          onCurrentChange={() => submit()} />
      </x-table-container>
    </x-content-evaluate-page>
  )
})

export default ClimbDataItemPage
