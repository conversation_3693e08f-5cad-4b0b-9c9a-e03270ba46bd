/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiStartOrStopPitch, getClimbDataItemList } from './episode-data-climb-api'
import { ElMessage } from 'element-plus'

const climbingStatusOptions: {
  label: string
  value: number
}[] = [
  // //爬坡状态 0 待爬坡 1 准备爬坡 2 爬坡中 3 爬坡淘汰 4 取消爬坡 5 爬坡完成
  {
    label: '待爬坡',
    value: 0,
  },
  {
    label: '准备爬坡',
    value: 1,
  },
  {
    label: '爬坡中',
    value: 2,
  },
  {
    label: '爬坡淘汰',
    value: 3,
  },
  {
    label: '取消爬坡',
    value: 4,
  },
  {
    label: '爬坡完成',
    value: 5,
  },
  {
    label: '重新爬坡',
    value: 6,
  },
]

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<M.ClimbDataItem[]>([])
const loading = ref(true)
const listLoading = ref(false)
const btnLoading = ref(false)

export const useEpisodeDataClimb = () => {
  const evaluateDetail = ref<M.ClimbDataItemDetail>()
  const form = ref<M.ClimbDataParams>({
    series_key_or_title_list: '',
    climbing_status: -1,
  })
  const evaluateForm = ref<{
    rating: string
    reason: string
  }>({
    rating: '',
    reason: '',
  })

  const resetForm = () => {
    form.value = {
      series_key_or_title_list: '',
      climbing_status: -1,
    }
    currentPage.value = 1
    pageSize.value = 10
    total.value = 0
    tableData.value = []
    void submit()
  }

  const submit = async (page_index?: number) => {
    const params = {
      page_index: page_index || currentPage.value,
      page_size: pageSize.value,
      ...form.value,
    }
    listLoading.value = true
    const res = await getClimbDataItemList(params)
    if (res.data) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
    listLoading.value = false
  }

  const startOrStopPitch = async (row: M.ClimbDataItem, status: number, hideDeleteDialog: () => void) => {
    btnLoading.value = true
    try {
      const res = await apiStartOrStopPitch({
        series_key: row.series_key,
        ope_status: status,
      })
      if (res.code === 200) {
        ElMessage.success('操作成功')
        hideDeleteDialog()
        void submit()
      } else {
        ElMessage.error('操作失败')
      }
    } catch (error: any) {
      ElMessage.error(error.response.data.err_msg || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  const query = () => {
    currentPage.value = 1
    void submit()
  }

  return {
    form,
    resetForm,
    submit,
    currentPage,
    pageSize,
    total,
    evaluateForm,
    evaluateDetail,
    tableData,
    startOrStopPitch,
    loading,
    climbingStatusOptions,
    listLoading,
    query,
    btnLoading,
  }
}
