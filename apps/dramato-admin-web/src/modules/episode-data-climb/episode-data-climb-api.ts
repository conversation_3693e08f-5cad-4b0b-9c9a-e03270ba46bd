import { httpClient } from 'src/lib/http-client'
import { checkEpisodeTitleOrId } from 'src/modules/resource-publish/util'

export const getClimbDataItemList = (params: M.ClimbDataParams & {
  page_index: number
  page_size: number
}) => {
  return httpClient.post<ApiResponse<{
    list: M.ClimbDataItem[]
    total: number
  }>>('/resource_climbing/list', params, {
    transformRequestData: {
      series_key_or_title_list: [checkEpisodeTitleOrId],
    },
  })
}

export const apiStartOrStopPitch = (params: {
  series_key: string
  ope_status: number // 1 启动 2 中止
}) => {
  return httpClient.post<ApiResponse<null>>('/resource_climbing/edit/status', params)
}
