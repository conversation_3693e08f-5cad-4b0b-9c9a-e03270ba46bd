declare namespace M {
  interface ClimbDataParams {
    climbing_status?: number | undefined // 1 已入库未发布 2 已发布未投放 3 爬坡中 4 爬坡完成 5 投放中
    series_key_or_title_list?: string | string[]
  }
  interface ClimbDataItem {
    series_resource_id: number
    title: string
    online_time: number
    climbing_status: number
    series_key: string
  }
  interface ClimbDataItemDetail {
    rating: string
    reason: string
  }

  interface EChartsItem {
    episode_list: {
      serial_number: number
      play_uv: number
    }[]
    series_resource_id: number
    series_resource_title: string
    all_revenue: number
    series_key: string
    play_uv_sum: number
    listing_time: number
    unlocked_episodes: number
    statistics_start_time: number
    statistics_end_time: number
  }

  interface EChartsData {
    list: EChartsItem[]
  }
}
