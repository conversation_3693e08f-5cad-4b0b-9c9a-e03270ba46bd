/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { CreateForm, CreateTableOld, DateTime, TableColumnOld, transformDatetime, transformInteger, transformTimestamp } from '@skynet/ui'
import { set, transform } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { apiGetStrategyList } from './commercial-search-tool-api'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
import { useCommercialStrategyStore } from '../commercial-strategy-new/commercial-strategy-store'
import { requiredLabel } from 'src/lib/required-label'
import { apiListCommercialStrategy } from '../commercial-strategy-new/commercial-strategy-api'
import { useCommercialSearchStrategy } from './use-commercial-search-strategy'
type CommercialSearchStrategyOptions = {
  props: {}
}
export const CommercialSearchStrategy = createComponent<CommercialSearchStrategyOptions>({
  props: {},
}, props => {
  const { listParams, list } = useCommercialSearchStrategy()
  const SearchForm = CreateForm<M.CommercialSearchTool.StrategyQuery>()
  const Table = CreateTableOld<M.CommercialSearchTool.StrategyQueryResult>()
  const columns: TableColumnOld<M.CommercialSearchTool.StrategyQueryResult>[] = [
    ['序号', (_, idx) => idx + 1],
    ['日期（从新到旧）', row => <DateTime value={(row.event_time - 8 * 3600) * 1000} format="YYYY-MM-DD HH:mm:ss" />],
    ['UID', 'user_id'],
    ['命中场景', 'business_scene'],
    ['命中分层ID', 'hit_layer_id'],
    ['命中分层名称', 'hit_layer_name'],
    ['命中策略ID', 'strategy_id'],
    ['命中策略名称', 'strategy_name'],
    ['命中剧包ID', 'series_package_id'],
    ['命中剧包名称', 'series_package_name'],
  ]

  const loading = ref<boolean>(false)
  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  const seriesPackageStore = useSeriesPackageStore()

  const commercialStrategyStore = useCommercialStrategyStore()

  const search = async () => {
    loading.value = true
    const res = await apiGetStrategyList({
      ...listParams.value,
      dt: listParams.value.dt ? listParams.value.dt.replaceAll('-', '') : undefined,
    }).finally(() => {
      loading.value = false
    })
    list.value = res?.data?.list || []
  }

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])

    void apiListCommercialStrategy({ page_info: { offset: 0, size: 9999 } }).then(res => {
      commercialStrategyStore.list.value = res.data?.list || []
    })
  })

  const strategyOptions = ref<{
    value: number | undefined
    label: string
  }[]>([])

  const hitLayerOptions = ref<{
    value: number | undefined
    label: string
  }[]>([])

  const packageOptions = ref<{
    value: number | undefined
    label: string
  }[]>([])

  return () => (
    <NavFormTablePager>
      {{
        form: () => (
          <SearchForm
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = {}
              void search()
            }}
            onSubmit={() => search()} data={listParams.value} items={[
              [requiredLabel('UID（必填）'), 'user_id', { type: 'text' }, { transform: transformInteger }],
              [requiredLabel('日期筛选'), 'dt', { type: 'date' }, { }],
              {
                label: '命中场景',
                path: 'business_scene',
                input: {
                  type: 'select',
                  options: [{
                    label: 'iap',
                    value: 'iap',
                  }, {
                    label: 'ads',
                    value: 'ads',
                  }],
                },
              },
              [
                '策略ID',
                'strategy_ids',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyOptions.value,
                  'onUpdate:keyword': (keyword: string) => {
                    if (!keyword) {
                      strategyOptions.value = []
                      return
                    }

                    const options = commercialStrategyStore.list.value.map((n, index) => {
                      return { value: n.id, label: `${n.id}/${n.name}` }
                    })
                    strategyOptions.value = options.filter(option =>
                      keyword ? option.label.toString().toLowerCase().includes(keyword.toLowerCase()) : true)
                  },
                },
              ],
              [
                '分层ID',
                'hit_layer_ids',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: hitLayerOptions.value,
                  'onUpdate:keyword': (keyword: string) => {
                    if (!keyword) {
                      hitLayerOptions.value = []
                      return
                    }

                    const options = strategyLayerList.value.map((n, index) => {
                      return { value: n.id, label: `${n.id}/${n.name}` }
                    })
                    hitLayerOptions.value = options.filter(option =>
                      keyword ? option.label.toString().toLowerCase().includes(keyword.toLowerCase()) : true)
                  },
                },
              ],
              [
                '剧包ID',
                'series_package_ids',
                {
                  type: 'multi-select',
                  search: true,
                  options: packageOptions.value,
                  'onUpdate:keyword': (keyword: string) => {
                    if (!keyword) {
                      packageOptions.value = []
                      return
                    }

                    const options = seriesPackageStore.list.value.map((n, index) => {
                      return { value: n.id, label: `${n.id}/${n.package_name}` }
                    })
                    packageOptions.value = options.filter(option =>
                      keyword ? option.label.toString().toLowerCase().includes(keyword.toLowerCase()) : true)
                  },
                },
                {
                  class: mc('col-span-1'),
                },
              ],
            ] as any}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              命中策略&分层
            </div>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />
        ),
      }}
    </NavFormTablePager>
  )
})

export default CommercialSearchStrategy
