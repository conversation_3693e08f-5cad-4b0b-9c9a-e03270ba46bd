import { createComponent, mc } from '@skynet/shared'
import { computed, onMounted } from 'vue'
import { RouterLink, RouterView, useRoute } from 'vue-router'
import { useCommercialStrategyStore } from '../commercial-strategy-new/commercial-strategy-store'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
import { apiListCommercialStrategy } from '../commercial-strategy-new/commercial-strategy-api'
type CommercialSearchToolLayerOptions = {
  props: {}
}

export const CommercialSearchToolLayer = createComponent<CommercialSearchToolLayerOptions>({
  props: {},
}, props => {
  const route = useRoute()

  const navList = computed(() => [
    ['查询命中策略&分层', '/commercial-search-tool'],
    ['查询命中剧包', '/commercial-search-tool/package'],
    ['查询策略命中ID（抽样）', '/commercial-search-tool/id'],
  ])

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  const seriesPackageStore = useSeriesPackageStore()

  const commercialStrategyStore = useCommercialStrategyStore()

  onMounted(async () => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)

    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])

    commercialStrategyStore.page.value = 1
    commercialStrategyStore.pageSize.value = 9999
    const res = await apiListCommercialStrategy({ page_info: { offset: 0, size: 9999 } })
    commercialStrategyStore.list.value = res.data?.list || []
  })

  return () => (
    <x-commercial-search-tool-layer class="block">
      <div class="flex flex-col gap-y-3 py-5">
        <x-list role="tablist" class="flex flex-row tabs-boxed tabs w-1/2 bg-slate-200">
          {navList.value.map(([name, path], i) => (
            [
              <RouterLink to={{ path, query: { ...route.query } }}>
                <div class={mc('tab', '[.router-link-exact-active_&]:tab-active')}>
                  {name}
                </div>
              </RouterLink>]
          )).flat()}
        </x-list>
        <main class="grow-1  shrink-1 flex-1 w-full overflow-hidden bg-white">
          <RouterView />
        </main>
      </div>
    </x-commercial-search-tool-layer>
  )
})

export default CommercialSearchToolLayer
