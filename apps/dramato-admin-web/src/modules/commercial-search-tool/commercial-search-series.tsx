/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { CreateForm, CreateTableOld, DateTime, TableColumnOld, transformDatetime, transformInteger, transformStringArray, transformTimestamp } from '@skynet/ui'
import { set, transform } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { ref } from 'vue'
import { apiGetSeriesList } from './commercial-search-tool-api'
import { requiredLabel } from 'src/lib/required-label'
import { useCommercialSearchSeries } from './use-commercial-search-series'
type CommercialSearchSeriesOptions = {
  props: {}
}
export const CommercialSearchSeries = createComponent<CommercialSearchSeriesOptions>({
  props: {},
}, props => {
  const { listParams, list } = useCommercialSearchSeries()
  const SearchForm = CreateForm<M.CommercialSearchTool.PackageQuery>()
  const Table = CreateTableOld<M.CommercialSearchTool.PackageQueryResult>()
  const columns: TableColumnOld<M.CommercialSearchTool.PackageQueryResult>[] = [
    ['序号', (_, idx) => idx + 1],
    ['日期（从新到旧）', row => <DateTime value={(row.event_time - 8 * 3600) * 1000} format="YYYY-MM-DD HH:mm:ss" />],
    ['UID', 'user_id'],
    ['命中资源ID', 'resource_id'],
    ['命中短剧ID', 'series_key'],
    ['命中剧包ID', 'series_package_id'],
    ['命中剧包名称', 'series_package_name'],
  ]

  const loading = ref<boolean>(false)
  const search = async () => {
    loading.value = true
    const res = await apiGetSeriesList({
      ...listParams.value,
      dt: listParams.value.dt ? listParams.value.dt.replaceAll('-', '') : undefined,
    }).finally(() => {
      loading.value = false
    })
    list.value = res?.data?.list || []
  }

  return () => (
    <NavFormTablePager>
      {{
        form: () => (
          <SearchForm
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = {}
              void search()
            }}
            onSubmit={() => search()} data={listParams.value} items={[
              ['UID', 'user_id', { type: 'text' }, { transform: transformInteger }],
              ['资源ID', 'resource_id', { type: 'text' }, { transform: transformInteger }],
              [requiredLabel('日期筛选'), 'dt', { type: 'date' }],
              ['短剧ID', 'series_keys', { type: 'text' }, { transform: transformStringArray }],
            ] as any}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              命中剧包
            </div>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />
        ),
      }}
    </NavFormTablePager>
  )
})

export default CommercialSearchSeries
