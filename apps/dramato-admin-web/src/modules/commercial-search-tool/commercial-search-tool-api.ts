import { httpClient } from 'src/lib/http-client'

/** 根据uid查询命中策略 */
export const apiGetStrategyList = (data: M.CommercialSearchTool.StrategyQuery) =>
  httpClient.post<ApiResponse<{ list: M.CommercialSearchTool.StrategyQueryResult[] }>>('/commercial-stat/user/strategy', data)

/** 根据uid查询命中剧包 */
export const apiGetSeriesList = (data: M.CommercialSearchTool.PackageQuery) =>
  httpClient.post<ApiResponse<{ list: M.CommercialSearchTool.PackageQueryResult[] }>>('/commercial-stat/user/package', data)

/** 根据策略id查询命中uid */
export const apiGetIdList = (data: M.CommercialSearchTool.IdQuery) =>
  httpClient.post<ApiResponse<{ list: M.CommercialSearchTool.IdQueryResult[] }>>('/commercial-stat/strategy/user', data)
