/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { CreateForm, CreateTableOld, DateTime, TableColumnOld, transformDatetime, transformInteger, transformTimestamp } from '@skynet/ui'
import { set, transform } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { apiGetIdList, apiGetStrategyList } from './commercial-search-tool-api'
import { useCommercialStrategyStore } from '../commercial-strategy-new/commercial-strategy-store'
import { requiredLabel } from 'src/lib/required-label'
import { apiListCommercialStrategy } from '../commercial-strategy-new/commercial-strategy-api'
import { useCommercialSearchId } from './use-commercial-search-id'
type CommercialSearchIdOptions = {
  props: {}
}
export const CommercialSearchId = createComponent<CommercialSearchIdOptions>({
  props: {},
}, props => {
  const { listParams, list } = useCommercialSearchId()
  const SearchForm = CreateForm<M.CommercialSearchTool.IdQuery>()
  const Table = CreateTableOld<M.CommercialSearchTool.IdQueryResult>()
  const columns: TableColumnOld<M.CommercialSearchTool.IdQueryResult>[] = [
    ['序号', (_, idx) => idx + 1],
    ['日期（从新到旧）', row => <DateTime value={(row.event_time - 8 * 3600) * 1000} format="YYYY-MM-DD HH:mm:ss" />],
    ['UID', 'user_id'],
    ['命中策略ID', 'strategy_id'],
    ['命中策略名称', 'strategy_name'],
  ]

  const loading = ref<boolean>(false)
  const commercialStrategyStore = useCommercialStrategyStore()

  const search = async () => {
    loading.value = true
    const res = await apiGetIdList({
      ...listParams.value,
      dt: listParams.value.dt ? listParams.value.dt.replaceAll('-', '') : undefined,
    }).finally(() => {
      loading.value = false
    })
    list.value = res?.data?.list || []
  }

  onMounted(() => {
    void apiListCommercialStrategy({ page_info: { offset: 0, size: 9999 } }).then(res => {
      commercialStrategyStore.list.value = res.data?.list || []
    })
  })

  const strategyOptions = ref<{
    value: number | undefined
    label: string
  }[]>([])

  return () => (
    <NavFormTablePager>
      {{
        form: () => (
          <SearchForm
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = {}
              void search()
            }}
            onSubmit={() => search()} data={listParams.value} items={[
              [requiredLabel('日期筛选'), 'dt', { type: 'date' }, { }],
              [
                requiredLabel('策略ID'),
                'strategy_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyOptions.value,
                  maxlength: 1,
                  'onUpdate:keyword': (keyword: string) => {
                    if (!keyword) {
                      strategyOptions.value = []
                      return
                    }

                    const options = commercialStrategyStore.list.value.map((n, index) => {
                      return { value: n.id, label: `${n.id}/${n.name}` }
                    })
                    strategyOptions.value = options.filter(option =>
                      keyword ? option.label.toString().toLowerCase().includes(keyword.toLowerCase()) : true)
                  },
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
            ] as any}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              命中策略&分层
            </div>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns} class="tm-table-fix-last-column" />
        ),
      }}
    </NavFormTablePager>
  )
})

export default CommercialSearchId
