declare namespace M {
  declare namespace CommercialSearchTool {
    interface StrategyQuery {
      user_id?: number // 用户id 必须
      dt?: string // 日期，年月日 必须
      hit_layer_ids?: number[] // 分层id数组
      strategy_ids?: number[] // 策略id数组 必须
      series_package_ids?: number[] // 剧包id数组
      business_scene?: string // 命中场景 iap ads
    }

    interface StrategyQueryResult {
      event_time: number // 时间戳
      user_id: number // 用户id
      hit_layer_id: number // 分层id
      hit_layer_name: string // 分层名
      strategy_id: number // 策略id
      strategy_name: string // 策略名
      series_package_id: number // 剧包id
      series_package_name: string // 剧包名
      business_scene: string // 命中场景 iap ads
    }

    interface PackageQuery {
      user_id?: number // 用户id 必须
      dt?: string // 日期，年月日 必须
      series_keys?: string[] // 剧包id数组
    }

    interface PackageQueryResult {
      event_time: number // 时间戳
      user_id: number // 用户id
      resource_id: number // 资源id
      series_key: string // series_key 数组
      series_package_id: number // 剧包id
      series_package_name: string // 剧包名
    }

    interface IdQuery {
      dt?: string // 日期，年月日 必须
      strategy_id?: number // 日期，年月日 必须
    }

    interface IdQueryResult {
      event_time: number // 时间戳
      user_id: number // 用户id
      strategy_id: number // 策略id
      strategy_name: string // 策略名
    }
  }
}
