import { r, redirect } from '@skynet/shared'

export const commercialSearchToolRoutes = [
  r('commercial-search-tool', '商业化查询工具', () => import('src/modules/commercial-search-tool/commercial-search-tool-layer.tsx'), [
    r('', '查询命中策略&分层', () => import('src/modules/commercial-search-tool/commercial-search-strategy.tsx')),
    r('package', '查询命中剧包', () => import('src/modules/commercial-search-tool/commercial-search-series.tsx')),
    r('id', '查询策略命中ID（抽样）', () => import('src/modules/commercial-search-tool/commercial-search-id.tsx')),
  ]),
]
