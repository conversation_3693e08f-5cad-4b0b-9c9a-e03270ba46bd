declare namespace M {
  namespace JDRank {
    interface params {
      // pid?: number
      // type?: number
      // app_id?: number
      language_version?: string
      tab?: string
    }

    interface RankInfo {
      id?: number
      name?: string
      priority?: number
      type?: number
    }

    interface Rank extends CreateRank {
      updated: number
      create_user_name: string
      created: number
      update_user_name: string
    }

    interface ListResponse {
      list: ListItem[]
      total: number
    }

    interface SeriesInfo {
      name?: string
      series_id?: string
      hot?: string
    }

    interface CreateRank {
      id?: number
      language_version?: string
      title?: string
      sub_title?: string
      priority?: number
      tab?: string
      series_info?: SeriesInfo[]
      type?: number
      subtype?: number
      start?: number
      end?: number
      series_ids?: string[]
      rank_id?: number
    }
  }
}
