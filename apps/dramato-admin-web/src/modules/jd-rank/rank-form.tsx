/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useJDRank } from './use-rank'
import dayjs from 'dayjs'
import { ref } from 'vue'

type JDRankFormOptions = {
  props: {}
}

export const JDRankForm = createComponent<JDRankFormOptions>({
  props: {},
}, props => {
  const {
    currentJDRank,
    closeJDRankDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useJDRank()

  const Form = CreateForm<M.JDRank.CreateRank>()
  const formRules = z.object({
    priority: z.number().min(0, '请填写'),
    title: z.string().min(1, '请输入'),
    sub_title: z.string().min(1, '请输入'),
    start: z.number().min(1, '请选择'),
    end: z.number().min(1, '请选择'),
    series_ids: z.string().min(1, '请输入'),
    // tab: z.string().min(1, '请输入')
  })

  const { error, validateAll } = useValidator(currentJDRank, formRules)

  const inputRef = ref<HTMLInputElement>()

  const handleFileSelect = (event: any) => {
    const file = event.target.files[0]
    const reader = new FileReader()
    reader.onload = async (e: any) => {
      const data = new Uint8Array(e.target.result)
      const XLSX = await import('xlsx')
      const workbook = XLSX.read(data, { type: 'array' })
      // 获取第一个工作表的名称
      const firstSheetName = workbook.SheetNames[0]
      // 获取第一个工作表的数据
      const worksheet = workbook.Sheets[firstSheetName]
      // 将工作表的数据转换为 JSON 格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet)
      console.log(jsonData)
      currentJDRank.value.series_info = (jsonData as M.JDRank.SeriesInfo[]).map(i => ({ series_id: (i.series_id || '').replace(/\n/g, "").replace(/\s/g, "") }))
      currentJDRank.value.series_ids = currentJDRank.value.series_info.map(item => item.series_id).join(',')
    }
    reader.readAsArrayBuffer(file)
  }

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentJDRank.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('优先级'),
              'priority',
              {
                type: 'number',
                min: 1,
                placeholder: '请输入榜单顺序',
              },
              {
                transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('主标题'),
              'title',
              {
                type: 'textarea',
                placeholder: '请输入主标题',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('副标题'),
              'sub_title',
              {
                type: 'text',
                placeholder: '请输入副标题',
              },
              {
                class: mc('col-span-1'),
              },
            ],
            // [
            //   requiredLabel('tab'),
            //   'tab',
            //   {
            //     type: 'text',
            //     placeholder: '请输入tab',
            //   },
            //   {
            //     class: mc('col-span-1'),
            //   },
            // ],
            {
              label: requiredLabel('生效时间'),
              path: 'start',
              input: {
                type: 'custom',
                render: () => (
                  <div class="flex gap-x-2 items-center">
                    <div class="input input-bordered input-sm flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentJDRank.value.start ? dayjs(currentJDRank.value.start * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentJDRank.value.start = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                    <span>-</span>
                    <div class="input input-bordered input-sm flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentJDRank.value.end ? dayjs(currentJDRank.value.end * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentJDRank.value.end = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                  </div>
                ),
              },
              class: 'col-span-1',
            },
            [
              requiredLabel('剧集ID'),
              'series_ids',
              {
                type: 'textarea',
                placeholder: '请输入主标题',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
                // transform: [
                //   (raw?: unknown) => raw && Array.isArray(raw) ? raw.map(i => i.series_id).join(',') : '',
                //   (display: string): M.JDRank.SeriesInfo[] => display.split(',').map((item: string) => ({ name: item })),
                // ] as const,
              },
            ],
            () => (
              <x-import class="flex flex-row gap-x-2 items-end text-[14px] text-gray-600">
                <Button class="btn btn-sm btn-outline w-[80px]" onClick={() => inputRef.value && inputRef.value?.click()}>导入剧单</Button>
                <input ref={inputRef} type="file" accept=".xlsx" name="file" class="btn btn-sm btn-outline w-[80px] hidden" onChange={handleFileSelect} />
                支持批量导入：xl文件，series_id，实际展示顺序同Excel内
              </x-import>
            ),
          ]}
          data={currentJDRank.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeJDRankDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentJDRank.value?.id ? void onCreate() : void onEdit()

            try {
              const exclude: string[] = []

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
