import { createComponent } from '@skynet/shared'
import { showFailToast, showSuccessToast } from '@skynet/ui'
import { ElButton, ElDatePicker } from 'element-plus'
import { PreviewFile  } from 'src/modules/script-management/components/preview-file'
type Demo1PageOptions = {
  props: {}
}
export const Demo1Page = createComponent<Demo1PageOptions>({
  props: {},
}, props => {
  return () => (
    <div>
      <ElButton onClick={() => {
        showSuccessToast('成功！')
      }}>成功</ElButton>
      <ElButton onClick={() => {
        showFailToast('失败！')
      }}>失败</ElButton>
    </div>
  )
})

export default Demo1Page
