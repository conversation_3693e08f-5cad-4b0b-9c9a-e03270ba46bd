import { createComponent } from '@skynet/shared'
import { ElTabs, ElTabPane } from 'element-plus'
import { ref } from 'vue'
import { ForeignPage } from './foreign-page'
import { DomesticPage } from './domestic-page'

type DataEyePagesOptions = {
  props: {}
}
export const DataEyePages = createComponent<DataEyePagesOptions>({
  props: {},
}, props => {
  const activeName = ref('1')
  return () => (
    <x-data-eye-pages class="block">
      <ElTabs v-model={activeName.value}>
        <ElTabPane label="国内数据" name="1">
          <DomesticPage key="domestic" />
        </ElTabPane>
        <ElTabPane label="海外数据" name="2">
          <ForeignPage key="foreign" />
        </ElTabPane>
      </ElTabs>
    </x-data-eye-pages>
  )
})

export default DataEyePages
