/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Pager } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useDataEyeDrama } from './use-data-eye-drama-domestic'
import { ElTable, ElTableColumn } from 'element-plus'

type DomesticPageOptions = {
  props: {}
}
export const DomesticPage = createComponent<DomesticPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange } = useDataEyeDrama()
  const tableRef = ref<InstanceType<typeof ElTable>>()

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>国内dataeye数据</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['短剧名称', 'title', { type: 'text', placeholder: '请输入短剧名称' }],
            ]}
          />
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
          >
            {columns.value.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn
                    key={col.prop}
                    prop={col.prop}
                    fixed={col.fixed}
                    label={col.label}
                    minWidth={col.minWidth}
                    v-slots={{
                      default: ({ row }: { row: any }) => col.render({ row }),
                    }}
                  />
                )
              } else {
                return (
                  <ElTableColumn
                    key={col.prop}
                    prop={col.prop}
                    label={col.label}
                    minWidth={col.minWidth}
                    fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default DomesticPage
