declare namespace Api {
  namespace DataEyeDrama {
    interface ListReqParams {
      page_index: number
      page_size: number
      title: string // 可选

    }

    interface DramaInfoListResp {
      last_update_time?: string // 最近一次数据更新时间
      list: DataEyeDramaItem[] // 剧列表
      total: number // 总数
    }

    interface DataEyeDramaItem {
      drama_id: number // 剧id
      title: string // 剧名
      cover: string // 封面
      count: number // 总集数
      labels: string // 标签
      hot_total: number// 累计热力值
      deploy_time: number// 投放时间
      deploy_days: number // 投放天数
      deploy_theaters: number// 投放剧场数
      material_total: number// 素材总数
      plan_total: number// 计划总数
      rank_count: number// 上榜数
      manufacturer: string // 承制方
      hongguo_labels: string // 红果标签
      hongguo_hot_total: number // 红果热度
      adx_playlet_name: string
    }

  }
}
