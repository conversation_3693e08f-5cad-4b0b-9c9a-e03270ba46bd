/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, DateTime } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetDataEyeDramaList } from './data-eye-drama-api'
import UploadImagePreview from 'src/modules/resource-publish/components/upload-image-preview'

export const useDataEyeDrama = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    updateTime,
    incDate,
    getList
  }
}
const defaultParams = {
  page_index: 1,
  page_size: 20,
  title: ''
}
const Form = CreateForm<Api.DataEyeDrama.ListReqParams>()
const params = ref<Api.DataEyeDrama.ListReqParams>({...defaultParams})
const total = ref<number>(0)
const Table = CreateTableOld<Api.DataEyeDrama.DataEyeDramaItem>()
const list = ref<Api.DataEyeDrama.DataEyeDramaItem[]>([])
const loading = ref<boolean>(false)
const updateTime = ref('')
const incDate = ref<string>('')

const columns = computed(() => [
  { prop: 'drama_id', label: '短剧Id', minWidth: 100, fixed: true },
  { prop: 'title', label: '短剧名称', minWidth: 200, fixed: true },
  { prop: 'manufacturer', label: '承制方', minWidth: 140, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.manufacturer || '-'
  } },
  { prop: 'cover', label: '封面', minWidth: 150, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.cover
      ? <UploadImagePreview deleteAble={false} image={scope.row.cover} /> : '-'
  } },
  { prop: 'count', label: '总集数', minWidth: 100 },
  { prop: 'labels', label: '标签', minWidth: 130 },
  { prop: 'hongguo_labels', label: '红果标签', minWidth: 130, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.hongguo_labels || '-'
  } },
  { prop: 'hot_total', label: '累计热力值', minWidth: 100 },
  { prop: 'hongguo_hot_total', label: '红果热度', minWidth: 100, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.hongguo_hot_total || '-'
  } },
  { prop: 'deploy_time', label: '投放时间', minWidth: 140, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return <DateTime value={scope.row.deploy_time * 1000} />
  } },
  { prop: 'deploy_days', label: '投放天数', minWidth: 100 },
  { prop: 'deploy_theaters', label: '投放剧场数', minWidth: 100 },
  { prop: 'material_total', label: '素材总数', minWidth: 100 },
  { prop: 'plan_total', label: '计划总数', minWidth: 100 },
  { prop: 'rank_count', label: '上榜数', minWidth: 100 },
])

const onReset = () => {
  params.value = {...defaultParams}
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetDataEyeDramaList(params.value)
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}
