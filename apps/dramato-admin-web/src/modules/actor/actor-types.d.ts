declare namespace Api {
  namespace Actor {

    interface Param {
      name?: string
      resource_id_or_title_list?: string | (number | string)[]
      has_icon?: 0 | 1 | 2 | number // 1 无图 2 有图
      gender?: -1 | 0 | 1 | 2 | number // 性别 -1 全部 0 未知 1 男 2 女
      page: number
      page_size: number
    }

    namespace Response {
      type List = ApiResponse<{ actors: ActorItem[], total: number }>
    }
    interface Lang {
      name: string
      lang: string
    }
    interface Item {
      id?: number
      name?: string
      icon?: string
      description?: string
      resources?: number[]
      gender?: 0 | 1 | 2
      showAll?: boolean
    }

    interface MultipleLang {
      [record: string]: string
      lang_list: Lang[] | []
    }

    type ActorItem = Item & MultipleLang

    type TranslateItem = {
      name: string
      lang: string
      err_msg: string
    }
  }
}
