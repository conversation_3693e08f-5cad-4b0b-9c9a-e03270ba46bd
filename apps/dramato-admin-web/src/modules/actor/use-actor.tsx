/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, Icon, openDialog, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { useValidator } from '@skynet/shared'
import { cloneDeep, set } from 'lodash-es'
import { computed, ref } from 'vue'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { apiUpdateActor, apiCreateActor, apiTranslateActor } from './actor-api'
import { Uploader } from '../common/uploader/uploader'
import UploadImagePreview from '../resource-publish/components/upload-image-preview'
import { langKey, langValue } from '../resource/constant'

export const useActor = () => {
  return {
    showEditDialog,
  }
}

const btnLoading = ref(false)
const Form = CreateForm<Api.Actor.Item>()
const editRow = ref<Api.Actor.Item>({})
const translateLoading = ref(false)
const formChanged = ref(false)

const v: any = {}
langKey.map(langCode => {
  v[`x_${langCode}`] = z.string().min(1, '请输入演员名称')
})

const formRules = z.object({
  name: z.string().min(1, '请输入演员名称'),
  ...v,
})

const { error, validateAll } = useValidator(editRow, formRules)

const items = computed(() => {
  return [
    [
      requiredLabel('演员名称：'),
      'name',
      {
        type: 'text',
      },
      {
        class: 'col-span-1',
        hint: () => <div>修改演员名称后，请点击翻译</div>
      },
    ],
    [
      () => <div class="text-white">_</div>,
      'x',
      {
        type: 'custom',
        render: () => (
          <Button class="btn btn-primary w-[130px] btn-sm" disabled={translateLoading.value} onClick={async () => {
            try {
              if (!editRow.value.name) {
                showFailToast('请输入演员名称')
                return
              }
              translateLoading.value = true
              const res = await apiTranslateActor({
                name: editRow.value.name,
              })
              if (res.data?.err_msg) {
                showFailToast(res.data?.err_msg)
              } else {
                const result: any = {}
                langKey.map(key => {
                  result[`x_${key}`] = res.data?.list.find(item => item.lang === key)?.name
                })
                editRow.value = {
                  ...editRow.value,
                  ...result
                }
                formChanged.value = false
              }
            } catch (error: any) {
              showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
            } finally {
              translateLoading.value = false
            }
          }}>
            {translateLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            翻译
          </Button>
        ),
      },
      {
        class: 'col-span-1',
      },
    ],
    ...langKey.map(langCode => {
      return [
        requiredLabel(`演员名称【${langValue[langKey.findIndex(key => key === langCode)]}】：`),
        `x_${langCode}`,
        {
          type: 'text',
        },
        {
          class: 'col-span-1',
        },
      ]
    }),
    [
      '演员性别：',
      'gender',
      {
        type: 'select',
        options: [
          { label: '-', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
        autoInsertEmptyOption: false,
      },
      {
        transform: transformNumber,
        class: 'col-span-2',
      },
    ],
    [
      '演员图片',
      'icon',
      {
        type: 'custom',
        render: () => (
          <Uploader
            multiple={false}
            ossKeyType="resource"
            class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
            onUploadSuccess={d => {
              showSuccessToast('上传成功！')
              editRow.value.icon = d.temp_path as string
            }}
            isImage={false}
          >
            {editRow.value.icon
              ? (
                  <UploadImagePreview image={
                    editRow.value.icon?.indexOf('http') > -1 ? editRow.value.icon : `https://img.tianmai.cn/${editRow.value.icon}`
                  } onDelete={() => {
                    const hideDialog = openDialog({
                      title: '提示',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-image-body>是否删除图片</x-image-body>
                          <x-image-footer class="flex justify-end gap-x-[10px] w-full">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" onClick={() => {
                              editRow.value.icon = ''
                              hideDialog()
                            }}
                            >确定
                            </button>
                          </x-image-footer>
                        </x-image-confirm-dialog>
                      ),
                    })
                  }}
                  />
                )
              : <span class="size-full flex items-center justify-center">上传演员图片</span>}
          </Uploader>
        ),
      },
      {
        class: 'col-span-2',
      },
    ],
    [
      '演员描述：',
      'description',
      {
        type: 'textarea',
      },
      {
        class: 'col-span-2',
      },
    ],
    [
      '参演资源（选填）',
      'resources',
      {
        type: 'textarea',
      },
      {
        class: 'col-span-2',
      },
    ],
  ]
}) as any

const showEditDialog = (row?: Api.Actor.Item, cb?: () => void) => {
  editRow.value = cloneDeep(row) || {}
  const hideDialog = openDialog({
    title: '演员详情',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-label-confirm-dialog class="flex flex-col gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-2"
          data={editRow.value}
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'name') {
              formChanged.value = true
            }
            set(editRow.value || {}, path, value)
          }}
          items={items.value}
        />
        <x-label-footer class="flex justify-end gap-x-[10px] w-full">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
            if (formChanged.value) {
              showFailToast('演员名称已修改，请点击翻译')
              return
            }
            if (!validateAll()) return
            try {
              btnLoading.value = true
              if (editRow.value.id) {
                await apiUpdateActor({ ...editRow.value, actor_id: editRow.value.id } as Api.Actor.Item)
              } else {
                await apiCreateActor({ ...editRow.value } as Api.Actor.Item)
              }
              cb && cb()
              showSuccessToast('操作成功')
              hideDialog()
            } catch (error: any) {
              showFailToast({
                duration: 5000,
                message: error.response.data.err_msg || '操作失败',
              })
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            保存
          </button>
        </x-label-footer>
      </x-label-confirm-dialog>
    ),
  })
}
