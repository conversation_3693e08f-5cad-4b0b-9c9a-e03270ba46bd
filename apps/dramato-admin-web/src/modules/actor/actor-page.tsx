/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, exportAsCsv } from '@skynet/shared'
import { ref, onMounted } from 'vue'
import { CreateTableOld, CreateForm, TableColumnOld, Button, Pager, openDialog, showSuccessToast, showFailToast, Icon, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiGetActorList, apiDeleteActor } from './actor-api'
import { set, trim } from 'lodash-es'
import { useActor } from './use-actor'
import { langKey, langValue } from '../resource/constant'
type ActorPageOptions = {
  props: {}
}
export const ActorPage = createComponent<ActorPageOptions>({
  props: {},
}, props => {
  const defaultParam = {
    page: 1,
    page_size: 20,
    resource_id_or_title_list: '',
    gender: -1,
    has_icon: 0
  }
  const loading = ref(false)
  const form = ref<Api.Actor.Param>({ ...defaultParam })
  const Table = CreateTableOld<Api.Actor.ActorItem>()
  const QueryForm = CreateForm<Api.Actor.Param>()
  const list = ref<Api.Actor.ActorItem[]>([])
  const total = ref(0)
  const { showEditDialog } = useActor()
  const downloadLoading = ref(false)

  const onEdit = (row: Api.Actor.Item) => {
    showEditDialog(row, onQuery)
  }

  const onDelete = (row: Api.Actor.Item) => {
    const btnLoading = ref(false)
    const hideDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-actor-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-actor-body>确认删除吗？</x-actor-body>
          <x-actor-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
              btnLoading.value = true
              void apiDeleteActor({
                actor_id: row.id!,
              }).then(() => {
                showSuccessToast('操作成功')
                void onQuery()
                hideDeleteDialog()
              }).catch((error: any) => {
                showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
              }).finally(() => {
                btnLoading.value = false
              })
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-actor-footer>
        </x-actor-confirm-dialog>
      ),
    })
  }

  const onCreateActor = () => {
    showEditDialog({}, onQuery)
  }

  const columns: TableColumnOld<Api.Actor.ActorItem>[] = [
    ['演员名称', 'name', { class: 'w-[140px]' }],
    ['演员名-多语言', row => {
      const langs = ['en', 'zh-CN']
      const len = row.showAll ? langKey.length : 2
      return (
        <x-actors key={`device_${row.id}_${row.showAll ? 1 : 2}`} class="flex flex-col gap-2">
          {(row.showAll ? row.lang_list : row.lang_list.filter(item => langs.includes(item.lang))).map((i, index) => (
            <x-item class="flex flex-col gap-2">
              <span>
                {i.name || '-'}【{langValue[langKey.findIndex(key => key === i.lang)]}】
                { len - 1 === index ? (
                  <Button
                    class="btn btn-xs btn-link inline-block"
                    onClick={() => {
                      row.showAll = !row.showAll
                    }}
                  >
                    { row.showAll ? '收起' : '展开' }
                  </Button>
                ) : null }
              </span>
            </x-item>
          ))}
        </x-actors>
      )
    }, { class: 'w-[140px]' }],
    ['演员图片', row => row.icon ? <img src={row.icon} /> : '-', { class: 'w-[130px]' }],
    ['演员性别', row => {
      return ['未知', '男', '女'][row.gender || 0] || '-'
    }, { class: 'w-[100px]' }],
    ['简介', 'description', { class: 'w-[300px]' }],
    ['参演资源', row => {
      return row.resources?.map(id => {
        return <a class="btn btn-link btn-xs block" target="_blank" href={`/resource-publish/detail/${id}`}>{id}</a>
      })
    }, { class: 'text-center w-[140px]' }],
    [<span>操作</span>,
      row => (
        <div class="flex items-center justify-center">
          <Button class="btn btn-link btn-xs" onClick={() => onEdit(row)}>编辑</Button>
          <Button class="btn btn-link btn-xs" onClick={() => onDelete(row)}>删除</Button>
        </div>
      ),
      { class: 'w-[130px] text-center' },
    ],
  ]

  const getList = async () => {
    loading.value = true
    try {
      const { data } = await apiGetActorList(form.value)
      list.value = data?.actors || []
      total.value = data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const getExportList = async () => {
    const { data } = await apiGetActorList({
      ...form.value,
      page_size: 1000000,
      page: 1,
    })
    return data?.actors || []
  }

  const onDownloadExcel = async () => {
    downloadLoading.value = true
    try {
      const _list = await getExportList()
      const tableHeader = ['演员名称', '演员性别', '简介', '参演资源id']
      const tableData = _list.map(item => {
        return [
          trim(item.name || ''),
          item.gender ? ['-', '男', '女'][item.gender] : '-',
          trim(item.description?.replaceAll(/\s+/g, '\xa0') || '').split('\n').map(line => `"${line}"`).join('\n'),
          trim(`"${item.resources?.map(id => id).join(';')};"` || ''),
        ]
      }) || []

      exportAsCsv([tableHeader, ...tableData], '演员信息.csv')
    } catch (error) {
      showFailToast('导出失败')
    } finally {
      downloadLoading.value = false
    }
  }

  const onQuery = async () => {
    form.value.page = 1
    await getList()
  }

  const onReset = async () => {
    form.value = { ...defaultParam }
    await onQuery()
  }

  const onPageChange = async (n: number) => {
    form.value.page = n
    await getList()
  }

  const onPageSizeChange = async (n: number) => {
    form.value.page = 1
    form.value.page_size = n
    await onQuery()
  }

  onMounted(async () => {
    await getList()
  })

  return () => (

    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>演员管理</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['演员名称', 'name', { type: 'text' }],
              ['资源ID/名称', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源ID/名称' }],
              ['性别', 'gender', { type: 'select', options: [{
                value: -1,
                label: '全部'
              }, {
                value: 0,
                label: '未知'
              }, {
                value: 1,
                label: '男'
              }, {
                value: 2,
                label: '女'
              }], autoInsertEmptyOption: false, placeholder: '请选择演员性别' }, {
                transform: transformNumber
              }],
              ['是否有图', 'has_icon', { type: 'select', options: [{
                value: 0,
                label: '全部'
              },{
                value: 1,
                label: '无图'
              }, {
                value: 2,
                label: '有图'
              }], autoInsertEmptyOption: false }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-end space-x-4">
            <Button class="btn btn-primary btn-sm" onClick={onCreateActor}>新增演员</Button>
            {/* <Button class="btn btn-primary btn-sm" onClick={onDownloadExcel}>导出excel</Button> */}
          </div>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={form.value.page} v-model:size={form.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ActorPage
