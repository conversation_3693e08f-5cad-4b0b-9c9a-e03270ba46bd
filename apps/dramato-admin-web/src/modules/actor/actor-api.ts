import { httpClient } from 'src/lib/http-client'
import { trim } from 'lodash-es'
import { langKey } from '../resource/constant'

const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

const formatResourceId = (value: string) => {
  if (!value) return []
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t', '\n'])
  // 如果数组中list每个元素都为数字，且大于 资源id大于等于9900，如果有一个符合，则 返回符合规则的，如果都不符合规则，则返回
  const list = trimSplit(value)
  const isResourceIds = list.filter(item => {
    return /^\d+$/.test(item) && +item >= 9900
  })
  if (isResourceIds.length > 0) {
    return isResourceIds.map(item => +item)
  } else {
    return [trim(value)]
  }
}

const formatActor = (lang_list: Api.Actor.Lang[], data: Api.Actor.ActorItem) => {
  return langKey.map(key => {
    return {
      lang: key,
      name: data[`x_${key}`],
    }
  })
}

const setDefaultGender = (gender?: number) => {
  return !gender ? 0 : gender
}

export const apiGetActorList = (data: Api.Actor.Param) =>
  httpClient.post<Api.Actor.Response.List>('/actor/list', data, {
    transformRequestData: {
      resource_id_or_title_list: [formatResourceId],
    },
    transformResponseData: {
      'data.actors': [(actors: Api.Actor.ActorItem[]) => {
        return actors.map(actor => {
          actor.lang_list = langKey.map(k => {
            actor[`x_${k}`] = actor.lang_list?.find(l => l.lang === k)?.name || ''
            return {
              lang: k,
              name: actor.lang_list?.find(l => l.lang === k)?.name || '',
            }
          })
          return actor
        })
      }],
    },
  })

export const apiUpdateActor = (data: Api.Actor.ActorItem | Api.Actor.Item) =>
  httpClient.post<ApiResponse<null>>('/actor/edit', data, {
    transformRequestData: {
      resources: [formatResourceId],
      lang_list: [formatActor.bind(data)],
      gender: [setDefaultGender],
    },
  })

export const apiCreateActor = (data: Api.Actor.Item) =>
  httpClient.post<ApiResponse<null>>('/actor/add', data, {
    transformRequestData: {
      resources: [formatResourceId],
      lang_list: [formatActor.bind(data)],
      gender: [setDefaultGender],
    },
  })

export const apiDeleteActor = (data: {
  actor_id: number
}) =>
  httpClient.post<ApiResponse<null>>('/actor/delete', data)

export const apiTranslateActor = (data: {
  name: string
}) =>
  httpClient.post<ApiResponse<{
    list: Api.Actor.TranslateItem[]
    err_msg: string
  }>>('/actor/translate', data)
