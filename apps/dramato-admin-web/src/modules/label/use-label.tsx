/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, openDialog, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { useValidator } from '@skynet/shared'
import { cloneDeep, set } from 'lodash-es'
import { ref } from 'vue'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { langKey, langValue } from 'src/modules/resource/constant'
import { FormItem } from '@skynet/ui/form/form-types'
import { apiCreateLabel } from './label-api'

export const useLabel = () => {
  return {
    Form,
    showEditDialog,
    levels,
    contentTypes,
    loading,
    list,
    total
  }
}

const loading = ref(false)
const list = ref<Api.Label.Item[]>()
const total = ref(0)

const Form = CreateForm<Api.Label.Item>()
const editRow = ref<Api.Label.Item>({})
const formRules = z.object({
  label_type: z.number().min(1, '请选择标签级别'),
  content_type: z.number().min(1, '请选择内容维度'),
  ...Object.fromEntries(
    langKey.map(key => [
      `${key}_content`,
      z.string().min(1, `请输入${key}标签内容`)
    ])
  )
})

const levels = [{
  value: 1,
  label: '一级标签',
}, {
  value: 2,
  label: '二级标签',
}, {
  value: 3,
  label: '三级标签',
}]

const contentTypes = [{
  value: 1,
  label: '受众',
  level: 1,
}, {
  value: 2,
  label: '类型',
  level: 2,
}, {
  value: 3,
  label: '情节',
  level: 3,
}, {
  value: 4,
  label: '角色',
  level: 3,
}, {
  value: 5,
  label: '背景',
  level: 3,
}]

const { error, validateAll } = useValidator(editRow, formRules)

const rows = langKey.map((key, index) => {
  return [
    requiredLabel(`${langValue[index]}`),
    `${key}_content`,
    {
      type: 'text',
      placeholder: '请输入标签',
    },
  ]
}) as FormItem[]

const formatToLangContent = (data: Api.Label.Item) => {
  const langContent = langKey.map(key => ({
    language_code: key,
    content: data[`${key}_content`] as string,
    meaning: key === 'cn' ? data.meaning : ''
  }))

  const params: Api.Label.Item = {
    label_type: data.label_type,
    content_type: data.content_type,
    lang_content: langContent
  }
  if (data.label_id) {
    params.label_id = data.label_id
  }
  return params
}

const showEditDialog = (row?: Api.Label.Item, cb?: () => void) => {
  editRow.value = cloneDeep(row) || {}
  const hideDialog = openDialog({
    title: '提示',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-label-confirm-dialog class="flex flex-col gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          data={editRow.value}
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'label_type') {
              if (!value) {
                set(editRow.value || {}, 'content_type', '')
              } else if (+value <= 2) {
                set(editRow.value || {}, 'content_type', value)
              } else if (+value > 2) {
                set(editRow.value || {}, 'content_type', 3)
              }
            }
            set(editRow.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('标签级别'),
              'label_type',
              {
                type: 'select',
                options: levels,
                placeholder: '请选择标签级别',
              },
              {
                transform: transformNumber
              }
            ],
            [
              requiredLabel('内容维度'),
              'content_type',
              {
                type: 'select',
                options: contentTypes.filter(contentType => contentType.level === editRow.value.label_type),
                placeholder: '请选择内容维度',
              },
              {
                transform: transformNumber
              }
            ],
            [
              '标签说明',
              'meaning',
              {
                type: 'text',
                placeholder: '请输入标签说明',
              },
            ],
            ...rows,
          ]}
        />
        <x-label-footer class="flex justify-end gap-x-[10px] w-full">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            if (!validateAll()) return
            try {
              await apiCreateLabel(formatToLangContent(editRow.value))
              cb && cb()
              showSuccessToast('操作成功')
            } catch (error: any) {
              showFailToast(error.response.data.message || '操作失败')
            } finally {
              hideDialog()
            }
          }}
          >确定
          </button>
        </x-label-footer>
      </x-label-confirm-dialog>
    ),
  })
}
