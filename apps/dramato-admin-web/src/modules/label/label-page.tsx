/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, transformNumber, Button, showSuccessToast, showFailToast, openDialog } from '@skynet/ui'
import { ref, onMounted, computed } from 'vue'
import { set } from 'lodash-es'
import { apiGetLabelList, apiDelLabel } from './label-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { useLabel } from './use-label'

type LabelPageOptions = {
  props: {}
}

export const LabelPage = createComponent<LabelPageOptions>({
  props: {},
}, props => {
  const { showEditDialog, levels, contentTypes, loading, list, total } = useLabel()
  const defaultParam = {
    content: '',
    page_index: 1,
    page_size: 20,
  }

  const form = ref<Api.Label.Param>({ ...defaultParam })
  const Table = CreateTableOld<Api.Label.Item>()
  const QueryForm = CreateForm<Api.Label.Param>()

  const onCreateLabel = () => {
    showEditDialog(undefined, onQuery)
  }

  const onEditLabel = (row: Api.Label.Item) => {
    showEditDialog(row, onQuery)
  }

  const onDelete = (id: number) => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-label-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-label-body>是否删除</x-label-body>
          <x-label-footer class="flex justify-end gap-x-[10px] w-full">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                await apiDelLabel({ label_id: id, status: 2 })
                void onQuery()
                showSuccessToast('操作成功')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-label-footer>
        </x-label-confirm-dialog>
      ),
    })
  }

  const columns = computed<TableColumnOld<Api.Label.Item>[]>(() => {
    return [
      [() => (
        <div class="flex space-x-4">
          <div>标签级别</div>
          <div>简体中文</div>
        </div>
      ), row => (
        <div class="flex space-x-4">
          <div class="shrink-0">{levels.find(level => level.value === row.label_type)?.label}</div>
          <div>{row['zh-CN_content']}</div>
        </div>
      ), { class: 'w-[220px]' }],
      ...langKey.filter(key => key !== 'zh-CN').map((key, index) => {
        return [`${langValue[index + 1]}`, `${key}_content`, { class: 'w-[140px]' }]
      }),
      ['含义', 'meaning', { class: 'w-[190px]' }],
      ['关联剧集数', 'resource_count', { class: 'w-[100px]' }],
      [<span class="px-3">操作</span>, row => (
        <div class="flex gap-x-2">
          <Button class="btn btn-link btn-xs" onClick={() => onEditLabel(row)}>编辑</Button>
          <Button class="btn btn-link btn-xs" onClick={() => onDelete(row.label_id || 0)}>删除</Button>
        </div>
      ), {
        class: 'w-[120px]',
      },
      ],
    ]
  })

  const getList = async () => {
    loading.value = true
    try {
      const { data } = await apiGetLabelList(form.value)
      list.value = data?.list
      total.value = data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = async () => {
    form.value.page_index = 1
    await getList()
  }

  const onReset = async () => {
    form.value = { ...defaultParam }
    await onQuery()
  }

  const onPageChange = async (n: number) => {
    form.value.page_index = n
    await getList()
  }

  const onPageSizeChange = async (n: number) => {
    form.value.page_index = 1
    form.value.page_size = n
    await onQuery()
  }

  onMounted(async () => {
    await getList()
  })

  return () => (

    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>标签管理</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['标签级别', 'label_type', { type: 'select', options: levels }, { transform: transformNumber }],
              ['内容维度', 'content_type', { type: 'select', options: contentTypes }, { transform: transformNumber }],
              ['标签名称', 'content', { type: 'text' }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            标签列表
            <Button class="btn-primary btn btn-sm" onClick={onCreateLabel}>新增</Button>
          </div>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns.value}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={form.value.page_index} v-model:size={form.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default LabelPage
