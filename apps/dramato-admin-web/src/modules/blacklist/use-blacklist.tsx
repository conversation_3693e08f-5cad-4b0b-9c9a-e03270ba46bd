/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, CreateTableOld, Icon, openDialog, showAlert, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetBlacklist, apiUpdateUserStatus, apiUpdateUser, apiCreateUser } from './blacklist-api'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { useValidator } from '@skynet/shared'
import { z } from 'zod'
import { countryCodes } from './constant'

export const useBlacklist = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    getList,
    showCreateBlock,
  }
}
const defaultParams = {
  page_index: 1,
  page_size: 20,
  status: -1,
} as Api.BlackList.ListReqParams

const Form = CreateForm<Api.BlackList.ListReqParams>()
const params = ref<Api.BlackList.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<Api.BlackList.BlackListItem>()
const list = ref<Api.BlackList.BlackListItem[]>([])
const loading = ref<boolean>(false)
const btnLoading = ref<boolean>(false)

const columns = computed(() => [
  { prop: 'uid', label: '用户ID', minWidth: 100, fixed: true },
  { prop: 'location', label: '地域', minWidth: 130, render: (scope: { row: Api.BlackList.BlackListItem }) => {
    return <span>{countryCodes.find(m => m.value === scope.row.location)?.label || '-' }</span>
  } },
  { prop: 'status', label: '状态', minWidth: 100, render: (scope: { row: Api.BlackList.BlackListItem }) => {
    return <div class={scope.row.status === 1 ? 'badge-default badge' : 'badge badge-primary'}>{scope.row.status === 1 ? '解封' : '封禁'}</div>
  } },
  { prop: 'vip_type', label: '购买VIP类型', minWidth: 120 },
  { prop: 'total_pay_amount', label: '累计订阅金额', minWidth: 120 },
  { prop: 'total_abnormal_days', label: '累计异常天数', minWidth: 120 },
  { prop: 'block_reason', label: '封禁原因', minWidth: 100, render: (scope: { row: Api.BlackList.BlackListItem }) => {
    return <span>{scope.row.block_reason === 1 ? '命中规则' : '手动封禁'}</span>
  } },
  { prop: 'hit_rule', align: 'center', label: '异常行为详情', minWidth: 120, render: (scope: { row: Api.BlackList.BlackListItem }) => {
    return (
      <Button class="btn btn-link btn-xs" onClick={() => {
        const hideDeleteDialog = openDialog({
          title: '异常行为详情',
          mainClass: 'pb-0 px-5',
          body: (
            <x-blacklist-detail-confirm-dialog class="flex flex-col gap-y-4">
              <pre class="whitespace-pre-wrap break-words">{scope.row.abnormal_detail}</pre>
              <x-blacklist-detail-footer class="flex w-full justify-end gap-x-4">
                <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
              </x-blacklist-detail-footer>
            </x-blacklist-detail-confirm-dialog>
          ),
        })
      }}>查看</Button>
    )
  } },

  { label: '操作', width: 160, align: 'center', fixed: 'right', render: (scope: { row: Api.BlackList.BlackListItem }) => {
    return (
      <>
        <Button class="btn btn-link btn-xs" onClick={() => {
          const hideDeleteDialog = openDialog({
            title: scope.row.status === 0 ? '解封' : '封禁',
            body: () => (
              <x-blacklist-unblock-confirm-dialog class="flex flex-col gap-y-4">
                <x-blacklist-unblock-body>{scope.row.status === 0 ? '解封' : '封禁'}【{scope.row.uid}】吗？</x-blacklist-unblock-body>
                <x-blacklist-unblock-footer class="mt-4 flex w-full justify-end gap-x-4">
                  <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                  <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                    try {
                      btnLoading.value = true
                      const res = await apiUpdateUserStatus({
                        id: scope.row.id!,
                        status: scope.row.status === 0 ? 1 : 0,
                      })
                      if (res.code === 200) {
                        showAlert('操作成功')
                        void getList()
                        hideDeleteDialog()
                      } else {
                        showFailToast(res.message || '操作失败')
                      }
                    } catch (error: any) {
                      showFailToast(error.message || '操作失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确定
                  </button>
                </x-blacklist-unblock-footer>
              </x-blacklist-unblock-confirm-dialog>
            ),
          })
        }}>{ scope.row.status === 0 ? '解封' : '封禁' }</Button>

      </>
    )
  } },
])

const showCreateBlock = () => {
  const btnLoading = ref(false)
  const form = ref<Api.BlackList.CreateUserReqParams>({})
  const CFrom = CreateForm<Api.BlackList.CreateUserReqParams>()
  const formRules = z.object({
    uid: z.string().min(1, '请输入用户ID').refine(e => {
      if (Number(e) !== Number(e)) return false
      if (Number(e) <= 0) return false
      return true
    }, {
      message: '请正确输入用户ID',
    }),
    abnormal_detail: z.string().min(1, '请输入异常行为详情'),
  })
  const { error, validateAll } = useValidator(form, formRules)

  const hideDeleteDialog = openDialog({
    title: '新增封禁用户',
    body: () => (
      <x-blacklist-create-confirm-dialog class="flex flex-col gap-y-2">
        <x-blacklist-create-body>
          <CFrom
            class="w-full flex flex-col gap-y-2"
            hasAction={false}
            data={form.value}
            error={error.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              [() => requiredLabel('用户ID'), 'uid', { type: 'text', placeholder: '请输入用户ID' }],
              ['地域', 'location', { type: 'multi-select', search: true, options: countryCodes.map(n => ({ value: n.value, label: `${n.label} - ${n.value}` })), popoverWrapperClass: 'z-popover-in-dialog', maxlength: 1 }],
              [() => requiredLabel('异常行为详情'), 'abnormal_detail', { type: 'textarea', placeholder: '请输入异常行为详情' }],
            ]}
          />
          <div class="flex justify-end space-x-2">
            <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (!validateAll()) return
              try {
                btnLoading.value = true
                const res = await apiCreateUser({
                  ...form.value,
                  block_type: form.value.block_type || 0
                })
                if (res.code === 200) {
                  showSuccessToast('操作成功')
                  void getList()
                  hideDeleteDialog()
                }
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || error.message || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}>
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </Button>
          </div>
        </x-blacklist-create-body>
      </x-blacklist-create-confirm-dialog>
    ),
  })
}

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetBlacklist(params.value)
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}
