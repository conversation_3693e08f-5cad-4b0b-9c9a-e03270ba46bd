import { httpClient } from 'src/lib/http-client'

export const apiGetBlacklist = (data: Api.BlackList.ListReqParams) =>
  httpClient.post<ApiResponse<Api.BlackList.BlackListResp>>('/block/user_list', data, {
    transformRequestData: {
      uid: [x => +x],
    },
  })

export const apiUpdateUser = (data: Api.BlackList.updateUserBlockTypeParams) =>
  httpClient.post<ApiResponse<Api.BlackList.BlackListResp>>('/block/user_update', data)

export const apiCreateUser = (data: Api.BlackList.CreateUserReqParams) =>
  httpClient.post<ApiResponse<null>>('/block/user_insert', data, {
    transformRequestData: {
      uid: [x => +x],
      location: [x => x.join(',')],
    },
  })

export const apiUpdateUserStatus = (data: Api.BlackList.UpdateUserReqParams) =>
  httpClient.post<ApiResponse<null>>('/block/user_block', data)
