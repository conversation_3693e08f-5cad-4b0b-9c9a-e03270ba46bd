declare namespace Api {
  namespace BlackList {
    interface ListReqParams {
      uid?: number // uid
      status: -1 | 0 | 1 // 状态 -1：全部 0：封禁 1：解封
      page_index?: number
      page_size?: number // 每页大小
    }

    interface BlackListResp {
      list: BlackListItem[] // 剧列表
      total: number // 总数
    }

    interface BlackListItem {
      id?: number // 主键
      uid?: number // uid
      location?: string // 地域
      vip_type?: number // 购买VIP类型 ??
      total_pay_amount?: number // 累计订阅金额
      total_abnormal_days?: number // 累计异常天数
      block_reason?: 1 | 2 // 屏蔽原因 1：命中规则 2：手动屏蔽
      hit_rule?: 1 | 2 // 命中规则，1：规则1 2：规则2
      status?: 0 | 1 // 状态 0：封禁 1：解封
      abnormal_detail?: string // 异常行为详情
    }

    interface UpdateUserReqParams {
      id: number // 主键
      status: 0 | 1 // 行为 0：封禁 1：解除封禁
    }

    interface updateUserBlockTypeParams {
      id: number
    }

    interface CreateUserReqParams {
      uid?: number // uid，必填
      location?: string // 地域，选填
      abnormal_detail?: string // 异常行为详情 必填
      block_type?: number
    }
  }
}
