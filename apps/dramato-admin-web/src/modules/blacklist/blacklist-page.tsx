/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { <PERSON><PERSON>, Pager, transformNumber } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useBlacklist } from './use-blacklist'
import { ElTable, ElTableColumn } from 'element-plus'

type CompetitionContentPageOptions = {
  props: {}
}
export const CompetitionContentPage = createComponent<CompetitionContentPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange, showCreateBlock } = useBlacklist()
  const tableRef = ref<InstanceType<typeof ElTable>>()

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>异常用户管理</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['用户ID', 'uid', { type: 'text', placeholder: '请输入用户ID' }],
              ['封禁状态', 'status', { type: 'select', options: [
                {
                  value: -1,
                  label: '全部'
                },
                {
                  label: '封禁',
                  value: 0,
                }, {
                  label: '解禁',
                  value: 1,
                },
              ], autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['封禁原因', 'block_reason', { type: 'select', options: [
                {
                  value: 0,
                  label: '全部'
                },
                {
                  label: '命中规则',
                  value: 1,
                }, {
                  label: '手动屏蔽',
                  value: 2,
                },
              ], autoInsertEmptyOption: false }, { transform: transformNumber }],

            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-end">
            <Button class="btn btn-primary btn-sm" onClick={() => {
              showCreateBlock()
            }}>新增封禁用户</Button>
          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
          >
            {columns.value.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn
                    key={col.prop}
                    prop={col.prop}
                    fixed={col.fixed}
                    minWidth={col.minWidth}
                    label={col.label}
                    width={col.width}
                    align={col.align}
                    v-slots={{
                      default: ({ row }: { row: any }) => col.render({ row }),
                    }}
                  />
                )
              } else {
                return (
                  <ElTableColumn
                    key={col.prop}
                    prop={col.prop}
                    label={col.label}
                    width={col.width}
                    minWidth={col.minWidth}
                    align={col.align}
                    fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default CompetitionContentPage
