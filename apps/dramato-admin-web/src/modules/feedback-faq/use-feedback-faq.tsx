/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetFeedbackFaqList } from './feedback-faq-api'
import { apiGetProblemTypes } from 'src/modules/user-feedback/user-feedback-api'

export const UseFeedbackFAQ = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    total,
    onPageChange,
    onPageSizeChange,
    getList,
    getProblemTypes,
    problemTypes
  }
}
const defaultParams = {
  page_index: 1,
  page_size: 20,
}
const Form = CreateForm<Api.FeedbackFaq.ListReqParams>()
const params = ref<Api.FeedbackFaq.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<Api.FeedbackFaq.DetailReqParams>()
const list = ref<Api.FeedbackFaq.DetailReqParams[]>([])
const loading = ref<boolean>(false)
const problemTypes = ref<M.UserFeedback.ProblemType[]>([])

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetFeedbackFaqList(params.value)
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}

const getProblemTypes = async () => {
  const res = await apiGetProblemTypes()
  problemTypes.value = res.data?.list || []
}
