import { httpClient } from 'src/lib/http-client'
import { langKey, langValue } from 'src/modules/resource/constant'

export const apiGetFeedbackFaqList = (data: Api.FeedbackFaq.ListReqParams) =>
  httpClient.post<ApiResponse<Api.FeedbackFaq.ListResponse>>('/customer_qa/list', data, {
    transformResponseData: {
      'data.list': [(array: Api.FeedbackFaq.DetailReqParams[]) => {
        return array.map(item => {
          const _list = item.qa_list || []
          langKey.map(langCode => {
            const singleLangFaq = _list.find(o => o.language_code === langCode) || null
            item[`${langCode}_question`] = singleLangFaq?.question || ''
            item[`${langCode}_answer`] = singleLangFaq?.answer || ''
            item.language_code = langCode
          })
          return {
            ...item,
          }
        })
      }],
    },
  })

// TODO 详情获取
export const apiGetFaqDetailById = (data: {
  id: number
}) =>
  httpClient.post<ApiResponse<Api.FeedbackFaq.DetailReqParams>>('/customer_qa/detail', data, {
    transformResponseData: {
      data: [(item: Api.FeedbackFaq.DetailReqParams) => {
        const _list = item.qa_list || []
        langKey.map(langCode => {
          const singleLangFaq = _list.find(o => o.language_code === langCode) || null
          item[`${langCode}_question`] = singleLangFaq?.question || ''
          item[`${langCode}_answer`] = singleLangFaq?.answer || ''
          item.language_code = langCode
        })
        return {
          ...item,
        }
      }],
    },
  })

export const apiCreateFeedbackFaq = (data: Api.FeedbackFaq.DetailReqParams) =>
  httpClient.post<ApiResponse<null>>('/customer_qa/create', data)

export const apiUpdateFeedbackFaq = (data: Api.FeedbackFaq.DetailReqParams) =>
  httpClient.post<ApiResponse<null>>('/customer_qa/update', data)

export const apiDelFeedbackFaq = (data: {
  id: number
}) =>
  httpClient.post<ApiResponse<null>>('/customer_qa/delete', data)
