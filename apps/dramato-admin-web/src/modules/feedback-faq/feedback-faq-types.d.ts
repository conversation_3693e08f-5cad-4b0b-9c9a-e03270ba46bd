declare namespace Api {
  namespace FeedbackFaq {
    interface ListReqParams {
      problem_type?: number // 问题类型，非必填
      question?: string // 问题，非必填
      min_version?: string // 最小版本号，例如：1.1.10，非必填
      max_version?: string // 最大版本号，例如：1.1.10，非必填
      page_index?: number
      page_size?: number
    }

    interface FAQ {
      question: string
      answer: string
      language_code: string
    }

    interface DetailReqParams {
      id?: number
      problem_type?: number
      min_version?: string
      max_version?: string
      question_id?: string
      qa_list?: FAQ[]
      [record?: string]: unknown
    }

    interface ListResponse {
      list: DetailReqParams[] // 剧列表
      total: number // 总数
    }
  }
}
