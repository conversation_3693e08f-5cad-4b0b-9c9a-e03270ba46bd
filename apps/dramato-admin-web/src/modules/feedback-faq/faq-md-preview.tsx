import { createComponent } from '@skynet/shared'
import { MdPreview } from 'md-editor-v3'
import "md-editor-v3/lib/preview.css";

type FaqMdPreviewOptions = {
  props: {
    content: string
  }
}

export const FaqMdPreview = createComponent<FaqMdPreviewOptions>({
  props: {
    content: ''
  },
}, props => {
  return () => (
    <x-faq-md-preview class="block">
      <MdPreview
        no-img-zoom-in={true}
        model-value={props.content}
        no-mermaid={true}
        showCodeRowNumber={true}
        preview-theme="default"
        class="
            px-1
            rounded-lg
            bg-[var(--grey-13)]
            text-[var(--grey-4)]
            py-4
            text-sm
            leading-tight
            [&_a]:bg-transparent
        [&_a]:text-[#4493f8]
            [&_a]:no-underline
            [&_ol]:list-decimal
            [&_ol]:pl-4
            [&_ol_li]:my-3
            [&_ul_li]:my-3
            [&_ul_li:nth-child(1)]:mt-0
            [&_ol_li:nth-child(1)]:mt-0
            [&_ol_li:nth-last-child(1)]:mb-0
            [&_ol+p]:mt-3
            [&_ul+p]:mt-3
            [&_p+ol]:mt-3
            [&_p+ul]:mt-3
            [&_img]:max-w-full
            [&_img]:mb-3
            [&_img]:pointer-events-none
            [&_img]:rounded-lg
            [&_figure_img]:mb-0
            "
      />
    </x-faq-md-preview>
  )
})

export default FaqMdPreview
