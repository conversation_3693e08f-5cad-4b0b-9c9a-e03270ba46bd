/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, Icon, openDialog, Pager, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set, trim } from 'lodash-es'
import { UseFeedbackFAQ } from './use-feedback-faq'
import { ElTable, ElTableColumn } from 'element-plus'
import { useRouter } from 'vue-router'
import { apiDelFeedbackFaq } from './feedback-faq-api'

type FeedbackFaqPageOptions = {
  props: {}
}
export const FeedbackFaqPage = createComponent<FeedbackFaqPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, total, onPageChange, onPageSizeChange, getProblemTypes, problemTypes } = UseFeedbackFAQ()
  const tableRef = ref<InstanceType<typeof ElTable>>()
  const router = useRouter()

  onMounted(async () => {
    await getProblemTypes()
    void onQuery()
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>FAQ管理</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              let v = value
              if (typeof value === 'string') v = trim(value)
              set(params.value, path, v)
            }}
            items={[
              ['问题类型', 'problem_type', { type: 'select', options: problemTypes.value.map(item => {
                return {
                  value: item.id,
                  label: item.problem_type
                }
              }), placeholder: '全部' }, {
                transform: transformNumber
              }],
              ['问题检索', 'question', { type: 'text' }],
              ['最小生效版本', 'min_version', { type: 'text', placeholder: '版本号格式：1.0.1' }],
              ['最大生效版本', 'max_version', { type: 'text', placeholder: '版本号格式：1.0.1' }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-end">
            <div>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                void router.push('/feedback-faq/detail')
              }}>
                新建
              </Button>
            </div>
          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
          >
            <ElTableColumn
              prop="problem_type"
              label="反馈类型"
              minWidth={100}
              v-slots={{
                default: ({ row }: { row: Api.FeedbackFaq.DetailReqParams }) => {
                  return <div>{problemTypes.value.find(o => o.id === row.problem_type)?.problem_type || '-'}</div>
                },
              }}
            />
            <ElTableColumn
              prop="min_version"
              label="版本号"
              minWidth={100}
              v-slots={{
                default: ({ row }: { row: Api.FeedbackFaq.DetailReqParams }) => {
                  return <div>{row.min_version ?? '- '} ~ {row.max_version ?? '-'}</div>
                },
              }}
            />
            <ElTableColumn
              prop="q"
              label="反馈问题"
              minWidth={200}
              v-slots={{
                default: ({ row }: { row: Api.FeedbackFaq.DetailReqParams }) => {
                  return <div>{row['zh-CN_question']}</div>
                },
              }}
            />
            <ElTableColumn
              prop="a"
              label="反馈答案"
              minWidth={200}
              v-slots={{
                default: ({ row }: { row: Api.FeedbackFaq.DetailReqParams }) => {
                  return <div>{row['zh-CN_answer']}</div>
                },
              }}
            />

            <ElTableColumn
              prop="opr"
              label="操作"
              align='center'
              width={120}
              v-slots={{
                default: ({ row }: { row: Api.FeedbackFaq.DetailReqParams }) => {
                  return (
                    <div>
                      <Button class="btn btn-link btn-xs" onClick={() => {
                        void router.push(`/feedback-faq/detail/${row.id}`)
                      }}>编辑</Button>
                      <Button class="btn btn-link btn-xs" onClick={() => {
                        const btnLoading = ref(false)
                        const hideDeleteDialog = openDialog({
                          title: '删除',
                          mainClass: 'pb-0 px-5',
                          body: () => (
                            <x-faq-del-form-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-faq-del-form-body>确认删除吗？</x-faq-del-form-body>
                              <x-faq-del-form-footer class="flex w-full justify-end gap-x-[10px]">
                                <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                                  try {
                                    btnLoading.value = true
                                    const res = await apiDelFeedbackFaq({
                                      id: row.id as number,
                                    })
                                    if (res.code === 200) {
                                      showSuccessToast('操作成功！')
                                    } else {
                                      showFailToast(res.message || 'FAQ保存失败')
                                    }
                                    hideDeleteDialog()
                                    onQuery()
                                  } catch (error: any) {
                                    showFailToast(error.response.data.message || 'FAQ保存失败')
                                  } finally {
                                    btnLoading.value = false
                                  }
                                }}
                                >
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                  确定
                                </Button>
                              </x-faq-del-form-footer>
                            </x-faq-del-form-confirm-dialog>
                          ),
                        })
                      }}>删除</Button>
                    </div>
                  )
                },
              }}
            />
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default FeedbackFaqPage
