/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { UseFeedbackFAQ } from './use-feedback-faq'
import { useRouter, useRoute, RouterLink } from 'vue-router'
import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElCol, ElDivider, ElRow } from 'element-plus'
import { FaqMdPreview } from './faq-md-preview'
import NormalEditor from 'src/modules/common/editor/normal-editor'
import { Button, Icon, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { apiGetFaqDetailById, apiCreateFeedbackFaq, apiUpdateFeedbackFaq } from './feedback-faq-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { requiredLabel } from 'src/lib/required-label'
import { Uploader } from 'src/modules/common/uploader/uploader'

type FeedbackFaqDetailPageOptions = {
  props: {}
}
export const FeedbackFaqDetailPage = createComponent<FeedbackFaqDetailPageOptions>({
  props: {},
}, props => {
  const { getProblemTypes, problemTypes } = UseFeedbackFAQ()
  const versionRegex = /^\d+\.\d+\.\d+$/
  const router = useRouter()
  const route = useRoute()
  const id = +route.params.id
  const loading = ref(false)
  const formRef = ref()
  const btnLoading = ref(false)

  const defaultParams = {
    min_version: '',
    max_version: '',
    qa_list: [],
  }

  const cn = 'zh-CN'

  const form = ref<Api.FeedbackFaq.DetailReqParams>({ ...defaultParams })

  function compareVersions(v1: string, v2: string) {
    const parts1 = v1.split('.').map(Number)
    const parts2 = v2.split('.').map(Number)

    for (let i = 0; i < 3; i++) {
      if (parts1[i] > parts2[i]) return 1
      if (parts1[i] < parts2[i]) return -1
    }
    return 0
  }

  const checkVersionNum = (value: string, rule: unknown, callback: (p?: unknown) => void) => {
    if (form.value.max_version && form.value.min_version) {
      if (compareVersions(form.value.min_version, form.value.max_version) === 1) {
        return callback(new Error('最大版本号必须大于最小版本号'))
      }
    }
    callback()
  }

  const rules = {
    problem_type: [
      { required: true, message: '请选择反馈类型', trigger: 'change' },
    ],
    max_version: [
      { validator: checkVersionNum, message: '最大版本号必须大于等于最小版本号', trigger: 'blur' },
    ],
  }

  const getDetail = async () => {
    try {
      const res = await apiGetFaqDetailById({
        id,
      })
      form.value = res.data || {}
    } catch (error: any) {
      showFailToast; (error.response.data.message || 'FAQ详情获取失败', 'error')
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    loading.value = true
    await getProblemTypes()
    if (id) {
      void getDetail()
    } else {
      loading.value = false
    }
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>
              <RouterLink to="/feedback-faq">FAQ管理</RouterLink>
            </li>
            <li>FAQ详情</li>
          </ul>
        ),
        table: () => (
          <div v-loading={loading.value} class="px-4 py-4">
            <ElForm rules={rules} model={form.value} ref={formRef} class="w-full">
              <ElFormItem style="width: 500px" prop="problem_type" required label="反馈类型:">
                <ElSelect v-model={form.value.problem_type} placeholder="请选择反馈类型">
                  {problemTypes.value.map(type => <ElOption value={type.id} label={type.problem_type}>{type.problem_type}</ElOption>)}
                </ElSelect>
              </ElFormItem>
              <ElRow style="width: 500px; display: flex; items-center">
                <ElCol span={11}>
                  <ElFormItem style="display: flex; items-center" prop="min_version" label="版本号:">
                    <ElInput
                      modelValue={form.value.min_version}
                      placeholder="版本号规则：1.0.4"
                      onUpdate:modelValue={(val: string) => {
                        form.value.min_version = val
                      }}
                      onChange={val => {
                        if (!val || versionRegex.test(val)) {
                          form.value.min_version = val
                        } else {
                          form.value.min_version = ''
                          showFailToast('生效版本不符合规则')
                        }
                      }}
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol class="text-center" span={2}>-</ElCol>
                <ElCol span={11}>
                  <ElFormItem style="display: flex; items-center" prop="max_version" label=" ">
                    <ElInput
                      modelValue={form.value.max_version}
                      onUpdate:modelValue={(val: string) => {
                        form.value.max_version = val
                      }}
                      onChange={val => {
                        if (!val || versionRegex.test(val)) {
                          form.value.max_version = val
                        } else {
                          form.value.max_version = ''
                          showFailToast('生效版本不符合规则')
                        }
                      }}
                      placeholder="版本号规则：1.0.4"
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <el-alert
                v-slots={{
                  default: () => (
                    <div>
                      <div>版本号填写说明（闭区间）</div>
                      <div>1.0.4 - 1.0.10：表示版本在 1.0.4 到 1.0.10 之间（含边界）</div>
                      <div>1.0.4 -：表示版本 大于等于 1.0.4</div>
                      <div>- 1.0.10：表示版本 小于等于 1.0.10</div>
                    </div>
                  ),
                }} />

            </ElForm>
            <x-faq-manage>
              {langKey.map((langCode, index) => {
                const currentInsertText = ref('')
                const insertText = (text: string) => {
                  currentInsertText.value = text
                }
                return (
                  <x-faq-manage-item class="space-y-2">
                    <ElDivider content-position="left">{langValue[index]}</ElDivider>
                    <x-faq-question class="flex items-center">
                      <ElCol span={1}>
                        {langCode === 'zh-CN' ? <div class="text-xs">{requiredLabel('问题:')}</div> : <div class="text-xs">问题:</div>}
                      </ElCol>
                      <ElCol span={20}>
                        <ElInput v-model={form.value[`${langCode}_question`]} />
                      </ElCol>
                    </x-faq-question>
                    <x-faq-answer class="flex">
                      <ElCol span={1}>
                        {langCode === 'zh-CN' ? <div class="text-xs">{requiredLabel('回答:')}</div> : <div class="text-xs">回答:</div>}
                      </ElCol>
                      <ElCol span={13}>
                        <div>
                          <Uploader
                            ossKeyType="resource"
                            accept="png,jpg,jpeg"
                            maxsize={1024 * 1024 * 10}
                            onUploadSuccess={d => {
                              const urlStr = encodeURI(`https://img.tianmai.cn/${d.temp_path}`)
                              currentInsertText.value = `\n![aa](${urlStr})\n`
                            }}
                            isImage={false}
                            multiple={false}
                            showFileList={false}
                          >
                            <Button class="btn btn-sm btn-primary mb-2" onClick={() => {
                              setTimeout(() => {
                                currentInsertText.value = ''
                              }, 1000)
                            }}>插入图片</Button>
                          </Uploader>
                        </div>
                        <NormalEditor
                          customClass="w-full min-h-[200px] border"
                          code={form.value[`${langCode}_answer`] as string}
                          onChange={e => {
                            form.value[`${langCode}_answer`] = e
                          }}
                          insertText={currentInsertText.value}
                        />
                      </ElCol>
                      <ElCol span={7}>
                        <div class="w-[375px]">
                          <FaqMdPreview class="flex-1 min-h-[200px] border" content={form.value[`${langCode}_answer`] as string} />
                        </div>
                      </ElCol>
                    </x-faq-answer>

                  </x-faq-manage-item>
                )
              })}
            </x-faq-manage>
            <x-faq-operation-bar class="h-[50px] w-full sticky bottom-0 space-x-4 flex justify-end">
              <Button class="btn btn-sm" onClick={() => {
                void router.push('/feedback-faq')
              }}>返回</Button>
              <Button class="btn btn-sm btn-primary" disabled={btnLoading.value} onClick={() => {
                formRef.value.validate((valid: boolean) => {
                  if (valid) {
                    if (!form.value[`${cn}_question`] || !form.value[`${cn}_answer`]) {
                      showFailToast('请输入简体中文的问题和答案')
                      return
                    }
                    const hideDeleteDialog = openDialog({
                      title: '提示',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <x-faq-form-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-faq-form-body>是否确认保存</x-faq-form-body>
                          <x-faq-form-footer class="flex w-full justify-end gap-x-[10px]">
                            <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                              btnLoading.value = true
                              const qaList = langKey?.map(langCode => {
                                return {
                                  language_code: langCode,
                                  question: form.value[`${langCode}_question`] as string || '',
                                  answer: form.value[`${langCode}_answer`] as string || '',
                                }
                              })
                              let apiFn = apiCreateFeedbackFaq
                              if (form.value.id) {
                                apiFn = apiUpdateFeedbackFaq
                              }
                              try {
                                const res = await apiFn({
                                  id: form.value.id,
                                  problem_type: form.value.problem_type,
                                  min_version: form.value.min_version,
                                  max_version: form.value.max_version,
                                  question_id: form.value.question_id,
                                  qa_list: qaList,
                                })
                                if (res.code === 200) {
                                  showSuccessToast('操作成功！')
                                } else {
                                  showFailToast(res.message || 'FAQ保存失败')
                                }
                                void router.replace('/feedback-faq')
                              } catch (error: any) {
                                showFailToast(error.response.data.message || 'FAQ保存失败')
                              } finally {
                                btnLoading.value = false
                              }
                            }}
                            >
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                              确定
                            </Button>
                          </x-faq-form-footer>
                        </x-faq-form-confirm-dialog>
                      ),
                    })
                  } else {
                    console.log('error submit!!')
                    return false
                  }
                })
              }}>保存</Button>
            </x-faq-operation-bar>
          </div>
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default FeedbackFaqDetailPage
