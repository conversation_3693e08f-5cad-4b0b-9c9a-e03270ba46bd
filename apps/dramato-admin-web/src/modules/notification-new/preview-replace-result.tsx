import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, openDialog, transformDatetime, transformInteger, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { set } from 'lodash-es'
import { ref } from 'vue'

type PreviewReplaceResultProps = {
  props: {
    result: M.PreviewReplaceResult
  }
  emits: {
    submit: (r: M.PreviewReplaceResult) => void
    hide: () => void
  }
}

export const PreviewReplaceResult = createComponent<PreviewReplaceResultProps>({
  props: {
    result: {
      language: '',
      title: '',
      body: '',
    },
  },
  emits: {
    submit: (r: M.PreviewReplaceResult) => { },
    hide: () => { },
  },
}, (props, { emit }) => {
  const result = ref<M.PreviewReplaceResult>(props.result)
  const Form = CreateForm<M.PreviewReplaceResult>()
  const formRules = z.object({
    title: z.string().min(1, 'push标题').max(100, '最多100个字符'),
    body: z.string().min(1, '请输入push内容'),
  })

  const { error, validateAll } = useValidator(result, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'timed_ts' && value === 'Invalid Date') {
              set(result.value || {}, path, undefined)
              return
            }
            set(result.value || {}, path, value)
          }}
          items={[
            ['语言', 'language', { type: 'text', disabled: true }],
            ['push标题', 'title', { type: 'text', suffix: <Icon name="ant-design:copy-filled" /> }],
            ['push内容', 'body', { type: 'text', suffix: <Icon name="ant-design:copy-filled" /> }],
          ]}
          data={result.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => emit('hide')}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} onClick={() => {
          if (!validateAll()) {
            return
          }

          emit('submit', result.value)
        }}
        >
          确定
        </Button>
      </div>
    </>
  )
})
