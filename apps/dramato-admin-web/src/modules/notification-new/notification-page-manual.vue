<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="Push ID">
          <el-input v-model="searchForm.push_task_id" type="number" placeholder="Push ID" clearable />
        </el-form-item>
        <el-form-item label="设定开始时间">
          <el-date-picker v-model="searchForm.timed_start_date_time" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="Pick a date" clearable />
        </el-form-item>
        <el-form-item label="设定结束时间">
          <el-date-picker v-model="searchForm.timed_end_date_time" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="Pick a date" clearable />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select style="width: 240px" v-model="searchForm.state" placeholder="请选择" clearable>
            <el-option label="待发送" :value="1"></el-option>
            <el-option label="发送中" :value="20"></el-option>
            <el-option label="已完成" :value="40"></el-option>
            <el-option label="发送失败" :value="60"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点击率PV小于%">
          <el-input v-model="searchForm.click_rate_pv_lt" placeholder="点击率(PV)小于%" clearable />
        </el-form-item>
        <el-form-item label="点击率UV小于%">
          <el-input v-model="searchForm.click_rate_uv_lt" placeholder="点击率(UV)小于%" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">筛选</el-button>
          <!-- <el-button @click="onReset">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <div class="flex justify-end mb-4 border-b-2">
        <el-popconfirm confirm-button-text="是" cancel-button-text="否" :icon="InfoFilled" icon-color="#626AEF"
          title="确定要取消?" @confirm="handleBatchDelete">
          <template #reference>
            <el-button type="danger"><el-icon>
                <Delete class="mr-1" />
              </el-icon>批量取消</el-button>
          </template>
        </el-popconfirm>

        <!-- <el-button @click="handleBatchDownload"><el-icon>
            <Download class="mr-1" />
          </el-icon>批量下载</el-button> -->
        <el-button type="primary" @click="handleAdd"><el-icon>
            <Plus class="mr-1" />
          </el-icon>新增</el-button>
      </div>
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="push_type" label="AI/人工" width="100">
          <template #default="scope">
            {{ scope.row.push_type === 2 ? 'AI' : '人工' }}
          </template>
        </el-table-column>
        <el-table-column prop="push_task_id" label="PushId" width="80" />
        <el-table-column prop="language" label="语言" width="80" />
        <el-table-column prop="style_config.title" label="Push标题" min-width="120" show-overflow-tooltip />
        <el-table-column prop="style_config.body" label="Push内容" min-width="120" show-overflow-tooltip />
        <el-table-column prop="target_app_names" label="应用" width="160" />
        <el-table-column prop="target_user_type" label="用户范围" width="120">
          <template #default="scope">
            {{ targetUserTypeMap[scope.row.target_user_type] }}
          </template>
        </el-table-column>
        <el-table-column prop="resource_scope_type" label="剧集范围" width="120">
          <template #default="scope">
            {{ resourceScopeTypeMap[scope.row.resource_scope_type] }}
          </template>
        </el-table-column>
        <el-table-column prop="resource_scope_type" label="剧集类型" width="120">
          <template #default="scope">
            {{ getAudioTypeText(scope.row.audio_types) }}
          </template>
        </el-table-column>
        <el-table-column prop="timed_start_date_time" label="设定开始时间" width="120" />
        <el-table-column prop="timed_end_date_time" label="设定结束时间" width="120" />
        <el-table-column prop="state" label="发送状态" width="100">
          <template #default="scope">
            <el-tag :type="getStateType(scope.row.state)">{{ getStateText(scope.row.state) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="start_ts_date_time" label="实际开始时间" width="120" />
        <el-table-column prop="end_ts_date_time" label="实际完成时间" width="120" />
        <el-table-column prop="send_success_rate" label="成功率" width="100" />
        <el-table-column prop="send_cnt" label="发出条数" width="100" />
        <el-table-column prop="arrive_rate" label="到达率" width="100" />
        <el-table-column prop="show_rate" label="曝光率" width="100" />
        <el-table-column prop="click_rate_pv" label="点击率PV" width="100" />
        <el-table-column prop="click_rate_uv" label="拉活率UV" width="100" />
        <el-table-column prop="active_rate" label="拉活率" width="100" />

        <el-table-column prop="push_type" label="样式" width="100">
          <template #default="scope">
            {{ pushTypeMap[scope.row.style_config.push_type] }}
          </template>
        </el-table-column>
        <el-table-column prop="style_config.small_image" label="小图链接" width="100">
          <template #default="scope">
            <img v-if="scope.row.style_config.small_image" :src="scope.row.style_config.small_image" alt=""
              style="width: 50px; height: 50px;">
          </template>
        </el-table-column>
        <el-table-column prop="style_config.image" label="大图链接" width="100">
          <template #default="scope">
            <img v-if="scope.row.style_config.image" :src="scope.row.style_config.image" alt=""
              style="width: 50px; height: 50px;">
          </template>
        </el-table-column>
        <el-table-column prop="style_config.tips" label="tip文案" width="100" />
        <el-table-column prop="style_config.tips_style" label="tip颜色" width="100" />
        <el-table-column prop="style_config.btn1_text" label="按钮1文案" width="100" />
        <el-table-column prop="style_config.btn1_deep_link" label="Deeplink" width="220" min-width="220"
          show-overflow-tooltip />
        <el-table-column prop="style_config.btn2_text" label="按钮2文案" width="100" />
        <el-table-column prop="style_config.btn2_deep_link" label="按钮2 Deeplink" width="220" min-width="220"
          show-overflow-tooltip />
        <el-table-column prop="style_config.media_progress" label="进度值" width="120" />

        <el-table-column prop="created" label="添加时间" width="160">
          <template #default="scope">
            {{ dayjs(scope.row.created * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="create_user_name" label="添加者" width="100" />
        <el-table-column prop="updated" label="更新时间" width="160">
          <template #default="scope">
            {{ dayjs(scope.row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="update_user_name" label="更新者" width="100" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-link type="success" v-if="scope.row.op_list.includes('preview')"
                @click="handlePreview(scope.row)">预览</el-link>
              <el-link type="primary" v-if="scope.row.op_list.includes('edit')"
                @click="handleEdit(scope.row)">编辑</el-link>
              <el-popconfirm confirm-button-text="是" cancel-button-text="否" :icon="InfoFilled" icon-color="#626AEF"
                title="确定要取消?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-link type="danger" v-if="scope.row.op_list.includes('delete')">取消</el-link>
                </template>
              </el-popconfirm>

              <el-link type="primary" v-if="scope.row.op_list.includes('duplicate_and_create')"
                @click="handleDuplicate(scope.row)">复制并新增</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_info.page_index"
          v-model:page-size="searchForm.page_info.page_size" :total="total" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>

      <el-dialog v-model="addVisible" title="新建Push" width="500">
        <AddForm ref="addFormRef" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="addVisible = false">取消</el-button>
            <el-button type="primary" :loading="isUpdating" @click="handleSave">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="previewVisible" title="预览" width="500">
        <Preview />
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
import AddForm from './notification-add-form.vue'
import Preview from './notification-preview.vue'
import { useNotification } from './use-notification';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

const { getConfig, onSearchNotifications, list, loading, searchForm, total, form, onDelete, onDownload, initFromData, deepLinks, isUpdating } = useNotification('manual')

onMounted(() => {
  console.log('mounted')
  getConfig();
  onSearchNotifications();
})

const addFormRef = ref<InstanceType<typeof AddForm>>()
const multipleSelection = ref<M.PushNotificationNew[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchNotifications();
}

const addVisible = ref<boolean>(false)
const previewVisible = ref<boolean>(false)

// 获取状态类型
const getStateType = (state: number): string => {
  const stateMap: Record<number, string> = {
    1: 'warning',
    20: 'primary',
    40: 'success',
    60: 'info'
  }
  return stateMap[state] || ''
}

// 获取状态文本
const getStateText = (state: number): string => {
  const stateMap: Record<number, string> = {
    1: '待发送',
    20: '发送中',
    40: '已完成',
    60: '已停用'
  }
  return stateMap[state] || '未知状态'
}

// 1：全部用户，2：指定用户user_id列表，3：特定范围用户/用户画像
const targetUserTypeMap: Record<number, string> = {
  1: '全部用户',
  2: '指定用户',
  3: '特定范围用户'
}

// 剧集范围类型，0：无，1：全部剧集，2：指定剧集
const resourceScopeTypeMap: Record<number, string> = {
  0: '无',
  1: '全部剧集',
  2: '指定剧集'
}

const getAudioTypeText = (audioType: string): string => {
  const audioTypes = audioType.split(',')
  const result = audioTypes.map(type => audioTypeMap[type])
  return result.join(',')
}
// 音频类型，0：无，1：剧集音频，2：自定义音频
const audioTypeMap: Record<string, string> = {
  '0': '字幕剧',
  '1': '人工配音剧',
  '2': 'AI配音剧'
}
//内容类型：1纯文本; 2小图; 3大图; 4左图右文
const pushTypeMap: Record<string, string> = {
  '1': '纯文本',
  '2': '小图',
  '3': '小图+大图',
  '4': '左图右文'
}


const handleSelectionChange = (val: M.PushNotificationNew[]) => {
  multipleSelection.value = val
}

// 处理操作方法
const handlePreview = (row: M.PushNotificationNew) => {
  console.log('预览', row)
  form.value = {
    ...row,
    audio_types_for_show: row.audio_types.split(',').map(item => Number(item)),
    style_config: {
      ...row.style_config,
      has_tips: row.style_config.tips ? true : false,
      // 需要手动管理按钮和进度条的显示
      media_progress_type: row.style_config.media_progress ? "2" : "1",
      buttonOrProgress: row.style_config.btn1_text ? '1' : row.style_config.media_progress ? '2' : '3',
      btn1_deep_link: extractUrl(row.style_config.btn1_deep_link),
      btn2_deep_link: extractUrl(row.style_config.btn2_deep_link),
      btn1_deep_link_url: row.style_config.btn1_deep_link,
      btn2_deep_link_url: row.style_config.btn2_deep_link,
      btn1_deep_link_custom: row.style_config.btn1_deep_link,
      btn2_deep_link_custom: row.style_config.btn2_deep_link,
    },
    target_app_names_source: row.target_app_names.split(','),
  }

  if (form.value.style_config.btn1_deep_link === '/') {
    form.value.style_config.btn1_deep_link_url = ''
  } else {
    if (form.value.style_config.btn1_deep_link === 'dramawave://dramawave.app/webpage') {
      form.value.style_config.btn1_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url.split('?url=')[1])
    }
    form.value.style_config.btn1_deep_link_custom = ''
  }

  if (form.value.style_config.btn2_deep_link === '/') {
    form.value.style_config.btn2_deep_link_url = ''
  } else {
    if (form.value.style_config.btn2_deep_link === 'dramawave://dramawave.app/webpage') {
      form.value.style_config.btn2_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url.split('?url=')[1])
    }
    form.value.style_config.btn2_deep_link_custom = ''
  }
  previewVisible.value = true
}

const handleEdit = (row: M.PushNotificationNew) => {
  console.log('编辑', row)
  form.value = {
    ...row,
    audio_types_for_show: row.audio_types.split(',').map(item => Number(item)),
    style_config: {
      ...row.style_config,
      has_tips: row.style_config.tips ? true : false,
      // 需要手动管理按钮和进度条的显示
      media_progress_type: row.style_config.media_progress ? "2" : "1",
      buttonOrProgress: row.style_config.btn1_text ? '1' : row.style_config.media_progress ? '2' : '3',
      btn1_deep_link: extractUrl(row.style_config.btn1_deep_link),
      btn2_deep_link: extractUrl(row.style_config.btn2_deep_link),
      btn1_deep_link_url: row.style_config.btn1_deep_link,
      btn2_deep_link_url: row.style_config.btn2_deep_link,
      btn1_deep_link_custom: row.style_config.btn1_deep_link,
      btn2_deep_link_custom: row.style_config.btn2_deep_link,
    },
    target_app_names_source: row.target_app_names.split(','),
  }

  if (form.value.style_config.btn1_deep_link === '/') {
    form.value.style_config.btn1_deep_link_url = ''
  } else {
    if (form.value.style_config.btn1_deep_link === 'dramawave://dramawave.app/webpage') {
      form.value.style_config.btn1_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url.split('?url=')[1])
    }
    form.value.style_config.btn1_deep_link_custom = ''
  }

  if (form.value.style_config.btn2_deep_link === '/') {
    form.value.style_config.btn2_deep_link_url = ''
  } else {
    if (form.value.style_config.btn2_deep_link === 'dramawave://dramawave.app/webpage') {
      form.value.style_config.btn2_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url.split('?url=')[1])
    }
    form.value.style_config.btn2_deep_link_custom = ''
  }
  addVisible.value = true
}
// dramawave://dramawave.com/series/123?url=https://dramawave.com/series/123
// 获取query中的url
// const getUrlFromDeepLink = (deepLink: string): string => {
//   if (!deepLink) return ''
//   return deepLink.split('url=')[1] || ''
// }
// dramawave://dramawave.com/series/123?url=https://dramawave.com/series/123
// 获取原始的url
const extractUrl = (url: string): string => {
  if (!url) return ''
  const isCustomer = deepLinks.filter(i => i.path !== '/').find(i => i.path === url)
  return isCustomer ? url : url.includes('webpage') ? 'dramawave://dramawave.app/webpage' : '/'
}

const handleDelete = (row: M.PushNotificationNew) => {
  console.log('取消', row)
  // 需要做二次确认

  onDelete([row.push_task_id])
}

const handleBatchDelete = () => {
  console.log('批量取消', multipleSelection.value)
  onDelete(multipleSelection.value.map(item => item.push_task_id))
}

const handleBatchDownload = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要下载的数据')
    return
  }
  onDownload(multipleSelection.value.map(item => item.id), 1, 1)
  console.log('批量下载', multipleSelection.value)
}

const handleDuplicate = (row: M.PushNotificationNew) => {
  console.log('复制并新增', row)
  form.value = {
    ...row,
    audio_types_for_show: row.audio_types.split(',').map(item => Number(item)),
    push_task_id: undefined,
    style_config: {
      ...row.style_config,
      has_tips: row.style_config.tips ? true : false,
      // 需要手动管理按钮和进度条的显示
      // media_progress_type: row.style_config.media_progress ? "2" : "1",
      buttonOrProgress: row.style_config.btn1_text ? '1' : row.style_config.media_progress ? '2' : '3',
      btn1_deep_link: extractUrl(row.style_config.btn1_deep_link),
      btn2_deep_link: extractUrl(row.style_config.btn2_deep_link),
      btn1_deep_link_url: row.style_config.btn1_deep_link,
      btn2_deep_link_url: row.style_config.btn2_deep_link,
      btn1_deep_link_custom: row.style_config.btn1_deep_link,
      btn2_deep_link_custom: row.style_config.btn2_deep_link,
      media_progress_type: row.style_config?.media_progress_type || '',
      media_progress: ''
    },
    target_app_names_source: row.target_app_names.split(','),
  }
  if (form.value.style_config.btn1_deep_link === '/') {
    form.value.style_config.btn1_deep_link_url = ''
  } else {
    console.log('form.value.style_config.btn1_deep_link', form.value.style_config.btn1_deep_link);

    if (form.value.style_config.btn1_deep_link === 'dramawave://dramawave.app/webpage') {
      console.log('>>>>', decodeURIComponent(form.value.style_config.btn1_deep_link_url));

      form.value.style_config.btn1_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url).split('?url=')[1]
      console.log('form.value.style_config.btn1_deep_link_url', form.value.style_config.btn1_deep_link_url);

    }
    form.value.style_config.btn1_deep_link_custom = ''
  }

  if (form.value.style_config.btn2_deep_link === '/') {
    form.value.style_config.btn2_deep_link_url = ''
  } else {
    if (form.value.style_config.btn2_deep_link === 'dramawave://dramawave.app/webpage') {
      form.value.style_config.btn2_deep_link_url = decodeURIComponent(form.value.style_config.btn1_deep_link_url.split('?url=')[1])
    }
    form.value.style_config.btn2_deep_link_custom = ''
  }
  addVisible.value = true
}

const handleAdd = (): void => {
  form.value = cloneDeep({ ...initFromData, list_type: 1 })
  addVisible.value = true
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_info.page_index = val
  onSearchNotifications()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_info.page_size = val
  onSearchNotifications()
}

const handleSave = async () => {
  const res = await addFormRef.value?.submit()
  // 保存成功再关闭页面
  if (res) {
    addVisible.value = false
  }
}
</script>

<style scoped></style>