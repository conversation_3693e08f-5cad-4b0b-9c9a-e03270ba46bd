<template>
  <div>
    <el-button-group class="ml-4 mt-4">
      <el-button @click="() => currentPage = c.id" v-for="c in pageConfig"
        :type="currentPage === c.id ? 'primary' : ''">{{ c.name }}</el-button>
    </el-button-group>
    <component :is="pageConfig[currentPage].component" />
  </div>
</template>

<script setup lang="ts">
import notificationPageManual from './notification-page-manual.vue';
import notificationPageDramaHalfRelated from './notification-page-drama-half-related.vue';
import notificationPageDramaRelated from './notification-page-drama-related.vue';
import notificationPageDramaNotRelated from './notification-page-drama-not-related.vue';
import { ref } from 'vue';

const pageConfig = [
  {
    id: 0,
    name: '人工',
    component: notificationPageManual,
  },
  {
    id: 1,
    name: '剧集无关',
    component: notificationPageDramaNotRelated,
  },
  {
    id: 2,
    name: '剧集半相关',
    component: notificationPageDramaHalfRelated,
  },
  {
    id: 3,
    name: '剧集相关',
    component: notificationPageDramaRelated,
  },
];

const currentPage = ref(0);


</script>

<style scoped></style>