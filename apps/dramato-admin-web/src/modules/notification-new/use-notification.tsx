/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { apiGetPushConfig, apiTranslate, apiSavePush, apiGetInfoPushList, apiDeletePush, apiExportExcel } from './notification-api'
import { ElMessage } from 'element-plus'
import { createCachedFn } from '@skynet/shared'

const deepLinks: M.Deeplink[] = [
  {
    name: '自定义',
    path: '/',
  },
  {
    name: '剧集播放页面',
    path: 'dramawave://dramawave.app/detail',
  },
  {
    name: 'My list Tab',
    path: 'dramawave://dramawave.app/library',
  },
  {
    name: '播放历史',
    path: 'dramawave://dramawave.app/library/history',
  },
  {
    name: 'Profile Tab',
    path: 'dramawave://dramawave.app/profile',
  },
  {
    name: '内部H5页面',
    path: 'dramawave://dramawave.app/webpage',
  },
  {
    name: '首页',
    path: 'dramawave://dramawave.app/home',
  },
  {
    name: '充值',
    path: 'dramawave://dramawave.app/store',
  },
  {
    name: 'rewards',
    path: 'dramawave://dramawave.app/rewards',
  },
  {
    name: 'my_wallet',
    path: 'dramawave://dramawave.app/my_wallet',
  },
]

const initFromData = {
  list_type: 1, // //1:人工、2：剧集无关、3：剧集半相关、4：剧集相关
  audio_types: '0', // 音频类型，0：无，1：剧集音频，2：自定义音频
  audio_types_for_show: [0], // 音频类型，0：无，1：剧集音频，2：自定义音频
  resource_scope_type: 2, // 剧集范围类型，0：无，1：全部剧集，2：指定剧集
  resource_scope_val: '', // 剧集范围类型对应的值(逗号拼接)，如剧集id列表：1,2,3
  target_user_type: 1, // 目标用户类型，1：全部用户，2：指定用户user_id列表，3：特定范围用户/用户画像
  target_user_val: '', // 目标用户类型对应的值(逗号拼接)，如uid列表：1,2,3、用户画像id列表：1,2,3
  ageing_type: 1, // 时效类型，1：立即发送，2：定时发送
  target_app_names_source: [], // 目标app名称，逗号拼接，如：Dramawave_iOS,Dramawave_Android
  target_app_names: '', // 目标app名称，逗号拼接，如：Dramawave_iOS,Dramawave_Android
  timed_start_date_time: '', // 定时发送开始时间
  timed_end_date_time: '', // 定时发送结束时间
  end_now_add_hours: 0, // 结束时间，立即发送时有效，单位：小时
  end_now_add_minute: 0, // 结束时间，立即发送时有效，单位：分钟
  style_config: {
    push_type: '1', // 内容类型：1纯文本; 2小图; 3大图+小图; 4左图右文
    buttonOrProgress: '1', // *前端自己管理*，按钮或进度条：1按钮; 2进度条; 3无
    title: '',
    body: '',
    image: '',
    small_image: '',
    has_tips: false, // *前端自己管理*，
    tips: '',
    tips_style: '',
    media_progress: '',
    media_progress_type: '1', // *前端自己管理*，进度条类型：1随机值; 2固定值
    has_btn1: false,
    btn1_bg_color: '',
    btn1_text: '',
    btn1_deep_link: '/',
    btn1_deep_link_custom: '',
    has_btn2: false,
    btn2_text: '',
    btn2_deep_link: '',
    btn2_deep_link_custom: '',
    btn1_deep_link_url: '',
    btn2_deep_link_url: '',
  },
  translate_list: [], // {text: '', type: 1, translate: { en: '', zh: '' }}
}

const form = ref<M.NewCreatePushForm>(cloneDeep(initFromData))
const pushConfig = ref<M.PushConfigData>()
const list = ref<M.PushNotificationNew[]>([])

export const useNotification = createCachedFn((id: string) => {
  const searchForm = ref<M.PushNotificationSearchOptionNew>({
    list_type: 1,
    page_info: {
      page_index: 1,
      page_size: 10,
    },
  })
  const total = ref<number>(10)
  const applicationList = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
  const closeEditPushNotificationModal = ref(() => {})
  const loading = ref(false)
  const isUpdating = ref(false)

  const InitPushNotificationOption: M.PushNotification = {
    priority: 3,
    user_identify_val: '',
    notify_btn_bg_color: '#FC2763',
  }

  const currentNotification = ref<M.PushNotification>(InitPushNotificationOption)

  const getList = async () => {
    loading.value = true
    const rs = await apiGetInfoPushList({
      ...searchForm.value,
      push_task_id: Number(searchForm.value.push_task_id),
      prompt_id: Number(searchForm.value.prompt_id),
      resource_id: Number(searchForm.value.resource_id),
    })
    list.value = rs.data?.items.map(item => {
      return {
        ...item,
        style_config: JSON.parse(item.style_config as unknown as string),
      }
    }) || []
    total.value = rs.data?.total || 0
    loading.value = false
  }

  const onSearchNotifications = (isFirst?: boolean) => {
    if (isFirst) {
      searchForm.value.page_info = {
        page_index: 1,
        page_size: 10,
      }
    }
    void getList()
  }

  const onPageChange = (page_index: number) => {
    searchForm.value.page_info = {
      ...(searchForm.value.page_info || {}),
      page_index,
    }
    onSearchNotifications()
  }

  const onPageSizeChange = (page_size: number) => {
    searchForm.value.page_info = {
      page_index: 1,
      page_size,
    }
    onSearchNotifications()
  }

  const onReset = (list_type?: number) => {
    searchForm.value = {
      list_type: list_type || 1,
      page_info: {
        page_index: 1,
        page_size: 10,
      },
    }
    onSearchNotifications(true)
  }

  const onDelete = async (ids: number[]) => {
    try {
      await apiDeletePush({ push_task_id_list: ids })
      ElMessage.success('删除成功')
      onSearchNotifications()
    } catch (error: any) {
      if (error.response.data.code === 400) {
        return
      }
      ElMessage.error(error.response.data.message || '删除失败')
    }
  }

  const onDownload = async (ids: number[], type: number, list_type: number) => {
    try {
      const response = await apiExportExcel({ id_list: ids, type, list_type })
      // 检查响应是否成功
      if (!response.ok) {
        ElMessage.error('下载失败')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        ElMessage.error('下载的文件为空')
        return
      }
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '推送任务.csv'
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error: any) {
      console.log(error)
      // 将返回的二进制流转换为csv文件并下载
      console.log(error.response.data)

      if (error.response.status === 400) {
        ElMessage.error('下载失败')
        return
      }
    }
  }

  const onEditSuccess = (isCreate?: boolean) => {
    isUpdating.value = false
    closeEditPushNotificationModal.value && closeEditPushNotificationModal.value()
    if (isCreate) {
      onSearchNotifications(true)
      return
    }
    onSearchNotifications()
  }

  const switchPushNotificationParams = () => {
    const ids = currentNotification.value.target_app_ids as number[] || []
    const names = applicationList.value.filter(item => ids.includes(item.value))?.map(item => item.label).join(',') || ''
    return {
      ...currentNotification.value,
      target_app_ids: ids.join(','),
      target_app_names: names,
      image_url: currentNotification.value?.image_url?.includes('https://') ? currentNotification.value.image_url.replace('https://static-v1.mydramawave.com/push/task/image/notify/', '') : currentNotification.value.image_url,
      timed_ts: currentNotification.value.ageing_type === 2 ? new Date(currentNotification.value.timed_ts || '').getTime() / 1000 : undefined,
      user_identify_val: currentNotification.value.target_user_type === 2 ? currentNotification.value.user_identify_val : undefined,
    }
  }

  const onCreate = async (data: M.NewCreatePushForm) => {
    try {
      if (isUpdating.value === true) {
        return
      }
      isUpdating.value = true
      await apiSavePush(data)
      ElMessage.success('创建成功')
      onEditSuccess(true)
      return true
    } catch (error: any) {
      isUpdating.value = false
      console.log(error)
      ElMessage.error(error.response.message || (error.response.data.message || '') + (error.response.data.err_msg || '') || '创建失败')
    }
  }

  const getConfig = async () => {
    const rs = await apiGetPushConfig()
    pushConfig.value = rs.data
  }

  const getTranslate = async (text: string, type: M.TranslateType) => {
    const rs = await apiTranslate({ text, type })
    return rs.data
  }

  return {
    searchForm,
    form,
    initFromData,
    list,
    total,
    closeEditPushNotificationModal,
    InitPushNotificationOption,
    loading,
    onPageChange,
    onPageSizeChange,
    onReset,
    onSearchNotifications,
    onCreate,
    onDelete,
    onDownload,
    currentNotification,
    applicationList,
    isUpdating,
    getConfig,
    pushConfig,
    getTranslate,
    deepLinks,
  }
})
