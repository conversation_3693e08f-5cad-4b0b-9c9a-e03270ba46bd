/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { But<PERSON>, Markdown, showAlert } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { apiGetPrompts, apiSavePrompt } from './notification-api'

type PromptSettingOptions = {
  props: {
    rec_type: number
    prompts: Array<{
      prompt_id: number
      prompt: string
    }>
  }
}

export const PromptSetting = createComponent<PromptSettingOptions>({
  props: {
    rec_type: 1,
    prompts: [],
  },
}, ({ rec_type, prompts }) => {
  const currentPromptId = ref<number>(prompts && prompts.length > 0 ? prompts[0].prompt_id : 0)
  const currentPrompt = ref<string>(prompts && prompts.length > 0 ? prompts[0].prompt : '')
  const needEditPromptId = ref<number[]>([])
  const promptList = ref<Array<{
    prompt_id: number
    prompt: string
  }>>([...prompts])

  return () => (
    <x-prompt-setting class="w-[800px] h-[500px] flex flex-col gap-y-2 overflow-hidden">
      <x-prompt-setting-header class="text-md flex justify-between items-center">
        Prompt管理
        <Button class="btn btn-primary btn-sm" disabled={needEditPromptId.value.length > 0} onClick={() => {
          if (promptList.value.length <= 0) {
            currentPromptId.value = 0
            currentPrompt.value = ''
            promptList.value.push({ prompt: '', prompt_id: 0 })
            needEditPromptId.value.push(0)
            return
          }
          const prompt_id = promptList.value[promptList.value.length - 1].prompt_id + 1

          currentPromptId.value = prompt_id
          currentPrompt.value = ''
          promptList.value.push({ prompt: '', prompt_id })
          needEditPromptId.value.push(prompt_id || 0)
        }}
        >添加
        </Button>
      </x-prompt-setting-header>
      <x-prompt-setting-container class="flex-1 flex gap-x-2 overflow-hidden">
        <x-prompt-setting-nav class="h-full tabs tab-sm tabs-boxed gap-y-2 flex flex-col overflow-y-auto hide-scrollbar">
          {
            promptList && promptList.value.length > 0 && promptList.value.map(i => (
              <a
                role="tab"
                class={mc('tab w-20 min-w-20 max-w-20 bg-gray-300 text-black', currentPromptId.value === i.prompt_id ? 'tab-active' : '')}
                onClick={() => {
                  currentPromptId.value = i.prompt_id
                  currentPrompt.value = i.prompt
                }}
              >
                {i.prompt_id}
              </a>
            ))
          }
        </x-prompt-setting-nav>
        <x-prompt-setting-content class="h-full p-2 gap-y-2 flex-1 flex flex-col border border-solid rounded-md border-1 border-gray-300 overflow-hidden">
          {
            !(needEditPromptId.value.includes(currentPromptId.value || 0))
              ? (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    {
                      currentPrompt.value && currentPrompt.value
                        ? <Markdown content={currentPrompt.value} class="markdown-body bg-white" />
                        : <div class="flex-1 flex justify-center items-center">暂无数据</div>
                    }

                  </x-prompt-setting-tab-panel>
                )
              : (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    <textarea
                      class={mc('textarea textarea-bordered w-full h-full')}
                      value={currentPrompt.value}
                      onInput={(e: Event) => {
                        currentPrompt.value = (e.target as HTMLTextAreaElement).value
                      }}
                    />
                  </x-prompt-setting-tab-panel>
                )
          }
          {
            !!(needEditPromptId.value.includes(currentPromptId.value || 0))
              ? (
                  <x-prompt-setting-tab-panel-btn class="flex justify-end items-center gap-4">
                    <Button class="btn btn-sm" onClick={() => {
                      needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPromptId.value)
                      promptList.value = promptList.value.filter(item => item.prompt_id !== currentPromptId.value)

                      const prompt_id = promptList.value[0]?.prompt_id
                      currentPromptId.value = prompt_id
                      currentPrompt.value = promptList.value[0]?.prompt
                    }}
                    >取消
                    </Button>
                    <Button class="btn btn-sm btn-primary" onClick={() => {
                      const errorKeys: string[] = []
                      const prompt = currentPrompt.value
                      Object.keys(currentPrompt.value || {}).forEach(key => {
                        if (!prompt) {
                          errorKeys.push(key)
                        }
                      })
                      if (errorKeys.length) {
                        return showAlert(`请填写`, 'error')
                      }

                      void apiSavePrompt(rec_type, prompt).then(() => {
                        showAlert('添加成功', 'success')
                        needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPromptId.value)

                        void apiGetPrompts(rec_type).then(rs => {
                          promptList.value = (rs.data?.items || []) as Array<{
                            prompt_id: number
                            prompt: string
                          }>
                          currentPromptId.value = promptList.value[promptList.value.length - 1].prompt_id
                          currentPrompt.value = promptList.value[promptList.value.length - 1].prompt
                        })
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message || '提交失败', 'error')
                        })
                    }}
                    >确认
                    </Button>
                  </x-prompt-setting-tab-panel-btn>
                )
              : null
          }
        </x-prompt-setting-content>
      </x-prompt-setting-container>
    </x-prompt-setting>
  )
})
