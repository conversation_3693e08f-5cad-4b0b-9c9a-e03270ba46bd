<template>
    <div>
        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="Android" v-if="form.style_config.push_type !== '4'" name="first">
                <div class="flex gap-2" v-if="form.style_config.push_type === '1'">
                    <div class="flex-1">
                        <div class="text-center">折叠态</div>
                        <div class="w-full rounded-2xl mt-4 justify-between  flex items-center gap-2 bg-gray-100 p-2">
                            <div class="w-8 h-8 rounded-full " style="background-color: aquamarine;"></div>
                            <div>
                                <div class="text-base ">{{ form.style_config.title }}</div>
                                <div class="text-gray-500">{{ form.style_config.body }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-center">展开态</div>
                        <div class="w-full rounded-2xl mt-4 justify-between flex items-center gap-2 bg-gray-100 p-2">
                            <div class="w-8 h-8 rounded-full " style="background-color: aquamarine;"></div>
                            <div>
                                <div class="text-base">{{ form.style_config.title }}</div>
                                <div class="text-gray-400">{{ form.style_config.body }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex gap-2" v-if="form.style_config.push_type === '2'">
                    <div class="flex-1">
                        <div class="text-center">折叠态</div>
                        <div class="w-full rounded-2xl mt-4 justify-between flex items-center gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base ">{{ form.style_config.title }}</div>
                                <div class="text-gray-500">{{ form.style_config.body }}</div>
                            </div>
                            <div><img class="rounded" :src="form.style_config.small_image" /></div>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-center">展开态</div>
                        <div class="w-full rounded-2xl mt-4 justify-between flex items-center gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base">{{ form.style_config.title }}</div>
                                <div class="text-gray-400">{{ form.style_config.body }}</div>
                            </div>
                            <div><img class="rounded" :src="form.style_config.small_image" /></div>
                        </div>
                    </div>
                </div>
                <div class="flex gap-2" v-if="form.style_config.push_type === '3'">
                    <div class="flex-1">
                        <div class="text-center">折叠态</div>
                        <div class="w-full rounded-2xl mt-4  justify-between flex items-center gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base ">{{ form.style_config.title }}</div>
                                <div class="text-gray-500">{{ form.style_config.body }}</div>
                            </div>
                            <div v-if="form.style_config.small_image"><img class="rounded"
                                    :src="form.style_config.small_image" /></div>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-center">展开态</div>
                        <div class="w-full rounded-2xl mt-4  flex-col flex gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base">{{ form.style_config.title }}</div>
                                <div class="text-gray-400">{{ form.style_config.body }}</div>
                            </div>
                            <div><img v-if="form.style_config.image" class="rounded" :src="form.style_config.image" />
                            </div>
                        </div>

                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="IOS" v-if="form.style_config.push_type === '4'" name="second">
                <div class="flex gap-2">
                    <div class="flex-1">
                        <div class="text-center">折叠态</div>
                        <div class="w-full rounded-2xl mt-4  justify-between flex items-center gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base ">{{ form.style_config.title }}</div>
                                <div class="text-gray-500">{{ form.style_config.body }}</div>
                            </div>
                            <div><img v-if="form.style_config.small_image" class="rounded"
                                    :src="form.style_config.small_image" /></div>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-center">展开态</div>
                        <div class="w-full rounded-2xl mt-4 flex-col flex gap-2 bg-gray-100 p-2">
                            <div>
                                <div class="text-base">{{ form.style_config.title }}</div>
                                <div class="text-gray-400">{{ form.style_config.body }}</div>
                            </div>
                            <div v-if="form.style_config.image"><img class="rounded" :src="form.style_config.image" />
                            </div>
                        </div>

                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useNotification } from './use-notification';

const { form } = useNotification('preview');

const activeName = ref('first')
</script>

<style scoped></style>