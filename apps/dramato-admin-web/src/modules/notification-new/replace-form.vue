<template>
	<el-form :model="form" :rules="rules">
		<el-form-item label="文案类型" :label-width="formLabelWidth">
			<el-select v-model="props.pushInfo.rec_type" disabled placeholder="请选择">
				<el-option label="签到提醒" :value="23"></el-option>
				<el-option label="订阅到期次日" :value="19"></el-option>
				<el-option label="订阅到期5天" :value="20"></el-option>
				<el-option label="免费部分完播" :value="21"></el-option>
				<el-option label="充值用户" :value="22"></el-option>
				<el-option label="续播剧" :value="3"></el-option>
				<el-option label="客户端主动获取" :value="15"></el-option>
				<el-option label="客户端主动获取" :value="16"></el-option>
				<el-option label="免费剧" :value="17"></el-option>
				<el-option label="折扣剧" :value="18"></el-option>
				<el-option label="新剧上线" :value="25"></el-option>
				<el-option label="付费版个性化推荐" :value="26"></el-option>
			</el-select>
		</el-form-item>
		<el-form-item label="Prompt编号" :label-width="formLabelWidth">
			<el-select v-model="form.prompt_id" placeholder="请选择">
				<el-option v-for="dpl in prompts" :label="dpl.prompt_id" :value="dpl.prompt_id"></el-option>
			</el-select>
		</el-form-item>
		<el-link type="primary" style="margin-left: 100px;" @click="onSetting">高级设置</el-link>
	</el-form>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref } from 'vue';
import { apiGenPushText, apiGetPrompts } from './notification-api';
import { openDialog } from '@skynet/ui';
import { PromptSetting } from './prompt-setting';
import { Message } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const formLabelWidth = '100px';

const props = defineProps({
	pushInfo: {
		type: Object,
		default: () => ({}),
	}
})

type Prompts = Array<{
	prompt_id: number,//prompt主键id
	prompt: string
}>

const prompts = ref<Prompts>([])

const form = ref<{
	rec_type: number,
	prompt_id?: number,
}>({
	rec_type: 23,
})

const rules = reactive({})

const getPrompts = async () => {
	const rs = await apiGetPrompts(props.pushInfo.rec_type)
	prompts.value = (rs.data?.items || []) as Prompts
}

const onSubmit = async () => {
	if (!form.value.prompt_id) {
		Message.error('请选择提示语')
		return null
	}

	let rs = null

	try {
		rs = await apiGenPushText({
			rec_type: props.pushInfo.rec_type,
			prompt_id: form.value.prompt_id,
			language: props.pushInfo.language,
			list_type: props.pushInfo.list_type,
			id: props.pushInfo.id,
		})
	} catch (error: any) {
		console.log('>>> eo');
	}

	if (!rs) {
		return rs
	}

	return {
		prompt_id: form.value.prompt_id,
		language: props.pushInfo.language,
		list_type: props.pushInfo.list_type,
		id: props.pushInfo.id,
		gen_txt: rs.data?.gen_txt
	}
}

defineExpose({
	getPrompts,
	onSubmit
})

const onSetting = () => {
	openDialog({
		title: '高级设置',
		body: () => <PromptSetting rec_type={props.pushInfo.rec_type} prompts={prompts.value} />,
		beforeClose: () => {
			void getPrompts()
		},
		mainClass: 'w-[820px] flex justify-center items-center',
		customClass: '!w-[820px]',
	})
}

// onMounted(() => {
// 	getPrompts()
// })
</script>