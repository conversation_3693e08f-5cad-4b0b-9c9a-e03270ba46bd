import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTableOld, MergeClass, showFailToast, SvgIcon, TableColumnOld, transformInteger, transformNumber } from '@skynet/ui'
import { Icon } from '@skynet/ui/icon/icon'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { LABEL_OPTIONS } from 'src/modules/resource-publish/constant'
import { useResourceStore } from 'src/modules/resource-publish/use-resource-publish-store'
import { computed, onMounted, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { z } from 'zod'
import { Uploader } from '../common/uploader/uploader'
import { EpisodeStatus } from './const'
import { cdn, useEpisodeDetails } from './use-episode-details'

dayjs.extend(duration)

const Empty = () => {
  return <span v-html={'&nbsp;'} />
}

export const EpisodeDetails = createComponent(null, () => {
  const router = useRouter()
  const route = useRoute()
  const {
    getTags,
    tags,
    getLanguageList,
    languageList,
    currentLanguage,
    getEpisodeDetails,
    episodeDetails,
    onDraftSave,
    onCopy,
    onRemove,
    onPreviewEpisodeListItem,
    onPreviewM3U8EpisodeListItem,
    onPreviewExternal,
    onEpisodeTranscode,
    onEpisodeBatchTranscode,
    onDeleteEpisodeListItem,
    onAddEpisodeListItem,
    checked,
    isCheckAll,
    checkedAllChange,
    switchLanguage,
    currentLanguageEpisodesIndex,
    // onUploadAudio,
    // onUploadCaption,
    onUploadSingleCaptionSuccess,
    onUploadSingleAudioSuccess,
    getCurrentLanguageEpisodeConfig,
    ...otherStore
  } = useEpisodeDetails()
  const { firstLabels, secondLabels, rootLabels, getLabelList, allLabels } = useResourceStore()
  const Form = CreateForm<M.EpisodeDate>()
  const LanguageEpisodesForm = CreateForm<M.EpisodeMultiVersion>()
  const formRules = z.object({
    serialize_status: z.number({
      message: '请选择连载状态',
    }),
    count: z.number({
      message: '请输入总集数',
    }),
    unlocked_episodes: z.number({
      message: '请输入解锁集数',
    }),
    // default_language_code: z.string({
    //   message: '请选择默认语言',
    // }),
    episode_list: z.array(z.object({
      serial_number: z.number().min(1, '请输入单集序号'),
      file_name: z.string().min(1, '请添加单集信息'),
    })).min(1, '请添加单集信息'),
    title: z.string().min(1, '请输入剧名').max(255, '最多255个字符'),
    description: z.string().min(1, '请输入剧简介').max(1000, '最多1000个字符'),
    cover: z.string().min(1, '请上传封面'),
    // language_code_list: z.array(z.string()).min(1, '请选择语言'),
  })

  const { error, validateAll } = useValidator(episodeDetails, formRules)

  const onPublish = () => {
    const validateAllResult = validateAll()
    if (validateAllResult) {
      void otherStore.onPublish()
    } else {
      showFailToast('请检查表单<br />')
    }
  }

  const getEpisodeNumTip = () => {
    // 已上传集数
    const episodeSerialNumbers = episodeDetails.value.episode_list?.map(row => row.serial_number) || []
    const count = episodeDetails.value.count
    // 如果完整上传的集数
    const fullEpisode = new Array(count).fill(0).map((_, i) => i + 1)

    const notExistNums = fullEpisode.filter(i => !episodeSerialNumbers?.includes(i))
    const moreExistNums = episodeSerialNumbers.filter(i => !fullEpisode?.includes(i))
    if (notExistNums.length === 0 && moreExistNums.length === 0) {
      return ''
    } else {
      let tips = notExistNums.length > 0 ? `缺少的集数: ${notExistNums?.join(', ')};` : ''
      tips += moreExistNums.length > 0 ? `多出来的集数: ${moreExistNums?.join(', ')}` : ''
      return tips
    }
  }

  const getFullPath = (path: string) => {
    if (path.indexOf('http') === -1) {
      return cdn + path.replace('video/cover/', '')
    } else {
      return path
    }
  }

  const Table = CreateTableOld<M.EpisodeSeries>()
  const columns: TableColumnOld<M.EpisodeSeries>[] = [
    [
      () => (
        <Checkbox
          label=""
          disabled={episodeDetails.value?.episode_list?.filter(row => !!row.episode_key)?.length === 0}
          modelValue={episodeDetails.value?.episode_list?.filter(row => !!row.episode_key)?.length === checked.value.length}
          onUpdate:modelValue={(value: boolean) => {
            checkedAllChange(value)
          }}
        />
      ),
      row => {
        const episodeId = row.episode_key as string
        return (
          !!row.episode_key
            ? (
                <Checkbox
                  label=""
                  disabled={!!!episodeId}
                  modelValue={checked.value.includes(episodeId)}
                  onUpdate:modelValue={(value: unknown) => {
                    if (value) {
                      if (!checked.value.includes(episodeId)) checked.value.push(episodeId)
                      if (episodeDetails.value?.episode_list?.filter(row => !!row.episode_key)?.length === checked.value.length) {
                        isCheckAll.value = true
                      }
                    } else {
                      const rowIndex = checked.value.findIndex(rowId => rowId === episodeId)
                      if (rowIndex !== -1) {
                        checked.value.splice(rowIndex, 1)
                        isCheckAll.value = false
                      }
                    }
                  }}
                />
              )
            : ''
        )
      },
      { class: 'w-[60px]' },
    ],
    ['单集序号', 'serial_number', { class: 'w-[80px]' }],
    ['单集ID', 'episode_key', { class: 'w-[120px]' }],
    ['文件名称', 'file_name', { class: 'w-[340px]' }],
    ['封面', row => row.cover ? <img alt={row.file_name} class="size-[200px] object-contain" src={getFullPath(row.cover)} /> : '-空-', { class: 'w-[200px]' }],
    ['视频时长', row => dayjs.duration(row?.duration || 0, 'seconds').format('HH:mm:ss'), { class: 'w-[140px]' }],
    ['转码状态', row => ['', '未转码', '转码中', '转码失败', '转码成功'][row?.transcoding_status || 1], { class: 'w-[90px]' }],
    ['外挂状态', row => ['未外挂', '', '外挂成功'][row?.transcoding_status_v2 || 0], { class: 'w-[90px]' }],
    [
      <span class="px-3">操作</span>,
      (row, rowIndex) => {
        return (
          <div class="flex flex-nowrap">
            { !!row.episode_key
              ? <Button class="btn btn-link btn-sm" onClick={() => onEpisodeTranscode(rowIndex)}>转码</Button>
              : null }
            { !!row.play_path
              ? <Button class="btn btn-link btn-sm" onClick={() => onPreviewM3U8EpisodeListItem(rowIndex)}>m3u8预览</Button>
              : null}
            { !!row.transcoding_status_v2
              ? <Button class="btn btn-link btn-sm" onClick={() => onPreviewExternal(rowIndex)}>外挂预览</Button>
              : null}
            <Button class="btn btn-link btn-sm" onClick={() => onPreviewEpisodeListItem(rowIndex)}>原文件预览</Button>
            <Button class="btn btn-link btn-sm" onClick={() => onDeleteEpisodeListItem(rowIndex)}>删除</Button>
          </div>
        )
      },
      { class: 'w-[260px]' },
    ],
  ]

  watch(
    () => episodeDetails.value?.language_code_list,
    () => {
      if (episodeDetails.value?.language_code_list && episodeDetails.value?.language_code_list?.length > 0) {
        episodeDetails.value.multiversion_list = episodeDetails.value?.multiversion_list?.filter(row => episodeDetails.value?.language_code_list?.includes(row.language_code || '')) || []
        episodeDetails.value?.language_code_list.forEach(language_code => {
          // const hasLanguage = episodeDetails.value?.multiversion_list?.find(row => row.language_code === language_code)
          // if (!hasLanguage) {
          //   switchLanguage(language_code, false)
          // }
        })
      } else {
        episodeDetails.value.multiversion_list = []
      }

      if (episodeDetails.value?.language_code_list?.indexOf(currentLanguage.value?.code || '') === -1 && episodeDetails.value.multiversion_list && episodeDetails.value.multiversion_list.length > 0) {
        currentLanguage.value = languageList.value?.find(i => i.code === episodeDetails.value.multiversion_list?.[0].language_code)
      }
    },
    { immediate: true, deep: true },
  )

  const coverPath = computed(() => {
    if (episodeDetails.value?.cover?.indexOf('http') !== -1) return episodeDetails.value?.cover
    return cdn + episodeDetails.value?.cover?.replace('video/cover/', '')
  })

  const cover2Path = computed(() => {
    if (episodeDetails.value?.cover2?.indexOf('http') !== -1) return episodeDetails.value?.cover2
    return cdn + episodeDetails.value?.cover2?.replace('video/cover/', '')
  })

  const onExportExcel = async () => {
    const XLSX = await import('xlsx')
    const worksheet = XLSX.utils.json_to_sheet(episodeDetails.value?.episode_list || [])
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

    // 生成 Excel 文件
    XLSX.writeFile(workbook, '剧集列表.xlsx')
  }

  onMounted(() => {
    void getLabelList()
    void getLanguageList().then(() => {
      if (route.params.seriesKey === 'create') return
      void getEpisodeDetails(route.params.seriesKey as string, true)
    })
  })

  return () => (
    <MergeClass tag="div" baseClass="block space-y-4 py-4">
      {/* 面包屑导航 */}
      <section class="breadcrumbs text-sm px-4">
        <ul>
          <li><RouterLink to="/episode">剧集管理</RouterLink></li>
          <li>剧集详情</li>
        </ul>
      </section>
      {/* 过滤表单 */}
      <section class="bg-base-100 rounded-lg px-4 pt-4 pb-1">
        <Form
          class="grid gap-y-3 grid-cols-4"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(episodeDetails.value, path, value)
          }}
          items={[
            { label: '状态', path: 'status', input: {
              type: 'select',
              disabled: true,
              options: [
                { label: '草稿', value: 1 },
                { label: '发布', value: 2 },
                { label: '归档', value: 3 },
                { label: '删除', value: 4 },
              ],
            } },
          ]}
          data={episodeDetails.value}
        />
        {
          episodeDetails.value.count
            ? (
                <div class="flex items-center pb-2 gap-x-2">
                  集数/总集数:
                  <span class={episodeDetails.value.episode_list?.length === episodeDetails.value.count ? 'text-[var(--text-1)]' : 'text-[var(--error-3)]'}> {episodeDetails.value.episode_list?.length}
                  </span> / {episodeDetails.value.count}
                  {
                    episodeDetails.value.episode_list?.length !== episodeDetails.value.count
                      ? (
                          <span class="tooltip" data-tip={getEpisodeNumTip()}>
                            <SvgIcon class="-mt-[4px] cursor-pointer" name="ic_question" />
                          </span>
                        )
                      : null
                  }
                </div>
              )
            : null
        }
      </section>
      <section class="bg-base-100 rounded-lg px-4 pt-4 pb-1">
        <Form
          class="grid gap-y-3 grid-cols-4"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            // if (path === 'language_code_list' && (value as string[]).length <= 0) {
            //   showFailToast('请至少选择1种语言')
            // }
            set(episodeDetails.value, path, value)
          }}
          items={[
            { label: requiredLabel('资源ID'), class: 'col-span-1', path: 'serialize_status', transform: transformNumber, input: {
              type: 'custom',
              render: () => episodeDetails.value.series_resource_id ? <a target="_blank" href={`/resource/${episodeDetails.value.series_resource_id}`} class="link link-primary">{episodeDetails.value.series_resource_id} (可跳转)</a> : <div class="text-[var(--text-3)] pl-2">无资源ID</div>,
            } },
            { label: requiredLabel('连载状态'), class: 'col-span-1', path: 'serialize_status', transform: transformNumber, input: {
              type: 'select',
              options: [
                { label: '未完结', value: 1 },
                { label: '已完结', value: 2 },
              ],
            } },
            { label: requiredLabel('总集数'), class: 'col-span-1', path: 'count', input: { type: 'number', placeholder: '请输入总集数' }, transform: transformInteger },
            { label: requiredLabel('解锁集数'), class: 'col-span-1', path: 'unlocked_episodes', input: { type: 'number', placeholder: '请输入解锁集数' }, transform: transformInteger },
            // {
            //   label: requiredLabel('语言'),
            //   path: 'language_code_list',
            //   class: 'col-span-3',
            //   input: {
            //     type: 'multi-select',
            //     search: true,
            //     options: languageList.value.map(i => ({ label: i.name, value: i.code })),
            //     placeholder: '请选择语言',
            //   },
            // },
            // {
            //   label: requiredLabel('默认语言'),
            //   path: 'default_language_code',
            //   class: 'col-span-3',
            //   input: {
            //     type: 'radio',
            //     options: languageList.value
            //       .filter(i => episodeDetails.value?.language_code_list?.includes(i.code || ''))
            //       .map(i => ({ label: i.name, value: i.code })),
            //   },
            // },
          ]}
          data={episodeDetails.value}
        />
      </section>
      <section class="bg-base-100 rounded-lg px-4 pt-4 pb-1 flex flex-col gap-y-4">
        <div role="tablist" class="tabs tab-sm tabs-boxed bg-white gap-x-2 flex">
          {
            episodeDetails.value?.multiversion_list?.map(item => (
              <a
                role="tab"
                class={mc('tab w-40 bg-gray-300 text-black', currentLanguage.value?.code === item.language_code ? 'tab-active' : '')}
                onClick={() => switchLanguage(item.language_code || '')}
              >
                {languageList.value.find(i => i.code === item.language_code)?.name}
              </a>
            ))
          }
        </div>
        <LanguageEpisodesForm
          class="grid gap-y-3 grid-cols-6"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(episodeDetails.value, path, value)
          }}
          items={[
            { label: requiredLabel('剧名'), path: 'title', input: { type: 'textarea', disabled: route.params.seriesKey !== 'create', maxlength: 255, placeholder: '请输入剧名, 最多255个字符' }, class: 'col-span-3' },
            { label: requiredLabel('剧简介'), path: 'description', input: { type: 'textarea', disabled: route.params.seriesKey !== 'create', maxlength: 1000, placeholder: '请输入剧简介, 最多1000个字符', class: 'max-w-full' }, class: 'col-span-3' },
            { label: requiredLabel('语言'), path: 'default_language_code', input: { type: 'select', options: languageList.value.map(i => ({ label: i.name, value: i.code })), disabled: route.params.seriesKey !== 'create' && episodeDetails.value.source_type !== 2 }, class: 'col-span-2' },
            { label: '国家/地区', path: 'default_language_code', input: { type: 'custom', render: () => <x-country class="py-2">{languageList.value.find(i => i.code === episodeDetails.value.default_language_code)?.area}</x-country> }, class: 'col-span-2' },
            { label: '', path: 'root_label_ids', class: 'col-span-2',
              input: {
                type: 'custom',
                render: () => (
                  <div class="space-y-2">
                    <div class="space-x-1 items-center flex-wrap flex">
                      一级标签：{
                        episodeDetails.value.root_label_ids && episodeDetails.value.root_label_ids.length > 0
                          ? episodeDetails.value.root_label_ids.map(id => {
                            const curLabel = rootLabels.value.find(label => label.label_id === +id)
                            return (
                              <div class="tooltip tooltip-top" data-tip={curLabel?.meaning || '-'}>
                                <div class="badge badge-outline">{curLabel?.content}</div>
                              </div>
                            )
                          })
                          : <div>暂无标签</div>
                      }
                    </div>
                    <div class="space-x-1 items-center flex-wrap flex">
                      二级标签：{
                        episodeDetails.value.first_label_ids && episodeDetails.value.first_label_ids.length > 0
                          ? episodeDetails.value.first_label_ids.map(id => {
                            const curLabel = firstLabels.value.find(label => label.label_id === +id)
                            return (
                              <div class="tooltip tooltip-top" data-tip={curLabel?.meaning || '-'}>
                                <div class="badge badge-outline">{curLabel?.content}</div>
                              </div>
                            )
                          })
                          : <div>暂无标签</div>
                      }
                    </div>
                    <div class="space-x-1 items-center flex-wrap flex">
                      三级标签：{
                        episodeDetails.value.second_label_ids && episodeDetails.value.second_label_ids.length > 0
                          ? episodeDetails.value.second_label_ids.map(id => {
                            const curLabel = allLabels.value.find(label => label.label_id === +id)
                            return (
                              <div class="tooltip tooltip-top" data-tip={curLabel?.meaning || '-'}>
                                <div class="badge badge-outline">{LABEL_OPTIONS.find(o => o.value === curLabel?.content_type)?.label}/{curLabel?.content}</div>
                              </div>
                            )
                          })
                          : <div>暂无标签</div>
                      }
                    </div>
                  </div>
                ),
              },
            },
            {
              label: requiredLabel('封面'),
              path: 'cover',
              input: {
                type: 'custom',
                render: () => (
                  <Uploader
                    class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                    maxsize={1 * 1024 * 1024}
                    accept="jpg,png,jpeg,gif"
                    onUploadSuccess={d => {
                      episodeDetails.value.cover = d.temp_path
                    }}
                    isImage={true}
                  >
                    {episodeDetails.value?.cover
                      ? <img src={coverPath.value} class="size-full object-contain" />
                      : <span class="size-full flex items-center justify-center">上传封面</span>}
                  </Uploader>
                ),
              },
            },
            {
              label: requiredLabel('封面2'),
              path: 'cover2',
              input: {
                type: 'custom',
                render: () => (
                  <Uploader
                    class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                    maxsize={1 * 1024 * 1024}
                    accept="jpg,png,jpeg,gif"
                    onUploadSuccess={d => {
                      episodeDetails.value.cover2 = d.temp_path
                    }}
                    isImage={true}
                  >
                    {episodeDetails.value?.cover2
                      ? <img src={cover2Path.value} class="size-full object-contain" />
                      : <span class="size-full flex items-center justify-center">上传封面</span>}
                  </Uploader>
                ),
              },
            },
          ]}
          data={episodeDetails.value || {}}
        />

      </section>
      {/* table */}
      <section class="bg-base-100 rounded-lg pt-4 pb-4">
        <div class="px-4">
          <x-table-actions class="flex items-center justify-between">
            单集列表
            <div class="space-x-2">
              {
                !episodeDetails.value.series_key ? null : <Button class="btn btn-primary btn-sm" onClick={() => onAddEpisodeListItem(episodeDetails.value.series_key!, episodeDetails.value.count)}>生成虚拟剧集</Button>
              }
              <Button class="btn btn-primary btn-sm" onClick={onExportExcel}>导出Excel</Button>
              {
                checked.value.length > 0 ? <Button class="btn btn-primary btn-sm" onClick={onEpisodeBatchTranscode}>批量转码</Button> : ''
              }
            </div>
          </x-table-actions>
        </div>
        <hr class="my-4" />
        <Table class="tm-table-fix-last-column" list={episodeDetails.value?.episode_list || []} columns={columns} />
        <div class="truncate text-xs text-error-6 px-4">{error.value?.['episode_list']?.[0]?.message ?? <Empty />}</div>
      </section>
      {/* <section class="bg-base-100 rounded-lg pt-4 pb-4">
        <div class="px-4">
          <x-table-actions class="flex items-center justify-between">
            单集配置列表 - 字幕/音频
            <div class="space-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => onUploadCaption()}>批量上传字幕</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => onUploadAudio()}>批量上传音频</Button>
            </div>
          </x-table-actions>
        </div>
        <hr class="my-4" />
        <VersionInfoTable
          class="tm-table-fix-last-column"
          list={getCurrentLanguageEpisodeConfig()?.data_list || []}
          columns={VersionInfoColumns}
        />
        <div class="truncate text-xs text-error-6 px-4">{error.value?.['episode_list']?.[0]?.message ?? <Empty />}</div>
      </section> */}
      <section class="bg-base-100 rounded-lg px-4 pt-4 pb-4 flex justify-end gap-x-2 sticky bottom-0">
        <Button class="btn btn-ghost" onClick={() => router.back()}>取消</Button>
        {[EpisodeStatus.draft, EpisodeStatus.published].includes(episodeDetails.value?.status ?? 0) || !episodeDetails.value?.id
          ? <Button class="btn btn-ghost" onClick={() => onDraftSave(episodeDetails.value?.status || 0)}>保存草稿</Button>
          : null}
        {episodeDetails.value.status !== EpisodeStatus.switched
        && <Button class="btn btn-primary" onClick={onPublish}>发布</Button>}
        {episodeDetails.value?.id && (
          <div class="dropdown dropdown-hover dropdown-top dropdown-end">
            <div tabindex="0" role="Button" class="btn"><Icon name="ant-design:ellipsis-outlined" /></div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-up w-52 p-2 shadow">
              {/* <li onClick={onCopy}><a>复制</a></li> */}
              <li onClick={onRemove}><a>删除</a></li>
            </ul>
          </div>
        )}
      </section>
      {/* pager */}
    </MergeClass>
  )
})

export default EpisodeDetails
