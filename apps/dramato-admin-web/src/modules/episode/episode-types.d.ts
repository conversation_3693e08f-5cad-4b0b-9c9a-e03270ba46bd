declare namespace M {

  interface EpisodeLanguage {
    code: string
    name: string
    area: string
  }

  interface Episode {
    drama_id?: number
    id?: number
    series_key?: string
    title?: string
    description?: string
    labels?: string
    count?: number// 单集数量
    status?: number // 状态  1 草稿 2 已发布 3 已归档 4 已删除
    created?: number// 创建时间
    updated?: number// 更新时间
    default_language_code?: string // 默认语言
    languages?: string // 语言列表
    cover?: string
    cover2?: string
    transcoding_status?: number
    real_count?: number // 单集数量
    series_resource_id?: number
    series_resource_title?: string
    series_key_or_title?: string
    resource_id_or_title?: string
    start_paying_episodes?: number
    episodes_price?: number
    transcoding_count?: {
      [record: string]: number
    }
    release_round?: number
    preview_real_count?: number
    preview_count?: number
    title2?: string
  }

  interface EpisodeSearchForm {
    language_code?: string
    title?: string
    status?: number // 状态 1 草稿 2 已发布 3 已归档 4 已删除,
    series_key?: string
    series_resource_id?: number
    series_resource_title?: string
    source_types?: number[]
    series_key_or_title?: string
    resource_id_or_title?: string
    series_key_or_title_list?: string[]
    resource_id_or_title_list?: string[]
    release_round?: 1 | 2
  }

  interface PageOption {
    page_index: number
    page_size: number
  }

  type ApiGetEpisodeRequestParams = EpisodeSearchForm & PageOption

  interface EpisodeSeries {
    id?: number
    episode_key?: string
    play_path?: string
    source_path?: string
    serial_number: number // 序号
    file_name: string
    duration?: number // 秒
    temp_path?: string // 文件上传到oss的地址
    modify_status?: 0 | 1 // 0 不变 1 新增
    transcoding_status?: 1 | 2 | 3 | 4 // 转码状态 1 未转码 2 转码中 3 转码失败 4 转码成功
    cover?: string
    region?: [number, number, number, number]
    subtitle_list?: {
      language: string
      type: string
      subtitle: string
    }[]
    play_path_v2?: string
    play_path_ext_v2?: string
    // 2024.11.29（新增）外挂音频状态 0：未外挂 2：外挂成功
    transcoding_status_v2?: number
  }

  interface AudioInfo {
    file_name: string
    duration?: number // 秒
    temp_path?: string // 文件上传到oss的地址
    modify_status?: 0 | 1 // 0 不变 1 新增
  }

  interface CaptionInfo {
    file_name: string
    duration?: number // 秒
    temp_path?: string // 文件上传到oss的地址
    modify_status?: 0 | 1 // 0 不变 1 新增
  }

  interface EpisodeMultiVersionInfo {
    serial_number?: number
    audio_info?: AudioInfo
    caption_info?: CaptionInfo
    cover?: string
  }

  interface EpisodeMultiVersion {
    multiversion_key?: string // 新增则没此key
    language_code?: string
    area?: string
    title?: string
    description?: string
    labels?: string | string[]
    serialize_status?: number // 连载状态  1 未完结 2 已完结
    cover?: string
    data_list?: EpisodeMultiVersionInfo[]
  }

  interface EpisodeDate {
    id?: number
    series_key?: string
    title?: string
    description?: string
    labels?: string | string[]
    serialize_status?: 1 | 2 // 连载状态  1 未完结 2 已完结
    cover?: string
    cover2?: string
    count?: number // 单集数量
    unlocked_episodes?: number // 解锁剧集
    status?: number // 状态 1 草稿 2 发布 3 归档 4 删除
    episode_list?: EpisodeSeries[]
    created?: number // 创建时间
    updated?: number // 更新时间
    default_language_code?: string // 默认语言
    multiversion_list?: EpisodeMultiVersion[]
    language_code_list?: string[]
    source_type?: number
    series_resource_id?: number
    serial_number?: number // 序号
    // 弹幕外挂新加
    region?: [number, number, number, number]
    subtitle_list?: {
      language: string
      type: string
      subtitle: string
    }[]
    play_path_v2?: string
    play_path_ext_v2?: string
    // 2024.11.29（新增）外挂音频状态 0：未外挂 2：外挂成功
    transcoding_status_v2?: number
    root_label_ids?: number[]
    first_label_ids?: number[]
    second_label_ids?: number[]
  }

  interface EpisodeTag {
    name: string
    id: number
  }

  interface ApiEpisodeTranscodeRequestParams {
    series_key: string
    episode_keys: string[]
    type: 1 | 2 | 3 | number
  }

  interface ApiSeriesFillEpisodeBatchRequestParams {
    series_keys?: string[]
    req_items: [
      {
        series_key: string
        start_seq?: number// 开始填充的集序号，闭区间；不填写则默认为付费剧集区间的第一个序号
        end_seq?: number// 结束填充的集序号，闭区间，以上就是填充[5-20]集为空单集，区间里面如果有的被填充/上传了，不重复填充;需要小于总集数，如果大于总集数，则填充start_seq到count的单集；不填写则用count
        count?: number// 优先用end_seq，没end_seq才用此参数；start_seq开始(包含)，填充count集[即start_seq+count-1]；若end_seq和count都为空，则默认填充到总集数；需要小于总集数，如果大于总集数，则填充start_seq到总集数的单集；
      },
    ]
  }
}
