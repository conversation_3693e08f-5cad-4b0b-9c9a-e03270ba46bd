export const getM3u8Content = (m3u8Content: string) => {
  const lines = m3u8Content.split('\n')
  const regex = /#EXT-X-STREAM-INF:.*?BANDWIDTH=(\d+).*?RESOLUTION=(\d+)x(\d+)/g
  const matches: { bandwidth: string, resolutionWidth: string, filename: string }[] = []
  lines.forEach((line, index) => {
    let match
    while ((match = regex.exec(line)) !== null) {
      matches.push({ bandwidth: match[1], resolutionWidth: '' + Math.min(+match[2], +match[3]), filename: lines[index + 1] })
    }
  })
  return matches
}

export const getSubtitle = (m3u8Content: string) => {
  // 正则表达式来匹配所有的 NAME 和 URI
  const regex = /#EXT-X-MEDIA:TYPE=SUBTITLES.*NAME="([^"]+)".*LANGUAGE="([^"]+)".*URI="([^"]+)"/g

  const matches = Array.from(m3u8Content.matchAll(regex))

  const result = []
  let index = -1
  // 遍历所有的匹配结果
  for (const match of matches) {
    const nameValue = match[2]
    const uriValue = match[3]
    const lang = match[1]
    console.log('NAME:', nameValue) // 输出不同的字幕名称
    console.log('URI:', uriValue) // 输出对应的 URI
    index++
    result.push({
      isDefault: index === 0 ? true : false,
      id: nameValue,
      filename: uriValue,
      label: nameValue,
      language: lang,
      text: nameValue,
    })
  }
  return result
}
