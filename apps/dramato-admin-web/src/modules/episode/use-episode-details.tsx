/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, nextTick } from 'vue'
import { apiEditEpisode, apiGetEpisodeData, apiGetTag, apiEpisodeBatchTranscode, apiGetLanguageList, apiSeriesFillEpisodeBatch } from './episode-api'
import { openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { EpisodeStatus } from './const'
import { useEpisode } from './use-episode'
import { Uploader, UploadImage } from '../common/uploader/uploader'
import { getM3u8Content } from './m3u8-helper.ts'
import axios from 'axios'
import { cloneDeep } from 'lodash-es'
import { M3u8Player } from 'src/modules/resource/components/m3u8-player'

export const cdn = 'https://video-v1.mydramawave.com/video/cover/'
export const videoCdn = 'https://video-v1.mydramawave.com/'

const episodeDetails = ref<M.EpisodeDate>({
  unlocked_episodes: 1,
  default_language_code: 'en-US',
  language_code_list: ['en-US'],
})
const tags = ref<M.EpisodeTag[]>([])
const checked = ref<string[]>([])
const isCheckAll = ref<boolean>(false)
const languageList = ref<M.EpisodeLanguage[]>([])
const currentLanguage = ref<M.EpisodeLanguage>()
const currentLanguageEpisodesIndex = ref<number>(0)

const switchLanguage = (language_code: string, doSwitch = true) => {
  const language = languageList.value.find(row => row.code === language_code)

  let episodeIndex = episodeDetails.value?.multiversion_list?.findIndex(row => row.language_code === language_code) as number

  if (episodeIndex === undefined || episodeIndex === -1) {
    const episode = {
      language_code: language_code,
      area: language?.area,
      title: '',
      description: '',
      cover: '',
      data_list: (episodeDetails.value?.episode_list || []).map(i => {
        return {
          serial_number: i.serial_number,
        }
      }),
    }
    episodeDetails.value = {
      ...episodeDetails.value,
      multiversion_list: [
        ...(episodeDetails.value?.multiversion_list || []),
        episode,
      ],
    }
    episodeIndex = (episodeDetails.value?.multiversion_list || [])?.length - 1
  }
  if (episodeDetails.value.multiversion_list && (!episodeDetails.value?.multiversion_list?.[episodeIndex].data_list || (episodeDetails.value?.multiversion_list?.[episodeIndex].data_list || []).length === 0)) {
    episodeDetails.value.multiversion_list[episodeIndex].data_list = (episodeDetails.value?.episode_list || []).map(i => {
      return {
        serial_number: i.serial_number,
      }
    }) || []
  }

  if (doSwitch) {
    currentLanguage.value = language
    currentLanguageEpisodesIndex.value = episodeIndex
    episodeDetails.value.multiversion_list && (episodeDetails.value.multiversion_list[episodeIndex].area = language?.area)
  }
}

const renderEpisodeDetails = (rs: ApiResponse<M.EpisodeDate>, doSwitch = true) => {
  // const code = rs.data?.default_language_code || rs.data?.multiversion_list?.[0]?.language_code || 'zh-CN'
  episodeDetails.value = rs.data || {}
  episodeDetails.value.labels = episodeDetails.value.labels ? (episodeDetails.value.labels as string).split(',') : []
  episodeDetails.value?.episode_list?.forEach(item => {
    if (Number(item.duration) <= 0) {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.src = item.source_path || ''
      video.onloadedmetadata = () => {
        item.duration = Math.round(video.duration)
      }
    }
  })
  // episodeDetails.value?.multiversion_list?.forEach(item => {
  //   const l = currentLanguage.value = languageList.value.find(row => row.code === item.language_code)
  //   item.labels = ((item.labels || '') as string).split(',')
  //   item.area = l?.area
  // })
  // episodeDetails.value.language_code_list = (episodeDetails.value?.multiversion_list?.map(i => i.language_code) || []) as string[]
  // if (!episodeDetails.value?.default_language_code) {
  //   episodeDetails.value.default_language_code = code
  // }
  // switchLanguage(code, doSwitch)
}

const getEpisodeDetails = async (seriesKey: string, doSwitch = true) => {
  // if (seriesKey === 'create') {
  //   switchLanguage('zh-CN', true)
  //   return
  // }
  const rs = await apiGetEpisodeData({ series_key: seriesKey })
  renderEpisodeDetails(rs, doSwitch)
}

const getTags = async () => {
  const rs = await apiGetTag()
  tags.value = rs.data?.labels || []
}

const onDeleteEpisodeListItem = (idx: number) => {
  const item = episodeDetails.value?.episode_list?.[idx]
  const hideDeleteDialog = openDialog({
    title: '删除',
    mainClass: 'pb-0 px-5',
    body: (
      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-delete-episode-body>确认删除单集【{item?.file_name}】吗？</x-delete-episode-body>
        <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            episodeDetails.value?.episode_list?.splice(idx, 1)
            hideDeleteDialog()
          }}
          >确定
          </button>
        </x-delete-episode-footer>
      </x-delete-episode-confirm-dialog>
    ),
  })
}

const onPreviewEpisodeListItem = (idx: number) => {
  const videoUrl = episodeDetails.value?.episode_list?.[idx]?.source_path || episodeDetails.value?.episode_list?.[idx]?.temp_path
  let videoPath = ''
  if (videoUrl && videoUrl.indexOf('http') > -1) {
    videoPath = videoUrl
  } else {
    videoPath = videoCdn + videoUrl
  }
  // TODO
  openDialog({
    title: `第${episodeDetails.value?.episode_list?.[idx].serial_number}集`,
    body: <video class="w-[309] h-[550px]" src={videoPath} controls />,
    customClass: '!w-[340px]',
  })
}

const onPreviewExternal = (idx: number) => {
  const seriesObj = episodeDetails.value?.episode_list?.[idx] as M.EpisodeDate
  if (seriesObj?.transcoding_status_v2 !== 2) {
    showFailToast('暂无外挂视频')
    return
  }
  openDialog({
    title: `第${seriesObj.serial_number}集`,
    mainClass: 'px-4 !py-0',
    customClass: '!w-[400px]',
    body: () => (
      <M3u8Player subtitles={seriesObj.subtitle_list} url={seriesObj.play_path_v2 as string} />
    ),
  })
}

const onPreviewM3U8EpisodeListItem = async (idx: number) => {
  let player: any = null
  openDialog({
    title: `第${episodeDetails.value?.episode_list?.[idx].serial_number}集`,
    beforeClose: () => {
      player && player.destroy()
    },
    body: () => {
      return (
        <>
          <div id="mse" class="m-auto w-[350px] h-[550px]" />
        </>
      )
    },
  })
  await nextTick()
  let url = ''
  if (episodeDetails.value?.episode_list?.[idx]?.play_path && episodeDetails.value?.episode_list?.[idx]?.play_path.indexOf('http') > -1) {
    url = episodeDetails.value?.episode_list?.[idx]?.play_path
  } else {
    url = videoCdn + episodeDetails.value?.episode_list?.[idx]?.play_path
  }
  const resp = await axios.get(url)
  const quality: { [record: string]: string } = {}
  getM3u8Content(resp.data).sort((a, b) => {
    return Number(a.resolutionWidth) - Number(b.resolutionWidth)
  }).map(row => {
    const changeVideoDefinition = (url: string, filename: string) => {
      return url.replace(/[^/]+\.m3u8$/, filename)
    }
    quality[`${+row.resolutionWidth === 540 ? 'FD' : +row.resolutionWidth === 720 ? 'LD' : 'HD'}`] = changeVideoDefinition(url, row.filename)
  })

  player = new (window as any).Aliplayer({
    id: 'mse',
    width: 350,
    height: 550,
    source: JSON.stringify(quality),
    language: 'zh-cn',
    enableH265: true,
    definition: 'FD,LD,HD',
    defaultDefinition: 'FD',
    extLanguageTexts: {
      'zh-cn': {
        HD: '1080p',
        LD: '720p',
        FD: '540p',
      },
    },
  }, (e: any) => {
    player = e
  })
  return player
}

let originEpisodeList: M.EpisodeMultiVersion[] = []

const transcode = async ({ episode_keys, series_key, type }: { episode_keys: string[], series_key: string, type: 1 | 2 | 3 | number }) => {
  try {
    await apiEpisodeBatchTranscode({
      series_key,
      episode_keys,
      type,
    })

    originEpisodeList = cloneDeep(episodeDetails.value.multiversion_list || [])

    const rs = await apiGetEpisodeData({ series_key })
    const code = rs.data?.default_language_code || rs.data?.multiversion_list?.[0]?.language_code || 'en-US'
    episodeDetails.value = rs.data || {}
    episodeDetails.value?.episode_list?.forEach(item => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.src = item.source_path || ''
      video.onloadedmetadata = () => {
        item.duration = Math.round(video.duration)
      }
    })
    episodeDetails.value.multiversion_list = originEpisodeList || []
    episodeDetails.value.language_code_list = (episodeDetails.value?.multiversion_list?.map(i => i.language_code) || []) as string[]
    if (!episodeDetails.value?.default_language_code) {
      episodeDetails.value.default_language_code = code
    }
    showSuccessToast('操作成功')
  } catch (error: any) {
    showFailToast(error?.response?.data?.message)
  }
}

const onEpisodeTranscode = (idx: number) => {
  const episode_keys = [episodeDetails.value?.episode_list?.[idx]?.episode_key] as string[]
  const series_key = episodeDetails.value.series_key as string
  const type = ref(3)
  const hideTransCodeDialog = openDialog({
    title: '转码',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-trans-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <select class="select select-bordered select-sm" value={type.value} onChange={e => {
          const target = e.target as HTMLSelectElement
          const value = target.value
          type.value = +value
        }}
        >
          <option value={3}>腾讯</option>
          <option value={2}>阿里点播</option>
          <option value={1}>阿里边转边播</option>
          <option value={4}>腾讯增强转码(价格较贵)</option>
        </select>
        <x-trans-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideTransCodeDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await transcode({
                episode_keys,
                series_key,
                type: type.value,
              })
            } catch (error: any) {
              showFailToast(error.response.data.message || '转码失败')
            } finally {
              hideTransCodeDialog()
            }
          }}
          >确定
          </button>
        </x-trans-episode-footer>
      </x-trans-episode-confirm-dialog>
    ),
  })
}

const onEpisodeBatchTranscode = () => {
  const series_key = episodeDetails.value.series_key as string
  const type = ref(3)
  const hideTransCodeDialog = openDialog({
    title: '批量转码',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-trans-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <select class="select select-bordered select-sm" value={type.value} onChange={e => {
          const target = e.target as HTMLSelectElement
          const value = target.value
          type.value = +value
        }}
        >
          <option value={3}>腾讯</option>
          <option value={2}>阿里点播</option>
          <option value={1}>阿里边转边播</option>
          <option value={4}>腾讯增强转码(价格较贵)</option>
        </select>
        <x-trans-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideTransCodeDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await transcode({
                episode_keys: checked.value,
                series_key,
                type: type.value,
              })
            } catch (error: any) {
              showFailToast(error.response.data.message || '转码失败')
            } finally {
              hideTransCodeDialog()
            }
          }}
          >确定
          </button>
        </x-trans-episode-footer>
      </x-trans-episode-confirm-dialog>
    ),
  })
}

const onAddEpisodeListItem = (series_key: string, count?: number) => {
  void apiSeriesFillEpisodeBatch({
    req_items: [
      {
        series_key: series_key,
        start_seq: 1, // 开始填充的集序号，闭区间；不填写则默认为付费剧集区间的第一个序号
        count,
      },
    ],
  }).then(() => {
    void getEpisodeDetails(series_key)
  })
}

const checkFile = (file: any) => {
  const episode = episodeDetails.value?.episode_list?.find(row => {
    return Number((row.file_name || '').match(/\d+/g)?.[0] || 1) === Number((file.file?.name || '').match(/\d+/g)?.[0] || 1)
  })

  return !!episode
}

const getCurrentEpisodeSerialNumber = (file_name: string) => {
  const episode = episodeDetails.value?.episode_list?.find(row => {
    return Number((row.file_name || '').match(/\d+/g)?.[0] || 1) === Number((file_name || '').match(/\d+/g)?.[0] || 1)
  })

  return episode?.serial_number
}

const getCurrentLanguageEpisodeConfig = () => (episodeDetails.value?.multiversion_list || [])[currentLanguageEpisodesIndex.value]

const getCurrentLanguageEpisode = (file_name: string) => {
  const serial_number = getCurrentEpisodeSerialNumber(file_name || '')
  const currentLanguageEpisodeConfig = getCurrentLanguageEpisodeConfig()
  const languageEpisode = currentLanguageEpisodeConfig?.data_list?.find(row => row.serial_number === serial_number) || {}
  return languageEpisode
}

const onUploadCaptionSuccess = (file: UploadImage) => {
  const languageEpisode = getCurrentLanguageEpisode(file.file?.name || '')
  languageEpisode.caption_info = {
    file_name: file.file?.name || '',
    temp_path: file.temp_path,
    modify_status: 1,
  }
}

const onUploadAudioSuccess = (file: UploadImage) => {
  const languageEpisode = getCurrentLanguageEpisode(file.file?.name || '')
  languageEpisode.audio_info = {
    file_name: file.file?.name || '',
    temp_path: file.temp_path,
    modify_status: 1,
  }
}

const onUploadSingleCaptionSuccess = (file: UploadImage, index: number) => {
  const currentLanguageEpisodeConfig = getCurrentLanguageEpisodeConfig()
  const languageEpisode = currentLanguageEpisodeConfig?.data_list?.[index] || {}
  languageEpisode.caption_info = {
    file_name: file.file?.name || '',
    temp_path: file.temp_path,
    modify_status: 1,
  }
}

const onUploadSingleAudioSuccess = (file: UploadImage, index: number) => {
  const currentLanguageEpisodeConfig = getCurrentLanguageEpisodeConfig()
  const languageEpisode = currentLanguageEpisodeConfig?.data_list?.[index] || {}
  languageEpisode.audio_info = {
    file_name: file.file?.name || '',
    temp_path: file.temp_path,
    modify_status: 1,
  }
}

const onUploadCaption = () => {
  openDialog({
    title: '上传字幕',
    body: (
      <Uploader
        accept="vtt,srt"
        isImage={false}
        maxsize={1024 * 1024 * 1}
        multiple
        checkFile={checkFile}
        onUploadSuccess={onUploadCaptionSuccess}
      >
        <x-uploader-wrapper class="w-full h-[120px] flex flex-col gap-y-4 justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
          <p>上传字幕, 支持VTT, SRT格式</p>
          <p>上传文件需要确保字幕文件名和视频对应</p>
        </x-uploader-wrapper>
      </Uploader>
    ),
  })
}

const onUploadAudio = () => {
  openDialog({
    title: '上传音频',
    body: (
      <Uploader
        accept="mp4,mp3"
        isImage={false}
        maxsize={1024 * 1024 * 100}
        multiple
        checkFile={checkFile}
        onUploadSuccess={onUploadAudioSuccess}
      >
        <x-uploader-wrapper class="w-full h-[120px] flex flex-col gap-y-4 justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
          <p>上传音频, 支持MP3, MP4格式</p>
          <p>上传文件需要确保音频文件名和视频对应</p>
        </x-uploader-wrapper>
      </Uploader>
    ),
  })
}

const onCopy = async () => {
  try {
    await apiEditEpisode({
      ...episodeDetails.value,
      status: EpisodeStatus.published,
      id: undefined,
      series_key: undefined,
      // multiversion_list: (episodeDetails.value?.multiversion_list || []).map(i => ({
      //   ...i,
      //   labels: (i.labels as string[] || []).join(','),
      // })),
      // default_language_code: currentLanguage.value?.code || 'zh-CN',
    })
    showSuccessToast('复制成功')
  } catch (error: any) {
    showFailToast(error?.response?.data?.message || '复制成功')
  }
}

const onRemove = () => {
  if (!episodeDetails.value.series_key) {
    return
  }
  const episodeStore = useEpisode()
  episodeStore.onRemove(episodeDetails.value?.series_key, false, episodeDetails.value?.multiversion_list?.[0]?.title)
}

const updateEpisodeState = async (status: number) => {
  if (!episodeDetails.value?.default_language_code) {
    episodeDetails.value.default_language_code = currentLanguage.value?.code
  }
  const rs = await apiEditEpisode({
    ...episodeDetails.value,
    labels: (episodeDetails.value.labels as string[] || []).join(','),
    status,
    // multiversion_list: (episodeDetails.value?.multiversion_list || []).map(i => ({
    //   ...i,
    //   labels: (i.labels as string[] || []).join(','),
    // })),
    // default_language_code: currentLanguage.value?.code || 'zh-CN',
  })

  renderEpisodeDetails(rs)
}

const saveDraft = async () => {
  try {
    await updateEpisodeState(EpisodeStatus.draft)
    showSuccessToast('保存成功')
  } catch (error: any) {
    showFailToast(error?.response?.data?.message || '保存失败')
  }
}

const onDraftSave = (status: number) => {
  if (status === 2) {
    const hideDeleteDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>是否确定保存草稿</x-delete-episode-body>
          <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void saveDraft()
              hideDeleteDialog()
            }}
            >确定
            </button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  } else {
    void saveDraft()
  }
}

const onArchive = async () => {
  try {
    await updateEpisodeState(EpisodeStatus.archived)
    showSuccessToast('归档成功')
  } catch (error: any) {
    showFailToast(error?.response?.data?.message || '归档失败')
  }
}

const onPublish = async () => {
  try {
    await updateEpisodeState(EpisodeStatus.published)
    showSuccessToast('发布成功')
  } catch (error: any) {
    showFailToast(error?.response?.data?.message || '发布失败')
  }
}

const checkedAllChange = (value: boolean) => {
  isCheckAll.value = value
  const ids = episodeDetails.value?.episode_list?.filter(row => !!row.episode_key).map(row => row.episode_key as string) || []
  if (isCheckAll.value) {
    checked.value = ids
  } else {
    checked.value = []
  }
}

const getLanguageList = async () => {
  const rs = await apiGetLanguageList()
  languageList.value = rs.data?.list || []
}

export const useEpisodeDetails = () => {
  return {
    tags,
    getTags,
    episodeDetails,
    getEpisodeDetails,
    onDeleteEpisodeListItem,
    onPreviewEpisodeListItem,
    onPreviewM3U8EpisodeListItem,
    onEpisodeTranscode,
    onEpisodeBatchTranscode,
    onCopy,
    onRemove,
    onArchive,
    onPublish,
    onDraftSave,
    onAddEpisodeListItem,
    checkedAllChange,
    checked,
    isCheckAll,
    getLanguageList,
    languageList,
    currentLanguage,
    switchLanguage,
    currentLanguageEpisodesIndex,
    onUploadCaption,
    onUploadAudio,
    onUploadCaptionSuccess,
    onUploadAudioSuccess,
    onUploadSingleCaptionSuccess,
    onUploadSingleAudioSuccess,
    getCurrentLanguageEpisodeConfig,
    onPreviewExternal,
  }
}
