import { httpClient } from 'src/lib/http-client'
import { filterIt, splitIt } from '@skynet/shared'

const arrToString = (value: number[] | string[]) => {
  if (!value || ('' + value.length).length === 0) {
    return ''
  } else {
    return value.join(',')
  }
}

export const apiGetEpisode = (data: M.ApiGetEpisodeRequestParams) =>
  httpClient.post<ApiResponse<{
    list: M.Episode[]
    total: number
  }>>('/episode_series/search', data)

export const apiEditEpisodeStatus = (data: { series_key: string, operate_status: number }) =>
  httpClient.post<ApiResponse<null>>('/episode_series/edit_status', data)

export const apiGetTag = () =>
  httpClient.get<ApiResponse<{
    labels: M.EpisodeTag[]
  }>>('/episode_series/labels')

export const apiEditEpisode = (d: M.EpisodeDate) => httpClient.post<ApiResponse<M.EpisodeDate>>('/episode_series/create', d, {
  transformRequestData: {
    root_label_ids: [arrToString],
    first_label_ids: [arrToString],
    second_label_ids: [arrToString],
  },
})

export const apiGetEpisodeData = (d: { series_key: string }) => httpClient.get<ApiResponse<M.EpisodeDate>>('/episode_series/detail', d, {
  transformResponseData: {
    'data.first_label_ids': [splitIt([',']), filterIt(Boolean)],
    'data.root_label_ids': [splitIt([',']), filterIt(Boolean)],
    'data.second_label_ids': [splitIt([',']), filterIt(Boolean)],
  },
})

export const apiEpisodeBatchTranscode = (d: M.ApiEpisodeTranscodeRequestParams) => httpClient.post<ApiResponse<M.EpisodeDate>>('/episode_series/episode_transcoding_batch', d)

export const apiGetLanguageList = () => httpClient.get<ApiResponse<{
  list: Array<M.EpisodeLanguage>
}>>('/episode_series/language_list')

export const apiSeriesFillEpisodeBatch = (data: M.ApiSeriesFillEpisodeBatchRequestParams) => httpClient.post<ApiResponse<unknown>>('/episode_series/series_fill_episode_batch', data)

export const apiBatchSeriesTransCode = (data: { series_keys: string[], type: 1 | 2 | 3 | number }) => httpClient.post<ApiResponse<unknown>>('/episode_series/series_transcoding_batch', data)
