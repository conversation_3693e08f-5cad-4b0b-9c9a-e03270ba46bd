import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, Pager, showSuccessToast, transformNumber, Icon } from '@skynet/ui'
import dayjs from 'dayjs'
import { set } from 'lodash-es'
import { watch, ComponentPublicInstance, onMounted, onUnmounted, ref } from 'vue'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { langKey, langValue } from 'src/modules/resource/constant'
import { RouterLink, useRouter } from 'vue-router'
import { EpisodeStatus } from './const'
import { useEpisode } from './use-episode'
import { useEpisodeDetails } from './use-episode-details'
import { useClipboard } from '@vueuse/core'
import { renderReleaseTitle } from 'src/modules/resource-publish/util'
import { releaseRounds } from 'src/modules/resource-publish/constant'

type EpisodePageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.Episode[]
    disableEpisodes?: string[]
    hasNav?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.Episode) => void
    remove: (id: M.Episode) => void
  }
}

export const EpisodePage = createComponent<EpisodePageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    disableEpisodes: [],
  },
  emits: {
    add: (item: M.Episode) => {},
    remove: (item: M.Episode) => {},
  },
}, (props, { emit }) => {
  const { searchForm, onSearch, onReset, page, total, pageSize, list, loading, onRemove, onPublish, onTransCode, onPageChange, onPageSizeChange } = useEpisode()
  const { episodeDetails } = useEpisodeDetails()
  const router = useRouter()
  const Form = CreateForm<M.EpisodeSearchForm>()
  const Table = CreateTableOld<M.Episode>()
  const wrapperRef = ref<ComponentPublicInstance>()
  const { copy, copied } = useClipboard()

  watch(() => copied.value, () => {
    if (copied.value) {
      showSuccessToast('复制成功')
    }
  })

  const columns: TableColumnOld<M.Episode>[] = [
    [
      '',
      row => {
        const id = row.id
        return (
          <Checkbox
            label=""
            disabled={!!!id || props.disableEpisodes.includes(row.series_key)}
            modelValue={props.checkedItem.map(i => i.drama_id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.id).includes(id)) emit('add', row)
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['剧集ID', row => <div class="flex cursor-pointer items-center" onClick={() => copy(row.series_key)}><Icon class="shrink-0 w-4 h-4 cursor-pointer ml-1" name="material-symbols:content-copy-outline" />{row.series_key}</div>, { class: 'w-[150px]' }],
    ['资源ID', 'series_resource_id', { class: 'w-[120px]' }],
    ['资源名', row => renderReleaseTitle(row), { class: 'w-[200px]' }],
    ['封面', row => {
      return row.cover ? <img src={row.cover} class="size-[100px] object-contain" /> : '-'
    }, { class: 'w-[140px] text-center' }],
    ['封面2', row => {
      return row.cover2 ? <img src={row.cover2} class="size-[100px] object-contain" /> : '-'
    }, { class: 'w-[140px] text-center' }],
    ['剧名', 'title', { class: 'w-[240px]' }],
    ['剧名2', 'title2', { class: 'w-[240px]' }],
    ['内嵌语言', 'default_language_code', { class: 'w-[120px]' }],
    ['完整集数', 'count', { class: 'w-[80px]' }],
    // ['剧简介', 'description', { class: 'w-[30em]' }],
    // ['标签', 'labels', { class: 'w-[200px] break-all' }],
    ['已上传集数', 'real_count', { class: 'w-[86px]' }],
    ['转码状态', row => {
      const counts = row.transcoding_count || {}
      const hasFinished = counts['4'] | 0
      const hasFailed = counts['3'] | 0
      return (
        <div>
          <div class="flex justify-center">
            正片：<div class={hasFinished !== row.count ? 'text-red-500' : ''}>{hasFinished}/{row.count}</div>
            { hasFailed !== 0 ? <div class="text-red-500">失败：{hasFailed}</div> : '' }

          </div>
          <div class="flex justify-center">
            预告：<div class={row.preview_real_count !== row.preview_count ? 'text-red-500' : ''}>{row.preview_real_count}/{row.preview_count}</div>
          </div>
        </div>
      )
    }, { class: 'w-[140px] text-center' }],
    ['剧集状态',
      row => <span>{['', '草稿', '已发布', '已归档', '已删除'][row.status || 0]}</span>,
      { class: 'w-[80px]' },
    ],
    [
      '创建时间',
      row => !!row.created ? dayjs(row.created * 1000).format('YYYY-MM-DD HH:mm') : '-',
      { class: 'w-[150px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm') : '-',
      { class: 'w-[150px]' },
    ],
    [
      <span class="px-3">操作</span>,
      row => {
        let OtherOperation = null
        if (row.status === EpisodeStatus.draft || row.status === EpisodeStatus.archived || row.status === EpisodeStatus.switchFailed) {
          OtherOperation = <Button class="btn btn-link btn-sm" onClick={() => onPublish(row.series_key)}>发布</Button>
        }
        return (
          <div class="flex flex-nowrap">
            <Button class="btn btn-link btn-sm" onClick={() => {
              episodeDetails.value = { unlocked_episodes: 1 }
              void router.push(`/episode/${row.series_key}`)
            }}
            >详情
            </Button>
            {OtherOperation}
            <Button class="btn btn-link btn-sm" onClick={() => onTransCode(row.series_key, row.title)}>转码</Button>
            {/* <Button class="btn btn-link btn-sm" onClick={() => onRemove(row.series_key, true, row.title)}>删除</Button> */}
          </div>
        )
      },
      { class: mc('w-[190px]', props.hasActions ? ' ' : 'hidden') },
    ],
  ]

  const highlightText = (e: Event) => {
    const target = e.target as HTMLElement
    if (target.classList.contains('btn-link')) {
      target.style.color = '#f00000'
    }
  }

  onMounted(() => {
    onSearch()
    // 为点击过的操作按钮添加高亮，页面刷新清除
    wrapperRef.value?.$el.addEventListener('click', highlightText, { capture: true })
  })

  onUnmounted(() => {
    wrapperRef.value?.$el.removeEventListener('click', highlightText, { capture: true })
  })

  return () => (
    <>
      <NavFormTablePager ref={wrapperRef}>{{
        nav: props.hasNav
          ? () =>
              (
                <ul>
                  <li><RouterLink to="/episode">剧集管理</RouterLink></li>
                </ul>
              )
          : null,
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              ['剧名/剧集ID', 'series_key_or_title', { type: 'text' }],
              ['剧集来源', 'source_types', { type: 'multi-select', options: [{
                label: '空',
                value: 0,
              }, {
                label: 'dramawave',
                value: 1,
              }, {
                label: 'moboshort',
                value: 2,
              }] }],
              ['资源/资源ID', 'resource_id_or_title', { type: 'text' }],
              ['内嵌字幕语言', 'language_code', {
                type: 'select', options: langKey.map((lang, index) => {
                  return {
                    label: langValue[index],
                    value: lang,
                  }
                }) },
              ],
              ['状态', 'status', {
                type: 'select', options: [
                  { label: '草稿', value: 1 },
                  { label: '已发布', value: 2 },
                  { label: '已归档', value: 3 },
                  // { label: '转码中', value: 5 },
                  // { label: '转码失败', value: 6 },
                ] }, { transform: transformNumber },
              ],
              ['发行轮次', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="flex justify-between items-center">
                  <span>剧集列表</span>
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    episodeDetails.value = {
                      unlocked_episodes: 1,
                      default_language_code: 'zh-CN',
                      language_code_list: ['zh-CN'],
                    }
                    void router.push('/episode/create')
                  }}
                  >新建虚拟剧集
                  </Button>
                </x-table-actions>
              )
            : null
        ),
        table: () => (
          <Table list={list.value} columns={columns} class="tm-table-fix-last-column" loading={loading.value} />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={onPageChange}
                  onUpdate:size={onPageSizeChange}
                />
              )
            : null
        ),
      }}
      </NavFormTablePager>
    </>

  )
})

export default EpisodePage
