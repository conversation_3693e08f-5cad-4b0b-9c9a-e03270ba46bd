import { useUploader } from '../common/uploader/use-uploader'

export const EpisodeStatus = {
  draft: 1,
  published: 2,
  archived: 3,
  deleted: 4,
  switched: 5,
  switchFailed: 6,
}

export const getAllSrc = (src: string) => {
  const { ossStaticVideoPrefix } = useUploader()

  return src.includes(ossStaticVideoPrefix.value) || src.includes('https://') ? src : `${ossStaticVideoPrefix.value}${src}`
}
