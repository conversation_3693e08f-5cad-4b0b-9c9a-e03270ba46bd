/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiEditEpisodeStatus, apiGetEpisode, apiBatchSeriesTransCode } from './episode-api'
import { EpisodeStatus } from './const'
import { openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { strToArr } from '../resource/util'
const searchForm = ref<M.EpisodeSearchForm>({
  source_types: [0, 1],
})
const page = ref<number>(1)
const pageSize = ref<number>(20)
const total = ref<number>(0)
const loading = ref<boolean>(false)

const list = ref<M.Episode[]>([])

const add = () => {

}

const getList = async () => {
  loading.value = true

  const series_key_or_title_list = strToArr(searchForm.value.series_key_or_title || '')
  const resource_id_or_title_list = strToArr(searchForm.value.resource_id_or_title || '')
  const params = {
    ...searchForm.value,
    page_index: page.value || 1,
    page_size: pageSize.value,
    series_key_or_title_list,
    resource_id_or_title_list
  }
  delete params.resource_id_or_title
  delete params.series_key_or_title
  const rs = await apiGetEpisode(params).finally(() => {
    loading.value = false
  })

  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
}

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    pageSize.value = 20
    page.value = 1
  }
  void getList()
}

const onPageChange = (n: number) => {
  page.value = n
  onSearch()
}

const onPageSizeChange = (n: number) => {
  pageSize.value = n
  page.value = 1
  onSearch()
}

const onReset = () => {
  searchForm.value = {
    source_types: [0, 1],
  }
  onSearch(true)
}

const updateState = async (series_key: string, operate_status: number, doSearch = true) => {
  // TODO
  await apiEditEpisodeStatus({ series_key, operate_status })
  doSearch && onSearch()
}

const onCopy = async (series_key: string, doSearch = true) => {
  try {
    await updateState(series_key, EpisodeStatus.draft, doSearch)
    showSuccessToast('复制成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '复制失败')
  }
}

const onRemove = (series_key: string, doSearch = true, name?: string) => {
  const hideDeleteDialog = openDialog({
    title: '删除',
    mainClass: 'pb-0 px-5',
    body: (
      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-delete-episode-body>确认删除剧集【{name}】吗？</x-delete-episode-body>
        <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await updateState(series_key, EpisodeStatus.deleted, doSearch)
              showSuccessToast('删除成功')
              hideDeleteDialog()
              if (!doSearch) {
                location.replace('/episode')
              }
            } catch (error: any) {
              showFailToast(error.response.data.message || '删除失败')
            }
          }}
          >确定
          </button>
        </x-delete-episode-footer>
      </x-delete-episode-confirm-dialog>
    ),
  })
}

const onArchive = (series_key: string, doSearch = true) => {
  const hideDeleteDialog = openDialog({
    title: '提示',
    mainClass: 'pb-0 px-5',
    body: (
      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-delete-episode-body>归档后将会下架，是否确认归档？</x-delete-episode-body>
        <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await updateState(series_key, EpisodeStatus.archived, doSearch)
              showSuccessToast('归档成功')
              hideDeleteDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '归档失败')
            }
          }}
          >确定
          </button>
        </x-delete-episode-footer>
      </x-delete-episode-confirm-dialog>
    ),
  })
}

const onTransCode = (series_key: string, title: string) => {
  const type = ref(3)
  const hideTransCodeDialog = openDialog({
    title: '批量转码',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-trans-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <select class="select select-bordered select-sm" value={type.value} onChange={e => {
          const target = e.target as HTMLSelectElement
          const value = target.value
          type.value = +value
        }}
        >
          <option value={3}>腾讯</option>
          <option value={2}>阿里点播</option>
          <option value={1}>阿里边转边播</option>
          <option value={4}>腾讯增强转码(价格较贵)</option>

        </select>
        <x-trans-episode-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideTransCodeDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await apiBatchSeriesTransCode({
                series_keys: [series_key],
                type: type.value
              })
              showSuccessToast('操作成功')
            } catch (error: any) {
              showFailToast(error.response.data.message || '转码失败')
            } finally {
              hideTransCodeDialog()
            }
          }}
          >确定
          </button>
        </x-trans-episode-footer>
      </x-trans-episode-confirm-dialog>
    ),
  })
}

const onPublish = async (series_key: string, doSearch = true) => {
  try {
    await updateState(series_key, EpisodeStatus.published, doSearch)
    showSuccessToast('发布成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '发布失败')
  }
}

export const useEpisode = () => {
  return {
    searchForm,
    page,
    pageSize,
    total,
    list,
    loading,
    add,
    updateState,
    onSearch,
    onReset,
    onPageChange,
    onPageSizeChange,
    onCopy,
    onRemove,
    onArchive,
    onTransCode,
    onPublish,
  }
}
