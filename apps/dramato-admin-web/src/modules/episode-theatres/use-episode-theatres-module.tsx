/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { omit } from 'lodash-es'
import { apiUpdateEpisodeModuleStatus, apiGetEpisodeModuleList, apiSaveEpisodeModule, apiGetDialogConfig, apiModuleLabels, apiGetEpisodeChannelList, apiBusinessKeyList } from './episode-theatre-api'
import { apiGetContentTemplateList } from '../content-template/content-template-api'
import { EpisodeModuleForm } from './episode-theatre-module-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeTheatresModule = () => {
  return {
    tags, Form, tabParams, channelList, Table, total, list, loading, search, searchChannel, languageOptions, getConfig, moduleLabels, getApiModuleLabels, changeTab, tab, checkedItems, batchDelete, batchUp, batchDown,
    copyBtn, showTagsFormDialog, hideTagsFormDialog, onPageChange, onPageSizeChange, content_template_key, get_content_template, seriesPackageStore, get_series_package, business_key_list, getBusinessKeyList,
  }
}

const Form = CreateForm<M.EpisodeModule.List.Request>()
const tabParams = ref<M.EpisodeModule.List.Request>({ page_info: { page_index: 1, page_size: 10 } })

const Table = CreateTableOld<M.EpisodeModule.List.Item>()
const list = ref<M.EpisodeModule.List.Item[]>([])
const loading = ref<boolean>(false)
const languageOptions = ref<string[]>([])
const moduleLabels = ref<M.EpisodeModule.ModuleLabels.labelsItem[]>([])
const tags = ref<M.ITag[]>([])
const total = ref<number>(0)
const channelList = ref<M.EpisodeTheatresChannel.List.Item[]>([])
const content_template_key = ref<M.ContentTemplate[]>([])
const business_key_list = ref<M.EpisodeModule.BusinessKeyList.keyListItem[]>([])

const voidTab = {
  // id: 0,
  // platform: 1, // 平台 1-IOS 2-Android
  // module_name: '', // 名称
  // module_index: 1, // 排序
  // module_key: '', // 唯一key
  // status: 1, // 状态 1 上架 2 下架
  // language: [],
}
const tab = ref<Partial<M.EpisodeModule.List.Item>>({
  ...voidTab,
})

const checkedItems = ref<M.EpisodeModule.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiGetEpisodeModuleList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  return list.value
}
const searchChannel = async () => {
  loading.value = true
  const res = await apiGetEpisodeChannelList({
    page_info: {
      page_index: 1,
      page_size: 9999,
    },
  })
  channelList.value = res.data?.list || []
  return list.value
}

const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) {
    return
  }
  languageOptions.value = Object.values(res?.data.lang_items)
  return languageOptions.value
}

const getBusinessKeyList = async (val: number) => {
  const res = await apiBusinessKeyList({ type: val })
  if (!res?.data) {
    return
  }
  business_key_list.value = Object.values(res?.data.list)
  return business_key_list.value
}

const get_content_template = async (appOptions: any) => {
  const res = await apiGetContentTemplateList({
    page_info: { page_index: 1, page_size: 9999 },
    language: '',
    // app_id: 2, // 'Dramawave_Android'
    app_id: appOptions.find((i: { label: string }) => i.label === 'Dramawave_Android')?.value || 2,
  })
  if (!res?.data) {
    return
  }
  content_template_key.value = res?.data?.list
  return content_template_key.value
}

import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'
const seriesPackageStore = useSeriesPackageStore()
const get_series_package = async () => {
  seriesPackageStore.page.value = 1
  seriesPackageStore.pageSize.value = 9999
  seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
  await seriesPackageStore.search(1, [1, 2, 3, 4, 5])
}

const getApiModuleLabels = async () => {
  const res = await apiModuleLabels()
  if (!res?.data) {
    return
  }
  moduleLabels.value = res?.data?.labels
  return moduleLabels.value
}

const onPageChange = async (page_index: number) => {
  tabParams.value.page_info = {
    ...(tabParams.value.page_info || {}),
    page_index,
  }
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  tabParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  await search()
}

const changeTab = (item: Partial<M.EpisodeModule.List.Item>) => {
  tab.value = JSON.parse(JSON.stringify(item))
  console.log(tab.value)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeModule.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeModuleForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const copyBtn = async (row: M.EpisodeModule.List.SaveItem) => {
  try {
    let d = row
    d = {
      ...omit(d, ['id', 'status', 'tab']),
      status: 2,
      module_name: d.module_name + '副本',
    }
    await apiSaveEpisodeModule(d)
    showAlert('复制成功')
    void search()
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  }
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架tab【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeModuleStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                status: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架Module【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeModuleStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除Module【{checkedItems.value.map(item => item.module_name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeModuleStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: -1,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
