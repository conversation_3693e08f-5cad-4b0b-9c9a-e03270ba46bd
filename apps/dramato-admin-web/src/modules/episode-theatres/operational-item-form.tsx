/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger, transformNumber, RadioGroup, Icon } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader.tsx'
import { useOperationalItem } from './use-operational-item'
import { onMounted, ref } from 'vue'
import { useEpisodeTheatresChannel } from './use-episode-theatres-channel.tsx'

export const OperationalItemForm = createComponent(null, () => {
  const {
    operItemForm,
    hideTagsFormDialog,
    onSave,
    positionList,
  } = useOperationalItem()
  const {
    languageOptions,
    getConfig,
  } = useEpisodeTheatresChannel()
  const Form = CreateForm<M.OperatingItem.List.SaveItem>()

  const formRules = z.object({
    name: z.string().min(1, '请填写名称'),
    lang: z.array(z.string()).min(1, '请选择语言'),
    link_type: z.number().min(1, '请选择类型'),
    link: z.string().min(1, '请填写link'),
    ad_slot: z.object({
      id: z.number().min(1, '请选择类型'),
    }),
    title: z.array(z.object({
      language: z.string().min(1, '请输入语言'),
      content: z.string().min(1, '请输入文案'),
    })).min(1, '请填写语言配置'),
    covers: z.array(z.object({
      cover: z.string().min(1, '请上传图片'),
      language: z.array(z.string()).min(1, '请选择语言'),
    })).min(1, '请填写封面配置'),
  })

  const { error, validateAll } = useValidator(operItemForm, formRules)

  const languageSureBtn = () => {
    operItemForm.value.title = []
    operItemForm.value.lang?.map((value: any) => {
      operItemForm.value.title?.push({
        language: value,
        content: '',
      })
    })
  }
  const optionSelectedStatus = ref<any>({})
  onMounted(() => {
    getConfig()
    console.log(operItemForm.value)
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="mb-3 grid flex-1 grid-cols-4 gap-y-3"
          actionClass="col-span-1 flex justify-end"
          error={error.value}
          hasAction={false}
          onChange={(path, value) => {
            if (path === 'langs') {
              console.log(value)
            }
            set(operItemForm.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('活动项名称'),
              path: 'name',
              input: {
                type: 'text',
                placeholder: '请输入活动项名称',
              },
              class: 'col-span-2',
            },
            {
              label: requiredLabel('跳转类型'),
              path: 'link_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: [
                  {
                    label: 'deeplink',
                    value: 1,
                  },
                  {
                    label: '站内h5链接',
                    value: 2,
                  },
                  {
                    label: '站外h5链接',
                    value: 3,
                  },
                ],
                autoInsertEmptyOption: false,
                class: 'col-span-1',
              },
              class: 'col-span-1',
            },
            {
              label: '描述',
              path: 'item_desc',
              input: {
                type: 'text',
                placeholder: '描述',
              },
              class: 'col-span-2',
            },
            {
              label: requiredLabel('链接'),
              path: 'link',
              input: {
                type: 'text',
              },
              hint: () => (<Button class="btn btn-link btn-sm" onClick={() => { window.open('https://rg975ojk5z.feishu.cn/docx/AkgadiZueoPf1mxwQSMct2HXn4h') }}>deeplink配置链接</Button>),
              class: 'col-span-2',
            },
            {
              label: requiredLabel('运营项类型'),
              path: 'ad_slot_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: [
                  {
                    label: '活动图',
                    value: 2,
                  },
                  {
                    label: '预告类型',
                    value: 1,
                  },
                ],
                autoInsertEmptyOption: false,
                class: 'col-span-2',
              },
              class: 'col-span-2',
            },

            {
              label: <div class="flex">{requiredLabel('语言')}<span class="pl-2 text-sm text-red-500">(修改语言后，点击下方确认按钮进行更新)</span></div>,
              path: 'lang',
              input: {
                type: 'multi-select',
                popoverWrapperClass: 'z-popover-in-dialog',
                options: languageOptions.value.map((n, index) => {
                  return { value: languageOptions.value[index], label: languageOptions.value[index] }
                }),
              },
              class: 'col-span-2',
            },

            {
              label: requiredLabel('运营位id'),
              path: 'ad_slot.id',
              transform: transformInteger,
              input: {
                type: 'select',
                options: positionList.value.map((n, index) => {
                  return { value: n.id, label: '' + n.id }
                }),
                autoInsertEmptyOption: false,
              },
              class: 'col-span-2',
            },
            () => (
              <div class="flex flex-col">
                <button class="btn btn-primary btn-sm mt-[25px] w-[100px]" onClick={() => { void languageSureBtn() }}>语言确认</button>
              </div>
            ),
            {
              label: requiredLabel('状态'),
              path: 'status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
              class: 'col-span-2',
            },
            () => (<hr class="col-span-4" />),
            [
              requiredLabel('多语言文案配置'),
              'title',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      <div class="flex pl-5">
                        {requiredLabel('运营项标题:')}
                        <RadioGroup
                          class="tm-radio ml-6"
                          options={[
                            {
                              value: 2,
                              label: '展示标题',
                            },
                            {
                              value: 1,
                              label: '不展示标题',
                            },
                          ]}
                          modelValue={operItemForm.value.show_title}
                          onUpdate:modelValue={(e: unknown) => operItemForm.value.show_title = e as 2 | 1}
                        />
                      </div>
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <x-type-select class="mx-[20px] w-[200px]">
                                {requiredLabel('应用语言:')}
                                <input
                                  class="input input-bordered flex h-8 items-center gap-1 !border-gray-300"
                                  value={i.language || 0}
                                  disabled="true"
                                />
                              </x-type-select>
                              <div class="flex flex-col">
                                {requiredLabel('文案内容:')}
                                <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                                  <textarea
                                    rows="1"
                                    placeholder="请输入文案"
                                    class="textarea textarea-bordered w-[300px] border-none"
                                    value={i.content}
                                    onInput={(e: any) => {
                                      if (!operItemForm.value.title) {
                                        return
                                      }
                                      const value = e.target.value
                                      operItemForm.value.title[idx].content = value + ''
                                    }}
                                  />
                                </span>
                              </div>
                            </div>
                          ))
                      }
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg col-span-4',
              },
            ],
            () => (<hr class="col-span-4 my-4" />),
            () => (
              <div class="col-span-4">
                <div><span class={mc('mr-1 text-[red]', operItemForm.value.ad_slot_type == 1 && 'hidden')}>*</span>封面配置</div>
                <Uploader
                  accept="png,jpg,jpeg"
                  maxsize={1024 * 1024 * 10}
                  class="btn btn-outline btn-sm m-2 cursor-pointer "
                  onUploadSuccess={d => {
                    const imgUrl = d.temp_path?.includes('https://') ? d.temp_path : 'https://static-v1.mydramawave.com/banner/cover/' + d.temp_path
                    if (!operItemForm.value?.covers) {
                      operItemForm.value.covers = []
                    }
                    operItemForm.value.covers.push({
                      cover: imgUrl,
                      language: [],
                    })
                  }}
                  isImage={true}
                  uploadUrl="/banner/upload/cover"
                >
                  <span class="flex size-full items-center justify-center">上传图片</span>
                </Uploader>
                <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>

              </div>
            ),
            [
              '封面语言配置',
              'covers',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2 pb-2 border-b-2 border-gray-300', i.is_delete ? 'hidden' : '')}>
                              <img src={i.cover} class="mr-6 h-auto w-[150px]" />
                              <div class="flex flex-1 flex-wrap gap-3">
                                {
                                  (operItemForm.value.lang || []).map((item: any) => (
                                    <label class="cursor-pointer">
                                      <input type="checkbox"
                                        value={item}
                                        checked={i.language.includes(item)}
                                        disabled={!i.language.includes(item) && optionSelectedStatus.value[item]}
                                        onChange={e => {
                                          const target = e.target as HTMLInputElement
                                          if (target.checked) {
                                            i.language.push(item)
                                            optionSelectedStatus.value[item] = true
                                          } else {
                                            const newArr1 = i.language.filter((s: string) => s !== item)
                                            i.language = newArr1
                                            optionSelectedStatus.value[item] = false
                                          }
                                        }}
                                        class="tm-radio mr-1 cursor-pointer" />
                                      {item}
                                    </label>
                                  ),
                                  )
                                }
                              </div>
                              <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                if (!operItemForm.value?.covers) {
                                  return
                                }
                                i.language.forEach((key: string) => {
                                  if (optionSelectedStatus.value.hasOwnProperty(key)) {
                                    optionSelectedStatus.value[key] = false
                                  }
                                })
                                i.language = []
                                operItemForm.value.covers.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg col-span-4',
              },
            ],
          ]}
          data={operItemForm.value as M.OperatingItem.List.SaveItem}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (operItemForm.value.ad_slot_type === 1) {
            exclude.push('covers')
          }
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
