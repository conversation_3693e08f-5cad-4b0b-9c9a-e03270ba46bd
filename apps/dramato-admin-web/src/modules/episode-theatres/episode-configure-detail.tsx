/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-floating-promises */
import { createComponent, mc } from '@skynet/shared'
import { Button, SvgIcon } from '@skynet/ui'
import { useEpisodeTheatres } from './use-episode-theatres.tsx'
import { onMounted } from 'vue'
import { useEpisodeTheatresChannel } from './use-episode-theatres-channel'
import { useEpisodeTheatresModule } from './use-episode-theatres-module'

export const ConfigureDetail = createComponent(null, () => {
  const { showTagsFormDialog: showChannelFormDialog } = useEpisodeTheatresChannel()
  const { showTagsFormDialog: showModuleFormDialog } = useEpisodeTheatresModule()
  const {
    hideConfigureDetailDialog,
    pageShowStatus, showConfigureChildrenData, treeEpisodeTheatreData,
  } = useEpisodeTheatres()
  onMounted(() => {
    void treeEpisodeTheatreData()
  })

  const renderConfigureItem = (menuSubItem: any) => {
    return (
      <li class="flex flex-row hover group items-center gap-x-4">
        <a href="javascript:void(0);">{menuSubItem.title}<span class={mc(menuSubItem.detail && '!inline-block', 'hidden')}> ---- {menuSubItem.detail}</span><span class={mc(menuSubItem.status !== undefined && '!inline-block', menuSubItem.status == 1 ? 'text-red-500' : 'text-green-400', 'hidden')}>{menuSubItem.status == 1 ? '(已上线)' : '(未上架)'}</span></a>
        <Button onClick={() => showChannelFormDialog(menuSubItem, '修改频道')} class={mc('btn btn-warning btn-xs hidden', menuSubItem.tab_name && '!inline-block')}>编辑</Button>
        <Button onClick={() => showModuleFormDialog(menuSubItem, '修改模块')} class={mc('btn btn-warning btn-xs hidden', menuSubItem.module_name && '!inline-block')}>编辑</Button>
      </li>
    )
  }

  // 渲染
  const renderSubTitle = (menuSubItem: any) => {
    return (
      <li>
        <details open>
          <summary class="inline-flex hover group">
            <span>{menuSubItem.id ? '(id:' + menuSubItem.id + ')' : ''}{menuSubItem.title}<span class={mc(menuSubItem.status !== undefined && '!inline-block', menuSubItem.status == 1 ? 'text-red-500' : 'text-green-400', 'hidden')}>{menuSubItem.status == 1 ? '(已上线)' : '(未上架)'}</span></span>
            <Button onClick={() => showChannelFormDialog(menuSubItem, '修改频道')} class={mc('btn btn-warning btn-xs hidden', menuSubItem.tab_name && '!inline-block')}>编辑</Button>
            <Button onClick={() => showModuleFormDialog(menuSubItem, '修改模块')} class={mc('btn btn-warning btn-xs hidden', menuSubItem.module_name && '!inline-block')}>编辑</Button>
          </summary>
          <ul>
            {menuSubItem.children && renderConfigure(menuSubItem.children)}
          </ul>
        </details>
      </li>
    )
  }

  const renderConfigure = (menus: any[]) => {
    const result = menus.map(menuSubItem => {
      if (menuSubItem.children && menuSubItem.children?.length) {
        return renderSubTitle(menuSubItem)
      } else {
        return renderConfigureItem(menuSubItem)
      }
    })
    return result
  }

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        {pageShowStatus.value
          ? (
              <ul class="menu lg:menu-horizontal bg-base-200 rounded-box lg:mb-64">
                {showConfigureChildrenData.value && showConfigureChildrenData.value.length > 0 ? renderConfigure(showConfigureChildrenData.value) : null}
              </ul>
            )
          : (
              <div class="flex justify-center pt-20">
                <SvgIcon class="loading loading-spinner size-4" name="ic_loading" />
              </div>
            )}
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn btn-primary btn-sm" onClick={hideConfigureDetailDialog.value}>关闭</Button>
      </div>
    </>
  )
})
