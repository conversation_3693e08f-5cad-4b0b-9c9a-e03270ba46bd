/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { omit } from 'lodash-es'
import { apiUpdateEpisodeChannelStatus, apiGetEpisodeChannelList, apiSaveEpisodeChannel, apiGetDialogConfig } from './episode-theatre-api'
import { EpisodeChannelForm } from './episode-theatre-channel-form'
const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeTheatresChannel = () => {
  return {
    tags, Form, tabParams, Table, total, list, loading, search, languageOptions, getConfig, changeTab, tab, checkedItems, batchDelete, batchUp, copyBtn, batchDown,
    showTagsFormDialog, hideTagsFormDialog, onSave, onPageChange, onPageSizeChange,
  }
}

const Form = CreateForm<M.EpisodeTheatresChannel.List.Request>()

const Table = CreateTableOld<M.EpisodeTheatresChannel.List.Item>()
const list = ref<M.EpisodeTheatresChannel.List.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([])
const languageOptions = ref<string[]>([])
const total = ref<number>(0)

const voidTab = {
  // id: 0,
  // platform: 1, // 平台 1-IOS 2-Android
  // tab_name: '', // 名称
  // tab_index: 1, // 排序
  // tab_key: '', // 唯一key
  // listing_status: 1, // 状态 1 上架 2 下架
}
const tab = ref<Partial<M.EpisodeTheatresChannel.List.Item>>({
  ...voidTab,
})

const tabParams = ref<Partial<M.EpisodeTheatresChannel.List.Request>>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})

const checkedItems = ref<M.EpisodeTheatresChannel.List.Item[]>([])

const search = async () => {
  loading.value = true
  console.log(tabParams.value.theater_id)

  if (tabParams.value.theater_id != undefined) {
    tabParams.value.theater_ids = [tabParams.value.theater_id]
  } else {
    delete tabParams.value.theater_ids
    delete tabParams.value.theater_id
  }
  const res = await apiGetEpisodeChannelList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  return list.value
}
const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) {
    return
  }
  languageOptions.value = Object.values(res?.data.lang_items)
  console.log(languageOptions.value)

  return languageOptions.value
}
const onPageChange = async (page_index: number) => {
  tabParams.value.page_info = {
    ...(tabParams.value.page_info || {}),
    page_index,
  }
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  tabParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  await search()
}
const changeTab = (item: Partial<M.EpisodeTheatresChannel.List.Item>) => {
  tab.value = Object.assign({}, voidTab, item)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeTheatresChannel.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeChannelForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}
const copyBtn = async (row: M.EpisodeTheatresChannel.List.SaveItem) => {
  try {
    let d = row
    d = {
      ...omit(d, ['id', 'status', 'module', 'theater']),
      status: 2,
      tab_name: d.tab_name + '副本',
      module: [],
    }
    console.log(d)

    await apiSaveEpisodeChannel(d)
    showAlert('复制成功')
    void search()
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  }
}
const onSave = (d: M.EpisodeTheatresChannel.List.SaveItem) => {

}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架频道【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeChannelStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                status: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架频道【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeChannelStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除频道【{checkedItems.value.map(item => item.tab_name).join('、')}】?</x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeChannelStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: -1,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
