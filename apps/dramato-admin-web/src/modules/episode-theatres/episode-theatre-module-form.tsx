/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, CreateForm, transformInteger, Icon, RadioGroup, openDialog, showAlert } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { showModeOptions, sf_key_list, rg_key_list, content_type_options, content_type_two_options } from './show-mode-options.ts'
import { set, trim } from 'lodash-es'
import { useEpisodeTheatres } from './use-episode-theatres.tsx'
import { useEpisodeTheatresModule } from './use-episode-theatres-module.tsx'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer.tsx'
import { onMounted, watch, ref } from 'vue'
import { useAppAndLangOptions } from '../options/use-app-options'
import { apiBusinessKeyAdd, apiBusinessKeyDel, apiSaveEpisodeModule } from './episode-theatre-api'
export const EpisodeModuleForm = createComponent(null, () => {
  const {
    tags,
    tab,
    hideTagsFormDialog,
    languageOptions,
    content_template_key,
    get_content_template,
    seriesPackageStore,
    get_series_package,
    getConfig,
    moduleLabels,
    getApiModuleLabels,
    business_key_list,
    getBusinessKeyList,
    search: theatresModuleSearch,
  } = useEpisodeTheatresModule()
  const { treeEpisodeTheatreData } = useEpisodeTheatres()
  const Form = CreateForm<M.EpisodeModule.List.Item>()
  const {
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const moduleLabelsShow = ref<M.EpisodeModule.ModuleLabels.labelsItem[]>([])
  const content_type_optionsShow = ref<{ value: number, label: string }[]>([])
  const formRules = z.object({
    module_name: z.string().min(1, '请输入模块名称'),
    module_index: z.number().min(1, '请选择模块顺序'),
    status: z.number().min(1, '请选择上架状态'),
  })
  const { appOptions } = useAppAndLangOptions(() => undefined, {})
  const { error, validateAll } = useValidator(tab, formRules)

  // 新增业务名称弹层
  const KeyForm = CreateForm<{
    module_key: string[]
  }>()
  const dialogForm = ref({
    module_key: [''],
  })
  const dialogRef = ref(() => {})
  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      title: () => <div>新增业务名称</div>,
      body: () => (
        <div>
          <KeyForm
            class="flex w-full flex-col"
            hasAction={false}
            data={dialogForm.value}
            onChange={(path, value) => {
              set(dialogForm.value, path, value)
            }}
            error={error.value}
            items={[
              [
                '已存在的业务名称：',
                '',
                {
                  type: 'custom',
                  render: () => (
                    business_key_list.value.map(item => (
                      <div class="flex h-[30px] items-center justify-between rounded-[6px] bg-slate-100 px-[10px]">
                        <div class="flex-1">{item.key}</div>
                        <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                          const showTipsDialog = () => {
                            const hideDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: (
                                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-status-body>是否确认删除业务名称：{item.key}?</x-status-body>
                                  <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                    <button class="btn btn-primary btn-sm" onClick={() => {
                                      void apiBusinessKeyDel({
                                        ids: item.id + '',
                                      }).then(() => {
                                        showAlert('删除成功')
                                        void getBusinessKeyList(2)
                                        hideDialog()
                                      })
                                        .catch((error: any) => {
                                          showAlert(error.response.data.message, 'error')
                                        })
                                    }}
                                    >确定
                                    </button>
                                  </x-status-footer>
                                </x-status-confirm-dialog>
                              ),
                            })
                          }

                          showTipsDialog()
                        }}
                        />
                      </div>
                    ))
                  ),
                },
              ],
              [
                requiredLabel('新增业务名称（注意⚠️：这个修改，会影响数据埋点统计数据！）'),
                'module_key',
                {
                  type: 'custom',
                  render: (r: any) => {
                    return (
                      <x-push-desc-list class="flex w-full flex-col gap-2">
                        {
                          (r.value || [])
                            .map((i: any, idx: number) => (
                              <div class={mc('flex items-center gap-2')}>
                                <div class="flex items-center">
                                  <span class="pr-[10px]">业务名称:</span>

                                  <span class={mc('input input-bordered flex items-center gap-1 h-8 text-xs')}>
                                    <input
                                      type="text"
                                      class="w-[300px]"
                                      value={i || ''}
                                      onInput={(e: any) => {
                                        if (!dialogForm.value.module_key) {
                                          return
                                        }
                                        const value = trim(e.target.value)
                                        dialogForm.value.module_key[idx] = value + ''
                                      }}
                                    />
                                  </span>
                                </div>
                                <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                  if (!dialogForm.value.module_key) {
                                    return
                                  }
                                  dialogForm.value.module_key.splice(idx, 1)
                                }}
                                />
                              </div>
                            ))
                        }
                      </x-push-desc-list>

                    )
                  },
                },
                {
                  class: 'p-4 bg-gray-100 rounded-lg w-[100%]',
                },
              ],
            ]}
          />
          <div class="flex justify-center gap-x-2 px-[20px] pt-[10px]">
            <Button
              class="btn btn-outline btn-sm"
              onClick={() => {
                if (!dialogForm.value.module_key) {
                  dialogForm.value.module_key = []
                }
                dialogForm.value.module_key.push('')
              }}
            >新增
            </Button>
            <Button class="btn btn-primary btn-sm" onClick={
              () => {
                console.log(dialogForm.value.module_key.join(','))
                const arrStr = dialogForm.value.module_key.filter((item: string) => {
                  return item !== ''
                })
                console.log(arrStr.join(','))
                if (arrStr.length < 1) {
                  return
                }
                void apiBusinessKeyAdd({
                  type: 2,
                  key: arrStr.join(','),
                }).then(() => {
                  showAlert('添加成功')
                  dialogForm.value = { module_key: [''] }
                  void getBusinessKeyList(2)
                  dialogRef.value()
                })
                  .catch((error: any) => {
                    showAlert(error.response.data.message, 'error')
                  })
              }
            }
            >提交
            </Button>
            <Button class="btn  btn-sm" onClick={dialogRef.value}>取消</Button>

          </div>
        </div>
      ),
    })
  }

  watch(
    () => tab.value.content_type,
    () => {
      switch (tab.value.content_type) {
        case 1:
          moduleLabelsShow.value = sf_key_list
          break
        case 2:
          moduleLabelsShow.value = rg_key_list
          break
        case 4:
          console.log(appOptions.value)
          void get_content_template(appOptions.value)
          break
        case 5:
          void get_series_package()
          break
      }
    },
    { deep: true, immediate: true },
  )
  watch(
    () => tab.value.show_mode,
    () => {
      switch (tab.value.show_mode) {
        case 'column_horizontal':
        case 'column_vertical_three':
        case 'recommend':
        case 'infinity_three':
          void get_content_template(appOptions.value)
          void get_series_package()
          content_type_optionsShow.value = content_type_two_options
          break
        default:
          content_type_optionsShow.value = content_type_options
          break
      }
    },
    { deep: true, immediate: true },
  )
  onMounted(async () => {
    page.value = 1
    pageSize.value = 9999
    tab.value.language = tab.value.language || [{ content: '', lang: '' }]
    await search(page.value)
    await getConfig()
    await getApiModuleLabels()
    dialogForm.value = { module_key: [''] }
    await getBusinessKeyList(2)
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="mb-3 grid flex-1 grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'content_type') {
              switch (value) {
                case 1:
                  moduleLabelsShow.value = sf_key_list
                  set(tab.value || {}, 'content_key', '')
                  break
                case 2:
                  moduleLabelsShow.value = rg_key_list
                  set(tab.value || {}, 'content_key', '')
                  break
                default:
                  set(tab.value || {}, 'content_key', '')
                  break
              }
            }
            if (path === 'show_mode') {
              switch (value) {
                case 'column_horizontal':
                case 'column_vertical_three':
                case 'recommend':
                case 'infinity_three':
                  void get_content_template(appOptions.value)
                  void get_series_package()
                  content_type_optionsShow.value = content_type_two_options
                  break
                default:
                  content_type_optionsShow.value = content_type_options
                  break
              }
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('模块名称'),
              path: 'module_name',
              input: {
                type: 'text',
                placeholder: '请输入模块名称',
              },
              class: 'col-span-3',
            },
            [{
              label: requiredLabel('模块业务名称'),
              path: 'module_key',
              input: {
                type: 'select',
                options: business_key_list.value.map((n, index) => {
                  return { value: n.key, label: n.key }
                }),
                autoInsertEmptyOption: false,
              },
              class: 'w-[300px]',
            },
            () => (
              <div class="flex items-center">
                <Button class=" btn btn-primary btn-sm" onClick={() => showCreateDialog()}>新增业务名称
                </Button>
              </div>
            )],
            {
              label: '描述',
              path: 'module_desc',
              input: {
                type: 'textarea',
                maxlength: 50,
                placeholder: '描述',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('排序（此排序会影响模块页面顺序）'),
              path: 'module_index',
              transform: transformInteger,
              input: {
                type: 'text',
                placeholder: '请输入排序',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块展示形式（单排流、双排流、三排流）'),
              path: 'show_mode',
              input: {
                type: 'select',
                options: showModeOptions,
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块数据源类型（人工配置、算法规则）'),
              path: 'content_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: content_type_optionsShow.value,
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'select',
                options: moduleLabelsShow.value.map((n, index) => {
                  return { value: n.id, label: n.label }
                }),
                autoInsertEmptyOption: false,
              },
              class: `col-span-3 ${(tab.value.content_type === 3 || tab.value.content_type === 4 || tab.value.content_type === 5) ? 'hidden' : ''}`,
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: moduleLabels.value.map((n, index) => {
                  return { value: n.id, label: n.label }
                }),
              },
              transform: [
                (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
              ] as const,
              class: `col-span-3 ${tab.value.content_type !== 3 ? 'h-0 overflow-hidden' : ''}`,
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: content_template_key.value.map((n, index) => {
                  return { value: n.id || 0, label: n.key }
                }),
              },
              transform: [
                (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
              ] as const,
              class: `col-span-3 ${tab.value.content_type !== 4 ? 'h-0 overflow-hidden' : ''}`,
            },
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: seriesPackageStore.list.value.map(n => {
                  return { value: n.id || 0, label: `${n.id}/${n.package_name}` }
                }),
              },
              transform: [
                (raw?: string) => raw ? raw.split(',').map(i => +i) : [],
                (display: string): string => Array.isArray(display) ? display.join(',') : display?.toString() ?? '',
              ] as const,
              class: `col-span-3 ${tab.value.content_type !== 5 ? 'h-0 overflow-hidden' : ''}`,
            },
            {
              label: requiredLabel('状态'),
              path: 'status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
              class: 'col-span-3',
            },
            [
              '多语言文案配置',
              'language',
              {
                type: 'custom',
                render: (r: any) => {
                  console.log('r.value', r.value)
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      <div class="flex pl-5">
                        {requiredLabel('模块标题:')}
                        <RadioGroup
                          class="tm-radio ml-6"
                          options={[
                            {
                              value: 1,
                              label: '展示标题',
                            },
                            {
                              value: 2,
                              label: '不展示标题',
                            },
                          ]}
                          modelValue={tab.value.show_title}
                          onUpdate:modelValue={(e: unknown) => tab.value.show_title = e as 2 | 1}
                        />
                      </div>
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <x-type-select class="mx-[20px] w-[200px]">
                                {requiredLabel('应用语言:')}
                                <select
                                  // id={id}
                                  class="select select-bordered select-sm w-full"
                                  value={i.lang || 0}
                                  onInput={(e: any) => i.lang = e.target.value}
                                >
                                  {
                                    languageOptions.value.map((n, index) => <option value={languageOptions.value[index]}>{languageOptions.value[index]}</option>)
                                  }
                                </select>
                              </x-type-select>
                              <div class="flex flex-col">
                                文案内容:
                                <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                                  <textarea
                                    rows="1"
                                    placeholder="请输入文案"
                                    class="textarea textarea-bordered w-[300px] border-none"
                                    value={i.content}
                                    onInput={(e: any) => {
                                      if (!tab.value.language) {
                                        return
                                      }
                                      const value = e.target.value
                                      tab.value.language[idx].content = value + ''
                                    }}
                                  />
                                </span>
                              </div>
                              <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                if (!tab.value.language) {
                                  return
                                }
                                tab.value.language.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                      <div class="flex justify-center">
                        <Button
                          class="btn btn-outline btn-sm mr-6"
                          onClick={() => {
                            tab.value.language = []
                            languageOptions.value.map((i, index) => {
                              tab.value.language?.push({
                                content: '',
                                lang: i,
                              })
                            })
                          }}
                        >全语言
                        </Button>
                        <Button
                          class="btn btn-outline btn-sm"
                          onClick={() => {
                            if (!tab.value.language) {
                              tab.value.language = []
                            }

                            tab.value.language.push({
                              content: '',
                              lang: '',
                            })
                          }}
                        >新增
                        </Button>
                      </div>
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg',
              },
            ],

          ]}
          data={tab.value as M.EpisodeModule.List.Item}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          console.log(tab.value)
          if (!validateAll({ exclude })) {
            console.log('error', error.value)
            return
          }
          // void onSave(tab.value as M.EpisodeModule.List.Item)
          const d = tab.value as M.EpisodeModule.List.SaveItem
          void apiSaveEpisodeModule(d).then(res => {
            console.log('>>> d')
            console.log(d)
            if (d?.data_type == 'theatres_configure_detail') { // 如果有这个属性，表示是树结构过来的
              void treeEpisodeTheatreData()
            } else {
              void theatresModuleSearch() // 列表刷新
            }
            hideTagsFormDialog.value && hideTagsFormDialog.value()
          }).catch((error: any) => {
            showAlert(error.response.data.message || '保存失败', 'error')
          })
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
