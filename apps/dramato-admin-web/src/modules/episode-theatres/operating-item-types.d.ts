declare namespace M {
  namespace OperatingItem {
    namespace List {
      interface Request {
        ad_slot_id?: number
        page_info?: {
          page_index: number
          page_size?: number
        }
        status?: number // -1 删除  1 上架  2下架
      }
      interface titleItem {
        language: string
        content: string
      }
      interface coversItem {
        cover?: string
        language?: string[]
      }
      interface Item {
        id?: number
        name: string
        item_desc: string
        ad_slot_type: number
        title: titleItem[]
        covers: coversItem[]
        show_title: number
        lang: string[]
        link_type: number
        link: string
        status: number
        ad_slot?: {
          id: number
        }
        created: number
        updated: number
        creator_user?: string
        updated_user?: string
      }
      interface Response {
        total: number
        list: Item[]
      }
      interface SaveItem {
        id?: number
        name?: string
        item_desc?: string
        ad_slot_type?: number
        title: titleItem[]
        show_title: number
        covers: coversItem[]
        lang: string[]
        link_type: number
        link: string
        status: number
        ad_slot?: {
          id: number
        }
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        status: number // 是否上架  1 上架 2 下架
      }
    }

    namespace UpdateSortStatus{
      interface tabItem {
        id?: number
        tab_index: number
      }
      interface Request extends List.Request {
        tabs: tabItem[]
      }
    }

    namespace module{
      interface listItem {
        id: number
        module_name: string
      }
      interface Request {
        total: number
        list: listItem[]
      }
    }
  }
}
