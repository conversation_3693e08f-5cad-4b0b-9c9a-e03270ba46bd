declare namespace M {
  namespace EpisodeTheatresChannel {
    namespace List {
      interface Request {
        app_id?: number
        strategy_id?: number
        theater_id?: number
        theater_ids?: number[]
        id_name?: string
        page_info?: {
          page_index: number
          page_size?: number
        }
        status?: number // -1 删除  1 上架  2下架
      }

      interface langItem {
        lang: string
        content: string
        item_id?: number
      }
      interface moduleItem {
        id: number
        language: langItem[] // 语言
        tab: { // 所属tab
          id: number
          tab_name: string
        }
        platform: number // 平台 1-IOS 2-Android
        module_name: string // 名称
        module_index: number // 排序
        module_desc: string // 描述
        module_key: string // 唯一key， 业务名称
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
      }
      interface Item {
        id: number
        language: langItem[] // 语言
        module: moduleItem[] // 关联模块
        theater: {
          id: number
          name: string
        }
        tab_name: string // 名称
        tab_index: number // 排序
        tab_desc: string // 描述
        tab_key: string // 唯一key， 业务名称
        show_title: number
        show_mode: string
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架 -1删除
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
      }
      interface SaveItem {
        id?: number
        language: langItem[] // 语言
        module: moduleItem[] // 关联模块
        theater?: {
          id: number
          name: string
        }
        tab_name: string // 名称
        tab_index: number // 排序
        tab_desc: string // 描述
        tab_key: string // 唯一key， 业务名称
        show_title: number
        show_mode: string
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架 -1删除
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
        data_type?: string
      }

      interface Response {
        total: number
        list: Item[]
      }
      interface Response {
        total: number
        list: Item[]
      }
    }

    namespace Save {
      interface Request extends List.Request {
        id: number
        language: langItem[]
        tab_name: string // 名称
        tab_index: number // 排序
        tab_desc: string // 描述
        tab_key: string // 唯一key， 业务名称
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        status: number // 是否上架  1 上架 2 下架
      }
    }

    namespace UpdateSortStatus{
      interface tabItem {
        id?: number
        tab_index: number
      }
      interface Request extends List.Request {
        tabs: tabItem[]
      }
    }
  }
}
