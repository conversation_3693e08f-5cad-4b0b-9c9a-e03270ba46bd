/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, fn } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { Button, Checkbox, DateTime, openDialog, showAlert, transformNumber, Icon, Pager } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { cloneDeep, set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { useEpisodeTheatresModule } from './use-episode-theatres-module'
import { apiUpdateEpisodeModuleStatus, apiUpdateEpisodeModuleUpdateSort } from './episode-theatre-api'

type EpisodeChannelOptions = {
  props: {
    checkedItems?: M.EpisodeModule.List.Item[]
    platform?: string
  }
  emits: {
    add: (item: M.EpisodeModule.List.Item[]) => void
    remove: (item: M.EpisodeModule.List.Item) => void
  }
}
export const EpisodeModule = createComponent<EpisodeChannelOptions>({
  props: {
    checkedItems: [],
    // @ts-expect-error never mind
    platform: undefined,
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  const { Form, tabParams, channelList, searchChannel, Table, total, list, loading, search, checkedItems, batchDelete, batchUp, batchDown, copyBtn, showTagsFormDialog, onPageChange, onPageSizeChange } = useEpisodeTheatresModule()
  const currentPriority = ref(1)

  onMounted(() => {
    void search()
    void searchChannel()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class={mc('space-y-2', props.platform == 'tabPage' && 'hidden')}>
            <ul>
              <li>剧场管理</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs inline-block bg-slate-200">
              <RouterLink to="theatre" class="tab w-[150px]" activeClass="tab-active">剧场</RouterLink>
              <RouterLink to="channel" class="tab w-[150px]" activeClass="tab-active">频道</RouterLink>
              <RouterLink to="module" class="tab w-[150px]" activeClass="tab-active">模块</RouterLink>
              <RouterLink to="operating-positon" class="tab w-[150px]" activeClass="tab-active">运营位</RouterLink>
              <RouterLink to="operational-item" class="tab w-[150px]" activeClass="tab-active">运营项</RouterLink>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(tabParams.value, path, value)
            }}
            onReset={() => {
              tabParams.value = { page_info: { page_index: 1, page_size: 10 } }
              void search()
            }}
            onSubmit={() => {
              set(tabParams.value, 'page_info', { page_index: 1, page_size: 10 })
              void search()
            }}
            data={tabParams.value}
            items={[
              {
                label: '关联频道id/频道名称',
                path: 'tab_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: channelList.value.map((n, index) => {
                    return { value: n.id, label: n.tab_name }
                  }),
                },
                class: 'w-[240px]',
              },
              // {
              //   label: '关联频道id/频道名称',
              //   path: 'tab_id',
              //   input: {
              //     type: 'text',
              //     placeholder: '关联频道id或频道名称',
              //   },
              //   class: 'w-[240px]',
              // },
              {
                label: '模块',
                path: 'id_name',
                input: {
                  type: 'text',
                  placeholder: '请输入模块ID、名称',
                },
                class: 'w-[240px]',
              },
              {
                label: '上架状态',
                path: 'status',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: [{
                    value: 1,
                    label: '上架',
                  }, {
                    value: 2,
                    label: '下架',
                  }],
                },
                class: 'w-[140px]',
              },
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <span />
            <x-actions class={mc('flex gap-x-2', props.platform == 'tabPage' && 'hidden')}>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchUp}>批量上架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDown}>批量下架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '新建模块')}>新建模块</Button>
            </x-actions>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['', row => (
              <Checkbox
                label=""
                modelValue={!!checkedItems.value.find(i => i.id === row.id)}
                onUpdate:modelValue={(v: boolean) => {
                  const idx = checkedItems.value.findIndex(i => i.id === row.id)
                  if (idx > -1) {
                    checkedItems.value.splice(idx, 1)
                  } else {
                    checkedItems.value.push(row)
                  }
                  emit('add', checkedItems.value)
                }}

              />
            ), { class: 'w-[40px]' }],
            ['id', 'id', { class: 'w-[100px]' }],
            ['模块名称', 'module_name', { class: 'w-[150px]' }],
            ['模块业务名称', 'module_key', { class: 'w-[150px]' }],
            ['模块样式', 'show_mode', { class: 'w-[150px]' }],
            ['关联频道名称', row => (
              <div>{row.tab.tab_name}</div>
            ), { class: 'w-[150px]' }],
            ['语言', row => (
              <div>{(Array.from(row.language, ({ lang }) => lang).join('、'))}</div>
            ), { class: 'w-[200px]' }],
            ['数据源类型', 'content_key', { class: 'w-[150px]' }],
            ['排序', row => (
              <x-input class="flex flex-row items-center gap-x-2">
                <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                  <input
                    type="number"
                    class={mc('grow w-100px')}
                    value={row.module_index}
                    disabled={!row.isEdit}
                    // ant-design:edit-filled
                    onFocus={() => {
                      currentPriority.value = +(row.module_index || '')
                    }}
                    onInput={(e: Event) => {
                      currentPriority.value = +((e.target as HTMLInputElement).value || '')
                    }}
                    onKeydown={(e: KeyboardEvent) => {
                      if (e.key !== 'Enter') {
                        return
                      }
                      if (currentPriority.value === row.module_index) {
                        return
                      }
                      void apiUpdateEpisodeModuleUpdateSort({
                        modules: [{
                          id: row.id,
                          module_index: currentPriority.value,
                        }],
                      }).then(() => {
                        showAlert('排序修改成功')
                        void search()
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                    onBlur={() => {
                      if (currentPriority.value === row.module_index) {
                        return
                      }
                      void apiUpdateEpisodeModuleUpdateSort({
                        modules: [{
                          id: row.id,
                          module_index: currentPriority.value,
                        }],
                      }).then(() => {
                        showAlert('排序修改成功')
                        void search()
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  />
                </label>
                <Icon name="ant-design:edit-filled" class="size-4 cursor-pointer" onClick={() => {
                  row.isEdit = true
                }}
                />
              </x-input>
            ), { class: 'w-[200px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 0 },
              ].find(item => item.value === row.status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.creator_user, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.updated_user, { class: 'w-[140px]' }],
            [
              <span class="px-3">操作</span>,
              row => {
                return (
                  <x-hide-when-in-dialog class="flex gap-x-2">
                    <Button
                      class="btn btn-outline btn-xs"
                      onClick={() => {
                        if (!row.id) return
                        void apiUpdateEpisodeModuleStatus({
                          ids: [row.id],
                          status: row.status !== 1 ? 1 : 2,
                        })
                          .then(() => {
                            void search()
                          })
                          .catch((error: any) => {
                            showAlert(error.response.data.message, 'error')
                          })
                      }}
                    >
                      {row.status !== 1 ? '上架' : '下架'}
                    </Button>
                    <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '修改模块')}>修改</Button>
                    <Button class="btn btn-outline btn-xs" onClick={() => copyBtn(row)}>复制</Button>
                    <Button
                      class={mc('btn btn-outline btn-xs', row.status !== 1 ? '' : 'hidden')}
                      onClick={() => {
                        const showTipsDialog = () => {
                          const hideDialog = openDialog({
                            title: '',
                            mainClass: 'pb-0 px-5',
                            body: (
                              <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                                <x-status-body>是否确认删除Module【{row.module_name}】?</x-status-body>
                                <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                  <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                  <button class="btn btn-primary btn-sm" onClick={() => {
                                    void apiUpdateEpisodeModuleStatus({
                                      ids: [row.id],
                                      status: -1,

                                    })
                                      .then(() => {
                                        void search()
                                      })
                                      .catch((error: any) => {
                                        showAlert(error.response.data.message, 'error')
                                      })
                                    hideDialog()
                                  }}
                                  >确定
                                  </button>
                                </x-status-footer>
                              </x-status-confirm-dialog>
                            ),
                          })
                        }

                        showTipsDialog()
                      }}
                    >
                      删除
                    </Button>
                  </x-hide-when-in-dialog>
                )
              }, { class: 'w-[260px] text-center hide-when-in-dialog' },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={tabParams.value.page_info?.page_index || 1}
                size={tabParams.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeModule
