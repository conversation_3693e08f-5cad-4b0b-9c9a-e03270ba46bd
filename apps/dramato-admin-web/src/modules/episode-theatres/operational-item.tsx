/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, fn } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { Button, openDialog, showAlert, transformNumber, DateTime, Pager, Checkbox } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { cloneDeep, set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { useOperationalItem } from './use-operational-item'
import { apiAdSlotItemUpdateStatus } from './episode-theatre-api'

type EpisodeChannelOptions = {
}
export const EpisodeModule = createComponent<EpisodeChannelOptions>({
  props: {},
}, props => {
  const { Form, tabParams, positionList, searchPosition, Table, total, list, loading, search, checkedItems, batchDelete, batchUp, batchDown, copyBtn, showTagsFormDialog, onPageChange, onPageSizeChange } = useOperationalItem()
  const currentPriority = ref(1)

  onMounted(() => {
    void search()
    void searchPosition()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class={mc('space-y-2')}>
            <ul>
              <li>剧场管理</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs inline-block bg-slate-200">
              <RouterLink to="theatre" class="tab w-[150px]" activeClass="tab-active">剧场</RouterLink>
              <RouterLink to="channel" class="tab w-[150px]" activeClass="tab-active">频道</RouterLink>
              <RouterLink to="module" class="tab w-[150px]" activeClass="tab-active">模块</RouterLink>
              <RouterLink to="operating-positon" class="tab w-[150px]" activeClass="tab-active">运营位</RouterLink>
              <RouterLink to="operational-item" class="tab w-[150px]" activeClass="tab-active">运营项</RouterLink>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(tabParams.value, path, value)
            }}
            onReset={() => {
              tabParams.value = { page_info: { page_index: 1, page_size: 10 } }
              void search()
            }}
            onSubmit={() => {
              set(tabParams.value, 'page_info', { page_index: 1, page_size: 10 })
              void search()
            }}
            data={tabParams.value}
            items={[
              {
                label: '运行位id',
                path: 'tab_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: positionList.value.map((n, index) => {
                    return { value: n.id, label: n.id + '' }
                  }),
                },
                class: 'w-[240px]',
              }, {
                label: '运营项状态',
                path: 'status',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: [{
                    value: 1,
                    label: '上架',
                  }, {
                    value: 2,
                    label: '下架',
                  }],
                },
                class: 'w-[140px]',
              },
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <span />
            <x-actions class={mc('flex gap-x-2')}>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchUp}>批量上架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDown}>批量下架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '新增运营项')}>新增运营项</Button>
            </x-actions>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['', row => (
              <Checkbox
                label=""
                modelValue={!!checkedItems.value.find(i => i.id === row.id)}
                onUpdate:modelValue={(v: boolean) => {
                  const idx = checkedItems.value.findIndex(i => i.id === row.id)
                  if (idx > -1) {
                    checkedItems.value.splice(idx, 1)
                  } else {
                    checkedItems.value.push(row)
                  }
                }}
              />
            ), { class: 'w-[40px]' }],
            ['id', 'id', { class: 'w-[80px]' }],
            ['活动项名称', 'name', { class: 'w-[150px]' }],
            ['描述', 'item_desc', { class: 'w-[200px]' }],
            ['运营位id', row => (
              <span>{row.ad_slot?.id}</span>
            ), { class: 'w-[150px]' }],
            ['运营项类型', row => (
              <span>{[
                { label: '预告类型', value: 1 },
                { label: '活动图', value: 2 },
              ].find(item => item.value === row.ad_slot_type)?.label ?? '活动图'}
              </span>
            ), { class: 'w-[150px]' }],
            ['链接', 'link', { class: 'w-[250px]' }],
            ['是否展示标题', row => (
              <span>{[
                { label: '展示', value: 2 },
                { label: '不展示', value: 1 },
              ].find(item => item.value === row.show_title)?.label ?? '不展示'}
              </span>
            ), { class: 'w-[80px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 0 },
              ].find(item => item.value === row.status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.creator_user, { class: 'w-[140px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.updated_user, { class: 'w-[140px]' }],
            [
              <span class="px-3">操作</span>,
              row => {
                return (
                  <x-hide-when-in-dialog class="flex gap-x-2">
                    <Button
                      class="btn btn-outline btn-xs"
                      onClick={() => {
                        if (!row.id) return
                        void apiAdSlotItemUpdateStatus({
                          ids: [row.id],
                          status: row.status !== 1 ? 1 : 2,
                        })
                          .then(() => {
                            void search()
                          })
                          .catch((error: any) => {
                            showAlert(error.response.data.message, 'error')
                          })
                      }}
                    >
                      {row.status !== 1 ? '上架' : '下架'}
                    </Button>
                    <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '修改运营项')}>修改</Button>
                    {/* <Button class="btn btn-outline btn-xs" onClick={() => copyBtn(row)}>复制</Button> */}
                    <Button
                      class={mc('btn btn-outline btn-xs', row.status !== 1 ? '' : 'hidden')}
                      onClick={() => {
                        const showTipsDialog = () => {
                          const hideDialog = openDialog({
                            title: '',
                            mainClass: 'pb-0 px-5',
                            body: (
                              <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                                <x-status-body>是否确认删除【{row.name}】?</x-status-body>
                                <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                  <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                  <button class="btn btn-primary btn-sm" onClick={() => {
                                    void apiAdSlotItemUpdateStatus({
                                      ids: [row.id || 0],
                                      status: -1,

                                    })
                                      .then(() => {
                                        void search()
                                      })
                                      .catch((error: any) => {
                                        showAlert(error.response.data.message, 'error')
                                      })
                                    hideDialog()
                                  }}
                                  >确定
                                  </button>
                                </x-status-footer>
                              </x-status-confirm-dialog>
                            ),
                          })
                        }

                        showTipsDialog()
                      }}
                    >
                      删除
                    </Button>
                  </x-hide-when-in-dialog>
                )
              }, { class: 'w-[260px] text-center hide-when-in-dialog' },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={tabParams.value.page_info?.page_index || 1}
                size={tabParams.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeModule
