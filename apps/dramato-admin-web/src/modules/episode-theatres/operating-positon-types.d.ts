declare namespace M {
  namespace OperatingPosition {
    namespace List {
      interface Request {
        app_id?: number
        page_info?: {
          page_index: number
          page_size?: number
        }
        status?: number // -1 删除  1 上架  2下架
      }
      interface modulesItem {
        id: number
        module_name: string
      }
      interface Item {
        id: number
        app_id: number
        app_name?: string
        slot: number
        status: number
        start_time: number
        end_time: number
        modules: modulesItem[]
        created: number
        updated: number
        creator_user?: string
        updated_user?: string
        content_key: string
      }
      interface Response {
        total: number
        list: Item[]
      }
      interface moduleItem {
        id: number
      }
      interface SaveItem {
        id?: number
        slot: number
        app_id: number[]
        app_name?: string[]
        status: number
        start_time: number
        end_time: number
        modules: moduleItem[]
        content_key: string
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        status: number // 是否上架  1 上架 2 下架
      }
    }

    namespace UpdateSortStatus{
      interface tabItem {
        id?: number
        tab_index: number
      }
      interface Request extends List.Request {
        tabs: tabItem[]
      }
    }

    namespace module{
      interface listItem {
        id: number
        module_name: string
      }
      interface Request {
        total: number
        list: listItem[]
      }
    }
  }
}
