/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, CreateForm, transformInteger, CreateTableOld, TableColumnOld, openDialog, Icon, RadioGroup, showAlert, transformNumber, showFailToast } from '@skynet/ui'
import { z } from 'zod'
import dayjs from 'dayjs'
import { requiredLabel } from 'src/lib/required-label'
import { set, uniqBy, trim } from 'lodash-es'
import { useOperatingPositon } from './use-operating-positon.tsx'
import { onMounted, ref } from 'vue'
import { useAppAndLangOptions } from '../options/use-app-options'
export const OperatingPositonForm = createComponent(null, () => {
  const {
    operatingPostionTab,
    hideTagsFormDialog,
    moduleIdList,
    getModuleIdList,
    onSave,
  } = useOperatingPositon()
  const Form = CreateForm<M.EpisodeTheatresChannel.List.Item>()
  const { appOptions } = useAppAndLangOptions(() => undefined, {})

  const formRules = z.object({
    app_id: z.array(z.number().min(1, {
      message: '请选择应用',
    })),
    slot: z.number().min(1, '请选择客户端排序'),
    modules: z.array(z.number().min(1, {
      message: '请选择模块id',
    })),
    start_time: z.number().refine(e => {
      if (e && operatingPostionTab.value.end_time) return true
      return false
    }, {
      message: '请设置开始和结束时间！',
    }).refine(e => {
      if (e && operatingPostionTab.value.end_time && new Date(e * 1000).getTime() < new Date(operatingPostionTab.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间必须晚于开始时间！',
    }).refine(e => {
      if (operatingPostionTab.value.end_time && new Date().getTime() < new Date(operatingPostionTab.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间需晚于当前时间！',
    }),
  })

  const { error, validateAll } = useValidator(operatingPostionTab, formRules)

  onMounted(() => {
    void getModuleIdList()
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="mb-3 grid flex-1 grid-cols-1 gap-y-1"
          data={operatingPostionTab.value as M.EpisodeTheatresChannel.List.Item}
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            if (path === 'app_id') {
              const labels = appOptions.value.filter(item => value.includes(item.value))
              set(operatingPostionTab.value || {}, 'app_name', labels.map(item => item.label))
            }
            set(operatingPostionTab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('生效时间'),
              path: 'start_time',
              input: {
                type: 'custom',
                render: () => (
                  <div class="flex flex-1 flex-wrap items-center gap-2">
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={operatingPostionTab.value.start_time ? dayjs(operatingPostionTab.value.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => operatingPostionTab.value.start_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                    <span>-</span>
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={operatingPostionTab.value.end_time ? dayjs(operatingPostionTab.value.end_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => operatingPostionTab.value.end_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                  </div>
                ),
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('选择生效应用'),
              path: 'app_id',
              input: {
                type: 'multi-select',
                popoverWrapperClass: 'z-popover-in-dialog',
                options: appOptions.value,
              },
              class: 'col-span-3',
            },
            () => (<h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">关联剧场模块</h1>),
            {
              label: requiredLabel('模块数据源key（选择数据源）'),
              path: 'content_key',
              input: {
                type: 'select',
                options: [
                  { value: 'popular_choice', label: 'popular_choice' },
                ],
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('模块id'),
              path: 'modules',
              input: {
                type: 'multi-select',
                popoverWrapperClass: 'z-popover-in-dialog',
                options: moduleIdList.value.map(n => {
                  return { value: n.id, label: n.module_name }
                }),
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('客户端排序'),
              path: 'slot',
              input: {
                type: 'select',
                options: [
                  { value: 1, label: '1' },
                  { value: 7, label: '7' },
                  { value: 14, label: '14' },
                  { value: 21, label: '21' },
                ],
                autoInsertEmptyOption: false,
              },
              transform: transformNumber,
              class: 'col-span-3',
            },
            {
              label: requiredLabel('状态'),
              path: 'status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
              class: 'col-span-2',
            },
          ]}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          if (operatingPostionTab.value.modules?.length == 0) {
            showFailToast('请选择模块id')
            return
          }
          void onSave()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
