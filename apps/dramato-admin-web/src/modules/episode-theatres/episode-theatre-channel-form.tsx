/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, CreateForm, transformInteger, CreateTableOld, TableColumnOld, openDialog, Icon, RadioGroup, showAlert } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set, uniqBy, trim } from 'lodash-es'
import { useEpisodeTheatres } from './use-episode-theatres.tsx'
import { useEpisodeTheatresChannel } from './use-episode-theatres-channel.tsx'
import { useEpisodeTheatresModule } from './use-episode-theatres-module.tsx'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer.tsx'
import { onMounted, ref } from 'vue'
import { apiBusinessKeyAdd, apiBusinessKeyDel, apiSaveEpisodeChannel } from './episode-theatre-api'

import { EpisodeModule } from './episode-theatre-module.tsx'
export const EpisodeChannelForm = createComponent(null, () => {
  const {
    tags,
    tab,
    hideTagsFormDialog,
    languageOptions,
    getConfig,
    search: theatresChannelSearch,
  } = useEpisodeTheatresChannel()
  const { treeEpisodeTheatreData } = useEpisodeTheatres()
  const { business_key_list, getBusinessKeyList } = useEpisodeTheatresModule()
  const Form = CreateForm<M.EpisodeTheatresChannel.List.Item>()

  const {
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  // 导入模块
  const checkedDramaList = ref<M.EpisodeModule.List.Item[]>([])
  const SpecialOfferTable = CreateTableOld<M.EpisodeModule.List.Item>()
  const specialOfferColumns: TableColumnOld<M.EpisodeModule.List.Item>[] = [
    ['id', 'id', { class: 'w-[100px]' }],
    ['排序', 'module_index', { class: 'w-[150px]' }],
    ['模块名称', 'module_name', { class: 'w-[150px]' }],
    ['模块业务名称', 'module_key', { class: 'w-[150px]' }],
    ['模块样式', 'show_mode', { class: 'w-[150px]' }],
    ['数据源类型', 'content_key', { class: 'w-[150px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              (tab.value.module || []).splice(idx, 1)
              checkedDramaList.value = checkedDramaList.value.filter(item => item.id !== row.id)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]
  const showImportDramaDialog = () => {
    checkedDramaList.value = [...tab.value.module ?? []]
    const closeDialog = openDialog({
      title: '导入模块',
      mainClass: 'flex flex-col flex-auto pb-0 h-[80vh] overflow-hidden px-4 [&_.hide-when-in-dialog]:hidden',
      body: () => (
        <x-import-recharge-level class="gxp-y-4 flex flex-1 flex-col overflow-hidden">
          <x-episode-list class="flex-1  overflow-y-auto">
            <EpisodeModule
              platform="tabPage"
              checkedItems={checkedDramaList.value}
              onAdd={item => {
                checkedDramaList.value = checkedDramaList.value.concat(item)
              }}
              onRemove={item => {
                checkedDramaList.value = checkedDramaList.value?.filter(i => i.id !== item.id)
              }}
            />
          </x-episode-list>
          <footer class="flex w-full justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              console.log(checkedDramaList.value)
              if (checkedDramaList.value.length === 0) return
              if (!tab.value.module) {
                tab.value.module = []
              }
              checkedDramaList.value = uniqBy(checkedDramaList.value, 'id')
              tab.value.module = checkedDramaList.value.map((item, index) => ({
                ...item,
              }))
              console.log(tab.value.module)
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-[80%] overflow-hidden',
    })
  }

  const formRules = z.object({
    tab_name: z.string().min(1, '请输入标题'),
    tab_index: z.number().min(1, '请选择标签顺序'),
    status: z.number().min(1, '请选择上架状态'),
  })

  const { error, validateAll } = useValidator(tab, formRules)

  // 新增业务名称弹层
  const KeyForm = CreateForm<{
    tab_key: string[]
  }>()
  const dialogForm = ref({
    tab_key: [''],
  })
  const dialogRef = ref(() => {})
  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      title: () => <div>新增业务名称</div>,
      body: () => (
        <div>
          <KeyForm
            class="flex w-full flex-col"
            hasAction={false}
            data={dialogForm.value}
            onChange={(path, value) => {
              set(dialogForm.value, path, value)
            }}
            error={error.value}
            items={[
              [
                '已存在的业务名称：',
                '',
                {
                  type: 'custom',
                  render: () => (
                    business_key_list.value.map(item => (
                      <div class="flex h-[30px] items-center justify-between rounded-[6px] bg-slate-100 px-[10px]">
                        <div class="flex-1">{item.key}</div>
                        <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                          const showTipsDialog = () => {
                            const hideDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: (
                                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-status-body>是否确认删除业务名称：{item.key}?</x-status-body>
                                  <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                    <button class="btn btn-primary btn-sm" onClick={() => {
                                      console.log(item.key)
                                      void apiBusinessKeyDel({
                                        ids: item.id + '',
                                      }).then(() => {
                                        showAlert('删除成功')
                                        void getBusinessKeyList(1)
                                        hideDialog()
                                      })
                                        .catch((error: any) => {
                                          showAlert(error.response.data.message, 'error')
                                        })
                                    }}
                                    >确定
                                    </button>
                                  </x-status-footer>
                                </x-status-confirm-dialog>
                              ),
                            })
                          }

                          showTipsDialog()
                        }}
                        />
                      </div>
                    ))
                  ),
                },
              ],
              [
                requiredLabel('新增业务名称（注意⚠️：这个修改，会影响数据埋点统计数据！）'),
                'tab_key',
                {
                  type: 'custom',
                  render: (r: any) => {
                    return (
                      <x-push-desc-list class="flex w-full flex-col gap-2">
                        {
                          (r.value || [])
                            .map((i: any, idx: number) => (
                              <div class={mc('flex items-center gap-2')}>
                                <div class="flex items-center">
                                  <span class="pr-[10px]">业务名称:</span>

                                  <span class={mc('input input-bordered flex items-center gap-1 h-8 text-xs')}>
                                    <input
                                      type="text"
                                      class="w-[300px]"
                                      value={i || ''}
                                      onInput={(e: any) => {
                                        if (!dialogForm.value.tab_key) {
                                          return
                                        }
                                        const value = trim(e.target.value)
                                        dialogForm.value.tab_key[idx] = value + ''
                                      }}
                                    />
                                  </span>
                                </div>
                                <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                  if (!dialogForm.value.tab_key) {
                                    return
                                  }
                                  dialogForm.value.tab_key.splice(idx, 1)
                                }}
                                />
                              </div>
                            ))
                        }
                      </x-push-desc-list>

                    )
                  },
                },
                {
                  class: 'p-4 bg-gray-100 rounded-lg w-[100%]',
                },
              ],
            ]}
          />
          <div class="flex justify-center gap-x-2 px-[20px] pt-[10px]">
            <Button
              class="btn btn-outline btn-sm"
              onClick={() => {
                if (!dialogForm.value.tab_key) {
                  dialogForm.value.tab_key = []
                }
                dialogForm.value.tab_key.push('')
              }}
            >新增
            </Button>
            <Button class="btn btn-primary btn-sm" onClick={
              () => {
                console.log(dialogForm.value.tab_key.join(','))
                const arrStr = dialogForm.value.tab_key.filter((item: string) => {
                  return item !== ''
                })
                console.log(arrStr.join(','))
                if (arrStr.length < 1) {
                  return
                }
                void apiBusinessKeyAdd({
                  type: 1,
                  key: arrStr.join(','),
                }).then(() => {
                  showAlert('添加成功')
                  dialogForm.value = { tab_key: [''] }
                  void getBusinessKeyList(1)
                  dialogRef.value()
                })
                  .catch((error: any) => {
                    showAlert(error.response.data.message, 'error')
                  })
              }
            }
            >提交
            </Button>
            <Button class="btn  btn-sm" onClick={dialogRef.value}>取消</Button>

          </div>
        </div>
      ),
    })
  }

  onMounted(async () => {
    page.value = 1
    pageSize.value = 9999
    tab.value.language = tab.value.language || [{ content: '', lang: '' }]
    await search(page.value)
    await getConfig()
    await getBusinessKeyList(1)
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="mb-3 grid flex-1 grid-cols-1 gap-y-1"
          data={tab.value as M.EpisodeTheatresChannel.List.Item}
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'label_ids') {
              const ids = (value as string || '').split(',').map(Number)
              const labels = tags.value.filter(item => ids.includes(item.label_id))
              set(tab.value || {}, 'label_names', labels.map(item => item.content).join(','))
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('频道名称'),
              path: 'tab_name',
              input: {
                type: 'text',
                placeholder: '请输入频道名称',
              },
              class: 'col-span-3',
            },
            // {
            //   label: requiredLabel('频道业务名称'),
            //   path: 'tab_key',
            //   input: {
            //     type: 'select',
            //     options: [{
            //       value: 'popular',
            //       label: 'popular',
            //     }, {
            //       value: 'free',
            //       label: 'free',
            //     }, {
            //       value: 'new',
            //       label: 'new',
            //     }, {
            //       value: 'vip',
            //       label: 'vip',
            //     }, {
            //       value: 'female',
            //       label: 'female',
            //     }, {
            //       value: 'male',
            //       label: 'male',
            //     }, {
            //       value: 'exclusive',
            //       label: 'exclusive',
            //     }],
            //     autoInsertEmptyOption: false,
            //   },
            //   class: 'col-span-3',
            // },
            [{
              label: requiredLabel('频道业务名称'),
              path: 'tab_key',
              input: {
                type: 'select',
                options: business_key_list.value.map((n, index) => {
                  return { value: n.key, label: n.key }
                }),
                autoInsertEmptyOption: false,
              },
              class: 'w-[300px]',
            },
            () => (
              <div class="flex items-center">
                <Button class=" btn btn-primary btn-sm" onClick={() => showCreateDialog()}>新增业务名称
                </Button>
              </div>
            )],
            {
              label: '描述',
              path: 'tab_desc',
              input: {
                type: 'textarea',
                placeholder: '描述',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('排序（此排序会影响频道页面顺序）'),
              path: 'tab_index',
              transform: transformInteger,
              input: {
                type: 'text',
                placeholder: '请输入排序',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('状态'),
              path: 'status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
              class: 'col-span-3',
            },
            [<div class="flex items-center">{requiredLabel('关联模块')}<span class="block text-red-400 text-xs pl-2">注意：双排popular choice和无限三排流排序最大</span></div>, 'ad_id', {
              type: 'custom',
              render: ({ item, value }) => {
                return (
                  <section class="rounded-lg bg-gray-100 p-4">
                    <x-table-actions class="flex items-center justify-between">
                      <div class="space-x-2">
                        <Button class="btn btn-primary btn-sm" onClick={() => showImportDramaDialog()}>导入模块</Button>
                        <span class="text-sm text-gray-500" />
                      </div>
                    </x-table-actions>
                    <hr class="my-4" />
                    <SpecialOfferTable
                      list={tab.value.module ?? []}
                      columns={specialOfferColumns}
                      class="tm-table-fix-last-column"
                    />
                  </section>
                )
              },
            }, { class: 'col-span-3' }],
            [
              '多语言文案配置',
              'language',
              {
                type: 'custom',
                render: (r: any) => {
                  console.log('r.value', r.value)
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      <div class="flex pl-5">
                        {requiredLabel('频道标题:')}
                        <RadioGroup
                          class="tm-radio ml-6"
                          options={[
                            {
                              value: 1,
                              label: '展示标题',
                            },
                            {
                              value: 2,
                              label: '不展示标题',
                            },
                          ]}
                          modelValue={tab.value.show_title}
                          onUpdate:modelValue={(e: unknown) => tab.value.show_title = e as 2 | 1}
                        />
                      </div>
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <x-type-select class="mx-[20px] w-[200px]">
                                {requiredLabel('应用语言:')}
                                <select
                                  // id={id}
                                  class="select select-bordered select-sm w-full"
                                  value={i.lang || 0}
                                  onInput={(e: any) => i.lang = e.target.value}
                                >
                                  {
                                    languageOptions.value.map((n, index) => <option value={languageOptions.value[index]}>{languageOptions.value[index]}</option>)
                                  }
                                </select>
                              </x-type-select>
                              <div class="flex flex-col">
                                文案内容:
                                <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                                  <textarea
                                    rows="1"
                                    placeholder="请输入文案"
                                    class="textarea textarea-bordered w-[300px] border-none"
                                    value={i.content}
                                    onInput={(e: any) => {
                                      if (!tab.value.language) {
                                        return
                                      }
                                      const value = e.target.value
                                      tab.value.language[idx].content = value + ''
                                    }}
                                  />
                                </span>
                              </div>
                              <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                if (!tab.value.language) {
                                  return
                                }
                                tab.value.language.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                      <div class="flex justify-center">
                        <Button
                          class="btn btn-outline btn-sm mr-6"
                          onClick={() => {
                            tab.value.language = []
                            languageOptions.value.map((i, index) => {
                              tab.value.language?.push({
                                content: '',
                                lang: i,
                              })
                            })
                          }}
                        >全语言
                        </Button>
                        <Button
                          class="btn btn-outline btn-sm"
                          onClick={() => {
                            if (!tab.value.language) {
                              tab.value.language = []
                            }

                            tab.value.language.push({
                              content: '',
                              lang: '',
                            })
                          }}
                        >新增
                        </Button>
                      </div>
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg',
              },
            ],

          ]}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          // void onSave(tab.value as M.EpisodeTheatresChannel.List.Item)
          const d = tab.value as M.EpisodeTheatresChannel.List.SaveItem
          void apiSaveEpisodeChannel(d).then(res => {
            console.log('>>> d')
            if (d?.data_type == 'theatres_configure_detail') { // 如果有这个属性，表示是树结构过来的
              void treeEpisodeTheatreData()
            } else {
              void theatresChannelSearch() // 列表刷新
            }
            hideTagsFormDialog.value && hideTagsFormDialog.value()
          }).catch((error: any) => {
            showAlert(error.response.data.message || '保存失败', 'error')
          })
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
