import { httpClient } from 'src/lib/http-client'
export const apiGetDialogConfig = () => httpClient.get<ApiResponse<M.PopupConfig>>('/popup/config/items', {})

// 剧场
export const apiGetEpisodeTheatreList = (data: M.EpisodeTheatres.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeTheatres.List.Response>>('/homepage/theater/list', data)

export const apiUpdateEpisodeTheatreStatus = (data: M.EpisodeTheatres.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/theater/update-status', data)

export const apiSaveEpisodeTheatre = (data: M.EpisodeTheatres.Save.Request) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/homepage/theater/edit', data)

export const apiCopyEpisodeTheatre = (data: { id: number }) =>
  httpClient.post<ApiResponse<M.EpisodeTheatres.Save.Request>>('/homepage/theater/copy', data)
export const apiTreeEpisodeTheatre = (data: { id: number }) =>
  httpClient.post<ApiResponse<M.EpisodeTheatres.Tree.Request>>('/homepage/theater/tree', data)

export const apiTheaterLayers = (data: { app_id?: number }) =>
  httpClient.post<ApiResponse<M.EpisodeTheatres.TheaterLayers.Request>>('/homepage/theater/layers', data)

// 频道
export const apiGetEpisodeChannelList = (data: M.EpisodeTheatresChannel.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeTheatresChannel.List.Response>>('/homepage/tab/list', data)

export const apiUpdateEpisodeChannelStatus = (data: M.EpisodeTheatresChannel.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/tab/update-status', data)

export const apiSaveEpisodeChannel = (data: M.EpisodeTheatresChannel.List.SaveItem) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/homepage/tab/edit', data)

export const apiUpdateEpisodeTabUpdateSort = (data: M.EpisodeTheatresChannel.UpdateSortStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/theater/update-tab-sort', data)

// 模块
export const apiGetEpisodeModuleList = (data: M.EpisodeModule.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeModule.List.Response>>('/homepage/module/list', data)

export const apiSaveEpisodeModule = (data: M.EpisodeModule.Save.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/module/edit', data)

export const apiUpdateEpisodeModuleStatus = (data: M.EpisodeModule.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/module/update-status', data)

export const apiUpdateEpisodeModuleUpdateSort = (data: M.EpisodeModule.UpdateSortStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/tab/update-module-sort', data)

export const apiModuleLabels = () =>
  httpClient.post<ApiResponse< M.EpisodeModule.ModuleLabels.Response>>('/homepage/module/labels')

export const apiBusinessKeyList = (data: M.EpisodeModule.BusinessKeyList.Request) =>
  httpClient.post<ApiResponse< M.EpisodeModule.BusinessKeyList.Response>>('/homepage/business-key/list', data)

export const apiBusinessKeyAdd = (data: M.EpisodeModule.BusinessKeyList.addItem) =>
  httpClient.post<ApiResponse< boolean>>('/homepage/business-key/add', data)

export const apiBusinessKeyDel = (data: { ids: string }) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/business-key/del', data)

// 运营位
export const apiPopularChoiceList = () =>
  httpClient.post<ApiResponse<M.OperatingPosition.module.Request>>('/homepage/module/popular-choice/list')

export const apiAdSlotList = (data: M.OperatingPosition.List.Request) =>
  httpClient.post<ApiResponse<M.OperatingPosition.List.Response>>('/homepage/ad-slot/list', data)

export const apiAdSlotEdit = (data: M.OperatingPosition.List.SaveItem) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/ad-slot/edit', data)

export const apiAdSlotUpdateStatus = (data: M.OperatingPosition.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/ad-slot/update-status', data)

// 运营项
export const apiAdSlotItemList = (data: M.OperatingItem.List.Request) =>
  httpClient.post<ApiResponse<M.OperatingItem.List.Response>>('/homepage/ad-slot-item/list', data)

export const apiAdSlotItemEdit = (data: M.OperatingItem.List.SaveItem) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/ad-slot-item/edit', data)

export const apiAdSlotItemUpdateStatus = (data: M.OperatingItem.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/ad-slot-item/update-status', data)
