/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert, showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { omit, cloneDeep } from 'lodash-es'
import { apiAdSlotItemUpdateStatus, apiAdSlotList, apiAdSlotItemList, apiAdSlotItemEdit, apiGetDialogConfig, apiModuleLabels, apiGetEpisodeChannelList, apiBusinessKeyList } from './episode-theatre-api'
import { apiGetContentTemplateList } from '../content-template/content-template-api'
import { OperationalItemForm } from './operational-item-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useOperationalItem = () => {
  return {
    tags, Form, tabParams, Table, total, list, loading, search, positionList, searchPosition, languageOptions, getConfig, changeTab, checkedItems, batchDelete, batchUp, batchDown,
    copyBtn, showTagsFormDialog, operItemForm, hideTagsFormDialog, onSave, onPageChange, onPageSizeChange,
  }
}

const Form = CreateForm<M.EpisodeModule.List.Request>()
const tabParams = ref<M.EpisodeModule.List.Request>({ page_info: { page_index: 1, page_size: 10 } })

const Table = CreateTableOld<M.OperatingItem.List.Item>()
const list = ref<M.OperatingItem.List.Item[]>([])
const loading = ref<boolean>(false)
const languageOptions = ref<string[]>([])
const tags = ref<M.ITag[]>([])
const total = ref<number>(0)
const positionList = ref<M.OperatingPosition.List.Item[]>([])

const defaultOperItemForm = {
  ad_slot_type: 1,
  status: 2,
  show_title: 2,
  link_type: 1,
  title: [],
  covers: [],
}
const operItemForm = ref<Partial<M.OperatingItem.List.SaveItem>>({ ...defaultOperItemForm })

const checkedItems = ref<M.OperatingItem.List.SaveItem[]>([])

const search = async () => {
  loading.value = true
  const res = await apiAdSlotItemList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  return list.value
}
const searchPosition = async () => {
  const res = await apiAdSlotList({
    page_info: {
      page_index: 1,
      page_size: 99999,
    },
  })
  positionList.value = res.data?.list || []
  return positionList.value
}

const getConfig = async () => {
  const res = await apiGetDialogConfig()
  if (!res?.data) {
    return
  }
  languageOptions.value = Object.values(res?.data.lang_items)
  return languageOptions.value
}

const onPageChange = async (page_index: number) => {
  tabParams.value.page_info = {
    ...(tabParams.value.page_info || {}),
    page_index,
  }
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  tabParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  await search()
}

const changeTab = (item: Partial<M.EpisodeModule.List.Item>) => {
  const data: any = cloneDeep(item)
  operItemForm.value = Object.assign({}, defaultOperItemForm, data)
  console.log(operItemForm.value)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeModule.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <OperationalItemForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const copyBtn = async (row: M.EpisodeModule.List.SaveItem) => {
  // try {
  //   let d = row
  //   d = {
  //     ...omit(d, ['id', 'status', 'tab']),
  //     status: 2,
  //     name: d.name + '副本',
  //   }
  //   await apiAdSlotItemEdit(d)
  //   showAlert('复制成功')
  //   void search()
  // } catch (error: any) {
  //   showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  // }
}
// const checkFieldForUndefined = (arr: any, field: any) => {
//   for (let i = 0; i < arr.length; i++) {
//     if (arr[i][field] === undefined) {
//       return true
//     }
//   }
//   return false
// }
const onSave = () => {
  const d: any = operItemForm.value
  console.log(operItemForm.value)

  // const hasUndefined = checkFieldForUndefined(d, 'cover')
  // console.log(hasUndefined)
  // if (hasUndefined) { // 检查 'cover' 字段是否包含 undefined,包含证明语言没有全部对应图片
  //   showFailToast('封面配置要包含所有语言哦～')
  // } else {
  void apiAdSlotItemEdit(d).then(d => {
    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.err_msg || '保存失败', 'error')
  })
  // }
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiAdSlotItemUpdateStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                status: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiAdSlotItemUpdateStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiAdSlotItemUpdateStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: -1,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
