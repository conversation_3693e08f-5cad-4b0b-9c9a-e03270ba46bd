/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep, omit } from 'lodash-es'
import { apiAdSlotUpdateStatus, apiAdSlotList, apiAdSlotEdit, apiPopularChoiceList } from './episode-theatre-api'
import { OperatingPositonForm } from './operating-positon-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useOperatingPositon = () => {
  return {
    tags, Form, tabParams, Table, total, list, loading, search, moduleIdList, getModuleIdList, changeTab, operatingPostionTab, checkedItems, batchDelete, batchUp, copyBtn, batchDown,
    showTagsFormDialog, hideTagsFormDialog, onSave, onPageChange, onPageSizeChange,
  }
}

const Form = CreateForm<M.EpisodeTheatresChannel.List.Request>()

const Table = CreateTableOld<M.OperatingPosition.List.Item>()
const list = ref<M.OperatingPosition.List.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([])
const moduleIdList = ref<M.OperatingPosition.module.listItem[]>([])
const total = ref<number>(0)

const voidTab = {
  content_key: 'popular_choice',
  status: 2,
}
const operatingPostionTab = ref<Partial<M.OperatingPosition.List.SaveItem>>({
  ...voidTab,
})

const tabParams = ref<Partial<M.EpisodeTheatresChannel.List.Request>>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})

const checkedItems = ref<M.OperatingPosition.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiAdSlotList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  return list.value
}
const getModuleIdList = async () => {
  const res = await apiPopularChoiceList()
  if (!res?.data) {
    return
  }
  moduleIdList.value = res?.data.list
  console.log(moduleIdList.value)

  return moduleIdList.value
}
const onPageChange = async (page_index: number) => {
  tabParams.value.page_info = {
    ...(tabParams.value.page_info || {}),
    page_index,
  }
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  tabParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  await search()
}
const getValuesByKey = (jsonArray: any, key: string) => {
  const result: any = []
  jsonArray.forEach((item: any) => {
    if (item.hasOwnProperty(key)) {
      result.push(item[key])
    }
  })
  return result
}
const changeTab = (item: any) => {
  const data: any = cloneDeep(item)
  if (item.id) {
    // 处理数据
    data.app_id = item.app_id.split(',').map(Number)
    data.app_name = item.app_name.split(',')
    data.modules = getValuesByKey(item.modules, 'id')
  }
  operatingPostionTab.value = Object.assign({}, voidTab, data)
  console.log(operatingPostionTab.value)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeTheatresChannel.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <OperatingPositonForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}
const copyBtn = async (row: M.EpisodeTheatresChannel.List.SaveItem) => {
  // try {
  //   let d = row
  //   d = {
  //     ...omit(d, ['id', 'status', 'module', 'theater']),
  //     status: 2,
  //     // operatingPostionTab_name: d.operatingPostionTab_name + '副本',
  //     module: [],
  //   }
  //   console.log(d)

  //   await apiAdSlotEdit(d)
  //   showAlert('复制成功')
  //   void search()
  // } catch (error: any) {
  //   showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  // }
}

const onSave = () => {
  console.log(operatingPostionTab.value)
  // 处理数据
  const data: any = cloneDeep(operatingPostionTab.value)
  data.app_id = operatingPostionTab.value.app_id?.join(',')
  data.app_name = operatingPostionTab.value.app_name?.join(',')
  const jsonArray = data.modules.map((id: number) => ({ id: id }))
  data.modules = jsonArray
  console.log(data)
  void apiAdSlotEdit(data).then(d => {
    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.message || '保存失败', 'error')
  })
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架运营位【{checkedItems.value.map(item => item.id).join('、')}】?</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiAdSlotUpdateStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                status: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架运营位【{checkedItems.value.map(item => item.id).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiAdSlotUpdateStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除运营位【{checkedItems.value.map(item => item.id).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiAdSlotUpdateStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: -1,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
