/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { apiGetEpisodeTheatreList, apiSaveEpisodeTheatre, apiUpdateEpisodeTheatreStatus, apiCopyEpisodeTheatre, apiTreeEpisodeTheatre, apiTheaterLayers } from './episode-theatre-api'
import { EpisodeTagsForm } from './episode-theatre-form'
import { ConfigureDetail } from './episode-configure-detail'
import { showModeOptions, content_type_two_options } from './show-mode-options.ts'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeTheatres = () => {
  return {
    Form, tabParams, Table, total, list, loading, search, changeTab, tab, checkedItems, copyBtn, configureDetailBtn, configureDetailId, hideConfigureDetailDialog, batchDelete, batchUp, batchDown,
    showTagsFormDialog, hideTagsFormDialog, onSave, onPageChange, onPageSizeChange, strategy_list, strategy_search, pageShowStatus, showConfigureChildrenData, treeEpisodeTheatreData,
  }
}

const Form = CreateForm<M.EpisodeTheatres.List.Request>()

const Table = CreateTableOld<M.EpisodeTheatres.List.Item>()
const total = ref<number>(0)
const list = ref<M.EpisodeTheatres.List.Item[]>([])
const loading = ref<boolean>(false)
// const configureDetailData = ref<M.EpisodeTheatres.Tree.Request>()
const configureDetailId = ref<number>()

const strategy_list = ref<{ id: number, name: string }[]>([])

const voidTab = {
  id: 0,
  name: '', // 名称
  status: 2, // 状态 1 上架 2 下架
  place_status: 2,
}
const tab = ref<Partial<M.EpisodeTheatres.List.Item>>({
  ...voidTab,
})
const tabParams = ref<Partial<M.EpisodeTheatres.List.Request>>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})
const checkedItems = ref<M.EpisodeTheatres.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiGetEpisodeTheatreList({
    ...tabParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  return list.value
}
const onPageChange = async (page_index: number) => {
  tabParams.value.page_info = {
    ...(tabParams.value.page_info || {}),
    page_index,
  }
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  tabParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  await search()
}
const changeTab = (item: Partial<M.EpisodeTheatres.List.Item>, title: string) => {
  tab.value = Object.assign({}, voidTab, item)
  if (title != '新建剧场' && (tab.value.strategy_id?.length == 0 || tab.value.strategy_id == undefined)) {
    tab.value.place_status = 1
  } else { tab.value.place_status = 2 }
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeTheatres.List.Item>, title: string) => {
  changeTab(d, title)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeTagsForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}
const copyBtn = async (row: M.EpisodeTheatres.List.Item) => {
  try {
    console.log(row)
    await apiCopyEpisodeTheatre({ id: row.id! })
    showAlert('复制成功')
    void search()
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  }
}
const hideConfigureDetailDialog = ref()
const configureDetailBtn = (id: number) => {
  try {
    console.log(id)
    configureDetailId.value = id
    hideConfigureDetailDialog.value = openDialog({
      title: '配置详情',
      body: () => <ConfigureDetail />,
      mainClass: dialogMainClass,
      customClass: '!w-[800px]',
    })
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '查询失败！')
  }
}

const strategy_search = async (app_id: any) => {
  console.log(app_id)

  const res = await apiTheaterLayers({
    app_id: app_id,
  })
  strategy_list.value = res.data?.list || []
}

const onSave = (d: M.EpisodeTheatres.List.Item) => {
  if (d.is_abtest == 1) { d.strategy_id = [], d.strategy_name = [] }
  void apiSaveEpisodeTheatre(d).then(d => {
    console.log('>>> d')

    hideTagsFormDialog.value && hideTagsFormDialog.value()
    void search()
  }).catch((error: any) => {
    showAlert(error.response.data.message || '保存失败', 'error')
  })
}

const pageShowStatus = ref(false)
const configureDetailData = ref<M.EpisodeTheatres.Tree.Request>()
const showConfigureChildrenData = ref<M.EpisodeTheatres.Tree.Request[]>([])
const treeEpisodeTheatreData = async () => {
  pageShowStatus.value = false
  const res = await apiTreeEpisodeTheatre({ id: configureDetailId.value! })
  console.log(res)
  configureDetailData.value = res?.data || {}
  console.log(configureDetailData.value)
  handleDataWay()
}
const extractIds = (data: unknown) => {
  if (!Array.isArray(data)) {
    return []
  }
  return data
    .filter(item => item && typeof item === 'object' && 'id' in item)
    .map(item => String(item.id))
    .join(',')
}
const handleTab = (tab: any) => {
  console.log(tab)
  tab.map((i: any) => {
    i.title = i.tab_name
    i.data_type = 'theatres_configure_detail' // 修改频道、模块数据做区分
    if (i.module) {
      i.children = i.module
      i.children.map((j: any) => {
        j.title = j.module_name
        j.data_type = 'theatres_configure_detail'
        if (j.show_mode) {
          const show_mode = showModeOptions.find(item => j.show_mode === item.value)?.label || j.show_mode || '无'
          const type = content_type_two_options.find(item => j.content_type === item.value)?.label || j.content_type || '无'
          // 如果show_mode是双排流，则显示数据源+运营位ad_slot; 如果是banner后面就显示她的相关信息；
          if (show_mode == '双排') {
            j.children = [{ title: show_mode, children: [
              { title: type },
              { title: j.ad_slot.ad_slot.name || '' },
            ] }]
          } else if (show_mode == 'Banner') {
            const banner = extractIds(j.banners || []) || '无'
            j.children = [{ title: show_mode, children: [
              // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
              { title: type + '  ----  bannerID:' + banner },
              // { title: j.ad_slot }, // 广告位
            ] }]
          } else {
            j.children = [{ title: show_mode + '  ----  ' + type, children: [] }]
          }
        }
      })
    }
  })
  return tab
}
const handleDataWay = () => {
  const showConfigureDetailData = cloneDeep(configureDetailData.value)
  if (showConfigureDetailData?.tab && showConfigureDetailData?.tab.length) {
    showConfigureDetailData.tab = handleTab(showConfigureDetailData?.tab)
    showConfigureDetailData?.tab?.unshift({ title: '剧场信息', children: [{ title: '用户分层', detail: showConfigureDetailData?.strategy_name ? showConfigureDetailData?.strategy_name?.join('、') : '无' }, { title: 'AB实验', detail: showConfigureDetailData?.is_abtest ? '是' : '无' }] })
  } else {
    showConfigureDetailData!.tab = [{ title: '剧场信息', children: [{ title: '用户分层', detail: showConfigureDetailData?.strategy_name ? showConfigureDetailData?.strategy_name?.join('、') : '无' }, { title: 'AB实验', detail: showConfigureDetailData?.is_abtest ? '是' : '无' }] }]
  }
  console.log(showConfigureDetailData)
  showConfigureChildrenData.value = [{ title: '剧场', children: showConfigureDetailData?.tab }]
  console.log(showConfigureChildrenData.value)
  pageShowStatus.value = true
}
const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架剧场【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeTheatreStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                status: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认下架剧场【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeTheatreStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: 2,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const batchDelete = () => {
  const hideDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>是否确认删除剧场【{checkedItems.value.map(item => item.name).join('、')}】?</x-status-body>
        <x-status-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiUpdateEpisodeTheatreStatus({
              ids: checkedItems.value.map(item => item.id || 0),
              status: -1,
            })
              .then(() => {
                void search()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
            hideDialog()
          }}
          >确定
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}
