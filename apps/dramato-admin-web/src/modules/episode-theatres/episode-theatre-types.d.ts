declare namespace M {
  namespace EpisodeTheatres {
    namespace List {
      interface Request {
        is_abtest?: 0 | 1
        app_id?: number
        id_name?: string
        strategy_id?: number
        page_info?: {
          page_index: number
          page_size?: number
        }
        status?: number // -1 删除  1 上架  2下架
      }
      interface langItem {
        lang: string
        content: string
        item_id?: number
      }
      interface moduleItem {
        id: number
        language: langItem[] // 语言
        tab: { // 所属tab
          id: number
          tab_name: string
        }
        platform: number // 平台 1-IOS 2-Android
        module_name: string // 名称
        module_index: number // 排序
        module_desc: string // 描述
        module_key: string // 唯一key， 业务名称
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
      }
      interface tabItem {
        id: number
        language: langItem[] // 语言
        module: moduleItem[] // 关联模块
        theater: {
          id: number
          name: string
        }
        tab_name: string // 名称
        tab_index: number // 排序
        tab_desc: string // 描述
        tab_key: string // 唯一key， 业务名称
        show_title: number
        show_mode: string
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架 -1删除
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
      }
      interface Item {
        id?: number
        app_id: Number[]
        app_name: string[]
        name: string // 剧场名称
        tab: tabItem[]
        index: number // 排序
        remark: string // 备注
        desc: string
        theater_key: string // 业务名称
        status: number
        strategy_name: string[]// 分层名称
        strategy_id: number[]// 分层id,
        updated: number
        updated_user: string
        created: number
        created_user: string
        place_status?: number // 是否兜底状态 1 是 2 否
        is_abtest?: 0 | 1
      }

      interface Response {
        total: number
        list: Item[]
      }
    }

    namespace Save {
      interface Request extends List.Request {
        id?: number
        app_id: Number[]
        app_name: string[]
        name: string // 剧场名称
        tab: tabItem[]
        index: number // 排序
        desc: string
        theater_key: string // 业务名称
        status: number
        strategy_name?: string[]// 分层名称
        strategy_id: number[]// 分层id,
        is_abtest?: 0 | 1
      }
    }
    namespace Tree {
      interface languageItem {
        lang: string
        content: string
      }
      interface moduleItem {
        id?: number
        module_name: string
        module_key?: string
        module_index?: number
        language?: languageItem[]
        banners?: [{}] // banner管理里面的结构
        ad_slot?: {} // ad_slot_item里面的结构
        other_msg?: string
        tab?: unknown
        show_mode?: string
        show_title?: number
        content_type?: number
        content_key?: string
        status?: number
        module_desc?: string
        creator_user?: string
        creator_id?: string
        updated_user?: string
        updated_id?: string
        created?: number
        updated?: number
      }
      interface childrenItem {
        title?: string
        status?: number
        detail?: string
        children?: childrenItem[]
      }
      interface tabItem {
        id?: number
        tab_name?: string
        tab_key?: string
        tab_index?: number
        status?: number
        module?: moduleItem[]
        title?: string
        children?: childrenItem[]
      }

      interface Request {
        id?: number
        app_id?: number | number[]
        app_name?: string | string[]
        name?: string
        tab?: tabItem[]
        index?: number
        remark?: string
        status?: number
        is_abtest?: number
        strategy_name?: sting[]
        strategy_id?: sting[]
        updated?: number
        updated_user?: string
        updated_id?: string
        created?: number
        created_id?: string
        created_user?: string
        title?: string
        children?: childrenItem[]
      }

      interface ShowData {
        title?: string
        status?: number
        detail?: string
        children?: childrenItem[]
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        status: number // 是否上架  1 上架 2 下架 -1删除
      }
    }
    namespace TheaterLayers {
      interface listItem {
        id: number
        name: string
      }
      interface Request {
        list: listItem[]
      }
    }
  }
}
