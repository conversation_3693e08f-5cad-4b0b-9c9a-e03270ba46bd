declare namespace M {
  namespace EpisodeModule {
    namespace List {
      interface Request {
        tab_id?: number
        id_name?: string
        page_info?: {
          page_index: number
          page_size?: number
        }
        status?: number // -1 删除  1 上架  2下架
      }
      interface langItem {
        lang: string
        content: string
        item_id?: number
      }
      interface Item {
        id: number
        language: langItem[] // 语言
        tab: { // 所属tab
          id: number
          tab_name: string
        }
        platform: number // 平台 1-IOS 2-Android
        module_name: string // 名称
        module_index: number // 排序
        module_desc: string // 描述
        module_key: string // 唯一key， 业务名称
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架
        creator_user: string // 创建人
        updated_user: string // 修改人
        created: number // 创建时间
        updated: number // 更新时间
        isEdit?: boolean
      }
      interface SaveItem {
        id?: number
        tab?: { // 所属tab
          id: number
          tab_name: string
        }
        language: langItem[] // 语言
        module_name: string // 名称
        module_index: number // 排序
        module_desc: string // 描述
        module_key: string // 唯一key， 业务名称
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number // 状态 1 上架 2 下架
        data_type?: string
      }

      interface Response {
        total: number
        list: Item[]
      }
    }

    namespace Save {
      interface Request extends List.Request {
        id?: number
        language: langItem[]
        module_name: string
        module_key: string
        module_index: number
        show_mode: string
        show_title: number
        content_type: number
        content_key: string
        status: number
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        status: number // 是否上架  1 上架 2 下架
      }
    }

    namespace UpdateSortStatus{
      interface modulesItem {
        id?: number
        module_index: number
      }
      interface Request extends List.Request {
        modules: modulesItem[]
      }
    }
    namespace ModuleLabels{
      interface labelsItem {
        id: number | string
        label: string
      }
      interface Response {
        labels: labelsItem[]
      }
      // interface Request extends List.Request {
      //   modules: modulesItem[]
      // }
    }

    namespace BusinessKeyList{
      interface Request {
        type: number // 1 tab; 2 module
      }
      interface keyListItem {
        id: number | string
        key: string
      }
      interface Response {
        list: keyListItem[]
      }
      interface addItem {
        type: number // 1 tab; 2 module
        key: string
      }
    }
  }
}
