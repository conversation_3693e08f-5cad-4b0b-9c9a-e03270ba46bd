/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { Button, Checkbox, DateTime, openDialog, showAlert, transformNumber, Pager, transformInteger } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { cloneDeep, set } from 'lodash-es'
import { RouterLink } from 'vue-router'
import { useEpisodeTheatres } from './use-episode-theatres'
import { apiUpdateEpisodeTheatreStatus } from './episode-theatre-api'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer.tsx'

type EpisodeTagsOptions = {
  props: {}
}
export const EpisodeTags = createComponent<EpisodeTagsOptions>({
  props: {},
}, props => {
  const { Form, tabParams, Table, total, list, loading, search, checkedItems, copyBtn, configureDetailBtn, batchDelete, batchUp, batchDown, showTagsFormDialog, onPageChange, onPageSizeChange, strategy_list, strategy_search } = useEpisodeTheatres()

  const { appOptions } = useAppAndLangOptions(() => tabParams.value.app_id, {
    onSuccess: search,
  })

  onMounted(() => {
    // void strategy_search(tabParams.value.app_id)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class="space-y-2">
            <ul>
              <li>剧场管理</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs inline-block bg-slate-200">
              <RouterLink to="theatre" class="tab w-[150px]" activeClass="tab-active">剧场</RouterLink>
              <RouterLink to="channel" class="tab w-[150px]" activeClass="tab-active">频道</RouterLink>
              <RouterLink to="module" class="tab w-[150px]" activeClass="tab-active">模块</RouterLink>
              <RouterLink to="operating-positon" class="tab w-[150px]" activeClass="tab-active">运营位</RouterLink>
              <RouterLink to="operational-item" class="tab w-[150px]" activeClass="tab-active">运营项</RouterLink>
            </x-nav-tab>

          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(tabParams.value, path, value)
              // if (path === 'app_id') {
              //   set(tabParams.value, 'strategy_id', undefined)
              //   void strategy_search(tabParams.value.app_id)
              // }
            }}
            onReset={() => {
              tabParams.value = { page_info: { page_index: 1, page_size: 10 } }
              void search()
            }}
            onSubmit={() => {
              set(tabParams.value, 'page_info', { page_index: 1, page_size: 10 })
              void search()
            }}
            data={tabParams.value}
            items={[
              {
                label: '应用',
                path: 'app_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: appOptions.value,
                },
                class: 'w-[240px]',
              },
              // {
              //   label: '用户分层',
              //   path: 'strategy_id',
              //   transform: transformNumber,
              //   input: {
              //     type: 'select',
              //     autoInsertEmptyOption: true,
              //     options: strategy_list.value.map((n, index) => {
              //       return { value: n.id, label: `${n.id}/${n.name}` }
              //     }),
              //   },
              //   class: 'w-[240px]',
              // },
              {
                label: '剧场',
                path: 'id_name',
                input: {
                  type: 'text',
                  placeholder: '请输入剧场ID、名称',
                },
                class: 'w-[240px]',
              },
              {
                label: '上架状态',
                path: 'status',
                transform: transformNumber,
                input: {
                  type: 'select',
                  autoInsertEmptyOption: true,
                  options: [{
                    value: 1,
                    label: '上架',
                  }, {
                    value: 2,
                    label: '下架',
                  }],
                },
                class: 'w-[140px]',
              },
              ['是否AB实验', 'is_abtest', {
                type: 'select',
                options: [
                  { label: '否', value: 0 },
                  { label: '是', value: 1 },
                ],
              }, { transform: transformInteger }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <span />
            <x-actions class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchUp}>批量上架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDown}>批量下架</Button>
              <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '新建剧场')}>新建剧场</Button>
            </x-actions>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['', row => (
              <Checkbox
                label=""
                modelValue={!!checkedItems.value.find(i => i.id === row.id)}
                onUpdate:modelValue={(v: boolean) => {
                  const idx = checkedItems.value.findIndex(i => i.id === row.id)
                  if (idx > -1) {
                    checkedItems.value.splice(idx, 1)
                  } else {
                    checkedItems.value.push(row)
                  }
                }}
              />
            ), { class: 'w-[40px]' }],
            ['id', 'id', { class: 'w-[100px]' }],
            ['剧场名称', 'name', { class: 'w-[100px]' }],
            ['剧场业务名称', 'theater_key', { class: 'w-[100px]' }],
            ['用户分层', row => (
              <div>{(row.strategy_name?.join('、'))}</div>
            ), { class: 'w-[200px]' }],
            ['是否AB实验', row => (
              <>
                {row.is_abtest === 1 ? '是' : '否'}
              </>
            ), { class: 'w-[100px]' }],
            ['已关联频道', row => (
              <div>{(Array.from(row.tab, ({ tab_name }) => tab_name).join('、'))}</div>
            ), { class: 'w-[200px]' }],
            ['应用', row => (
              <div>{(row.app_name?.join('、'))}</div>
            ), { class: 'w-[200px]' }],
            ['权重', 'index', { class: 'w-[100px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 0 },
              ].find(item => item.value === row.status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.created_user, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.updated_user, { class: 'w-[100px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.id) return
                      void apiUpdateEpisodeTheatreStatus({
                        ids: [row.id],
                        status: row.status !== 1 ? 1 : 2,
                      })
                        .then(() => {
                          void search()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    {row.status !== 1 ? '上架' : '下架'}
                  </Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '修改剧场')}>修改</Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => showTagsFormDialog(cloneDeep(row), '关联频道')}>关联频道</Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => copyBtn(cloneDeep(row))}>复制</Button>
                  <Button
                    class={mc('btn btn-outline btn-xs', row.status !== 1 ? '' : 'hidden')}
                    onClick={() => {
                      const showTipsDialog = () => {
                        const hideDialog = openDialog({
                          title: '',
                          mainClass: 'pb-0 px-5',
                          body: (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-status-body>是否确认删除剧场【{row.name}】?</x-status-body>
                              <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" onClick={() => {
                                  void apiUpdateEpisodeTheatreStatus({
                                    ids: [row.id || 0],
                                    status: -1,

                                  })
                                    .then(() => {
                                      void search()
                                    })
                                    .catch((error: any) => {
                                      showAlert(error.response.data.message, 'error')
                                    })
                                  hideDialog()
                                }}
                                >确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }

                      showTipsDialog()
                    }}
                  >
                    删除
                  </Button>
                  <Button class="btn btn-outline btn-xs" onClick={() => configureDetailBtn(row.id || 0)}>查看配置详情</Button>
                </div>
              ), {
                class: 'w-[400px]',
              },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={tabParams.value.page_info?.page_index || 1}
                size={tabParams.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeTags
