import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger, CreateTableOld, TableColumnOld, openDialog } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set, uniqBy } from 'lodash-es'
import { useEpisodeTheatres } from './use-episode-theatres.tsx'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer.tsx'
import { onMounted, ref } from 'vue'

import { EpisodeChannel } from './episode-theatre-channel'
import { useAppAndLangOptions } from '../options/use-app-options'

export const EpisodeTagsForm = createComponent(null, () => {
  const {
    tab,
    hideTagsFormDialog,
    onSave,
  } = useEpisodeTheatres()
  const Form = CreateForm<M.EpisodeTheatres.List.Item>()

  const {
    list: strategy_list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()

  // 导入模块
  const checkedDramaList = ref<M.EpisodeTheatresChannel.List.Item[]>([])
  const SpecialOfferTable = CreateTableOld<M.EpisodeTheatresChannel.List.Item>()
  const specialOfferColumns: TableColumnOld<M.EpisodeTheatresChannel.List.Item>[] = [
    ['id', 'id', { class: 'w-[100px]' }],
    ['排序', 'tab_index', { class: 'w-[150px]' }],
    ['频道名称', 'tab_name', { class: 'w-[150px]' }],
    ['频道业务名称', 'tab_key', { class: 'w-[150px]' }],
    ['关联剧场', row => (
      <div>{row.theater?.name}</div>
    ), { class: 'w-[150px]' }],
    ['关联模块个数', row => (
      <div>{row.module?.length || 0}</div>
    ), { class: 'w-[150px]' }],
    ['语言', row => (
      <div>{(Array.from(row.language, ({ lang }) => lang).join('、'))}</div>
    ), { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              (tab.value.tab || []).splice(idx, 1)
              checkedDramaList.value = checkedDramaList.value.filter(item => item.id !== row.id)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]
  const showImportDramaDialog = () => {
    checkedDramaList.value = [...tab.value.tab ?? []]
    const closeDialog = openDialog({
      title: '导入频道',
      mainClass: 'flex flex-col flex-auto pb-0 h-[80vh] overflow-hidden px-4 [&_.hide-when-in-dialog]:hidden',
      body: () => (
        <x-import-recharge-level class="gxp-y-4 flex flex-1 flex-col overflow-hidden">
          <x-episode-list class="flex-1  overflow-y-auto">
            <EpisodeChannel
              platform="tabPage"
              checkedItems={checkedDramaList.value}
              onAdd={item => {
                checkedDramaList.value = checkedDramaList.value.concat(item)
              }}
              onRemove={item => {
                checkedDramaList.value = checkedDramaList.value?.filter(i => i.id !== item.id)
              }}
            />
          </x-episode-list>
          <footer class="flex w-full justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              console.log(checkedDramaList.value)
              if (checkedDramaList.value.length === 0) return
              if (!tab.value.tab) {
                tab.value.tab = []
              }
              checkedDramaList.value = uniqBy(checkedDramaList.value, 'id')
              tab.value.tab = checkedDramaList.value.map((item, index) => ({
                ...item,
              }))
              console.log(tab.value.tab)
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-[80%] overflow-hidden',
    })
  }

  const formRules = z.object({
    name: z.string().min(1, '请输入标题'),
    theater_key: z.string().min(1, '请选择业务名称'),
  })

  const { error, validateAll } = useValidator(tab, formRules)
  const { appOptions } = useAppAndLangOptions(() => undefined, {})
  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid flex-1 grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange={(path, value: any) => {
            if (path === 'app_id') {
              const labels = appOptions.value.filter(item => value.includes(item.value))
              set(tab.value || {}, 'app_name', labels.map(item => item.label))
            }
            if (path === 'strategy_id') {
              const lab = strategy_list.value.filter(item => value.includes(item.id))
              set(tab.value || {}, 'strategy_name', lab.map(item => item.name))
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('剧场名称'),
              path: 'name',
              input: {
                type: 'text',
                placeholder: '请输入剧场名称',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('剧场业务名称'),
              path: 'theater_key',
              input: {
                type: 'select',
                options: [{
                  value: 'home',
                  label: 'home',
                }],
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: '描述',
              path: 'desc',
              input: {
                type: 'textarea',
                maxlength: 50,
                placeholder: '描述',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('应用'),
              path: 'app_id',
              input: {
                type: 'multi-select',
                popoverWrapperClass: 'z-popover-in-dialog',
                options: appOptions.value,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('权重'),
              path: 'index',
              transform: transformInteger,
              input: {
                type: 'text',
                placeholder: '权重会导致用户分层下发顺序',
              },
              class: 'col-span-3',
            },
            // {
            //   label: requiredLabel('用户分层（是表示用户分层，否为全量用户）'),
            //   path: 'place_status',
            //   transform: transformInteger,
            //   input: {
            //     type: 'radio',
            //     options: [
            //       { value: 2, label: '是' },
            //       { value: 1, label: '否' },
            //     ],
            //   },
            //   class: 'col-span-3',
            // },
            [
              requiredLabel('是否AB实验'),
              'is_abtest', {
                type: 'radio',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 },
                ],
              }, { transform: transformInteger }],
            tab.value.is_abtest !== 1 && [
              '分层画像',
              'strategy_id',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: strategy_list.value.map((n, index) => {
                  return { value: n.id, label: `${n.id}/${n.name}` }
                }),
              },
              {
                class: mc('col-span-3'),
              },
            ],
            {
              label: requiredLabel('状态'),
              path: 'status',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { value: 1, label: '上架' },
                  { value: 2, label: '下架' },
                ],
              },
              class: 'col-span-3',
            },
            [requiredLabel('关联频道'), 'ad_id', {
              type: 'custom',
              render: ({ item, value }) => {
                return (
                  <section class="rounded-lg bg-gray-100 p-4">
                    <x-table-actions class="flex items-center justify-between">
                      <div class="space-x-2">
                        <Button class="btn btn-primary btn-sm" onClick={() => showImportDramaDialog()}>导入频道</Button>
                        <span class="text-sm text-gray-500" />
                      </div>
                    </x-table-actions>
                    <hr class="my-4" />
                    <SpecialOfferTable
                      list={tab.value.tab ?? []}
                      columns={specialOfferColumns}
                      class="tm-table-fix-last-column"
                    />
                  </section>
                )
              },
            }],
          ]}
          data={tab.value as M.EpisodeTheatres.List.Item}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(tab.value as M.EpisodeTheatres.List.Item)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
