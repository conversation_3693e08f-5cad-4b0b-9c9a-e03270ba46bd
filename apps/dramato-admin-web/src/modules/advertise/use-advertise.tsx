/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiCreateAdvertise, apiDeleteAdvertise, apiEditAdvertise, apiGetAdvertiseList } from './advertise-api'
import { Button, openDialog, showAlert } from '@skynet/ui'
import { AdvertiseForm } from './advertise-form'
const periodMap = {
  daily: '每天',
  twelve_hours: '每12小时',
  six_hours: '每6小时',
  three_hours: '每3小时',
  hourly: '每小时',
}
const initSearchForm = {
  page_info: {
    size: 10,
    offset: 1,
  },
  platform: 'ios',
  advertise_id: '',
  advertise_unit: '',
}
const searchForm = ref<M.AdvertiseSearchParams>(initSearchForm)
const applicationList = ref<Array<Required<M.Application>>>([])
const list = ref<M.Advertise[]>([])
const total = ref<number>(0)
const loading = ref(false)

const getList = () => {
  loading.value = true
  void apiGetAdvertiseList(searchForm.value).then(res => {
    if (!res.data) return
    list.value = res.data.list
    total.value = res.data.page_info.total
  }).finally(() => {
    loading.value = false
  })
}

const onSearch = (isFirst?: boolean) => {
  if (isFirst) {
    searchForm.value.page_info = {
      size: 10,
      offset: 1,
    }
  }
  void getList()
}

const onReset = () => {
  searchForm.value = initSearchForm
  onSearch(true)
}

const onCreate = () => {
  const closeDialog = openDialog({
    title: '新增广告',
    body: (
      <AdvertiseForm advertise={{
        id: undefined,
        app_id: undefined,
        name: '',
        ad_unit: '',
        ad_type: 'Rewarded',
        coins: undefined,
        period: 'daily',
        mediation: 'bidding',
        count_limit: undefined,
        ad_platform: 'admob',
        netbits_group: [
          { name: '', link: '', type: 'time' },
        ],
      }} onCancel={() => closeDialog()} onSubmit={data => {
        void apiCreateAdvertise(data).then(res => {
          showAlert('新增成功')
          onSearch()
        }).finally(() => {
          closeDialog()
        })
      }}
      />
    ),
  })
}

const onEdit = (data: M.Advertise) => {
  const cloneData = { ...data }
  const closeDialog = openDialog({
    title: '编辑广告',
    body: (
      <AdvertiseForm advertise={cloneData} onCancel={() => closeDialog()} onSubmit={data => {
        void apiEditAdvertise(data).then(res => {
          showAlert('编辑成功')
          onSearch()
        }).finally(() => {
          closeDialog()
        })
      }}
      />
    ),
  })
}

const onDelete = (data: M.Advertise) => {
  if (!data.id) return
  const closeDialog = openDialog({
    title: '删除广告',
    body: (
      <div>
        确定删除该广告吗？
        <div class="flex justify-end gap-x-2 px-[20px]">
          <Button class="btn  btn-sm" onClick={() => closeDialog()}>取消</Button>
          <Button class="btn btn-primary btn-sm" onClick={() => {
            if (!data.id) return
            void apiDeleteAdvertise([data.id]).then(() => {
              showAlert('删除成功')
              if (list.value.length === 1) {
                searchForm.value.page_info.offset -= 1
              }
              onSearch()
            }).catch((e: any) => {
              showAlert(e.response.data.message, 'error')
            }).finally(() => {
              closeDialog()
            })
          }}
          >确定
          </Button>
        </div>
      </div>
    ),
  })
}

export const useAdvertisePage = () => {
  return {
    searchForm,
    list,
    total,
    applicationList,
    periodMap,
    onSearch,
    onReset,
    onCreate,
    onEdit,
    onDelete,
    loading,
  }
}
