declare namespace M {
  interface AdvertiseSearchParams {
    page_info: {
      size: number // 每页数据
      offset: number // 当前页数
    }
    app_id?: number // 查询的APP ID
    platform?: string // 查询的APP名称
    ad_id?: number // 广告ID
    ad_unit?: string // 广告 unit
  }

  type Advertise = {
    id?: number
    name: string
    app_id?: number
    platform?: string
    ad_type: string
    ad_unit: string// 广告组单元
    mediation: string // 竞价
    count_limit?: number// 频次
    period?: string // 周期类型:daily
    preload?: 0 | 1 | number // 是否预加载 1.是 0.否
    coins?: number // Coin价格
    version?: string // 最低版本号
    created?: number// 创建时间
    create_user?: string
    updated?: number// 更新时间
    update_user?: string
    version: string // 最低版本号
    ad_platform: string // 广告平台
    netbits_group?: Array<{
      name: string
      link: string
      type: string
    }>
  }
}
