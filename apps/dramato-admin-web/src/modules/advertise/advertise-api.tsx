import { httpClient } from 'src/lib/http-client'

export const apiGetAdvertiseList = (data: M.AdvertiseSearchParams) => {
  return httpClient.post<ApiResponse<{
    list: M.Advertise[]
    page_info: {
      offset: number
      size: number
      has_more: boolean
      total: number
    }
  }>>('/ads/list', data)
}

export const apiCreateAdvertise = (data: M.Advertise) => {
  return httpClient.post<ApiResponse<M.Advertise>>('/ads/create', data)
}

export const apiEditAdvertise = (data: M.Advertise) => {
  return httpClient.post<ApiResponse<M.Advertise>>('/ads/edit', data)
}

export const apiDeleteAdvertise = (ids: number[]) => {
  return httpClient.post<ApiResponse>('/ads/delete', { ids })
}
