/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, showAlert } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { ref, watch } from 'vue'
import { z } from 'zod'
type AdvertiseFormOptions = {
  props: {
    advertise?: M.Advertise
  }
  emits: {
    cancel: Fn
    submit: (data: M.Advertise) => void
  }
}
export const AdvertiseForm = createComponent<AdvertiseFormOptions>({
  props: {
    advertise: {
      id: undefined,
      app_id: undefined,
      name: '',
      ad_unit: '',
      ad_type: 'Rewarded',
      mediation: 'bidding',
      platform: 'ios',
      ad_platform: 'admob',
      netbits_group: [
        { name: '', link: '', type: 'time' },
      ],
    },
  },
  emits: {
    cancel: fn,
    submit: fn,
  },
}, (props, { emit }) => {
  const Form = CreateForm<M.Advertise>()
  const formData = ref<M.Advertise>(props.advertise)
  const formRules = z.object({
    name: z.string().min(1, '必填'),
    ad_unit: z.string().min(1, '必填'),
    ad_type: z.string().min(1, '必填'),
    mediation: z.string().min(1, '必填'),
    version: z.string().min(1, '必填'),
    platform: z.string().min(1, '必填'),
    ad_platform: z.string().min(1, '请选择广告平台'),
  })
  const { error, validateAll } = useValidator(formData, formRules)

  watch(() => props.advertise, () => {
    formData.value = props.advertise
  }, {
    immediate: true,
  })

  const submit = () => {
    const exclude: any[] = []
    if (!validateAll({ exclude })) {
      console.log(error.value)
      return
    }
    emit('submit', formData.value)
  }

  return () => (
    <x-advertise-form>
      <Form
        class="grid grid-cols-1 gap-y-3"
        hasAction={false}
        error={error.value}
        data={formData.value}
        onChange={(path, value) => {
          set(formData.value || {}, path, value)
        }}
        items={[
          {
            label: () => (
              <x-label-with-tips class="flex flex-col space-y-1">
                <div class="text-sm text-gray-500">请检查广告组Ad unit ID与app端的对应正确</div>
                <div>{requiredLabel('广告ID')}</div>
              </x-label-with-tips>
            ),
            path: 'id',
            input: {
              type: 'custom',
              render: () => <x-advertise-id>{formData.value?.id || 'To be generated'}</x-advertise-id>,
            },
          },
          {
            label: '平台',
            path: 'platform',
            input: {
              type: 'select',
              options: [{
                label: 'iOS',
                value: 'ios',
              }, {
                label: 'Android',
                value: 'android',
              }],
              autoInsertEmptyOption: false,
              disabled: !!formData.value.id,
            },
          },
          {
            label: requiredLabel('最低版本号'),
            path: 'version',
            input: {
              type: 'text',
            },
          },
          {
            label: requiredLabel('广告位名称'),
            path: 'name',
            input: {
              type: 'text',
              placeholder: '请输入命名，以便识别',
            },
          },
          [
            requiredLabel('聚合平台'),
            'ad_platform',
            {
              type: 'radio',
              options: [
                { label: 'admob', value: 'admob' },
                { label: 'max', value: 'max' },
                { label: 'meta', value: 'meta' },
                { label: 'netbits', value: 'netbits' }],
            },
            {
              class: mc('col-span-1'),
            },
          ],
          formData.value.ad_platform === 'netbits' && [
            'netbits_group',
            'netbits_group',
            {
              type: 'custom',
              render: ({ item, value }) => (
                <x-gap-group class="flex flex-col gap-y-2">
                  {
                    formData.value.netbits_group?.map((item, index) => {
                      return (
                        <x-gap-item class="border-1 flex flex-col items-center gap-y-2 rounded-md border border-solid p-2">
                          <div class="flex w-full flex-row items-center justify-between">
                            广告{index + 1}
                            <Icon name="ant-design:delete-filled" class="cursor-pointer" onClick={() => {
                              if (!formData.value.netbits_group) {
                                formData.value.netbits_group = []
                              }
                              formData.value.netbits_group = formData.value.netbits_group.filter((_, i) => i !== index)
                            }}
                            />
                          </div>
                          <div class="flex w-full flex-row items-center gap-x-2">
                            <label class="min-w-[110px]">H5跳转文案:</label>
                            <input
                              type="text"
                              class={mc('grow input input-bordered flex items-center gap-1 h-8 w-full')}
                              value={item.name}
                              onInput={(e: Event) => {
                                item.name = (e.target as HTMLInputElement).value || ''
                              }}
                            />
                          </div>
                          <div class="flex w-full flex-row items-center gap-x-2">
                            <label class="min-w-[110px]">渠道code:</label>
                            <input
                              type="text"
                              class={mc('grow input input-bordered flex items-center gap-1 h-8 w-full')}
                              value={item.link}
                              onInput={(e: Event) => {
                                item.link = (e.target as HTMLInputElement).value || ''
                              }}
                            />
                          </div>
                          <div class="flex w-full flex-row items-center gap-x-2">
                            <label class="min-w-[110px]">实现方案:</label>
                            <select
                              class="select select-bordered select-sm w-[120px]"
                              value={item.type}
                              onInput={(e: Event) => {
                                item.type = (e.target as HTMLInputElement).value || ''
                              }}
                            >
                              {[
                                { label: '15s', value: 'time' },
                                { label: '点击完成', value: 'click' },
                              ].map(option => (
                                <option value={option.value}>{option.label}</option>
                              ))}
                            </select>
                          </div>
                        </x-gap-item>
                      )
                    })
                  }
                  <Button class="btn btn-outline btn-sm  w-[160px]" onClick={() => {
                    if (!formData.value.netbits_group) {
                      formData.value.netbits_group = []
                    }
                    if (formData.value.netbits_group.length >= 4) {
                      return showAlert('最多填写4个', 'error')
                    }
                    formData.value.netbits_group.push({ name: '', link: '', type: 'time' })
                  }}
                  >新增
                  </Button>
                </x-gap-group>
              )
              ,
            },
          ],
          {
            label: requiredLabel('Ad unit'),
            path: 'ad_unit',
            input: {
              type: 'text',
              placeholder: '请从广告平台提取填入',
            },
          },
          {
            label: requiredLabel('类型'),
            path: 'ad_type',
            input: {
              type: 'select',
              options: formData.value.ad_platform !== 'netbits' ? [
                { label: 'rewarded', value: 'rewarded' },
                { label: 'interstitial', value: 'interstitial' },
                { label: 'native', value: 'native' },
                { label: 'app_open', value: 'app_open' },
                { label: 'banner', value: 'banner' },
              ] : [
                { label: 'h5', value: 'h5' },
              ],
            },
          },
          {
            label: requiredLabel('Mediation模式'),
            path: 'mediation',
            input: {
              type: 'select',
              options: [
                { label: 'bidding', value: 'bidding' },
                { label: 'waterfall', value: 'waterfall' },
              ],
            },
          },
        ]}
      />
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => {
          emit('cancel')
        }}
        >取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={submit}>确定</Button>
      </div>
    </x-advertise-form>
  )
})
