/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, DateTime, showFailToast, SvgIcon } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetCompetitionContentList, apiExportCompetitionContentList } from './competition-content-api'
import { RouterLink } from 'vue-router'
import UploadImagePreview from 'src/modules/resource-publish/components/upload-image-preview'
import { dramaLangValue, dramaLangCode } from './constant'
import dayjs from 'dayjs'

export const useCompetitionContent = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    updateTime,
    incDate,
    getList,
    exportData,
    exportLoading,
  }
}
const defaultParams = {
  page_index: 1,
  page_size: 20,
}
const Form = CreateForm<Api.CompetitionContent.ListReqParams>()
const params = ref<Api.CompetitionContent.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<Api.CompetitionContent.DramaInfoItem>()
const list = ref<Api.CompetitionContent.DramaInfoItem[]>([])
const loading = ref<boolean>(false)
const updateTime = ref('')
const incDate = ref<string>('')
const externalTitles = ref<string[]>([])
const exportLoading = ref<boolean>(false)

const columns = computed(() => [
  { prop: 'source', label: '应用', minWidth: 120, fixed: true },
  { label: '短剧名称', minWidth: 250, render: (scope: { row: Api.CompetitionContent.DramaInfoItem }) => {
    return (
      <div class="flex items-center gap-1">
        {scope.row.is_new ? (
          <div class="w-[32px]">
            <SvgIcon name="ic_new_episode" class="size-8 text-lg" />
          </div>
        )
          : null}
        <div class="flex-1">
          {scope.row.title}
        </div>
      </div>
    )
  }, fixed: true },
  { prop: 'language', label: '语言', minWidth: 120, render: (scope: { row: Api.CompetitionContent.DramaInfoItem }) => {
    return <span>{dramaLangValue[dramaLangCode.findIndex(key => key === scope.row.language)] || '-'}</span>
  } },
  { prop: 'cover', label: '封面', minWidth: 150, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.cover
      ? <UploadImagePreview deleteAble={false} image={scope.row.cover} /> : '-'
  } },
  { prop: 'tag', label: '标签', minWidth: 130 },
  { prop: 'start_date', label: '上线时间', minWidth: 130 },
  { prop: 'deploy_time', label: '投放时间', minWidth: 150, render: (scope: { row: Api.DataEyeDrama.DataEyeDramaItem }) => {
    return scope.row.deploy_time ? dayjs(scope.row.deploy_time * 1000).format('YYYY/MM/DD') : '-'
  } },
  { prop: 'view_count', label: '总观看', minWidth: 120, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'link_count', label: '总点赞', minWidth: 120, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'collect_count', label: '总收藏', minWidth: 120, sortable: 'custom', sortOrders: ['descending', null] },
  ...externalTitles.value.map((title, index) => {
    return { prop: `index_${index}`, label: title, render: (scope: { row: Api.CompetitionContent.DramaInfoItem }) => {
      return <span>{scope.row.external_values[index] || '-'}</span>
    }, minWidth: 120, sortable: 'custom', sortOrders: ['descending', null] }
  }),
  { label: '操作', width: 80, fixed: 'right', render: (scope: { row: Api.CompetitionContent.DramaInfoItem }) => {
    return (
      <RouterLink class="btn btn-link btn-xs" to={`/competition-content/${scope.row.id}`}>详情</RouterLink>
    )
  } },
])

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetCompetitionContentList(params.value)
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
    updateTime.value = res.data?.last_update_time || ''
    incDate.value = res.data?.count_inc_date || ''
    externalTitles.value = res.data?.external_titles || []
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}

const exportData = async () => {
  exportLoading.value = true
  try {
    const res = await apiExportCompetitionContentList({
      title: params.value.title,
      source: params.value.source,
      language: params.value.language,
    })
    if (res.data?.oss_path) {
      window.open(res.data?.oss_path, '_blank')
    } else {
      showFailToast('导出失败')
    }
  } catch (error) {
    exportLoading.value = false
    showFailToast('导出失败')
  } finally {
    exportLoading.value = false
  }
}
