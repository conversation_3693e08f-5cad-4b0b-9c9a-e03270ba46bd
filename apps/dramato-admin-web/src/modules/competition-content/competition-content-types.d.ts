declare namespace Api {
  namespace CompetitionContent {
    interface ListReqParams {
      title?: string // 剧名
      source?: string // 来源
      language?: string // 语言
      order_by?: number // 降序排序字段：1-总浏览量 2-总点赞数 3-总收藏数 4-总关注数 5-增量浏览量 6-增量点赞数 7-增量收藏数 8-增量关注数
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
    }

    interface DramaInfoListResp {
      last_update_time?: string // 最近一次数据更新时间
      count_inc_date?: string // 显示用增量的日期:[观看0304]中的0304
      external_titles: string[] // ["收藏0407","收藏0406","收藏0405","收藏0404"],
      list: DramaInfoItem[] // 剧列表
      total: number // 总数
    }

    interface DramaInfoItem {
      id?: number // 剧ID
      title?: string // 剧名
      source?: string // 来源：shortmax dramabox reelshort goodshort
      language?: string // 语言：en ja 等
      view_count?: number // 最新浏览量
      link_count?: number // 最新点赞数
      collect_count?: number // 最新收藏数
      follow_count?: number // 最新关注数
      external_values: number[] // [100, 200, 300, 400],
      is_new: boolean
      deploy_time?: number
    }

    interface DramaInfoDetailReqParams {
      id?: number // 剧ID
    }

    interface IChartData {
      day: number
      value: number
    }

    interface Material {
      id: number
      cover: string // 素材封面
      video: string // 素材视
      heat_num: number // 素材热度
      duration_millis: number // 素材时长（毫秒）
      title1: string
      title2: string
      exposure_num: number // 素材预估曝光数
    }

    interface DramaInfoDetailResp {
      title?: string // 剧名
      source?: string // 来源：shortmax dramabox reelshort goodshort
      language?: string // 语言：en ja 等
      episode_number?: number // 集数
      description?: string // 描述
      view_count?: number // 最新浏览量
      link_count?: number // 最新点赞数
      deploy_time?: number // 投放时间
      collect_count?: number // 最新收藏数
      follow_count?: number // 最新关注数
      list?: DramaInfoDetailItem[] // 每天1条，最多30天
      self_list?: IChartData[]
      compare_title?: string
      compare_list?: IChartData[]
      tag?: string
      cover?: string
      materials?: Material[]
    }

    interface DramaInfoDetailItem {
      date?: string // 日期
      view_count_inc?: number // 增量浏览量
      link_count_inc?: number // 增量点赞数
      collect_count_inc?: number // 增量收藏数
      follow_count_inc?: number // 增量关注数
    }
  }
}
