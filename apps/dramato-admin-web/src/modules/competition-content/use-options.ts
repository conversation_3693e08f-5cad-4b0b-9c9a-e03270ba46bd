/* eslint-disable @typescript-eslint/no-explicit-any */
import { nextTick } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption, LineSeriesOption } from 'echarts'

const color = ['#3B82F6', '#FACC15', '#D50B95', '#14B8A6', '#F59E0B', '#FF5555']

const formatXAxis = (arr: Api.CompetitionContent.IChartData[]) => {
  return arr.map((row: { day: number }) => row.day)
}

function getGradientColor(color: string) {
  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: hexToRgba(color, 0.37), // 半透明
    },
    {
      offset: 1,
      color: hexToRgba(color, 0), // 完全透明
    },
  ])
}

// 将 hex 颜色转换为 rgba 格式
function hexToRgba(hex: string, alpha: number) {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

export const getOptions = (
  data1: Api.CompetitionContent.IChartData[],
  data2: Api.CompetitionContent.IChartData[],
  title: string,
  compareTitle: string,
): EChartsOption => {
  const legendKeys = [title, compareTitle]
  // 获取x轴时间
  const xAxis = formatXAxis(data2.map(row => ({ day: row.day, value: row.value })) || [])

  const yAxis: LineSeriesOption[] = [{
    name: title,
    type: 'line',
    smooth: true,
    showSymbol: true,
    data: data1.map((row: { value: number }) => row.value),
    areaStyle: {
      color: getGradientColor(color[0]), // 动态计算渐变色
    },
  }, {
    name: compareTitle,
    type: 'line',
    smooth: true,
    showSymbol: true,
    data: data2.map((row: { value: number }) => row.value),
    areaStyle: {
      color: getGradientColor(color[1]), // 动态计算渐变色
    },
  }]

  return {
    color: color,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendKeys,
      bottom: 0,
    },
    grid: {
      left: 30,
      right: 30,
      bottom: 50,
      top: 10,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E7EAEE',
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E7EAEE',
        },
      },
    },
    series: yAxis,
  }
}
