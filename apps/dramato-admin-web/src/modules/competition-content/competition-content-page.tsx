/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, Icon, Pager } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useCompetitionContent } from './use-competition-content'
import { ElTable, ElTableColumn } from 'element-plus'
import { dramaLangValue, dramaLangCode } from './constant'

type CompetitionContentPageOptions = {
  props: {}
}
export const CompetitionContentPage = createComponent<CompetitionContentPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange, updateTime, exportData, exportLoading } = useCompetitionContent()
  const tableRef = ref<InstanceType<typeof ElTable>>()

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>竞品内容数据</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              tableRef.value?.clearSort()
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['应用', 'source', { type: 'select', options: [{
                label: 'shortmax',
                value: 'shortmax',
              }, {
                label: 'dramabox',
                value: 'dramabox',
              }, {
                label: 'reelshort',
                value: 'reelshort',
              }, {
                label: 'goodshort',
                value: 'goodshort',
              }, {
                value: 'kalostv',
                label: 'kalostv'
              }, {
                value: 'stardust',
                label: 'stardust'
              }, {
                value: 'flickreels',
                label: 'flickreels'
              }, {
                value: 'netshort',
                label: 'netshort'
              }, {
                value: 'minishorts',
                label: 'minishorts'
              }],
              }],
              ['短剧名称', 'title', { type: 'text', placeholder: '请输入短剧名称' }],
              ['语言', 'language', { type: 'select', placeholder: '请选择语言', options: dramaLangCode.map((key, index) => {
                return {
                  value: key,
                  label: dramaLangValue[index]
                }
              }) }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div>
              更新时间：{updateTime.value}
            </div>
            <div>
              {/* <Button class="btn btn-primary btn-sm" onClick={exportData} disabled={exportLoading.value}>
                {exportLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                导出
              </Button> */}
            </div>
          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            onSortChange={(column: any) => {
              if (column.order === 'descending') {
                const orderMap = {
                  view_count: 1,
                  link_count: 2,
                  collect_count: 3,
                  index_0: 4,
                  index_1: 5,
                  index_2: 6,
                  index_3: 7,
                  index_4: 8,
                }
                params.value.order_by = orderMap[column.prop as keyof typeof orderMap]
              } else {
                params.value.order_by = undefined
              }
              void onQuery()
            }}
          >
            {columns.value.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn
                    sortable={col.sortable}
                    key={col.prop}
                    prop={col.prop}
                    fixed={col.fixed}
                    label={col.label}
                    width={col.width}
                    minWidth={col.minWidth}
                    sortOrders={col.sortOrders as ('descending' | 'ascending' | null)[]}
                    v-slots={{
                      default: ({ row }: { row: any }) => col.render({ row }),
                    }}
                  />
                )
              } else {
                return (
                  <ElTableColumn
                    key={col.prop}
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-expect-error
                    sortOrders={col.sortOrders}
                    sortable={col.sortable}
                    prop={col.prop}
                    label={col.label}
                    minWidth={col.minWidth}
                    width={col.width}
                    fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default CompetitionContentPage
