/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Icon, openDialog, SvgIcon } from '@skynet/ui'
import { Wrapper } from 'src/layouts/wrapper'
import { RouterLink } from 'vue-router'
import { apiGetCompetitionContentDetail } from './competition-content-api'
import { useRoute } from 'vue-router'
import { ref } from 'vue'
import { ElTable, ElTableColumn } from 'element-plus'
import { ContrastChart } from './contrast-chart'
import UploadImagePreview from 'src/modules/resource-publish/components/upload-image-preview'
import { dramaLangValue, dramaLangCode } from './constant'
import dayjs from 'dayjs'
import { ElRow, ElCol } from 'element-plus'
import { M3u8Player } from '../resource/components/m3u8-player'

type CompetitionContentDetailPageOptions = {
  props: {}
}

type MetricKey = 'view' | 'link' | 'collect' | 'follow'

export const CompetitionContentDetailPage = createComponent<CompetitionContentDetailPageOptions>({
  props: {},
}, props => {
  const dramaDetails = ref<Api.CompetitionContent.DramaInfoDetailResp>({})
  const data = ref<any[]>([])
  const header = ref<string[]>([])
  const getDetail = async () => {
    const { id } = useRoute().params
    const res = await apiGetCompetitionContentDetail({ id: Number(id) })

    // 处理表头
    header.value = ['指标', '总数', ...(res.data?.list || []).map(item => item.date || '')]

    // 处理表格数据
    const metrics = [
      { key: 'view' as MetricKey, label: 'view' },
      { key: 'link' as MetricKey, label: 'like' },
      { key: 'collect' as MetricKey, label: 'collect' },
    ]

    data.value = metrics.map(metric => {
      return {
        metric: metric.label,
        total: res.data?.[`${metric.key}_count`] || 0,
        ...res.data?.list?.reduce((acc, item) => ({
          ...acc,
          [(item.date as string)]: item[`${metric.key}_count_inc`] || 0,
        }), {}),
      }
    })

    dramaDetails.value = res?.data || {}
  }
  void getDetail()

  function formatToWan(num: number) {
    if (typeof num !== 'number' || isNaN(num)) return ''

    if (num >= 10000) {
      return (num / 10000).toFixed(2).replace(/\.00$/, '') + '万'
    }

    return num.toString()
  }

  const showVideo = (material: Api.CompetitionContent.Material) => {
    openDialog({
      title: () => (
        <div class="flex items-center space-x-2">
          <span>{material.title1}</span>
        </div>
      ),
      body: (<>
        <div class="flex space-x-4">
          <div>热度: {material.heat_num}</div>
          <div>曝光: {material.exposure_num}</div>
        </div>
        <M3u8Player class="max-h-[550px] w-[350px]" url={material.video} />
      </>),
      customClass: '!w-[350px]',
    })
  }

  return () => (
    <x-competition-content-detail-page class="block">
      <Wrapper>
        <section class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/competition-content">竞品内容数据</RouterLink></li>
            <li>详情</li>
          </ul>
        </section>
        <div>
          <div class="flex w-full items-center gap-2 text-base font-bold">
            <Icon name="uis:web-section-alt" />
            基本信息
          </div>
          <div class="flex gap-x-4">
            <div class="w-[200px] mt-4">
              {
                dramaDetails.value.cover
                  ? <UploadImagePreview deleteAble={false} image={dramaDetails.value.cover} />
                  : <div class="h-[230px] bg-gray-200 w-full text-center leading-[230px]">暂无封面</div>
              }
            </div>
            <div class="flex w-full flex-col gap-1 py-4">
              <div class="flex">
                <div>短剧名称：</div>
                <div>{dramaDetails.value.title}</div>
              </div>
              <div class="flex">
                <div>应用名称：</div>
                <div>{dramaDetails.value.source}</div>
              </div>
              <div class="flex">
                <div>标签：</div>
                <div>{dramaDetails.value.tag || '-'}</div>
              </div>
              <div class="flex">
                <div>发行语言：</div>
                <div>{dramaLangValue[dramaLangCode.indexOf(dramaDetails.value.language || '')]}</div>
              </div>
              <div class="flex">
                <div>集数：</div>
                <div>{dramaDetails.value.episode_number}</div>
              </div>
              <div class="flex">
                <div>投放时间：</div>
                <div>{dramaDetails.value.deploy_time ? dayjs(dramaDetails.value.deploy_time * 1000).format('YYYY/MM/DD') : '-' }</div>
              </div>
              <div class="flex">
                <div>简介：</div>
                <div class="flex-1">{dramaDetails.value.description}</div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="flex w-full items-center gap-2 pb-4 text-base font-bold">
            <Icon name="uis:web-section-alt" />
            数据统计
          </div>
          <ElTable data={data.value}>
            {header.value.map((col, index) => (
              <ElTableColumn
                key={index}
                prop={index === 0 ? 'metric' : index === 1 ? 'total' : col}
                label={col}
              />
            ))}
          </ElTable>
        </div>
        <ContrastChart
          selfList={dramaDetails.value.self_list || []}
          compareList={dramaDetails.value.compare_list || []}
          compareTitle={dramaDetails.value.compare_title || ''}
          title={dramaDetails.value.title || ''}
        />
        {dramaDetails.value.materials && dramaDetails.value.materials.length > 0 ? (
          <div class="flex w-full items-center gap-2 text-base font-bold">
            <Icon name="uis:web-section-alt" />
            爆款素材
          </div>
        ) : null }
        <ElRow gutter={16}>
          {
            dramaDetails.value.materials?.map(item => {
              return (
                <ElCol class="mb-4 relative" span={4}>
                  <img title={item.title1} class="size-full cursor-pointer" src={item.cover} alt="" onClick={() => showVideo(item)} />
                  <div class="absolute space-x-2 px-2 py-1 bottom-2 right-4 text-base rounded-md bg-black/60 text-white">
                    <SvgIcon class="size-4" name="ic_heat" /> {formatToWan(item.heat_num)}
                    <SvgIcon class="size-4" name="ic_view" /> {formatToWan(item.exposure_num)}
                  </div>
                </ElCol>
              )
            })
          }
        </ElRow>
      </Wrapper>
    </x-competition-content-detail-page>
  )
})

export default CompetitionContentDetailPage
