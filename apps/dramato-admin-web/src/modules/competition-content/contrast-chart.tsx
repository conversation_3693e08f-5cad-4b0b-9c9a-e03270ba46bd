import { createComponent } from '@skynet/shared'
import { getOptions } from './use-options'
import { EChart, Icon } from '@skynet/ui'

type ContrastChartOptions = {
  props: {
    selfList?: Api.CompetitionContent.IChartData[]
    compareList: Api.CompetitionContent.IChartData[]
    compareTitle: string
    title: string
  }
}
export const ContrastChart = createComponent<ContrastChartOptions>({
  props: {
    selfList: [],
    compareList: [],
    compareTitle: '',
    title: '',
  },
}, props => {
  return () => (
    <x-contrast-chart class="block space-y-4">

      {
        props.selfList && props.compareList && props.selfList.length > 0 && props.compareList.length > 0 ? (
          <>
            <div class="flex w-full items-center gap-2 pb-4 text-base font-bold">
              <Icon name="uis:web-section-alt" />
              对比数据
            </div>
            <EChart class="h-[420px] w-full bg-white" option={getOptions(props.selfList, props.compareList, props.title, props.compareTitle)} />

          </>
        ) : null
      }
    </x-contrast-chart>
  )
})

export default ContrastChart
