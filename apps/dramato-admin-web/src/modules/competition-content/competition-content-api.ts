import { httpClient } from 'src/lib/http-client'

export const apiGetCompetitionContentList = (data: Api.CompetitionContent.ListReqParams) =>
  httpClient.post<ApiResponse<Api.CompetitionContent.DramaInfoListResp>>('/dramainfo/list', data)

export const apiGetCompetitionContentDetail = (data: Api.CompetitionContent.DramaInfoDetailReqParams) =>
  httpClient.post<ApiResponse<Api.CompetitionContent.DramaInfoDetailResp>>('/dramainfo/detail', data)

export const apiExportCompetitionContentList = (data: Api.CompetitionContent.ListReqParams) =>
  httpClient.post<ApiResponse<{
    oss_path: string
  }>>('/dramainfo/export', data)
