/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, fn } from '@skynet/shared'
import { CreateForm, showFailToast, Icon, Button } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { langKey, langValue } from '../constant'
import { z } from 'zod'
import { useResourceStore } from '../use-resource-store'
import { apiTransSeriesInfo } from '../resource-api'

type TranslateFormOptions = {
  props: {
    series_resource_id: number
  }
  emits: {
    submitTrans: (item: {
      src_lang: string
      target_lang_list: string[]
    }) => void
  }
}
export const TranslateForm = createComponent<TranslateFormOptions>({
  props: {
    series_resource_id: 0,
  },
  emits: {
    submitTrans: () => fn,
  },
}, (props, { emit }) => {
  const { episodeSeriesInfoList } = useResourceStore()

  const form = ref({
    src_lang: '',
    target_lang_list: [],
  })
  const btnLoading = ref(false)

  const TranslateForm = CreateForm<{
    src_lang: string
    target_lang_list: string[]
  }>()

  const rules = z.object({
    src_lang: z.string().min(1, {
      message: '请选择来源语言',
    }),
    target_lang_list: z.array(z.string()).min(1, {
      message: '请选择目标语言',
    }),
  })

  const { error, validateAll } = useValidator(form, rules)

  const options = langKey.map((n, index) => {
    return { value: langKey[index], label: langValue[index] }
  })

  const onSubmit = async () => {
    if (!validateAll()) {
      return
    }
    const originLangObj = episodeSeriesInfoList.value.find(row => row.language_code === form.value.src_lang)
    if (!originLangObj?.title || !originLangObj.description) {
      showFailToast(`请填写原语言对应的标题和描述`)
      return
    }
    btnLoading.value = true
    try {
      const res = await apiTransSeriesInfo({
        title: originLangObj?.title,
        description: originLangObj?.description,
        series_resource_id: props.series_resource_id,
        ...form.value,
      })

      const list = res.data?.list || []
      list.forEach(row => {
        const langCol = episodeSeriesInfoList.value.find(item => row.language_code === item.language_code)
        if (langCol) {
          langCol.description = row.description
          langCol.title = row.title
        }
      })
    } catch (error: any) {
      showFailToast(error?.response?.data?.message)
    } finally {
      btnLoading.value = false
    }
  }

  return () => (
    <div class="inline-flex space-x-4 items-center justify-start">
      <TranslateForm
        class="w-full"
        error={error.value}
        data={form.value}
        hasAction={false}
        submitText="翻译"
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        items={[
          {
            label: '原语言',
            path: 'src_lang',
            input: {
              type: 'select',
              options: options,
            },
          },
          {
            label: '目标语言',
            path: 'target_lang_list',
            input: { type: 'multi-select',
              options: options, class: 'w-[220px]',
            },
          },
        ]}
      />
      <Button disabled={btnLoading.value} class="btn mt-2 btn-primary btn-sm " onClick={onSubmit}>
        翻译
        {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
      </Button>
    </div>
  )
})
