/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, SlotFn } from '@skynet/shared'
import { ref, watch } from 'vue'
import { Button, Icon } from '@skynet/ui'
import Editor from './editor'
import { openDialog } from '@skynet/ui'
import DiffEditor from './diffEditor'
import { langKeyForCol, langValue } from '../constant'

type ISubtitle = {
  code: string
  language: string
}

type SubtitleOptions = {
  props: {
    reference: ISubtitle
    original: ISubtitle
    pageNum?: number
    loading: boolean
  }
  emits: {
    close: () => void
    afterSave: (editSubtitle: ISubtitle) => void
    languageChange: (lang: string) => void
  }
  slots: {
    default: () => SlotFn
  }
}
export const Subtitle = createComponent<SubtitleOptions>({
  props: {
    reference: {
      code: '',
      language: '',
    },
    original: {
      code: '',
      language: '',
    },
    pageNum: 0,
    loading: false,
  },
  emits: {
    close: () => {},
    afterSave: () => {},
    languageChange: () => {}
  },
}, (props, { emit, slots }) => {
  const top = ref(0)
  const editSubtitle = ref({
    code: '',
    language: '',
  })

  watch(() => [props.pageNum, props.original.language], () => {
    editSubtitle.value = {
      code: props.original.code,
      language: props.original.language,
    }
    top.value = 0
  }, {
    immediate: true,
  })

  return () => (
    <div>
      <div class="flex justify-between">
        <div class="flex flex-col w-[46%]">
          {/* <div class="py-2 text-[var(--text-2)]">{props.reference.language}</div> */}
          <select class="select w-[100px] select-sm text-[var(--text-2)" onChange={(e: any) => {
            const value = e.target.value as string
            emit('languageChange', value)
          }}
          >
            {
              langKeyForCol.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
            }
          </select>
          <Editor
            customClass="h-[55vh]"
            code={props.reference.code}
            options={{
              language: 'plaintext',
              formatOnPaste: false,
              readOnly: true,
              tabSize: 2,
              inDiffEditor: false,
              minimap: {
                enabled: false,
              },
            }}
            scrollTop={top.value}
            onScroll={scrollTop => {
              top.value = scrollTop
            }}
          />
        </div>
        <div class="flex w-[46%] flex-col">
          <div class="py-2 text-[var(--text-2)]">{props.original.language}</div>
          <Editor
            customClass="h-[55vh]"
            code={props.original.code}
            options={{
              language: 'plaintext',
              formatOnPaste: false,
              tabSize: 2,
              inDiffEditor: false,
              minimap: {
                enabled: false,
              } }}
            onChange={e => {
              editSubtitle.value.code = e
            }}
            scrollTop={top.value}
            onScroll={scrollTop => {
              top.value = scrollTop
            }}
          />
        </div>
      </div>

      <div class="flex justify-between items-center gap-x-2 pt-4">
        {slots?.default()}
        <div class="flex gap-x-2">
          <Button class="btn btn-sm btn-default" onClick={() => emit('close')}>取消</Button>
          <Button class="btn btn-sm btn-online" onClick={() => {
            openDialog({
              title: '对比修改',
              showParentWhenChildClose: true,
              mainClass: 'px-4 !py-0',
              customClass: '!w-[1000px] !min-h-[60vh]',
              body: (
                <>
                  <DiffEditor
                    code1={props.original.code}
                    code2={editSubtitle.value.code}
                    customClass="h-[55vh]"
                  />
                </>
              ),
            })
          }}
          >
            查看对比
          </Button>
          <Button class="btn btn-sm btn-primary" disabled={props.loading} loading={props.loading} onClick={() => {
            emit('afterSave', editSubtitle.value)
          }}
          >
            {props.loading ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            提交
          </Button>
        </div>
      </div>
    </div>
  )
})
