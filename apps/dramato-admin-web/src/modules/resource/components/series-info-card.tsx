/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, CreateForm, Icon } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { langValue, langKey } from '../constant'
type SeriesInfoCardOptions = {
  props: {
    item: M.IEpisodeSeriesInfo
  }
}
export const SeriesInfoCard = createComponent<SeriesInfoCardOptions>({
  props: {
    item: {
      language_code: '',
      title: '',
      description: '',
      cover: '',
      is_sync: 0,
      horizontal_cover: '',
      push_content_list: [],
    },
  },
}, props => {
  const langMap: { [x: string | number]: string } = {}
  langKey.forEach((key, index) => {
    langMap[key] = langValue[index]
  })
  const Form = CreateForm<{
    language_code: string | number
    title: string
    description: string
    cover: string
  }>()

  const form = ref<{
    language_code: string | number
    title: string
    description: string
    cover: string
    horizontal_cover: string
    push_content_list: Array<{
      id?: number
      content: string
      is_delete: boolean
    }> }>(props.item)

  return () => (
    <div class="card bg-white text-left w-40 shadow-xl">
      <div class="card-title text-[var(--text-2)] items-center flex text-sm">
        <span>{langMap[props.item.language_code]}</span>
        {props.item.is_sync === 1 ? <div class="text-primary text-xs">已同步</div> : <div class="text-[var(--text-3)] text-xs">未同步</div>}
      </div>
      <div class="card-body items-center">
        <Form
          data={form.value}
          onChange={(path, value) => {
            set(form.value, path, value)
          }}
          hasAction={false}
          items={[
            { label: '', path: 'title', input: { type: 'text', maxlength: 255, class: 'w-[120px] text-xs px-1', placeholder: '剧名,最多255个字符' } },
            { label: '', path: 'description', input: { type: 'textarea', maxlength: 1000, class: 'w-[120px] text-xs px-1', placeholder: '简介，最多1000个字符' } },
            {
              label: '',
              path: 'cover',
              input: {
                type: 'custom',
                render: () => (
                  <Uploader
                    maxsize={2 * 1024 * 1024}
                    class="size-[150px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                    onUploadSuccess={d => {
                      form.value.cover = d.show_cover as string
                    }}
                    isImage={true}
                  >
                    {form.value?.cover
                      ? <img src={form.value.cover} class="size-full object-cover" />
                      : <span class="size-full flex items-center justify-center">上传封面</span>}
                  </Uploader>
                ),
              },
            },
            {
              label: '',
              path: 'horizontal_cover',
              input: {
                type: 'custom',
                render: () => (
                  <Uploader
                    maxsize={2 * 1024 * 1024}
                    class="size-[150px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                    onUploadSuccess={d => {
                      form.value.horizontal_cover = d.show_cover as string
                    }}
                    isImage={true}
                  >
                    {form.value?.horizontal_cover
                      ? <img src={form.value.horizontal_cover} class="size-full object-cover" />
                      : <span class="size-full flex items-center justify-center">上传横图</span>}
                  </Uploader>
                ),
              },
            },
            {
              label: '推送消息通知',
              path: 'push_content_list',
              input: {
                type: 'custom',
                render: (r: any) => {
                  console.log('r.value', r.value)
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <span class={mc('input input-bordered flex items-center gap-1 h-8 w-[120px] text-xs px-1')}>
                                <input
                                  type="text"
                                  class="grow"
                                  value={i.content || ''}
                                  onInput={(e: any) => {
                                    const value = e.target.value
                                    form.value.push_content_list[idx].content = value
                                  }}
                                />
                              </span>
                              <Icon name="ant-design:delete-filled" class="size-4 cursor-pointer" onClick={() => {
                                if (!i.id) {
                                  form.value.push_content_list.splice(idx, 1)
                                  return
                                }
                                form.value.push_content_list[idx].is_delete = true
                              }}
                              />
                            </div>
                          ))
                      }
                      <Button
                        class={mc('btn btn-sm btn-outline')}
                        onClick={() => {
                          if (!form.value.push_content_list) {
                            form.value.push_content_list = []
                          }
                          console.log('form.value.push_content_list', form.value.push_content_list)

                          form.value.push_content_list.push({
                            content: '',
                            is_delete: false,
                          })
                        }}
                      >新增
                      </Button>
                    </x-push-desc-list>
                  )
                },
              },
            },
          ]}
        />

      </div>
    </div>
  )
})
