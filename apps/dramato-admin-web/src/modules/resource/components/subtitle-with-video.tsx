/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, SlotFn } from '@skynet/shared'
import { computed, ref, watch } from 'vue'
import { Button, Icon, Checkbox, showSuccessToast, showFailToast } from '@skynet/ui'
import Editor from './editor'
import { langKeyForCol, langValue } from '../constant'
import { M3u8Player } from './m3u8-player'
import TermTable from 'src/modules/resource-publish/components/term-table'
import { apiUpdateOtherLanguageTerminology, apiGetTransTerminologyList } from 'src/modules/resource-publish/resource-publish-api'
import { useRoute } from 'vue-router'
import { throttle, trim } from 'lodash-es'

type ISubtitle = {
  code: string
  language: string
  path?: string
}

type SubtitleOptions = {
  props: {
    reference: ISubtitle
    original: ISubtitle
    pageNum?: number
    loading: boolean
    videoUrl?: string
    videoType?: 'origin_path' | 'pure_path' | string
    errs?: M.IResourceSubtitleDetailError[]
    originalLanguageChangeAble?: boolean
    termVisible?: boolean
    showOnlineButton?: boolean
    showLanguageSelect?: boolean
    saveBtnDisabled?: boolean
    defaultLanguage?: string
    isTermCheck?: boolean
    termLanguage?: string
  }
  emits: {
    close: () => void
    afterSave: (editSubtitle: ISubtitle, isSync?: boolean) => void
    languageChange: (lang: string) => void
    fontSizeChange: (fontSize: number) => void
    videoChange: (type: 'pure_path' | 'origin_path') => void
    originalLanguageChange: (lang: string) => void
    closeDialog: () => void
  }
  slots: {
    default: () => SlotFn
  }
}
export const SubtitleWithVideo = createComponent<SubtitleOptions>({
  props: {
    reference: {
      code: '',
      language: '',
      path: '',
    },
    original: {
      code: '',
      language: '',
      path: '',
    },
    videoUrl: '',
    termVisible: true,
    pageNum: 0,
    loading: false,
    videoType: 'pure_path',
    errs: [],
    originalLanguageChangeAble: false,
    showOnlineButton: true,
    showLanguageSelect: true,
    saveBtnDisabled: false,
    defaultLanguage: '',
    isTermCheck: false,
    termLanguage: ''
  },
  emits: {
    close: () => {},
    afterSave: () => {},
    languageChange: () => {},
    fontSizeChange: () => {},
    videoChange: () => {},
    originalLanguageChange: () => {},
    closeDialog: () => {},
  },
}, (props, { emit, slots }) => {
  const route = useRoute()
  const fontSize = ref(12)
  const currentTime = ref(0)
  const isAsync = ref(true)
  const top = ref(0)
  const fontSizeList = [12, 14, 16, 18, 20]
  const curDuration = ref<number>(+Infinity)
  const player = ref<any>(null)
  const termList = ref<M.ITerminology[]>([])
  const btnLoading = ref(false)
  const isReplacing = ref(false)
  const cdnUrl = 'https://img.tianmai.cn/'

  const subtitles = ref<{
    language: string
    type: string
    subtitle: string
  }[]>([{
    language: props.original.language,
    type: 'normal',
    subtitle: props.original.path || '',
  }])

  const editSubtitle = ref({
    code: '',
    language: '',
  })

  const isUpdateTerm = computed(() => {
    if (props.isTermCheck) {
      return props.defaultLanguage !== props.termLanguage
    } else {
      return props.defaultLanguage !== props.original.language
    }
  })

  watch(() => props.original.path, () => {
    subtitles.value = [{
      language: props.original.language,
      type: 'normal',
      subtitle: props.original.path?.indexOf('http') !== 0 ? `${cdnUrl}${props.original.path}` : props.original.path || '',
    }]
  }, {
    immediate: true,
  })

  watch(() => props.original.language, async () => {
    if (props.isTermCheck) {
      const res = await apiGetTransTerminologyList({
        series_resource_id: +route.params.id,
        language_code: props.termLanguage,
      })
      termList.value = res.data?.term_list || []
      isReplacing.value = res.data?.replacing === 1
      if (isReplacing.value) {
        showFailToast('术语替换中，当前语言字幕暂时无法操作')
      }
    } else  if (props.original.language && props.termVisible) {
      const res = await apiGetTransTerminologyList({
        series_resource_id: +route.params.id,
        language_code: props.original.language,
      })
      termList.value = res.data?.term_list || []
      isReplacing.value = res.data?.replacing === 1
      if (isReplacing.value) {
        showFailToast('术语替换中，当前语言字幕暂时无法操作')
      }
    }
  }, {
    immediate: true,
  })

  const getVideoDuration = (url: string): Promise<number> => {
    return new Promise(resolve => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.src = url
      video.onloadedmetadata = () => {
        resolve(Math.round(video.duration))
      }
    })
  }

  watch(() => [props.pageNum, props.original.language, props.original.code], async () => {
    editSubtitle.value = {
      code: props.original.code,
      language: props.original.language,
    }
    top.value = 0
    if (props.videoUrl) {
      const duration = await getVideoDuration(props.videoUrl)
      curDuration.value = duration
    }
  }, {
    immediate: true,
  })

  const throttleScroll = throttle(() => {
    currentTime.value = player.value.getCurrentTime() || 0
  }, 500)

  watch(() => player.value, newVal => {
    if (newVal) {
      player.value.on('timeupdate', throttleScroll)
    }
  })

  return () => (
    <div>
      <div class="flex justify-between">
        <div class="flex items-center">
          {
            props.videoUrl
              ? (
                  <M3u8Player
                    currentLanguage={editSubtitle.value.language}
                    subtitles={subtitles.value}
                    url={props.videoUrl}
                    onPlayerReady={(e: any) => {
                      player.value = e
                    }}
                  />
                )
              : (
                  <div class="flex h-[540px] w-[350px] items-center justify-center text-[var(--text-2)]">
                    <span>暂无视频</span>
                  </div>
                )
          }
        </div>
        <div class="flex flex-1 overflow-x-auto">
          {!props.isTermCheck ? (
            <div class={`flex flex-col ${!props.termVisible ? 'w-1/2' : props.isTermCheck ? 'w-1/2' : 'w-2/5'}`}>
              {props.showLanguageSelect ? (
                <select value={props.reference.language} class="text-[var(--text-2) select select-bordered select-sm h-[40px] w-[100px]" onChange={(e: any) => {
                  const value = e.target.value as string
                  emit('languageChange', value)
                }}
                >
                  {
                    langKeyForCol.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                  }
                </select>
              ) : null }
              <Editor
                customClass="h-[520px]"
                code={props.reference.code}
                options={{
                  language: 'plaintext',
                  formatOnPaste: false,
                  readOnly: true,
                  tabSize: 2,
                  fontSize: fontSize.value,
                  inDiffEditor: false,
                  minimap: {
                    enabled: false,
                  },
                }}
                scrollTop={top.value}
                onScroll={scrollTop => {
                  top.value = scrollTop
                }}
              />
            </div>
          ) : null }
          <div class={`flex flex-col ${props.termVisible ? 'w-1/2' : 'w-2/5'}`}>
            {
              props.originalLanguageChangeAble && props.showLanguageSelect ? (
                <select value={props.original.language} class="text-[var(--text-2) select select-bordered select-sm h-[40px] w-[100px]" onChange={(e: any) => {
                  const value = e.target.value as string
                  emit('originalLanguageChange', value)
                }}
                >
                  {
                    langKeyForCol.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                  }
                </select>
              ) : null
            }

            <Editor
              customClass="h-[520px]"
              code={props.original.code}
              errs={props.errs}
              highlightTerms={termList.value.map(obj => obj.translation || '')}
              options={{
                language: 'plaintext',
                formatOnPaste: false,
                tabSize: 2,
                fontSize: fontSize.value,
                inDiffEditor: false,
                minimap: {
                  enabled: false,
                } }}
              onChange={e => {
                editSubtitle.value.code = e
              }}
              onVideoProgress={(time: number) => {
                if (player.value) player.value.seek(time)
              }}
              scrollTop={top.value}
              onScroll={scrollTop => {
                top.value = scrollTop
              }}
              isAsync={isAsync.value}
              currentTime={currentTime.value}
            />
          </div>
          {props.termVisible ? (
            <div class={props.isTermCheck ? 'w-1/2' : 'w-1/5'}>
              <TermTable
                class="h-[500px] overflow-y-auto"
                isTrans={true}
                language={props.isTermCheck ? props.termLanguage : props.original.language}
                list={termList.value}
                onChange={({ item }) => {
                  const index = termList.value.findIndex(obj => obj.term_id === item.term_id)
                  termList.value.splice(index, 1, {
                    ...item,
                  })
                }} />
              <div class="mt-4 flex justify-end space-x-2">
                {
                  isUpdateTerm.value ? (
                    <Button class="btn btn-primary btn-xs" disabled={btnLoading.value || isReplacing.value} onClick={async () => {
                      btnLoading.value = true
                      try {
                        if (termList.value.filter(item => item.delete === 0 || item.delete === 1).length > 0) {
                          await apiUpdateOtherLanguageTerminology({
                            series_resource_id: +route.params.id,
                            terms: termList.value.filter(item => item.delete === 0 || item.delete === 1).map(item => {
                              item.name = trim(item.name)
                              item.official_name = trim(item.official_name)
                              item.translation = trim(item.translation)
                              return item
                            }),
                          })
                        }
                        showSuccessToast('保存成功')
                        emit('closeDialog')
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                      } finally {
                        btnLoading.value = false
                      }
                    }}>
                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      保存术语
                    </Button>
                  ) : null
                }
              </div>
            </div>
          ) : null}
        </div>
      </div>

      <div class="flex items-center justify-between gap-x-2 pt-4">
        {slots?.default()}
        <div class="flex gap-x-2">
          <div class="flex items-center">
            <select value={props.videoType} onChange={(e: any) => {
              const value = e.target.value as 'pure_path' | 'origin_path'
              emit('videoChange', value)
            }}
            >
              <option value="origin_path">原视频</option>
              <option value="pure_path">无字幕视频</option>
            </select>
          </div>
          <div class="flex items-center">
            <span>字体大小:</span>
            <select value={fontSize.value} onChange={(e: any) => {
              const value = e.target.value as string
              fontSize.value = +value
            }}
            >
              {
                fontSizeList.map(fontSize => <option value={fontSize}>{fontSize}</option>)
              }
            </select>
          </div>
          <label class="flex items-center">
            <Checkbox
              label=""
              modelValue={isAsync.value}
              onUpdate:modelValue={(value: boolean) => {
                isAsync.value = value
              }}
            />
            <span>同步滚动</span>
          </label>
          <Button class="btn-default btn btn-sm" onClick={() => emit('close')}>取消</Button>
          {!props.isTermCheck ? (
            <Button class="btn btn-primary btn-sm" disabled={props.loading || isReplacing.value || props.saveBtnDisabled} loading={props.loading} onClick={() => {
              currentTime.value = 0
              emit('afterSave', editSubtitle.value)
            }}
            >
              {props.loading ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              保存
            </Button>
          ) : null }
          {props.showOnlineButton && !props.isTermCheck ? (
            <Button class="btn btn-primary btn-sm" disabled={props.loading || isReplacing.value || props.saveBtnDisabled} loading={props.loading} onClick={() => {
              emit('afterSave', editSubtitle.value, true)
            }}
            >
              {props.loading ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              提交到线上
            </Button>
          ) : null }
        </div>
      </div>
    </div>
  )
})
