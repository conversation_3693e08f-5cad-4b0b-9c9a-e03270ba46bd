/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, watchEffect } from 'vue'
import { CreateForm, transformNumber, transformDatetime } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { z } from 'zod'
import { createComponent, useValidator } from '@skynet/shared'
import { apiUpdateResource } from '../resource-api'
import { useRoute } from 'vue-router'
import { showSuccessToast, showFailToast } from '@skynet/ui'
import { useResourceStore } from '../use-resource-store'

type ResourceFormOptions = {
  props: M.ISourceInfo
  emits: {
    close: () => void
    afterSave: () => void
  }
}

export const ResourceForm = createComponent<ResourceFormOptions>({
  props: {
    clazz: '',
    type: '',
    count: undefined,
    title: '',
    btnHidden: false,
    disabled: false,
    release_round: 0,
    resource_type: 0,
    unlocked_episodes: 0,
    serialize_status: 0,
    label_ids: '',
    labels: '',
    free_online_time: ''
  },
  emits: {
    close: () => {},
    afterSave: () => {},
  },
}, (props, { emit }) => {
  const { tags } = useResourceStore()

  const route = useRoute()
  const Form = CreateForm<M.ISourceInfo>()

  const form = ref<M.ISourceInfo & { labelList?: string[] }>({
    title: '',
    count: undefined,
    release_round: 0,
    resource_type: 0,
    unlocked_episodes: 0,
    serialize_status: 0,
    labelList: [],
    label_ids: '',
    free_online_time: ''
  })

  const rules = z.object({
    title: z.string().min(1, {
      message: '请输入资源名称',
    }),
    count: z.number({
      message: '请输入资源完整集数',
    }).int({
      message: '请正确输入资源完整集数',
    }).min(1, {
      message: '请输入资源完整集数',
    }),
    release_round: z.number().min(1, {
      message: '请选择发行轮次',
    }),
    resource_type: z.number().min(1, {
      message: '请选择资源类型',
    }),
    unlocked_episodes: z.number().min(1, {
      message: '请输入解锁集数',
    }),
    serialize_status: z.number().min(1, {
      message: '请选择连载状态',
    }),
  })

  const { error, validateAll } = useValidator(form, rules)

  const getLabelIds = (labelList: string[]) => {
    return labelList.map(id => {
      return tags.value.find(row => row.label_id === +id)?.label_id
    }).join(',')
  }

  const getLabelNames = (labelList: string[]) => {
    return labelList.map(id => {
      return tags.value.find(row => row.label_id === +id)?.content
    }).join(',')
  }

  const onSave = async () => {
    if (!validateAll()) {
      return
    }
    const params: M.ISourceInfo & { series_resource_id?: number } = {
      title: form.value.title,
      count: form.value.count!,
      resource_type: form.value.resource_type,
      release_round: form.value.release_round,
      unlocked_episodes: form.value.unlocked_episodes,
      serialize_status: form.value.serialize_status,
      free_online_time: form.value.free_online_time ? new Date(form.value.free_online_time).getTime() / 1000 : 0,
    }
    if (route.params.id) params.series_resource_id = +route.params.id
    params.label_ids = getLabelIds(form.value.labelList || [])
    params.labels = getLabelNames(form.value.labelList || [])
    try {
      await apiUpdateResource(params)
      showSuccessToast('操作成功')
      emit('afterSave')
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    }
  }

  const onReset = () => {
    form.value = {
      title: props.title || '',
      count: props.count,
      release_round: +route.params.id ? props.release_round : 0,
      resource_type: +route.params.id ? props.resource_type : 0,
      free_online_time:  +route.params.id ? props.free_online_time : '',
      unlocked_episodes: +route.params.id ? props.unlocked_episodes : 0,
      serialize_status: +route.params.id ? props.serialize_status : 0,
      labels: '',
      labelList: +route.params.id ? props.label_ids.split(',').filter(str => !!str) : [],
    }
  }
  watchEffect(() => {
    form.value = {
      title: props.title || '',
      count: props.count,
      release_round: props.release_round,
      resource_type: props.resource_type,
      free_online_time: props.free_online_time ? (props.free_online_time as number)  * 1000 : '',
      unlocked_episodes: props.unlocked_episodes,
      serialize_status: props.serialize_status,
      labels: getLabelNames(props.label_ids.split(',').filter(str => !!str) || []) ,
      labelList: props.label_ids.split(',').filter(str => !!str),
    }
    if (!route.params.id && tags.value.length > 0) {
      form.value.labels = tags.value[0].content
      form.value.labelList = ['' + tags.value[0].label_id]
      form.value.serialize_status = 2
    }
  })

  return () => (
    <>
      <Form
        onSubmit={onSave}
        onReset={onReset}
        class={props.clazz}
        error={error.value}
        data={form.value}
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        hasAction={!props.btnHidden}
        items={[
          { label: requiredLabel('资源名称'), path: 'title', input: { disabled: props.btnHidden, type: 'text' } },
          { label: requiredLabel('资源完整集数'), path: 'count', input: { disabled: props.btnHidden, type: 'number' }, transform: transformNumber },
          props.btnHidden
            ? { label: '标签',
                path: 'labels',
                class: 'col-span-2',
                input: {
                  type: 'text',
                  disabled:  props.btnHidden,
                  placeholder: '暂无标签'
                },
              }
            : { label: '标签', path: 'labelList', class: 'col-span-2',
                input: {
                  popoverWrapperClass: 'z-popover-in-dialog',
                  disabled: props.btnHidden,
                  type: 'multi-select',
                  search: true,
                  options: tags.value?.map(i => ({ label: i.content, value: '' + i.label_id, disabled: i.status === 2 })),
                  placeholder: '请选择标签',
                },
                hint: '请选择 「背景」「角色」「类型」「情节」「情绪」「受众」 标签'
              },
          { label: requiredLabel('解锁集数'), path: 'unlocked_episodes', input: { disabled: props.btnHidden, type: 'number' }, transform: transformNumber },
          { label: requiredLabel('连载状态'), path: 'serialize_status', input: { autoInsertEmptyOption: false, disabled: props.btnHidden, type: 'select', options: [
            { value: 0, label: '-空-' },
            { value: 1, label: '未完结' },
            { value: 2, label: '已完结' },
          ] }, transform: transformNumber },
          { label: requiredLabel('发行轮次'), path: 'release_round', input: { autoInsertEmptyOption: false, disabled: props.btnHidden, type: 'select', options: [
            { value: 0, label: '-空-' },
            { value: 1, label: '首发' },
            { value: 2, label: '二轮' },
          ] }, transform: transformNumber },
          { label: requiredLabel('资源类型'), path: 'resource_type', input: { autoInsertEmptyOption: false, disabled: props.btnHidden, type: 'select', options: [
            { value: 0, label: '-空-' },
            { value: 1, label: '本土' },
            { value: 2, label: '翻译' },
          ] }, transform: transformNumber },
          // {
          //   label: '免费上线时间',
          //   path: 'free_online_time',
          //   input: { type: 'datetime', disabled: props.btnHidden, displayFormat: 'YYYY-MM-DD HH:mm:ss' },
          //   transform: transformDatetime,
          // }
        ]}
      />
    </>
  )
})
