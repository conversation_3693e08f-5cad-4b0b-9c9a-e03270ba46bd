import { httpClient } from 'src/lib/http-client'

export const apiGetABTestList = (data: M.ABTest.params) =>
  httpClient.post<ApiResponse<M.ABTest.ListResponse>>('/abteststrategy/list', data)

export const apiCreateABTest = (data: M.ABTest.CreateAbTest) =>
  httpClient.post<ApiResponse<boolean>>('/abteststrategy/save', data)

export const apiEditABTest = (data: M.ABTest.CreateAbTest) =>
  httpClient.post<ApiResponse<boolean>>('/abteststrategy/save', data)

export const apiEditABTestStatus = (data: { id: number, state: number }) =>
  httpClient.post<ApiResponse<boolean>>('/abteststrategy/set-state', data)

export const apiDeleteABTest = (data: { ids: number[] }) =>
  httpClient.post<ApiResponse<boolean>>('/abteststrategy/delete', data)
