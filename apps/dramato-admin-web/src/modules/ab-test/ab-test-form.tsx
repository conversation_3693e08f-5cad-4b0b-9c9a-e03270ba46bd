/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useABTest } from './use-ab-test'
import { ref } from 'vue'

type ABTestFormOptions = {
  props: {}
}

export const ABTestForm = createComponent<ABTestFormOptions>({
  props: {},
}, props => {
  const {
    currentABTest,
    closeABTestDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useABTest()

  const Form = CreateForm<M.ABTest.CreateAbTest>()
  const formRules = z.object({
    exp_id: z.number().min(1, '请填写实验编号'),
    exp_desc: z.string().min(1, '请输入'),
    sampling_type: z.number().min(1, '请选择'),
    sampling_strategy_ids: z.string().min(1, '请选择'),
    // version_config: z.array(z.object({
    //   version_id: z.number().min(1, '请输入Control Version ID'),
    //   strategy_id: z.string().min(1).optional(),
    // })).min(1, '请填写定价'),
  })

  const { error, validateAll } = useValidator(currentABTest, formRules)
  const currentVersion = ref<M.ABTest.VersionConfig>({})

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentABTest.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('实验编号'),
              'exp_id',
              {
                type: 'number',
                min: 1,
                placeholder: '请输入实验编号',
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('实验描述'),
              'exp_desc',
              {
                type: 'textarea',
                placeholder: '请输入实验描述，以便识别',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('取样方式'),
              'sampling_type',
              {
                type: 'radio',
                options: [{ label: '画像筛选', value: 1 }, { label: '随机筛选', value: 2 }],
              },
              {
                // transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('画像ID'),
              'sampling_strategy_ids',
              {
                type: 'textarea',
                placeholder: '可填写多个，英文逗号隔开',
                rows: 3,
              },
              {
                class: mc('col-span-1', currentABTest.value.sampling_type !== 1 ? 'hidden' : ''),
              },
            ],
            [
              '对照/实验组',
              'version_config',
              {
                type: 'custom',
                render: () => {
                  return (
                    <x-ab-test-group class="flex flex-col gap-y-2">
                      <x-tips class="text-[14px] text-gray-500">请按照A/B testing平台对应填写</x-tips>
                      {
                        currentABTest.value.version_config && currentABTest.value.version_config.length > 0
                          ? currentABTest.value.version_config.map((item, index) => {
                            return (
                              <x-ab-test-version class="p-2 border rounded-md flex flex-col gap-y-2 text-[14px]" key={index} index={index}>
                                <x-title class="flex flex-row items-center justify-between">
                                  {index === 0 ? 'Base' : `Exp${index}`}
                                  <Icon name="ant-design:delete-filled" class={mc(index === 0 ? 'hidden' : '')} onClick={() => {
                                    if (!currentABTest.value.version_config || currentABTest.value.version_config.length <= 1) {
                                      return
                                    }
                                    currentABTest.value.version_config = currentABTest.value.version_config.filter((_, i) => i !== index)
                                  }}
                                  />
                                </x-title>
                                <x-ab-test-item>
                                  <label>Control Version ID</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="number"
                                      class={mc('grow')}
                                      value={item.version_id}
                                      onFocus={() => {
                                        currentVersion.value = item
                                      }}
                                      onInput={(e: Event) => {
                                        currentVersion.value.version_id = Number((e.target as HTMLInputElement).value) || 0
                                      }}
                                      onKeydown={(e: KeyboardEvent) => {
                                        if (e.key !== 'Enter') {
                                          return
                                        }
                                        if (currentVersion.value.version_id === item.version_id) {
                                          return
                                        }
                                        if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                          return
                                        }
                                        currentABTest.value.version_config[index].version_id = currentVersion.value.version_id
                                      }}
                                      onBlur={() => {
                                        if (currentVersion.value.version_id === item.version_id) {
                                          return
                                        }
                                        if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                          return
                                        }
                                        currentABTest.value.version_config[index].version_id = currentVersion.value.version_id
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>
                                <x-ab-test-item>
                                  <label>执行策略组ID</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="text"
                                      class={mc('grow')}
                                      value={item.strategy_id}
                                      onFocus={() => {
                                        currentVersion.value = item
                                      }}
                                      onInput={(e: Event) => {
                                        currentVersion.value.strategy_id = (e.target as HTMLInputElement).value || ''
                                      }}
                                      onKeydown={(e: KeyboardEvent) => {
                                        if (e.key !== 'Enter') {
                                          return
                                        }
                                        if (currentVersion.value.strategy_id === item.strategy_id) {
                                          return
                                        }
                                        if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                          return
                                        }
                                        currentABTest.value.version_config[index].strategy_id = currentVersion.value.strategy_id
                                      }}
                                      onBlur={() => {
                                        if (currentVersion.value.strategy_id === item.strategy_id) {
                                          return
                                        }
                                        if (!currentABTest.value.version_config || !currentABTest.value.version_config[index]) {
                                          return
                                        }
                                        currentABTest.value.version_config[index].strategy_id = currentVersion.value.strategy_id
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>

                              </x-ab-test-version>
                            )
                          })
                          : null
                      }
                      <Button class="btn btn-outline btn-sm  w-[120px]" onClick={() => {
                        if (!currentABTest.value.version_config) {
                          return
                        }
                        currentABTest.value.version_config.push({})
                      }}
                      >新增实验组
                      </Button>
                    </x-ab-test-group>
                  )
                },
              },
              {
                class: mc('col-span-1'),
              },
            ],
          ]}
          data={currentABTest.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeABTestDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentABTest.value?.id ? void onCreate() : void onEdit()

            try {
              const exclude: string[] = []

              if (currentABTest.value.sampling_type !== 1) {
                exclude.push('sampling_strategy_ids')
              }

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
