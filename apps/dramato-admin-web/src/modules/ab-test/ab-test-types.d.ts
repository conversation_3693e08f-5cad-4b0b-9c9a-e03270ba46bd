declare namespace M {
  namespace ABTest {
    interface params {
      page_info?: {
        page_index?: number
        page_size?: number
      }
      exp_id?: 0
      state?: 0
    }

    interface ABTest extends CreateAbTest {
      state: number
      updated: number
      updated_operator_id: string
      updated_operator_name: string
      created: number
      created_operator_id: string
      created_operator_name: string
    }

    interface ListResponse {
      list: ListItem[]
      total: number
    }

    interface PriceConfig {
      c: string[]
      v: number
    }

    interface VersionConfig {
      version_id?: number
      strategy_id?: string
      vars_name?: string
      whitelist?: string
    }

    interface CreateAbTest {
      id?: number
      exp_id?: number
      exp_desc?: string
      sampling_type?: number
      sampling_strategy_ids?: string
      version_config?: VersionConfig[]
    }
  }
}
