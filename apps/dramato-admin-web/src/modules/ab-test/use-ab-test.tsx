/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateABTest, apiDeleteABTest, apiEditABTest, apiEditABTestStatus, apiGetABTestList } from './ab-test-api'
import { ABTestForm } from './ab-test-form'

export const useABTest = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    showADDialog,
    closeABTestDialog,
    currentABTest,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
    onUpdateState,
    onDelete,
  }
}

const Form = CreateForm<M.ABTest.params>()
const params = ref<M.ABTest.params>({
  // platform: 'ios',/
})

const Table = CreateTableOld<M.ABTest.ABTest>()
const list = ref<M.ABTest.ABTest[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)

const currentABTest = ref<M.ABTest.CreateAbTest>({
  version_config: [{}]
})

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetABTestList({
    ...params.value,
    page_info: {
      page_index: _page, page_size: pageSize.value } })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  page.value = _page
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeABTestDialog = ref()

const showADDialog = () => {
  closeABTestDialog.value = openDialog({
    title: currentABTest.value.id ? '编辑ABTest' : '新建ABTest',
    body: () => <ABTestForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentABTest.value = {
    version_config: [{}]
  }
  showADDialog()
}

const onEditBtnClick = (r: M.ABTest.ABTest) => {
  currentABTest.value = {
    ...r,
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeABTestDialog.value && closeABTestDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search(page.value)
}

const switchRequestParams = () => ({
  ...currentABTest.value,
  version_config: (currentABTest.value.version_config || []).map((i, idx) => {
    const vars_name = idx === 0 ? 'base' : `exp${idx}`
    return {
      ...i,
      vars_name
    }
  })
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateABTest(switchRequestParams())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditABTest(switchRequestParams())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}

const onUpdateState = async (id: number, state: number) => {
  try {
    await apiEditABTestStatus({ id, state })
    showAlert('更新成功')
    void search(page.value)
  } catch (error: any) {
    showAlert(error.response.data.message || '更新失败', 'error')
  }
}

const onDelete = async (id: number) => {
  try {
    await apiDeleteABTest({ ids: [id] })
    showAlert('删除成功')
    void search(page.value)
  } catch (error: any) {
    showAlert(error.response.data.message || '删除失败', 'error')
  }
}
