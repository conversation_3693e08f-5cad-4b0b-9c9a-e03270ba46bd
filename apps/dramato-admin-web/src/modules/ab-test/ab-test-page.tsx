import { createComponent } from '@skynet/shared'
import { But<PERSON>, DateTime, Pager, transformNumber } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useABTest } from './use-ab-test'
import { onMounted } from 'vue'

type ABTestOptions = {
  props: {}
}
export const ABTest = createComponent<ABTestOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    onCreateBtnClick,
    onEditBtnClick,
    onUpdateState,
    onDelete,
  } = useABTest()

  onMounted(() => {
    void search(1)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li class="flex items-center gap-x-2">
              ABTest管理后台
              <span class="text-[14px] text-gray-500">本后台仅做管理，创建实验请前往平台：
                <a
                  class="cursor-pointer text-primary"
                  target='_blank'
                  href={
                    location.origin.includes('-test') ? 'https://abtest-web-test.mydramawave.com/' : 'https://abtest-web.mydramawave.com/'
                  }
                >A/B Testing
                </a>
              </span>
            </li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              page.value = 1
              pageSize.value = 20
              void search(1)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['实验编号', 'exp_id', { type: 'number' }, { transform: transformNumber }],
              ['状态', 'state', { type: 'select', options: [{ label: '进行中', value: 1 }, { label: '暂停', value: 2 }] }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            ABTest表
            <Button class="btn-primary btn btn-sm" onClick={onCreateBtnClick}>新增ABTest</Button>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['实验编号', 'exp_id', { class: 'w-[100px]' }],
            ['实验描述', 'exp_desc', { class: 'w-[200px]' }],
            ['对照/实验组数', row => row.version_config?.length, { class: 'w-[100px]' }],
            ['取样方式', row => ['-空-', '策略筛选', '随机筛选'][row.sampling_type || 0], { class: 'w-[100px]' }],
            ['状态', row => ['-空-', '进行中', '暂停'][row.state || 0], { class: 'w-[100px]' }],
            ['创建时间', row => <DateTime value={(row.created || 0) * 1000} />, { class: 'w-[200px]' }],
            ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
            ['更新时间', row => <DateTime value={(row.updated || 0) * 1000} />, { class: 'w-[200px]' }],
            ['更新人', 'updated_operator_name', { class: 'w-[150px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => onUpdateState(row.id || 0, row.state === 1 ? 2 : 1)}>{row.state === 1 ? '停止实验' : '启动实验'}</Button>
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
                <Button class="btn-outline btn btn-xs" onClick={() => onDelete(row.id || 0)}>删除</Button>
              </div>
            ), {
              class: 'w-[280px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default ABTest
