/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from 'src/lib/http-client'
import { transformGetUserConfigMap, transformSetUserConfigMap } from '../strategy-group/strategy-group-api'

export const popupApi = {
  updatePersona: (popup_id: number, user_config: Record<string, any>) => {
    const { condition_type, strategy_id, strategy_layer_ids, ...otherUserConfig } = user_config
    if (condition_type === 2) {
      return httpClient.post<ApiResponse<null>>('/popup/config/update-persona', { popup_id, condition_type, strategy_id: strategy_id || '' }, {
        transformRequestData: transformSetUserConfigMap,
      })
    }
    return httpClient.post<ApiResponse<null>>('/popup/config/update-persona', { popup_id, condition_type, strategy_layer_ids, user_config: { custom_users_config: otherUserConfig } }, {
      transformRequestData: transformSetUserConfigMap,
    })
  },
  getPersona: (popup_id: number) =>
    httpClient.post<ApiResponse<{ user_config: { custom_users_config: Record<string, string[]> }, condition_type: number, strategy_id: string, strategy_layer_ids: number[] }>>('/popup/config/get-persona', { popup_id }, {
      transformResponseData: transformGetUserConfigMap,
    }),
}

export const apiGetActivityPopupList = (data: M.ActivityPopupSearchProps) =>
  httpClient.post<ApiResponse<{
    list: M.ActivityPopup[]
    total: number
  }>>('/popup/position/list', data)

export const apiEditActivityPopup = (data: M.ActivityPositionPopup) =>
  httpClient.post<ApiResponse<null>>('/popup/position/save', data)

export const checkDeepLink = (data: {
  target_app_ids: string // 生效应用id,用逗号分割
  link: string
}) => httpClient.post<ApiResponse<null>>('/content/template/link/check', data)

export const updateDialogStatus = (data: {
  popup_id: number
  status: number // -1删除 0.下架 1.上架
}) => httpClient.post<ApiResponse<null>>('/popup/status/update', data)

export const apiGetActivityPopupSceneList = (data: M.ActivityPopupSearchProps) =>
  httpClient.post<ApiResponse<M.DialogScene.SceneList>>('/popup/list', data)

export const apiEditActivityPopupScene = (data: M.DialogScene.Scene) =>
  httpClient.post<ApiResponse<null>>('/popup/save', data)

export const updateDialogStatusScene = (data: {
  id: number
}) => httpClient.post<ApiResponse<null>>('/popup/scene/delete', data)

export const apiGetDialogConfig = () => httpClient.get<ApiResponse<M.PopupConfig>>('/popup/config/items', {})
