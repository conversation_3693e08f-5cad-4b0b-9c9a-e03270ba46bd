/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, openDialog, transformDatetime, transformInteger, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader'
import dayjs from 'dayjs'
import { ElSelect, ElOption } from 'element-plus'
import { computed, ref, watch, onMounted } from 'vue'
import { useActivityPopup } from './use-activity-popup'
import { checkDeepLink } from './activity-popup-api'
import { useAppAndLangOptions } from '../options/use-app-options'
import { MultiLangForm } from './activity-popup-multi-lang-form'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'

export const ActivityPopupFormNew = createComponent(null, props => {
  const {
    currentActivityPopup,
    onEdit,
    onCreate,
    closeEditActivityPopupModal,
    isUpdating,
    config,
    commodityList,
    getCommodityList,
    getConfigByType,
    isCoupon,
    popupItemsConfig,
    searchForm,
    getLangCode,
    checkAlias,
    getConfig
  } = useActivityPopup()

  const isUploading = ref(false)
  const isUploadedFail = ref(false)
  const Form = CreateForm<M.ActivityPopup>()
  const formRules = z.object({
    name: z.string().min(1, '请输入名称'),
    priority: z.number().min(1, '请输入优先级'),
    scene_type: z.number().min(1, '请选择位置'),
  })


  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  const { error, validateAll } = useValidator(currentActivityPopup, formRules)

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
    void getConfig()
  })
  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            console.log('path', path, value, typeof value)

            set(currentActivityPopup.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('名称'),
              'name',
              {
                type: 'text',
                maxlength: 100,
                placeholder: '请输入标题，最大支持100字符',
              },
              {
                class: 'col-span-1',
              },
            ],
            [

              requiredLabel('是否AB实验'),
              'is_abtest',
              {
                type: 'radio',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 },
                ],
              }
            ],
            [
              '用户分层',
              'strategy_layer_ids',
              {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: strategyLayerList.value.map((n, index) => {
                  return { value: n.id, label: `${n.id}/${n.name}` }
                }),
              },
              {
                class: mc('col-span-1', currentActivityPopup.value.is_abtest !== 0 ? 'hidden' : ''),
              },
            ],
            [
              requiredLabel('优先级'),
              'priority',
              {
                type: 'text',
                placeholder: '必填项，数值越大排序越高',

              },
              {
                transform: transformNumber,
              },
            ],
            [

              [
                '状态',
                'status',
                {
                  type: 'select',
                  options: [
                    { label: '生效', value: 1 },
                    { label: '失效', value: 0 },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
            ],
            [
              [
                requiredLabel('位置'),
                'scene_type',
                {
                  type: 'custom',
                  render: () => (
                    <x-custom-box class="block w-[150px]">
                      <ElSelect class="w-[150px]" onChange={e => currentActivityPopup.value.scene_type = +e} modelValue={currentActivityPopup.value.scene_type}>
                        <ElOption value={0} label="请选择" />
                        {Object.keys(config.value.popup_scene_items).map(key => (
                          <ElOption value={+key} label={config.value.popup_scene_items[+key]} disabled={[7, 11, 8].includes(+key)} />
                        ))}
                      </ElSelect>
                    </x-custom-box>
                  ),
                },
                // {
                //   type: 'select',
                //   options: Object.keys(config.value.popup_scene_items).map(key => ({
                //     label: config.value.popup_scene_items[+key],
                //     value: +key,
                //     disabled: [7, 11, 8].includes(+key)
                //   }))
                //   ,
                // },
                {
                  transform: transformNumber,
                  class: 'col-span-1',
                },
              ],
            ],


            [
              '备注',
              'remark',
              {
                type: 'textarea',
                maxlength: 300,
                placeholder: '用于运营配置时辨别区分，自定义编辑',
              },
              {
                class: 'col-span-1',
              },
            ],

          ]}
          data={currentActivityPopup.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditActivityPopupModal.value}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} disabled={isUpdating.value} onClick={async () => {
          const next = () => void onEdit()

          const showTipsDialog = () => {
            const hideDialog = openDialog({
              title: '',
              mainClass: 'pb-0 px-5',
              body: (
                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-status-body>deeplink 对应短剧已下线，请确认是否保存</x-status-body>
                  <x-status-footer class="w-full flex justify-end gap-x-[10px]">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      next()
                      hideDialog()
                    }}
                    >确定
                    </button>
                  </x-status-footer>
                </x-status-confirm-dialog>
              ),
            })
          }
          try {
            const exclude: string[] = []
            if (!validateAll({ exclude })) {
              console.log('error', error.value)
              return
            }
            next()
          } catch (error) {
            showTipsDialog()
          }
        }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
