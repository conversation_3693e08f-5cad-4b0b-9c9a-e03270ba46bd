/* eslint-disable @typescript-eslint/no-explicit-any */
import { bindLoading, createCachedFn, Key } from '@skynet/shared'
import { ref } from 'vue'
import { apiGetStrategyGroupUserProfile } from '../strategy-group/strategy-group-api'
import { CreateForm, openDialog } from '@skynet/ui'
import { PopupCondition } from './popup-condition'

export type UserProfile = Api.StrategyGroupUserProfile.Item
export const usePopupConditionStore = createCachedFn((popupId: Key) => {
  const Form = CreateForm<Record<string, string[]>>()
  const userProfileList = ref<Api.StrategyGroupUserProfile.Item[]>([])
  const fetchingUserProfileList = ref(false)
  const formData = ref<Record<string, any>>({ condition_type: 1 })

  const fetchUserProfileList = async () => {
    fetchingUserProfileList.value = true
    const res = await bindLoading(apiGetStrategyGroupUserProfile({ platform: 'All' }), fetchingUserProfileList)
    if (!res.data) return
    userProfileList.value = res.data.list
  }
  const showPopupCondition = (popupId: number, popupName: string) => {
    const close = openDialog({
      title: `${popupName} - 触发条件`,
      customClass: '!w-auto min-w-[400px]',
      body: () => <PopupCondition popupId={popupId} onSubmit={() => close()} />,
    })
  }

  return {
    Form,
    formData,
    userProfileList,
    fetchingUserProfileList,
    fetchUserProfileList,
    showPopupCondition,
  }
})
