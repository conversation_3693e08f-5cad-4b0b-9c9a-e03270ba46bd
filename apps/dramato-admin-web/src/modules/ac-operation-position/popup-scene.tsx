import { createComponent } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { cloneDeep, set } from 'lodash-es'
import { RouterLink, useRoute } from 'vue-router'
import { usePopupScene } from './use-popup-scene'
import { PopupSceneNewForm } from './popup-scene-new-form'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useActivityPopup } from './use-activity-popup'
import { apiEditActivityPopupScene } from './activity-popup-api'
import { showAlert } from '@skynet/ui'
const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[1200px] overflow-hidden'

export const PopupScene = createComponent(null, () => {
  const {
    onSearch,
    total,
    searchForm,
    onReset,
    currentPopupScene,
    InitPopupSceneOption,
    closeEditPopupSceneModal,
    list,
    onPageChange,
    onPageSizeChange,
    loading,
    config,
    onUpdate,
    getConfig,
    onEdit,
  } = usePopupScene()

  const route = useRoute()
  const {
    getConfigByType,
    currentActivityPopup,
  } = useActivityPopup()

  const Form = CreateForm<M.DialogScene.Scene>()
  const Table = CreateTableOld<M.DialogScene.Scene>()

  const { appOptions } = useAppAndLangOptions(() => undefined, {})

  const priorityChange = (row: any) => {
    console.log(row)
    currentPopupScene.value = cloneDeep(row)
    void onEdit()
  }

  const getCurConfigByType = (type: number, popType: any) => {
    let str = '-'
    const arr = getConfigByType(type)
    for (const i in arr) {
      if (Number(i) == popType) {
        str = arr[i]
      }
    }
    return str
  }

  const getCurJumpType = (type: number) => {
    const arr = ['', '支付类', '链接类', '兑换券']
    return arr[type]
  }

  const getCurJumMethod = (type: number) => {
    const arr = ['', '手动跳转', '自动跳转', '外部链接跳转']
    return arr[type]
  }

  const columns: TableColumnOld<M.DialogScene.Scene>[] = [
    ['弹窗ID', 'popup_id', { class: 'w-[200px]' }],
    ['弹窗名称', 'popup_name', { class: 'w-[260px]' }],
    ['跳转类型', row => getCurJumpType(row.target_type), { class: 'w-[260px]' }],
    ['样式', row => getCurConfigByType(row.target_type, row.popup_type), { class: 'w-[260px]' }],
    ['排序',
      row => (
        <div class="flex flex-nowrap">
          <input type="text" min="0" max="1000" onChange={e => {
            row.priority = Number((e.target as HTMLInputElement).value || '')
            priorityChange(row)
          }} value={row.priority} class="" />
        </div>
      ),
      { class: 'w-[140px]' }],
    ['跳转方式', row => getCurJumMethod(row.jump_method), { class: 'w-[160px]' }],

    ['累计展示次数', row => row.max, { class: 'w-[120px]' }],
    ['间隔时间', row => row.interval + '/' + config.value.interval_unit_items[row?.interval_unit || 0]],
    ['状态', row => ['失效', '生效', ''][row?.status || 0], { class: 'w-[160px]' }],
    ['弹窗类型', row => config.value.popup_type_items[row?.popup_type || 1], { class: 'w-[160px]' }],

    ['开始时间', row => (<><DateTime value={(row?.start_time as number || 0) * 1000} /></>), { class: 'w-[300px]' }],
    ['结束时间', row => (<><DateTime value={(row?.end_time as number || 0) * 1000} /></>), { class: 'w-[300px]' }],
    ['应用版本', row => appOptions.value.find(i => i.value === row?.app_id)?.label + ' ' + (row?.app_version || '空') + ['', '+'][row.version_compare || 0], { class: 'w-[260px]' }],
    ['下发语言', 'language_version_code', { class: 'w-[160px]' }],

    ['创建时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['创建人', 'updated_operator_name', { class: 'w-[200px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap gap-1">

          <Button class={'btn btn-' + (row?.status === 1 ? 'warning' : 'accent') + ' btn-sm text-[#fff]'}
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: row?.status === 1 ? '失效' : '生效',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认{row?.status === 1 ? '失效' : '生效'}【{row.popup_name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopupScene({
                          ...row,
                          status: row?.status == 1 ? 0 : 1,
                        }).then(() => {
                          onSearch()
                          showAlert('操作成功')
                          hideDeleteDialog()
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}>
            {row?.status === 1 ? '失效' : '生效'}
          </Button>
          <Button class="btn btn-primary btn-sm  text-white" onClick={() => {
            currentPopupScene.value = {
              ...row,
            }

            closeEditPopupSceneModal.value = openDialog({
              title: '元素管理',
              body: () => <PopupSceneNewForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[1200px] overflow-hidden',
            })
          }}
          >编辑
          </Button>
          <Button class="btn btn-error btn-sm text-white"
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: '复制',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认删除【{row.popup_name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopupScene({
                          ...row,
                          status: -1,
                        }).then(() => {
                          showAlert('删除成功')
                          onSearch()
                          hideDeleteDialog()
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}>
            删除
          </Button>
          <Button class="btn btn-primary btn-sm text-white"
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: '复制',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认复制【{row.popup_name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopupScene({
                          ...row,
                          popup_id: undefined,
                        }).then(() => {
                          showAlert('复制成功')
                          onSearch()
                          hideDeleteDialog()
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}>
            复制
          </Button>
        </div>
      ),
      { class: 'w-[260px]' },
    ],
  ]

  onMounted(() => {
    console.log(route.params.id)
    searchForm.value.position_id = Number(route.params.id)
    onSearch()
    void getConfig()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/operation-position">返回</RouterLink><span class="ml-4">运营位元素管理</span></li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[
              [
                '弹窗名称',
                'popup_name',
                {
                  type: 'text',
                },
              ],
              [
                '弹窗ID',
                'popup_id',
                {
                  type: 'text',
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '跳转类型',
                'target_type',
                {
                  type: 'select',
                  options: Object.keys(config.value.popup_scene_items).map(key => ({
                    label: config.value.popup_scene_items[+key],
                    value: +key,
                  }))
                  ,
                },
                {
                  transform: transformNumber,
                },
              ],
              // [
              //   '样式',
              //   'popup_type',
              //   {
              //     type: 'select',
              //     options: Object.keys(getConfigByType(currentPopupScene.value.target_type!) as {}).map(key => ({
              //       label: getConfigByType(currentPopupScene.value.target_type!)?.[+key] ?? '',
              //       value: +key,
              //     }))
              //   }
              // ],
              ['排序', 'sortType', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-DESC' },
                  { label: '创建时间：从旧到新', value: 'created-ASC' },
                  { label: 'ID：从大到小', value: 'id-DESC' },
                  { label: 'ID：从小到大', value: 'id-ASC' },
                  { label: '优先级：从大到小', value: 'priority-DESC' },
                  { label: '优先级：从小到大', value: 'priority-ASC' },
                  // { label: '冻结', value: 4 },
                ],
              }],
              [
                '应用版本',
                'app_id',
                {
                  type: 'select',
                  options: appOptions.value.filter(i => i.label.includes('Drama')),
                  // disabled: props.appIdSelectDisabled,
                },
                {
                  transform: transformNumber,
                }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-between">
            <span>元素管理列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => {
                currentPopupScene.value = {
                  ...InitPopupSceneOption,
                }
                closeEditPopupSceneModal.value = openDialog({
                  title: '元素管理',
                  body: () => <PopupSceneNewForm />,
                  mainClass: dialogMainClass,
                  customClass: '!w-[1200px] overflow-hidden',
                })
              }}
              >添加弹窗
              </Button>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate: page={onPageChange}
                onUpdate: size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default PopupScene
