declare namespace M {
  interface ActivityPopupSearchProps {
    app_id?: number
    language_version_code?: string
    popup_type?: number
    app_version?: string
    status?: number // 生效状态，1：已生效，0：已停用
    scene_type?: number
    sort?: {
      [key: string]: string
    }
    page_info?: {
      page_index?: number
      page_size?: number// 选填参数
    }
    position_id?: number
    sortType?: string
  }

  interface ActivityPopup {
    popup_id?: number // 选填
    popup_name?: string
    popup_type?: number// 1.新用户首日优惠 2.未付费老用户首充 3.收藏鼓励折扣 7.首页冷启动 1001.打折票 1002.优惠活动 1000.解锁礼包
    start_time?: number | string // 开始时间，秒
    end_time?: number | string // 结束时间，秒
    updated_operator_id?: string
    updated_operator_name?: string
    target_link?: string
    app_id?: number // 生效应用
    language_version_codes: string[]
    content_i18n: Record<string, Record<string, string>>
    image?: string
    updated?: number
    jump_method?: number // 跳转方式，1-内部跳转，2-外部跳转
    jump_interval?: number // 跳转间隔
    status?: number // 状态，1：已生效，0：已停用
    limited_time?: number// 限时时间0.不限制，单位小时
    target_type?: number// 跳转类型1.商品2.跳转链接
    app_version?: string
    version_compare?: number// 版本比较 0 等于，1大于等于
    status?: number// 生效状态，1：已生效，0：已停用
    content?: {
      [key: string]: string
    }
    product_type?: string
    strategy_layer_ids?: number[]
    strategy_layer_names?: string
  }

  interface ActivityPositionPopup {
    id?: number
    name?: string
    strategy_layer_ids?: number[]
    strategy_layer_names?: string
    strategy_layer_name_items?: Array<{
      id: number
      name: string
    }>
    scene_type?: number
    priority?: number
    status?: number
    remark?: string
    created?: number
    updated?: number
    updated_operator_name?: string
    deleted?: number
    position_id?: number

    popup_id?: number // 选填
    popup_name?: string
    popup_type?: number// 1.新用户首日优惠 2.未付费老用户首充 3.收藏鼓励折扣 7.首页冷启动 1001.打折票 1002.优惠活动 1000.解锁礼包
    start_time?: number | string // 开始时间，秒
    end_time?: number | string // 结束时间，秒
    updated_operator_id?: string
    updated_operator_name?: string
    target_link?: string
    app_id?: number // 生效应用
    language_version_codes: string[]
    content_i18n?: Record<string, Record<string, string>>
    image?: string
    updated?: number
    jump_method?: number // 跳转方式，1-内部跳转，2-外部跳转
    jump_interval?: number // 跳转间隔
    status?: number // 状态，1：已生效，0：已停用
    limited_time?: number// 限时时间0.不限制，单位小时
    target_type?: number// 跳转类型1.商品2.跳转链接
    app_version?: string
    version_compare?: number// 版本比较 0 等于，1大于等于
    status?: number// 生效状态，1：已生效，0：已停用
    content?: {
      [key: string]: string
    }
    product_type?: string
    strategy_layer_ids?: number[]
    strategy_layer_names?: string
    resources?: popupResources[]
    is_abtest?: number
    popup_ids?: number[]
  }

  interface popupResources {
    text?: string
    text_color?: string
    expire_color?: string
    sub_title?: string
    title?: string
    type?: number
    language?: string
    uri?: string
  }

  interface PopupConfig {
    popup_type_items: {
      [key: number]: string
    }
    popup_scene_items: {
      [key: number]: string
    }
    popup_jump_target_items: {
      [key: number]: string
    }
    content_items: {
      [key: string]: string
    }
    lang_items: {
      [key: string]: string
    }
    interval_unit_items: {
      [key: number]: string
    }
    popup_payment_type_items: {
      [key: number]: string
    }
    popup_link_type_items: {
      [key: number]: string
    }
    popup_coupon_type_items: {
      [key: number]: string
    }
    popup_scene_items: {
      [key: number]: string
    }
    content_item_list_v2: Array<{
      key: string
      desc: string
      color_key: string
      color_desc: string
    }>
    popup_banner_type_items: {
      [key: number]: string
    }
  }

  namespace DialogScene {
    interface Scene {
      id?: number
      scene_type?: number // 弹窗场景
      popup_id?: number// 弹窗ID
      popup_name?: string
      popup_type?: number// 1.新用户首日优惠 2.未付费老用户首充 3.收藏鼓励折扣 7.首页冷启动 1001.打折票 1002.优惠活动 1000.解锁礼包
      popup_status?: number// 1.启用。0.未启用
      app_id?: number// appId
      platform?: string
      freq_limit_type?: number // 频率限制类型,1.按天
      interval?: number// 间隔时间,小时
      interval_unit?: number
      max?: number// 最大弹出次数
      priority?: number // 优先级权重，越大越靠前
      updated?: number // 更新时间，秒
      start_time?: number
      end_time?: number
      language_version_code?: string
      app_version?: string
      version_compare?: number
      updated_operator_name?: string
      language_version_codes?: string[]
      need_jumptype?: number

      target_type: number
      position_id?: number
      status?: number
      resources?: popupResources[]
      pop_type?: number
      content_i18n?: Record<string, Record<string, string>>
      target_link?: string | number
      limited_time?: number
      button_color?: string

    }

    interface SceneList {
      list: Scene[]
      total: number
    }
  }
}
