/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, openDialog, showAlert, showFailToast, transformInteger, transformNumber, transformTimestamp } from '@skynet/ui'
import dayjs from 'dayjs'
import { get, set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { z } from 'zod'
import { Uploader } from '../common/uploader/uploader'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useActivityPopup } from './use-activity-popup'
import { usePopupScene } from './use-popup-scene'

export const PopupSceneNewForm = createComponent(null, props => {
  const {
    currentPopupScene,
    InitPopupSceneOption,
    onEdit,
    onCreate,
    closeEditPopupSceneModal,
    isUpdating,
    config,
    getConfig,
  } = usePopupScene()

  const {
    getConfigByType,
    getCommodityList,
    commodityList,
    currentActivityPopup,
  } = useActivityPopup()

  interface i18Item {
    [key: string]: string
  }

  const route = useRoute()
  const isUploading = ref(false)
  const isUploadedFail = ref(false)
  const mutiUpImgs = ref<any>([])
  const tempFiles = ref([])
  const Form = CreateForm<M.DialogScene.Scene>()
  const formRules = z.object({
    popup_name: z.string().min(1, '请输入'),
    priority: z.number().min(1, '请填写优先级').max(999, '支持输入1-999的整数'),
    // app_id: z.number().min(1, '请选择'),
    target_type: z.number().min(1, '请输入'),
    popup_type: z.number().min(1, '请选择'),
    // 'coupon_info.coupon_id': z.string().min(1, '请输入'),
    // 'coupon_info.series_key': z.string().min(1, '请输入'),
    // 'coupon_info.coupon_num': z.string().min(1, '请输入'),
    target_link: z.string().min(1, '请输入'),
    jump_method: z.number().min(1, '请选择'),
    max: z.number().min(-1, '请输入展示次数'),
    interval: z.number().min(-1, '请输入展示次数'),
    interval_unit: z.number().min(1, '请选择'),
  })

  const imgLanOptions = {
    'de-DE': 'German,德语,de,DE,german,德国',
    'en-US': 'English,英语,en,US,english,美国',
    'es-MX': 'Spanish,西班牙,es,MX,spanish,西班牙语',
    'fr-FR': 'French,法语,fr,FR,french,法国',
    'id-ID': 'Indonesian,印度尼西亚,id,ID,indonesian,印度尼西亚语',
    'it-IT': 'Italian,意大利,it,IT,italian,意大利语',
    'ja-JP': 'Japanese,日语,日本,ja,JP,japanese',
    'ko-KR': 'Korean,韩语,韩国,ko,KR,korean',
    'ms-MY': 'Malaysia,马来西亚,ms,MY,malaysia,马来西亚语',
    'pt-PT': 'Portuguese,葡萄牙,pt,PT,portuguese,葡萄牙语',
    'ru-RU': 'Russian,俄罗斯,俄国,ru,RU,russian,俄罗斯语',
    'th-TH': 'Thai,泰国,泰语,th,TH,thai',
    'tl-PH': 'Filipino,菲律宾,tl,PH,filipino,菲律宾语',
    'tr-TR': 'Turkish,土耳其,tr,TR,turkish,土耳其语',
    'vi-VN': 'Vietnamese,越南,vi,VN,vietnamese,越南语',
    'zh-CN': '简体中文,ch,CN,chinese,中国,China,china',
    'hi-IN': '印地,hi,IN,hindi,India,india,Hindi,印地语',
    'zh-TW': '繁体中文,tw,TW,Taiwan,台湾,taiwan,hk,HK,Hong kong,hong kong,hongkong,Hongkong,香港',
  }

  const { appOptions, languageOptions } = useAppAndLangOptions(() => 54, {
    onSuccess: () => {
      void getConfig()
      void getCommodityList(currentPopupScene.value.app_id || 0)
      // onSearch()
    },
  })

  watch(() => currentPopupScene.value.app_id, () => {
    console.log(currentPopupScene.value.app_id)
    void getCommodityList(currentPopupScene.value.app_id || 0)
  })

  onMounted(() => {
    mutiUpImgInit()
  })

  watch(() => currentPopupScene.value.popup_type, () => {
    currentPopupScene.value = {
      ...InitPopupSceneOption,
      target_type: currentPopupScene.value.target_type,
      popup_type: currentPopupScene.value.popup_type,
    }
  })

  watch(() => currentPopupScene.value.target_type, () => {
    currentPopupScene.value = {
      ...InitPopupSceneOption,
      target_type: currentPopupScene.value.target_type,
    }

    console.log('currentPopupScene.value.target_type', currentPopupScene.value.target_type)

    if (currentPopupScene.value.target_type === 1) {
      currentPopupScene.value.popup_type = 1006
    }

    if (currentPopupScene.value.target_type === 2) {
      currentPopupScene.value.popup_type = 1005
    }

    if (currentPopupScene.value.target_type === 3) {
      currentPopupScene.value.popup_type = 1014
    }

    if (currentPopupScene.value.target_type === 4) {
      currentPopupScene.value.popup_type = 1015
    }
  })

  const mutiUpImgInit = () => {
    const newArr = []
    if (currentPopupScene.value.target_type === 2 && currentPopupScene.value.popup_type == 1005) {
      for (const i in currentPopupScene.value.resources) {
        newArr.push(currentPopupScene.value.resources[i]['uri'])
      }
    }
    mutiUpImgs.value = newArr
  }

  const formatMutiUpImg = () => {
    const newArr = []
    for (const i in mutiUpImgs.value) {
      newArr.push({
        uri: mutiUpImgs.value[i],
        type: 1,
        text: '',
        text_color: '',
        expire_color: '',
        sub_title: '',
        title: '',
        language: '',
      })
    }
    // currentPopupScene.value.resources = newArr
    set(currentPopupScene.value, 'resources', newArr)
    console.log(currentPopupScene.value)
  }

  watch(() => mutiUpImgs.value, () => {
    formatMutiUpImg()
  })

  // watch(() => currentPopupScene.value.button_color, () => {
  //   for (let i in currentPopupScene.value.content_i18n) {
  //     currentPopupScene.value.content_i18n[i]['button_color'] = currentPopupScene.value.button_color || '0xFF000000'
  //   }

  //   console.log(currentPopupScene.value.content_i18n)
  // })

  watch(() => currentPopupScene.value.language_version_codes, () => {
    // if (currentPopupScene.value.target_type === 2 && currentPopupScene.value.popup_type == 1005) {
    //   // currentPopupScene.value.resources = []
    //   return
    // }
    const newArr = {}
    const nullInitObj = {}
    console.log(111)
    for (const ni in config.value.content_item_list_v2) {
      const nItem = config.value.content_item_list_v2[ni]
      nullInitObj[nItem['key']] = ''
      if (!['button_color'].includes(nItem['color_key'])) {
        nullInitObj[nItem['color_key']] = '0xFF000000'
      }
      nullInitObj['target_link'] = ''
    }
    console.log(currentPopupScene.value.language_version_codes)
    for (const j in currentPopupScene.value.language_version_codes) {
      const has = false
      const initObj = JSON.parse(JSON.stringify(nullInitObj))

      if (currentPopupScene.value?.content_i18n.hasOwnProperty(currentPopupScene.value.language_version_codes[j])) {
        newArr[currentPopupScene.value.language_version_codes[j]] = currentPopupScene.value.content_i18n[currentPopupScene.value.language_version_codes[j]]
      } else {
        newArr[currentPopupScene.value.language_version_codes[j]] = initObj
      }
    }

    currentPopupScene.value.content_i18n = JSON.parse(JSON.stringify(newArr))
    // if (currentPopupScene.value.target_type === 1 && [1006].includes(currentPopupScene.value.popup_type)) {
    //   for (let m in currentPopupScene.value.content_i18n) {
    //     currentPopupScene.value.content_i18n[m]['button_color'] = currentPopupScene.value.button_color || '0xFF000000'
    //   }
    // }

    console.log(currentPopupScene.value.content_i18n)
  }, { deep: true, immediate: true })

  const { error, validateAll } = useValidator(currentPopupScene, formRules)

  const checkItem = ref<M.ActivityPopup[]>([])

  const getLangCode = (val: string) => {
    let lan = val
    for (const i in config.value.lang_items) {
      if (i == val) {
        lan = config.value.lang_items[i]
        break
      }
    }
    return lan
  }

  const currentPopTargetLink = ref<Record<string, string>>({})

  const hexToRgba = (hex: number) => {
    const r = (hex >> 16) & 0xFF
    const g = (hex >> 8) & 0xFF
    const b = hex & 0xFF
    const a = ((hex >> 24) & 0xFF) / 255
    return `rgba(${r}, ${g}, ${b}, ${a})`
  }
  const raw2display = <T extends unknown>(data: object, item: FormItemObject): T => {
    const path = item.path
    const transform = item.transform?.[0]
    const raw = get(data, path) || '#000000'
    const display = transform ? transform(raw, item) : raw
    return display ?? undefined
  }

  const display2raw = (display: unknown, item: FormItemObject) => {
    const transform = item.transform?.[1]
    return transform ? transform(display, item) : display
  }

  const parse0xToHex = (hexWith0x: string) => {
    if (!hexWith0x) {
      return ''
    }
    if (hexWith0x.includes('#')) return hexWith0x
    const hex = hexWith0x.slice(4) // 移除 Alpha 通道
    return `#${hex}`
  }

  const formatColorTo0x = () => {
    for (const i in currentPopupScene.value.content_i18n) {
      for (const j in currentPopupScene.value.content_i18n[i]) {
        if (j.includes('_color')) {
          if (!currentPopupScene.value.content_i18n[i][j].includes('0x')) {
            const color = raw2display(currentPopupScene.value.content_i18n[i], { path: j })
            const value = display2raw(color, j)
            const hex = value.replace('#', '')
            const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
            const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
            currentPopupScene.value.content_i18n[i][j] = result
          }
        }
      }
    }
  }

  const getCurrentGoodData = (id: any) => {
    const goodData = commodityList.value.find(i => i.id == id)
    const reData = {
      price: (goodData?.price / 100).toFixed(2),
      discount_price: (goodData?.discount_price / 100).toFixed(2),
      type: goodData?.product_type,
    }
    return reData
  }

  const getPopClass = () => {
    let classStr = ''
    if ([1006, 1014, 1009].includes(currentPopupScene.value.popup_type)) {
      classStr = 'absolute bottom-[20px] w-full'
    }
    return classStr
  }
  const getBtnTopTextShow = () => {
    const typeArr = [1007, 1012, 1008, 1009]
    return typeArr.includes(currentPopupScene.value.popup_type)
  }

  const getBtnTopText = () => {
    let result = ''
    console.log('getBtnTopText')
    if ([1007, 1012, 1008].includes(currentPopupScene.value.popup_type)) {
      result = currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1']
    }
    return result
  }

  const getBtnTopTextColor = () => {
    let color = ''
    if ([1007, 1012, 1008].includes(currentPopupScene.value.popup_type)) {
      color = currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']
      color = color?.includes('0x') ? hexToRgba(color) : color
    }
    return color
  }

  const previewPop = () => {
    console.log(currentPopupScene.value)
    console.log('currentPopupScene.value.popup_type', currentPopupScene.value.popup_type)

    // const exclude: string[] = []
    // if (currentPopupScene.value.target_type === 3) {
    //   exclude.push('target_link')
    // }

    // if (!validateAll({ exclude })) {
    //   return
    // }
    const currentPopLan = currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]
    const hide = openDialog({
      title: '弹窗预览',
      mainClass: 'pb-0 w-[350px] mx-auto',
      body: () => (
        <x-preview-pop class="relative">
          <x-preview-pop-box class="relative">
            <img class="mx-auto overflow-hidden rounded-lg" src={currentPopLan['img'].includes('https://') ? currentPopLan['img'] : 'https://static-v1.mydramawave.com/' + currentPopLan['img']} />
            <x-preview-pop-bot class={getPopClass()}>
              {getBtnTopTextShow() ? (
                <x-preview-pop-bot-text class="mx-auto my-[10px] flex w-4/5 items-center justify-center text-center" style={{ color: getBtnTopTextColor() }}>
                  {getBtnTopText()}
                </x-preview-pop-bot-text>
              ) : null}
              {
                [1014].includes(currentPopupScene.value.popup_type) ? (
                  <>
                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['title_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['title_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['title_color'] }}>
                      <span class="text-[20px] font-semibold">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['title']}</span>
                    </x-preview-pop-bot-btn>
                    <x-preview-pop-bot-btn class="mx-auto mb-[40px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color'] }}>
                      <span class="text-[18px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1']}</span>
                    </x-preview-pop-bot-btn>
                  </>
                ) : null
              }
              {
                [1006].includes(+currentPopupScene.value.popup_type) ? (
                  <>
                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color'] }}>
                      <span class="text-[18px] font-bold">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1']}</span>
                    </x-preview-pop-bot-btn>

                    {/* <x-preview-pop-bot-btn class="mx-auto mb-[10px] block w-4/5 text-center">
                      <span class="text-[30px] font-bold text-[#fd4b86]">98%OFF</span>
                    </x-preview-pop-bot-btn>

                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color'] }}>
                      <span class="text-[12px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2']}</span>
                    </x-preview-pop-bot-btn>
                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color'] }}>
                      <span class="text-[16px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3']}</span>
                    </x-preview-pop-bot-btn> */}
                  </>
                ) : null
              }
              {
                [1009].includes(currentPopupScene.value.popup_type) ? (
                  <>
                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1_color'] }}>
                      <span class="text-[18px] font-bold">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content1']}</span>
                    </x-preview-pop-bot-btn>

                    <x-preview-pop-bot-btn class="mx-auto mb-[10px] block w-4/5 text-center">
                      <span class="text-[30px] font-bold text-[#fd4b86]">98%OFF</span>
                    </x-preview-pop-bot-btn>

                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2_color'] }}>
                      <span class="text-[12px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content2']}</span>
                    </x-preview-pop-bot-btn>
                    <x-preview-pop-bot-btn class="mx-auto mb-[20px] block w-4/5 text-center" style={{ color: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3_color'] }}>
                      <span class="text-[16px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['content3']}</span>
                    </x-preview-pop-bot-btn>
                  </>
                ) : null
              }
              {![1013, 1014, 1015, 1005].includes(currentPopupScene.value.popup_type) ? (
                <x-preview-pop-bot-btn class="mx-auto flex h-[40px] w-4/5 items-center justify-center rounded text-center text-white" style={{ backgroundColor: (currentPopupScene.value.button_color || '0xFFFC2763')?.includes('0x') ? hexToRgba(currentPopupScene.value.button_color) : currentPopupScene.value.button_color }}>
                  <span class="text-[18px]">${getCurrentGoodData(currentPopupScene.value.target_link)['discount_price'] ? getCurrentGoodData(currentPopupScene.value.target_link)['discount_price'] : getCurrentGoodData(currentPopupScene.value.target_link)['price']}</span>{getCurrentGoodData(currentPopupScene.value.target_link)['type'] == 'membership' && getCurrentGoodData(currentPopupScene.value.target_link)['price'] != getCurrentGoodData(currentPopupScene.value.target_link)['discount_price'] ? <span class="ml-2 text-[14px] opacity-50" style="text-decoration: line-through;">${getCurrentGoodData(currentPopupScene.value.target_link)['price']}</span> : null}
                </x-preview-pop-bot-btn>
              ) : ([1014].includes(currentPopupScene.value.popup_type) ? (
                <x-preview-pop-bot-btn class="mx-auto mt-[10px] flex h-[40px] w-4/5 items-center justify-center rounded text-center text-white" style={{ backgroundColor: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color'] }}>
                  <span class="text-[18px]">{currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button']}</span>
                </x-preview-pop-bot-btn>
              ) : (
                ![1015, 1005].includes(currentPopupScene.value.popup_type) ? (
                  <x-preview-pop-bot-btn class="mx-auto mt-[10px] flex h-[40px] w-4/5 items-center justify-center rounded text-center text-white" style={{ backgroundColor: currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color']?.includes('0x') ? hexToRgba(currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color']) : currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['button_color'] }}>
                    <span class="text-[18px]">Spin</span>
                  </x-preview-pop-bot-btn>
                ) : null
              ))}
              {[1006, 1010, 1012, 1014].includes(currentPopupScene.value.popup_type) ? (
                <x-preview-pop-bot-text class="mx-auto mt-[10px] flex w-4/5 items-center justify-center text-center" style={{ color: currentPopupScene.value.limited_time_color?.includes('0x') ? hexToRgba(currentPopupScene.value.limited_time_color) : currentPopupScene.value.limited_time_color }}>
                  Limited time 05:09:33
                </x-preview-pop-bot-text>
              ) : null}
            </x-preview-pop-bot>
          </x-preview-pop-box>
        </x-preview-pop>

      ),
      customClass: '!w-[375px] bg-white-0  !shadow-none',
    })
  }

  const showImportDialog = () => {
    currentPopTargetLink.value = {}
    for (const i in currentPopupScene.value.content_i18n) {
      currentPopTargetLink.value[i] = currentPopupScene.value.content_i18n[i]['target_link']
    }
    console.log(currentPopTargetLink.value)
    const hide = openDialog({
      title: 'Deeplink',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-language-target-link class="relative">
          <x-language-target-link-box>
            {Object.keys(currentPopupScene.value.content_i18n).map((item, index) => {
              return (
                index == 0 ? null : (
                  <x-language-target-link-item class="mb-2 flex">
                    <x-language-target-link-item-left class="w-[100px]">
                      {getLangCode(item)}
                    </x-language-target-link-item-left>
                    <x-language-target-link-item-right class="flex-1">
                      <input placeholder="请输入deeplink" type="text" class="input input-sm input-bordered w-full" value={currentPopTargetLink.value[item]} onChange={e => currentPopTargetLink.value[item] = (e.target as HTMLInputElement).value || ''} />
                    </x-language-target-link-item-right>
                  </x-language-target-link-item>
                ))
            })}
          </x-language-target-link-box>
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              for (const i in currentPopTargetLink.value) {
                currentPopupScene.value.content_i18n[i]['target_link'] = currentPopTargetLink.value[i]
              }
              console.log(currentPopupScene.value)
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-language-target-link>

      ),
      customClass: '!w-800px',
    })
  }

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid grid-cols-1 gap-y-3"
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            console.log('path', path, value, typeof value)

            if (['start_time', 'end_time'].includes(path) && value === 'Invalid Date' || !value) {
              set(currentPopupScene.value || {}, path, undefined)
              return
            }

            set(currentPopupScene.value || {}, path, value)
          }}
          items={[
            [
              [
                'w-[45%] flex-wrap',
                [
                  requiredLabel('名称'),
                  'popup_name',
                  {
                    type: 'text',
                    maxlength: 100,
                    placeholder: '请输入标题，最大支持100字符',
                  },
                  {
                    class: 'w-full',
                  },
                ],
                { label: '开始时间（UTC-08:00北京时间）', path: 'start_time', input: { type: 'datetime', min: dayjs().format('YYYY-MM-DD HH:mm:ss') }, transform: transformTimestamp, class: 'w-full' },
                { label: '结束时间', path: 'end_time', input: { type: 'datetime', min: dayjs().format('YYYY-MM-DD HH:mm:ss') }, transform: transformTimestamp, class: 'w-full' },
                [
                  requiredLabel('排序'),
                  'priority',
                  {
                    type: 'number',
                    max: 999,
                    min: 1,
                    placeholder: '请输入优先级, 支持输入 1-999 的整数',
                  },
                  {
                    transform: transformInteger,
                    class: 'w-full',
                  },
                ],
                [
                  requiredLabel('生效应用'),
                  'app_id',
                  {
                    type: 'select',
                    autoInsertEmptyOption: false,
                    options: appOptions.value.filter(i => i.label.includes('Drama')),
                    // disabled: props.appIdSelectDisabled,
                  },
                  {
                    class: 'w-full',
                    transform: transformNumber,
                  },
                ],
                [
                  requiredLabel('下发语言'),
                  'language_version_codes',
                  {
                    type: 'checkbox-group',
                    // options: config.value.lang_items.map((i,idx) => ({ label: i, value: i })),
                    options: Object.keys(config.value.lang_items).map(key => ({
                      label: config.value.lang_items[key],
                      value: key,
                    })),
                    class: 'flex-wrap',
                  },
                  {
                    class: 'flex-wrap w-full',
                  },

                ],
                [
                  'w-full flex-wrap',

                  ['',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custom-lan-box>
                          <x-lan-item class="mb-2 flex gap-1">
                            批量上传
                            <x-lan-item-upload class="w-4/5 border px-2 py-1">
                              <x-up-img-box class="mb-3 flex flex-wrap gap-2">
                                {Object.keys(currentPopupScene.value.content_i18n).map((item: any, key: any) => {
                                  return (
                                    currentPopupScene.value.content_i18n[item] && currentPopupScene.value.content_i18n[item]['img'] && (
                                      <x-up-img-item-box class="relative w-[100px] text-center">
                                        <x-close-btn class="absolute right-1 top-1 cursor-pointer rounded bg-[rgba(0,0,0,0.5)] p-1 text-[12px] text-white"
                                          onClick={() => {
                                            const hideDeleteDialog = openDialog({
                                              title: '删除',
                                              mainClass: 'pb-0 px-5',
                                              body: (
                                                <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                                                  <x-delete-episode-body>确认删除吗？</x-delete-episode-body>
                                                  <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                                                    <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                                                    <button class="btn btn-primary btn-sm" onClick={() => {
                                                      currentPopupScene.value.content_i18n[item]['img'] = ''
                                                      hideDeleteDialog()
                                                      showAlert('删除成功')
                                                    }}
                                                    >确定
                                                    </button>
                                                  </x-delete-episode-footer>
                                                </x-delete-episode-confirm-dialog>
                                              ),
                                            })
                                          }}
                                        >删除</x-close-btn>
                                        <x-up-img-item class="block h-[150px]">
                                          <img class="size-full" src={currentPopupScene.value.content_i18n[item]['img'].includes('https://') ? currentPopupScene.value.content_i18n[item]['img'] : 'https://static-v1.mydramawave.com/' + currentPopupScene.value.content_i18n[item]['img']} />
                                        </x-up-img-item>
                                        {currentPopupScene.value.content_i18n[item] && currentPopupScene.value.content_i18n[item]['imgName']
                                        && (
                                          <x-preview-btn class="relative block cursor-pointer text-center text-[14px]">
                                            {currentPopupScene.value.content_i18n[item]['imgName']}
                                          </x-preview-btn>
                                        )}
                                        {currentPopupScene.value.content_i18n[item] && currentPopupScene.value.content_i18n[item]['img']
                                        && (
                                          <x-preview-btn class="relative block h-[15px] cursor-pointer text-center text-[14px]">
                                            {/* <Image src={currentPopupScene.value.content_i18n[item]['img'].includes('https://') ? currentPopupScene.value.content_i18n[item]['img'] : 'https://static-v1.mydramawave.com/' + currentPopupScene.value.content_i18n[item]['img']} /> */}
                                            <el-image class="!absolute left-0 top-0 size-full" src={currentPopupScene.value.content_i18n[item]['img'].includes('https://') ? currentPopupScene.value.content_i18n[item]['img'] : 'https://static-v1.mydramawave.com/' + currentPopupScene.value.content_i18n[item]['img']}
                                              zoom-rate={1.2} max-scale={7} min-scale={0.2} preview-src-list={[currentPopupScene.value.content_i18n[item]['img'].includes('https://') ? currentPopupScene.value.content_i18n[item]['img'] : 'https://static-v1.mydramawave.com/' + currentPopupScene.value.content_i18n[item]['img']]} initial-index={4}
                                              fit="cover" />

                                            <x-pre-inner class="pointer-events-none absolute left-0 top-0 block size-full bg-white">预览</x-pre-inner>
                                          </x-preview-btn>
                                        )}
                                        {/* <up-img-tip class="mt-[5px]">预览</up-img-tip> */}
                                      </x-up-img-item-box>
                                    )
                                  )
                                })}

                              </x-up-img-box>
                              <x-btn-box class="flex justify-center">
                                <Uploader
                                  accept="png,jpg,jpeg"
                                  ossKeyType="resource"
                                  isImage={true}
                                  maxsize={1024 * 1024 * 1}
                                  multiple
                                  uploadUrl="/popup/upload/image"
                                  showFileList={false}
                                  beforeUpload={({ files }) => {
                                    files.forEach(file => tempFiles.value.push(file))
                                    console.log(tempFiles.value)
                                  }}
                                  onUploadSuccess={file => {
                                    console.log(file)
                                    const fileName = file?.file?.name.split('.')[0]
                                    let lan = ''
                                    for (const i in imgLanOptions) {
                                      const lanArr = imgLanOptions[i].split(',')
                                      if (lanArr.includes(fileName)) {
                                        lan = i
                                      }
                                    }
                                    console.log(lan)
                                    if (lan != '' && currentPopupScene.value.content_i18n[lan]) {
                                      const d = file
                                      currentPopupScene.value.content_i18n[lan]['img'] = d.temp_path.includes('https://') ? d.temp_path : 'popup/image/' + d.temp_path
                                      currentPopupScene.value.content_i18n[lan]['imgName'] = file.file.name
                                    }
                                  }}
                                >
                                  <x-uploader-wrapper class="flex h-[60px] w-[200px] cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
                                    批量上传
                                  </x-uploader-wrapper>
                                </Uploader>
                              </x-btn-box>
                            </x-lan-item-upload>
                          </x-lan-item>
                          <x-tips class="pl-16 text-sm">不包含关闭按钮的限制尺寸：885*1290px</x-tips>
                        </x-custom-lan-box>
                      ),
                    },
                    {
                      class: mc('flex-wrap w-full',
                        ((currentPopupScene.value.target_type === 2 && [1005, 1011].includes(currentPopupScene.value.popup_type))
                        || (currentPopupScene.value.target_type === 1 && [1010, 1007, 1006, 1008, 1012, 1009, 1013].includes(currentPopupScene.value.popup_type))
                        || (currentPopupScene.value.target_type === 3)
                        || (currentPopupScene.value.target_type === 4 && [1015].includes(currentPopupScene.value.popup_type))
                        ) ? '' : 'hidden'),
                    },
                  ],
                  ['',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custom-lan-box>
                          {currentPopupScene.value.language_version_codes && currentPopupScene.value.language_version_codes.map(item => {
                            return (
                              <x-lan-item class="mb-2 flex gap-1">
                                <x-lan-item-left class="w-[100px]">
                                  {getLangCode(item || '')}：
                                </x-lan-item-left>
                                <x-lan-item-right class="">
                                  <input type="text" placeholder="标题" class="input input-sm input-bordered mb-1 w-full" value={currentPopupScene.value.content_i18n[item]['title']} onChange={e => currentPopupScene.value.content_i18n[item]['title'] = (e.target as HTMLInputElement).value || ''} />
                                  <input type="text" placeholder="内容1" class="input input-sm input-bordered mb-1 w-full" value={currentPopupScene.value.content_i18n[item]['content1']} onChange={e => currentPopupScene.value.content_i18n[item]['content1'] = (e.target as HTMLInputElement).value || ''} />
                                  <input type="text" placeholder="内容2" class="input input-sm input-bordered mb-1 w-full" value={currentPopupScene.value.content_i18n[item]['content2']} onChange={e => currentPopupScene.value.content_i18n[item]['content2'] = (e.target as HTMLInputElement).value || ''} />
                                </x-lan-item-right>
                              </x-lan-item>
                            )
                          })}
                        </x-custom-lan-box>
                      ),
                    },
                    {
                      class: mc('flex-wrap w-full', currentPopupScene.value.target_type === 2 && currentPopupScene.value.popup_type == 1011 ? '' : 'hidden'),
                    },
                  ],

                  ['',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custom-lan-box>
                          {currentPopupScene.value.language_version_codes && currentPopupScene.value.language_version_codes.map(item => {
                            return (
                              <x-lan-item class="mb-2 flex gap-1">
                                <x-lan-item-left class="w-[100px]">
                                  {getLangCode(item || '')}：
                                </x-lan-item-left>
                                <x-lan-item-right class="">
                                  <textarea placeholder="内容1" class="textarea textarea-bordered mb-1 w-full" value={currentPopupScene.value.content_i18n[item]['content1']} onInput={e => {
                                    currentPopupScene.value.content_i18n[item]['content1'] = (e.target as HTMLInputElement).value || ''
                                  }} />
                                  <lan-item-bot class="flex items-center">
                                    <span>内容1 颜色</span>
                                    <input type="color"
                                      value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'content1_color' }))}
                                      onInput={e => {
                                        const value = display2raw((e.target as HTMLSelectElement).value, item)
                                        const hex = value.replace('#', '')
                                        const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                        const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                        currentPopupScene.value.content_i18n[item]['content1_color'] = result
                                      }}
                                      placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                    <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[120px]" value={currentPopupScene.value.content_i18n[item]['content1_color']} onChange={e => currentPopupScene.value.content_i18n[item]['content1_color'] = (e.target as HTMLInputElement).value || ''} />

                                  </lan-item-bot>
                                </x-lan-item-right>
                              </x-lan-item>
                            )
                          })}
                        </x-custom-lan-box>
                      ),
                    },
                    {
                      class: mc('flex-wrap w-full', currentPopupScene.value.target_type === 1 && [1007, 1006, 1008, 1012].includes(currentPopupScene.value.popup_type) ? '' : 'hidden'),
                    },
                  ],

                  ['',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custom-lan-box>
                          {currentPopupScene.value.language_version_codes && currentPopupScene.value.language_version_codes.map(item => {
                            return (
                              <x-lan-item class="mb-2 flex gap-1">
                                <x-lan-item-left class="w-[100px]">
                                  {getLangCode(item || '')}：
                                </x-lan-item-left>
                                {currentPopupScene.value.target_type === 1 ? (
                                  <x-lan-item-right class="block w-[310px]">
                                    <div class="flex items-center gap-1">
                                      <input type="text" placeholder="内容1" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['content1']} onChange={e => currentPopupScene.value.content_i18n[item]['content1'] = (e.target as HTMLInputElement).value || ''} />
                                      <input type="color"
                                        value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'content1_color' }))}
                                        onInput={e => {
                                          const value = display2raw((e.target as HTMLSelectElement).value, item)
                                          const hex = value.replace('#', '')
                                          const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                          const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                          currentPopupScene.value.content_i18n[item]['content1_color'] = result
                                        }}
                                        placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                      <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['content1_color']} onInput={e => currentPopupScene.value.content_i18n[item]['content1_color'] = (e.target as HTMLInputElement).value || ''} />
                                    </div>
                                    <div class="flex items-center gap-1">
                                      <input type="text" placeholder="内容2" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['content2']} onChange={e => currentPopupScene.value.content_i18n[item]['content2'] = (e.target as HTMLInputElement).value || ''} />
                                      <input type="color"
                                        value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'content2_color' }))}
                                        onInput={e => {
                                          const value = display2raw((e.target as HTMLSelectElement).value, item)
                                          const hex = value.replace('#', '')
                                          const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                          const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                          currentPopupScene.value.content_i18n[item]['content2_color'] = result
                                        }}
                                        placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                      <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['content2_color']} onInput={e => currentPopupScene.value.content_i18n[item]['content2_color'] = (e.target as HTMLInputElement).value || ''} />
                                    </div>
                                    <div class="flex items-center gap-1">
                                      <input type="text" placeholder="内容3" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['content3']} onChange={e => currentPopupScene.value.content_i18n[item]['content3'] = (e.target as HTMLInputElement).value || ''} />
                                      <input type="color"
                                        value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'content3_color' }))}
                                        onInput={e => {
                                          const value = display2raw((e.target as HTMLSelectElement).value, item)
                                          const hex = value.replace('#', '')
                                          const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                          const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                          currentPopupScene.value.content_i18n[item]['content3_color'] = result
                                        }}
                                        placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                      <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['content3_color']} onInput={e => currentPopupScene.value.content_i18n[item]['content3_color'] = (e.target as HTMLInputElement).value || ''} />
                                    </div>
                                  </x-lan-item-right>
                                )

                                  : (
                                      <x-lan-item-right class="block w-[310px]">
                                        <div class="flex items-center gap-1">
                                          <input type="text" placeholder="标题" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['title']} onChange={e => currentPopupScene.value.content_i18n[item]['title'] = (e.target as HTMLInputElement).value || ''} />
                                          <input type="color"
                                            value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'title_color' }))}
                                            onInput={e => {
                                              const value = display2raw((e.target as HTMLSelectElement).value, item)
                                              const hex = value.replace('#', '')
                                              const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                              const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                              currentPopupScene.value.content_i18n[item]['title_color'] = result
                                            }}
                                            placeholder="标题颜色" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                          <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['title_color']} onInput={e => currentPopupScene.value.content_i18n[item]['title_color'] = (e.target as HTMLInputElement).value || ''} />
                                        </div>
                                        <div class="text-xs text-gray-500 mb-1">标题颜色如需配置，需控制版本&gt;1.3.60</div>
                                        <div class="flex items-center gap-1">
                                          <input type="text" placeholder="使用说明" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['content1']} onChange={e => currentPopupScene.value.content_i18n[item]['content1'] = (e.target as HTMLInputElement).value || ''} />
                                          <input type="color"
                                            value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'content1_color' }))}
                                            onInput={e => {
                                              const value = display2raw((e.target as HTMLSelectElement).value, item)
                                              const hex = value.replace('#', '')
                                              const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                              const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                              currentPopupScene.value.content_i18n[item]['content1_color'] = result
                                            }}
                                            placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                          <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['content1_color']} onInput={e => currentPopupScene.value.content_i18n[item]['content1_color'] = (e.target as HTMLInputElement).value || ''} />
                                        </div>
                                        <div class="flex items-center gap-1">
                                          <input type="text" placeholder="按钮文案" class="input input-sm input-bordered mb-1 " value={currentPopupScene.value.content_i18n[item]['button']} onChange={e => currentPopupScene.value.content_i18n[item]['button'] = (e.target as HTMLInputElement).value || ''} />
                                          {/* <input type="color"

                                            value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'button_color' }))}
                                            onChange={e => {
                                              const value = display2raw((e.target as HTMLSelectElement).value, item)
                                              const hex = value.replace('#', '')
                                              const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                              const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                              currentPopupScene.value.content_i18n[item]['button_color'] = result
                                            }}
                                            placeholder="内容1" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                          <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['button_color']} onInput={e => currentPopupScene.value.content_i18n[item]['button_color'] = (e.target as HTMLInputElement).value || ''} /> */}
                                        </div>
                                      </x-lan-item-right>
                                    )}
                              </x-lan-item>
                            )
                          })}
                        </x-custom-lan-box>
                      ),
                    },
                    {
                      class: mc('flex-wrap w-full', (currentPopupScene.value.target_type === 1 && [1009].includes(currentPopupScene.value.popup_type)) || (currentPopupScene.value.target_type === 3) ? '' : 'hidden'),
                    },
                  ],

                  ['',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custom-lan-box>
                          {currentPopupScene.value.language_version_codes && currentPopupScene.value.language_version_codes.map(item => {
                            return (
                              <x-lan-item class="mb-2 flex gap-1">
                                <x-lan-item-left class="w-[100px]">
                                  {getLangCode(item || '')}：
                                </x-lan-item-left>
                                <x-lan-item-right class="block w-[310px]">
                                  <input type="text" placeholder="拓展配置（JSON格式）" class="input input-sm input-bordered mb-1 w-full" value={currentPopupScene.value.content_i18n[item]['extra_json']} onChange={e => currentPopupScene.value.content_i18n[item]['extra_json'] = (e.target as HTMLInputElement).value || ''} />
                                  <x-lan-item-bot class="flex items-center">
                                    <span>按钮颜色</span>
                                    <input type="color"
                                      value={parse0xToHex(raw2display(currentPopupScene.value.content_i18n[item], { path: 'button_color' }))}
                                      onChange={e => {
                                        const value = display2raw((e.target as HTMLSelectElement).value, item)
                                        const hex = value.replace('#', '')
                                        const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                                        const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式
                                        currentPopupScene.value.content_i18n[item]['button_color'] = result
                                      }}
                                      placeholder="按钮颜色" class="input input-sm input-bordered mb-1 w-[50px] px-0" />
                                    <input type="text" placeholder="输入颜色" class="input input-sm input-bordered mb-1 w-[80px]" value={currentPopupScene.value.content_i18n[item]['button_color']} onInput={e => currentPopupScene.value.content_i18n[item]['button_color'] = (e.target as HTMLInputElement).value || ''} />
                                  </x-lan-item-bot>
                                </x-lan-item-right>
                              </x-lan-item>
                            )
                          })}
                        </x-custom-lan-box>
                      ),
                    },
                    {
                      class: mc('flex-wrap w-full', currentPopupScene.value.target_type === 1 && [1013].includes(currentPopupScene.value.popup_type) ? '' : 'hidden'),
                    },
                  ],
                  [
                    [
                      '',
                      '',
                      {
                        type: 'custom',
                        render: () => (
                          <x-upload-cover-tip class=" flex flex-col gap-y-1 text-sm text-gray-600">
                            <x-tip>
                              注意：
                            </x-tip>
                            <x-tip>
                              1、选择下发语言弹窗才会下发，至少选择一个，可多选语言
                            </x-tip>
                            <x-tip>
                              2、若需下发文案，请填写对应语言，否则对应语言弹窗内容为空
                            </x-tip>
                            <x-tip>
                              3、一天写语言文案的，可不选择不下发（不勾选下发语言）
                            </x-tip>
                          </x-upload-cover-tip>
                        ),
                      },
                      {
                        class: 'w-full',
                      },
                    ],
                  ],

                ],
              ],
              [
                'w-[45%] block',
                [
                  requiredLabel('跳转类型'),
                  'target_type',
                  {
                    type: 'radio',
                    options: Object.keys(config.value.popup_jump_target_items).map(key => ({
                      label: config.value.popup_jump_target_items[key],
                      value: Number(key),
                    })),
                  },
                  {
                    transform: transformInteger,
                    class: 'w-full',
                  },
                ],
                [
                  requiredLabel('样式'),
                  'popup_type',
                  {
                    type: 'radio',
                    options: Object.keys(getConfigByType(1) as {}).map(key => ({
                      label: getConfigByType(1)?.[+key] ?? '',
                      value: +key,
                      disabled: +key === 1007 || +key === 1013 || +key === 1009,
                    })),
                    class: 'flex-wrap',
                  },
                  {
                    transform: transformInteger,
                    class: mc('col-span-1 !flex-wrap wrap h-[130px]', currentPopupScene.value.target_type !== 1 ? 'hidden' : ''),

                  },
                ],
                [
                  requiredLabel('样式/弹窗模板'),
                  'popup_type',
                  {
                    type: 'radio',
                    options: Object.keys(getConfigByType(2) as {}).map(key => ({
                      label: getConfigByType(2)?.[+key] ?? '',
                      value: +key,
                      disabled: +key === 1011,
                    })),
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type !== 2 ? 'hidden' : ''),

                  },
                ],
                [
                  requiredLabel('样式/弹窗模板'),
                  'popup_type',
                  {
                    type: 'radio',
                    options: Object.keys(getConfigByType(3) as {}).map(key => ({
                      label: getConfigByType(3)?.[+key] ?? '',
                      value: +key,
                    })),
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type !== 3 ? 'hidden' : ''),

                  },
                ],
                [
                  requiredLabel('样式/弹窗模板'),
                  'popup_type',
                  {
                    type: 'radio',
                    options: Object.keys(getConfigByType(4) as {}).map(key => ({
                      label: getConfigByType(4)?.[+key] ?? '',
                      value: +key,
                    })),
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type !== 4 ? 'hidden' : ''),

                  },
                ],

                [
                  requiredLabel('兑换券ID'),
                  'coupon_info.coupon_id',
                  {
                    type: 'text',
                    placeholder: '请从券库里复制一个兑换券ID',
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type === 3 ? '' : 'hidden'),
                  },
                ],
                [
                  requiredLabel('兑换剧'),
                  'coupon_info.series_key',
                  {
                    type: 'text',
                    placeholder: '请从剧库里复制一个短剧ID',
                  },
                  {
                    class: mc('w-full', currentPopupScene.value.target_type === 3 ? '' : 'hidden'),
                  },
                ],
                [
                  requiredLabel('发券数量'),
                  'coupon_info.coupon_num',
                  {
                    type: 'text',
                    placeholder: '0-10，用户通过该弹窗一次性可以领取的数量',
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type === 3 ? '' : 'hidden'),
                  },
                ],

                [
                  ((currentPopupScene.value.target_type === 2 && currentPopupScene.value.language_version_codes.length > 1) || currentPopupScene.value.target_type === 4 ? [
                    requiredLabel('跳转链接'),
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custon-button>
                          { Object.keys(currentPopupScene.value.content_i18n)[0]
                            ? <input placeholder="deeplink链接" type="text" class="input input-sm input-bordered w-[200px]" value={currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['target_link']} onChange={e => currentPopupScene.value.content_i18n[Object.keys(currentPopupScene.value.content_i18n)[0]]['target_link'] = (e.target as HTMLInputElement).value || ''} />
                            : <span class="text-red-500">请选择语言</span>}
                        </x-custon-button>
                      ),
                    }, {
                      class: mc('w-full', (currentPopupScene.value.target_type === 2 && currentPopupScene.value.language_version_codes.length > 1) || currentPopupScene.value.target_type === 4 ? '' : 'hidden'),
                    },
                  ] : [
                    requiredLabel('跳转链接'),
                    'target_link',
                    {
                      type: 'text',
                      placeholder: 'deeplink链接',
                    },
                    {
                      class: mc('w-full', currentPopupScene.value.target_type !== 2 ? 'hidden' : ''),
                    },
                  ]),
                  [
                    '',
                    '',
                    {
                      type: 'custom',
                      render: () => (
                        <x-custon-button>
                          <Button class="btn btn-primary btn-sm" onClick={() => showImportDialog()}>多语言</Button>
                        </x-custon-button>
                      ),
                    }, {
                      class: mc('w-full pt-6', (currentPopupScene.value.target_type === 2 && currentPopupScene.value.language_version_codes.length > 1) || currentPopupScene.value.target_type === 4 ? '' : 'hidden'),
                    },
                  ],
                ],
                [
                  requiredLabel('跳转方式'),
                  'jump_method',
                  {
                    type: 'radio',
                    options: [
                      {
                        label: '手动跳转',
                        value: 1,
                      },
                    ],
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', [1, 3, 4].includes(currentPopupScene.value.target_type) ? '' : 'hidden'),
                  },
                ],
                [
                  requiredLabel('跳转方式'),
                  'jump_method',
                  {
                    type: 'radio',
                    options: [
                      {
                        label: '手动跳转',
                        value: 1,
                      },
                      {
                        label: '自动跳转',
                        value: 2,
                      },
                      {
                        label: '外部链接跳转',
                        value: 3,
                      },
                    ],
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type === 2 ? '' : 'hidden'),
                  },
                ],
                [
                  requiredLabel('商品ID'),
                  'target_link',
                  {
                    type: 'multi-select',
                    search: true,
                    popoverWrapperClass: 'z-popover-in-dialog',
                    options: commodityList.value.map((n, index) => {
                      return { value: '' + n.id, label: `${n.id}/${n.title}` }
                    }),
                    maxlength: 1,
                  },
                  {
                    transform: [
                      (raw?: unknown) => raw ? [raw] : [],
                      (display: string[]): string => display[0] ? '' + (display[0]) : '',
                    ] as const,
                    class: mc('w-full', currentPopupScene.value.target_type === 1 ? '' : 'hidden'),
                  },
                ],
                [
                  '自动跳转倒计时',
                  'jump_interval',
                  {
                    type: 'number',
                    placeholder: '支持 0-300整数输入',
                    suffix: 's后自动跳转',
                    class: 'w-[80%] text-[14px] whitespace-nowrap',
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type === 2 && currentPopupScene.value.jump_method === 2 ? '' : 'hidden'),
                  },
                ],
                [[
                  '倒计时时间',
                  'limited_time',
                  {
                    type: 'number',
                    placeholder: '支持输入整数0-100',
                    suffix: 'h',
                    max: 100,
                    min: 0,
                  },
                  {
                    class: mc('w-[190px]', currentPopupScene.value.target_type === 4 || ![1014, 1006, 1012, 1010].includes(currentPopupScene.value.popup_type) ? 'hidden' : ''),
                    transform: transformInteger,
                  },
                ],
                [
                  '倒计时时间颜色',
                  'limited_time_color',
                  {
                    type: 'color',
                    placeholder: '点击色块选择色值',
                    hasAlpha: true,
                    class: 'w-[100px]',
                  },
                  {
                    class: mc('flex-wrap w-[220px]', currentPopupScene.value.target_type === 4 || ![1014, 1006, 1012, 1010].includes(currentPopupScene.value.popup_type) ? 'hidden' : ''),
                  },
                ],
                [
                  '按钮颜色',
                  'button_color',
                  {
                    type: 'color',
                    placeholder: '点击色块选择色值',
                    hasAlpha: true,
                    class: 'w-[100px]',
                  },
                  {
                    class: mc('flex-wrap  w-[220px]', currentPopupScene.value.target_type === 1 && [1006].includes(currentPopupScene.value.popup_type) ? '' : 'hidden'),

                  },
                ]],
                [[
                  requiredLabel('展示次数'),
                  'max',
                  {
                    type: 'number',
                    placeholder: '请输入展示次数',
                    min: -1,
                  },
                  {
                    transform: transformNumber,
                    class: mc('w-full w-[250px]', currentPopupScene.value.target_type === 4 ? 'hidden' : ''),
                  },
                ],

                [
                  requiredLabel('间隔时间'),
                  'interval',
                  {
                    type: 'number',
                    placeholder: '请输入间隔时间',
                    class: 'w-[150px]',
                  },
                  {
                    transform: transformNumber,
                    class: mc('w-full w-[250px]', currentPopupScene.value.target_type === 4 ? 'hidden' : ''),
                  },
                ]],
                [
                  requiredLabel('单位'),
                  'interval_unit',
                  {
                    type: 'radio',
                    options: Object.keys(config.value.interval_unit_items).map(key => ({
                      label: config.value.interval_unit_items[+key],
                      value: +key,
                    })),
                  },
                  {
                    transform: transformInteger,
                    class: mc('w-full', currentPopupScene.value.target_type === 4 ? 'hidden' : ''),
                  },
                ],
                // [
                //   '曝光屏蔽',
                //   '',
                //   {
                //     type: 'custom',
                //     render: () => (
                //       <custom-lan-box>

                //         <lan-item class="flex mb-2 gap-1">
                //           <lan-item-right class="flex w-full items-center gap-2">
                //             <span>曝光</span>
                //             <input type="text" placeholder='' class="input input-sm mb-1 input-bordered w-[100px]" value={currentPopupScene.value.language_version_codes} onChange={e => onUpdateThirdKey(e, row)} />
                //             <span>次未点击，屏蔽</span>
                //             <input type="text" placeholder='' class="input input-sm mb-1 input-bordered w-[100px]" value={currentPopupScene.value.language_version_codes} onChange={e => onUpdateThirdKey(e, row)} />
                //             <span>天</span>
                //           </lan-item-right>
                //         </lan-item>
                //       </custom-lan-box>
                //     ),
                //   },
                //   {
                //     class: mc('w-full', currentPopupScene.value.target_type == 3 ? 'hidden' : ''),
                //   },
                // ],
                // [
                //   '备注',
                //   'popup_name',
                //   {
                //     type: 'textarea',
                //     placeholder: '',
                //   },
                //   {
                //     class: mc('w-full', currentPopupScene.value.target_type !== 3 ? 'hidden' : ''),
                //   },
                // ],
              ],
            ],

          ]}
          data={currentPopupScene.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        {![1011].includes(currentPopupScene.value.popup_type) && <Button class="btn btn-primary btn-sm" onClick={previewPop}>预览</Button>}
        <Button class="btn  btn-sm" onClick={closeEditPopupSceneModal.value}>取消</Button>
        <Button class={mc('btn btn-primary btn-sm')} disabled={isUpdating.value} onClick={() => {
          currentPopupScene.value = {
            ...currentPopupScene.value,
            position_id: Number(route.params.id),
          }
          if (currentPopupScene.value.target_type === 2 && currentPopupScene.value.popup_type == 1005) {
            formatMutiUpImg()
            console.log(currentPopupScene.value)
          }
          if (!currentPopupScene.value.id) {
            currentPopupScene.value.id = undefined
          }
          for (const i in currentPopupScene.value.content_i18n) {
            if (!currentPopupScene.value.content_i18n[i]['img']) {
              showFailToast('请上传所有图片')
              return false
            }
          }

          const next = () => void onEdit()

          try {
            const exclude: string[] = []
            if (currentPopupScene.value.target_type === 3) {
              exclude.push('target_link')
            }
            if (currentPopupScene.value.target_type === 4) {
              exclude.push('max')
              exclude.push('target_link')
            }
            if ((currentPopupScene.value.target_type === 2 && Object.keys(currentPopupScene.value.content_i18n).length > 1) || currentPopupScene.value.target_type === 4) {
              exclude.push('target_link')
              for (const i in currentPopupScene.value.content_i18n) {
                if (!currentPopupScene.value.content_i18n[i]['target_link']) {
                  showFailToast('请填写所有语言的跳转链接')
                  return false
                }
              }
            }

            formatColorTo0x()
            if (!validateAll({ exclude })) {
              console.log('validateAll', error)
              return
            }
            next()
          } catch (error) {
            console.log('error', error)
          }
        }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
