import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import dayjs from 'dayjs'
import { cloneDeep, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, watch } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useAppAndLangOptions } from '../options/use-app-options'
import { ActivityPopupForm } from './activity-popup-form'
import { ActivityPopupFormNew } from './activity-popup-new-form'
import { useActivityPopup } from './use-activity-popup'
import { usePopupConditionStore } from './use-popup-condition-store'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { apiEditActivityPopup } from './activity-popup-api'
import { showAlert } from '@skynet/ui'
import { ElSelect, ElOption } from 'element-plus'

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] h-[80vh] overflow-hidden'

type ActivityPopupOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.ActivityPopup[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.ActivityPopup) => void
    remove: (id: M.ActivityPopup) => void
  }
}

export const ActivityPopup = createComponent<ActivityPopupOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.ActivityPopup) => { },
    remove: (item: M.ActivityPopup) => { },
  },
}, (props, { emit }) => {
  const router = useRouter()
  const { onSearch, total, searchForm, onReset, currentActivityPopup, InitActivityPopupOption, closeEditActivityPopupModal, list,
    onPageChange, onPageSizeChange, loading, config, onUpdate, getConfig, onEdit } = useActivityPopup()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: () => {
      void getConfig()
      // void getCommodityList()
      onSearch()
    },
  })

  // watch(appOptions, () => {
  //   if (appOptions.value.length > 0 && !searchForm.value.app_id) {
  //     searchForm.value.app_id = appOptions.value[0].value
  //   }
  // })

  // watch(() => searchForm.value.app_id, id => {
  //   searchForm.value.language_version_code = id ? (languageOptions.value[0]?.value) : ''
  // })

  const Form = CreateForm<M.ActivityPopupSearchProps>()
  const Table = CreateTableOld<M.ActivityPopup>()

  const { showPopupCondition } = usePopupConditionStore()

  const priorityChange = (row: any) => {
    console.log(row)
    currentActivityPopup.value = cloneDeep(row)
    void onEdit()
  }

  const columns: TableColumnOld<M.ActivityPositionPopup>[] = [
    ['运营位ID', 'id', { class: 'w-[70px]' }],
    ['运营位名称', 'name', { class: 'w-[200px]' }],
    ['运营位位置', row => JSON.parse(JSON.stringify(config.value.popup_scene_items))[row?.scene_type] || row?.scene_type, { class: 'w-[260px]' }],
    ['弹窗数量', row => row.popup_ids?.length || '-空-'],
    ['弹窗ID', row => row.popup_ids?.join(',') || '-空-', { class: 'w-[200px]' }],
    ['用户分层', row => (row.is_abtest === 1 ? '' : (
      <x-links class="flex flex-wrap gap-2">
        {
          row?.strategy_layer_name_items && row?.strategy_layer_name_items?.length > 0 ? row.strategy_layer_name_items?.map(i =>
            <a target="_blank" class="link link-primary" href={`/user-strategy-layer/edit?id=${i.id}`}>{i.name}</a>,
          ) : '-空-'
        }
      </x-links>
    )) || '-空-', { class: 'w-[200px]' }],
    ['优先级', row => (
      <div class="flex flex-nowrap">
        <input type="text" min="0" max="1000" onChange={e => {
          row.priority = Number((e.target as HTMLInputElement).value || '')
          priorityChange(row)
        }} value={row.priority} class="" />
      </div>
    ),
    { class: 'w-[140px]' }],
    ['是否AB实验', row => !row.is_abtest ? '否' : '是', { class: 'w-[160px]' }],
    ['状态', row => ['失效', '生效'][row?.status || 0], { class: 'w-[60px]' }],
    ['创建时间时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
    ['创建人', 'updated_operator_name', { class: 'w-[200px]' }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
    ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],

    // ['弹窗类型', row => config.value.popup_type_items[row?.popup_type || 1], { class: 'w-[160px]' }],
    // ['应用/版本', row => appOptions.value.find(i => i.value === row?.app_id)?.label + ' ' + (row?.app_version || '空') + ['', '+'][row.version_compare || 0], { class: 'w-[260px]' }],
    // ['下发语言', row => row.language_version_codes?.join(','), { class: 'w-[160px]' }],
    // ['跳转类型', row => config.value.popup_jump_target_items[row.target_type || 0], { class: 'w-[160px]' }],
    // ['是否限时', row => !row.limited_time ? '不限时' : row.limited_time + 'h', { class: 'w-[160px]' }],
    // ['有效期', row => (<><DateTime value={(row?.start_time as number || 0) * 1000} /> ~ <DateTime value={(row?.end_time as number || 0) * 1000} /></>), { class: 'w-[300px]' }],

    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap gap-1">

          <Button class={'btn btn-' + (row?.status === 1 ? 'warning' : 'accent') + ' btn-sm text-[#fff]'}
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: row?.status === 1 ? '失效' : '生效',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认{row?.status === 1 ? '失效' : '生效'}【{row.name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopup({
                          ...row,
                          status: row?.status == 1 ? 0 : 1,
                        }).then(() => {
                          void other.search(other.page.value)
                          onSearch()
                          showAlert('操作成功')
                          hideDeleteDialog()
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}>
            {row?.status === 1 ? '失效' : '生效'}
          </Button>
          {/* <Button class="btn btn-link btn-sm" onClick={() => { showPopupCondition(row.popup_id!, row.popup_name!) }}>
            弹窗条件
          </Button> */}
          <Button class="btn btn-info btn-sm text-white" onClick={() => { router.push('/operation-position-item/' + row.id) }}>
            添加元素
          </Button>
          <Button class="btn btn-info btn-sm text-white" onClick={() => {
            currentActivityPopup.value = cloneDeep(row)
            closeEditActivityPopupModal.value = openDialog({
              title: '编辑运营位',
              body: () => <ActivityPopupFormNew />,
              mainClass: dialogMainClass,
              customClass: '!w-[800px] overflow-hidden',
            })
          }}
          >编辑
          </Button>
          <Button class="btn btn-error btn-sm text-white"
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: '删除',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认删除【{row.name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopup({
                          ...row,
                          deleted: 1,
                        }).then(() => {
                          showAlert('删除成功')
                          onSearch()
                          hideDeleteDialog()
                          void other.search(other.page.value)
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}
          >
            删除
          </Button>
          <Button class="btn btn-primary btn-sm text-white"
            onClick={() => {
              const hideDeleteDialog = openDialog({
                title: '复制',
                mainClass: 'pb-0 px-5',
                body: (
                  <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-delete-episode-body>确认复制【{row.name}】吗？</x-delete-episode-body>
                    <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" onClick={() => {
                        void apiEditActivityPopup({
                          ...row,
                          id: undefined,
                        }).then(() => {
                          showAlert('复制成功')
                          onSearch()
                          hideDeleteDialog()
                          void other.search(other.page.value)
                        }).catch((error: any) => {
                          showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                        })
                      }}
                      >确定
                      </button>
                    </x-delete-episode-footer>
                  </x-delete-episode-confirm-dialog>
                ),
              })
            }}>
            复制
          </Button>
        </div>
      ),
      { class: mc('w-[360px]', props.hasActions ? '' : 'hidden') },
    ],
  ]

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => props.hasNav
          ? (
              <ul>
                <li><RouterLink to="/banner">运营位管理</RouterLink></li>
              </ul>
            )
          : null,
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              set(searchForm.value, path, value)
            }}
            items={[

              [
                '运营位名称',
                'name',
                {
                  type: 'text',
                },
              ],
              [
                '运营位ID',
                'id',
                {
                  type: 'text',
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '分层名称',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
              [
                '状态',
                'status',
                {
                  type: 'select',
                  options: [
                    { label: '生效', value: 1 },
                    { label: '失效', value: 0 },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],

              ['排序', 'sortType', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-DESC' },
                  { label: '创建时间：从旧到新', value: 'created-ASC' },
                  { label: 'ID：从大到小', value: 'id-DESC' },
                  { label: 'ID：从小到大', value: 'id-ASC' },
                  { label: '优先级：从大到小', value: 'priority-DESC' },
                  { label: '优先级：从小到大', value: 'priority-ASC' },
                  // { label: '冻结', value: 4 },
                ],
              }],
              [
                '位置',
                'scene_type',
                {
                  type: 'custom',
                  render: () => (
                    <x-custom-box class="block w-[150px]">
                      <ElSelect class="w-[150px]" placeholder="" onChange={e => searchForm.value.scene_type = +e} modelValue={searchForm.value.scene_type}>
                        {Object.keys(config.value.popup_scene_items).map(key => (
                          <ElOption value={+key} label={config.value.popup_scene_items[+key]} disabled={[7, 11, 8].includes(+key)} />
                        ))}
                      </ElSelect>
                    </x-custom-box>
                  ),
                }
              ],
              [
                '弹窗ID',
                'popup_id',
                {
                  type: 'text',
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '弹窗样式',
                'popup_type',
                {
                  type: 'multi-select',
                  options: Object.keys(config.value.popup_type_items).map(key => ({
                    label: config.value.popup_type_items[+key],
                    value: +key
                  }))
                  ,
                },
                {
                  // transform: transformNumber,
                },
              ],
              // [
              //   '分层画像',
              //   'strategy_layer_id',
              //   {
              //     type: 'multi-select',
              //     search: true,
              //     popoverWrapperClass: 'z-popover-in-dialog',
              //     options: strategyLayerList.value.map((n, index) => {
              //       return { value: n.id, label: `${n.id}/${n.name}` }
              //     }),
              //     maxlength: 1,
              //   },
              //   {
              //     transform: [
              //       (raw?: unknown) => raw ? [+raw] : [],
              //       (display: string[]): number => display[0] ? +(display[0] || '') : 0,
              //     ] as const,
              //   },
              // ],
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="flex w-full items-center justify-between">
                  <span>运营位管理列表</span>
                  <x-table-actions-right class="flex gap-x-2">
                    <Button class="btn btn-primary btn-sm" onClick={() => {
                      currentActivityPopup.value = cloneDeep(InitActivityPopupOption)
                      closeEditActivityPopupModal.value = openDialog({
                        title: '添加运营位',
                        body: () => <ActivityPopupFormNew />,
                        mainClass: dialogMainClass,
                        customClass: '!w-[800px] overflow-hidden',
                      })
                    }}
                    >添加运营位
                    </Button>
                  </x-table-actions-right>

                </x-table-actions>
              )
            : null
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate: page={onPageChange}
                onUpdate: size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default ActivityPopup
