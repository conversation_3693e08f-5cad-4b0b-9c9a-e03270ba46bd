import { httpClient } from 'src/lib/http-client'

export const apiGetJDRankList = (data: M.JDRank.params) =>
  httpClient.post<ApiResponse<M.JDRank.ListResponse>>('/rank/special/list', { ...data, type: 2 })

export const apiCreateJDRank = (data: M.JDRank.CreateRank) =>
  httpClient.post<ApiResponse<boolean>>('/rank/special/save', data)

export const rankApi = {
  getTabList: (data: { language_version: string, pid: string }) => {
    return httpClient.post<ApiResponse<{ list: string[] }>>('/rank/tab/list', data)
  },
  getSubRankList: (data: M.Rank.params) =>
    httpClient.post<ApiResponse<M.Rank.ListResponse>>('/rank/sub/list', data),

}
