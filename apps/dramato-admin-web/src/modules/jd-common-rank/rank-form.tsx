/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useJDCommonRank } from './use-rank'
import dayjs from 'dayjs'
import { onMounted, ref } from 'vue'
import { rankApi } from './rank-api'
import { useJDRank } from '../jd-rank/use-rank'
import { apiGetJDRankList } from '../jd-rank/rank-api'

type JDRankFormOptions = {
  props: {}
}

export const JDRankForm = createComponent<JDRankFormOptions>({
  props: {},
}, props => {
  const {
    currentJDRank,
    closeJDRankDialog,
    onCreate,
    onEdit,
    isUpdating,
    submitParams,
    params,
  } = useJDCommonRank()

  const Form = CreateForm<M.JDRank.CreateRank>()
  const formRules = z.object({
    priority: z.number().min(0, '请填写'),
    title: z.string().min(1, '请输入'),
    rank_id: z.number().min(1, '请选择'),
    start: z.number().min(1, '请选择'),
    end: z.number().min(1, '请选择'),
    // series_ids: z.array(z.string()).min(1, '请输入'),
    // tab: z.string().min(1, '请输入')
  })

  const { error, validateAll } = useValidator(currentJDRank, formRules)

  const subRankList = ref<M.SubRank[]>([])
  onMounted(async () => {
    const result = await rankApi.getSubRankList({ pid: 0, language_version: submitParams.value.language_version || '' })
    if (!result.data) return
    subRankList.value = result.data.list
  })

  const jdIds = ref<M.JDRank.Rank[]>([])

  onMounted(async () => {
    const res = await apiGetJDRankList({ language_version: submitParams.value.language_version || ''})
    jdIds.value = (res.data?.list || [])
  })

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentJDRank.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('坑位'),
              'priority',
              {
                type: 'number',
                min: 1,
                placeholder: '请输入坑位',
              },
              {
                transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('主标题'),
              'title',
              {
                type: 'textarea',
                placeholder: '请输入主标题',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            // [
            //   requiredLabel('tab'),
            //   'tab',
            //   {
            //     type: 'text',
            //     placeholder: '请输入tab',
            //   },
            //   {
            //     class: mc('col-span-1'),
            //   },
            // ],
            [
              requiredLabel('模块类型'),
              'subtype',
              {
                type: 'radio',
                placeholder: '请输入主标题',
                options: [
                  { label: '剧单', value: 2 },
                  { label: '榜单', value: 1 },
                ],
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('榜单id'),
              'rank_id',
              {
                type: 'select',
                // placeholder: '请输入主标题',
                options: subRankList.value.map(item => ({ label: item.name, value: item.id })),
              },
              {
                class: mc('col-span-1', currentJDRank.value?.subtype === 2 ? 'hidden' : ''),
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('剧单ID'),
              'rank_id',
              {
                type: 'select',
                // placeholder: '请输入主标题',
                // popoverWrapperClass: 'z-popover-in-dialog',
                options: jdIds.value.map(item => ({ label: item.title + '', value: item.id + '' })),
              },
              {
                class: mc('col-span-1', currentJDRank.value?.subtype === 1 ? 'hidden' : ''),
                transform: transformNumber,
              },
            ],
            // () => (
            //   <x-import class={mc("flex flex-row gap-x-2 items-end text-[14px] text-gray-600 mb-2", currentJDRank.value?.subtype === 1 ? 'hidden' : '')} ref={inputRef}>
            //     <Button class="btn btn-sm btn-outline w-[80px]" onClick={() => inputRef.value && inputRef.value?.click()}>导入剧单</Button>
            //     支持批量导入：xl文件，series_id，实际展示顺序同Excel内
            //   </x-import>
            // ),
            // () => <input ref={inputRef} type="file" accept=".xlsx" name="file" class="btn btn-sm btn-outline w-[80px] hidden" onChange={handleFileSelect} />,
            {
              label: requiredLabel('生效时间'),
              path: 'start',
              input: {
                type: 'custom',
                render: () => (
                  <div class="flex gap-x-2 items-center">
                    <div class="input input-bordered input-sm flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentJDRank.value.start ? dayjs(currentJDRank.value.start * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentJDRank.value.start = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                    <span>-</span>
                    <div class="input input-bordered input-sm flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentJDRank.value.end ? dayjs(currentJDRank.value.end * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentJDRank.value.end = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                  </div>
                ),
              },
              class: mc('col-span-1'),
            },
          ]}
          data={currentJDRank.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeJDRankDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentJDRank.value?.id ? void onCreate() : void onEdit()

            try {
              const exclude: string[] = []

              // if (currentJDRank.value?.subtype === 1) {
              //   exclude.push('series_ids')
              // } else {
              //   exclude.push('rank_id')
              // }

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
