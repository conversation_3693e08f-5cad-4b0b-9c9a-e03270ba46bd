import { createComponent, mc } from '@skynet/shared'
import { Icon, showAlert } from '@skynet/ui'
import { useVocalGenerate } from '../use-vocal-generate'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'
import { useClipboard } from '@vueuse/core'
import { watch } from 'vue'

type NpcCardOptions = {
  props: {
    npc: M.VocalGenerate.Role
    origin: 'ai_generate' | 'customer'
  }
}

const fullIcon = (icon: string) => {
  if (!icon) {
    return ''
  }

  console.log('icon', icon, icon.indexOf('https'))

  if (icon.indexOf('https') >= 0) {
    return icon
  }

  return `https://us-dramato-prod.oss-us-east-1.aliyuncs.com/${icon}`
}

export const NpcCard = createComponent<NpcCardOptions>({
  props: {
    npc: {},
    origin: 'ai_generate',
  },
}, props => {
  const {
    onNpcCardEdit,
    currentNpc,
    onDeleteNpc,
  } = useVocalGenerate()

  const {
    showImagePreviewDialog,
  } = useImagePreviewStore()

  const { copy, copied } = useClipboard()

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  return () => (
    <x-avatar-list-item class={mc('min-w-[180px] w-[180px] overflow-hidden flex flex-row justify-start items-start gap-x-2 border border-gray-200 rounded-lg p-2', currentNpc.value?.role_id === props.npc.role_id && 'border-blue-500')}>
      <div class="avatar">
        <div class="w-16 rounded-full" onClick={() => showImagePreviewDialog({
          imageList: [{ src: props.npc?.icon || '', width: 1000, height: 1000 }],
        })}
        >
          <img src={fullIcon(props.npc?.icon || '')} alt={props?.npc?.name} />
        </div>
      </div>
      <x-avatar-list-item-content class="flex-1 flex flex-col justify-start items-start gap-y-1 overflow-hidden">
        <x-title class="font-medium text-sm">{props?.npc?.name?.split(/\n/)[0]}</x-title>
        <x-text class="text-xs truncate w-full">{props?.npc?.desc || props?.npc?.name?.split(/\n/).slice(1).join(',')}</x-text>
        {/* <input type="text" class="w-full input input-xs input-bordered" /> */}
        <x-actions class="flex flex-row justify-start items-center gap-x-2">
          <Icon
            name="ant-design:edit-outlined"
            class="cursor-pointer"
            title="编辑"
            onClick={() => onNpcCardEdit({ ...props.npc }, props.origin)}
          />
          <Icon name="ant-design:delete-outlined" class="cursor-pointer" title="删除" onClick={() => onDeleteNpc(props.npc)} />
          <Icon name="ant-design:copy-outlined" class="cursor-pointer" title="添加" onClick={() => {
            void copy(props.npc?.name || '')
          }}
          />
        </x-actions>
      </x-avatar-list-item-content>
    </x-avatar-list-item>
  )
})
