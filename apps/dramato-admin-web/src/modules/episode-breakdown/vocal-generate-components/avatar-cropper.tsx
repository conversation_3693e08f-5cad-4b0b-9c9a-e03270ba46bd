import { createComponent } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import 'cropperjs/dist/cropper.css'
import { Button } from '@skynet/ui'

type AvatarCropperOptions = {
  props: {
    url: string
  }
  emits: {
    hide: () => void
    success: (b: Blob) => void
  }
}
export const AvatarCropper = createComponent<AvatarCropperOptions>({
  props: {
    url: '',
  },
  emits: {
    hide: () => {},
    success: (b: Blob) => {},
  },
}, (props, { emit }) => {
  const cropperDom = ref()
  const widescreenCropper = ref()

  onMounted(() => {
    console.log('onMounted')

    void import('cropperjs').then(module => {
      // console.log('module', module)
      const Cropper = module.default
      widescreenCropper.value = new Cropper(cropperDom.value, {
        viewMode: 1,
        dragMode: 'crop',
        aspectRatio: 1,
        cropBoxMovable: true,
        cropBoxResizable: true,
        autoCrop: true,
      })
    })
  })
  return () => (
    <x-avatar-cropper class="flex flex-col  justify-center gap-4">
      <div class="relative w-[350px] h-[550px] overflow-hidden rounded-[8px]">
        <img src={props.url} crossorigin="anonymous" class="hidden w-[350px] h-[550px]" ref={cropperDom} />
        <img src={props.url.includes('https://') ? props.url : 'https://us-dramato-prod.oss-us-east-1.aliyuncs.com/' + props.url} class="w-[350px] h-[550px]" ref={cropperDom} />
      </div>
      <x-generate-material-model-footer class="flex justify-end gap-x-2">
        <Button class="btn btn-sm" onClick={() => emit('hide')}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          widescreenCropper.value
            .getCroppedCanvas({})
            .toBlob((data: Blob) => {
              emit('success', data)
            })
        }}
        >确定
        </Button>
      </x-generate-material-model-footer>
    </x-avatar-cropper>

  )
})
