import { createComponent } from '@skynet/shared'
import { useVocalGenerate } from '../use-vocal-generate'
import { VocalCategoryCard } from './vocal-category-card'
type AiVocalsOptions = {
  props: {

  }
}
export const AiVocals = createComponent<AiVocalsOptions>({
  props: {},
}, props => {
  const { vocalGenerateData } = useVocalGenerate()
  return () => (
    <x-vocal-content id="ai-vocals" class="border border-1 border-base-300 rounded-lg p-4 h-[320px] flex flex-col gap-y-4 overflow-y-auto">
      { vocalGenerateData.value?.ai_vocals && vocalGenerateData.value?.ai_vocals.length > 0
        ? (
            vocalGenerateData.value?.ai_vocals.map(i => <VocalCategoryCard vocalCategory={i} key={i.label} />)
          )
        : <x-empty class="w-full flex justify-center items-center">暂无数据</x-empty>}
    </x-vocal-content>
  )
})
