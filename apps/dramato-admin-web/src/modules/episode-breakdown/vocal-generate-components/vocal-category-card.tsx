import { createComponent } from '@skynet/shared'
import { VocalCard } from './vocal-card'
type VocalCategoryCardOptions = {
  props: {
    vocalCategory: M.VocalGenerate.AIVocal
  }
}
export const VocalCategoryCard = createComponent<VocalCategoryCardOptions>({
  props: {
    vocalCategory: {
      label: '',
      vocals: [],
    },
  },
}, props => {
  return () => (
    <x-category-item>
      <x-category>类别{props.vocalCategory.label}</x-category>
      <x-vocal-list class="flex flex-col gap-y-4">
        {props.vocalCategory.vocals.map(vocal => <VocalCard origin="ai_generate" key={vocal.vocal_id} vocal={vocal} />)}
      </x-vocal-list>
    </x-category-item>
  )
})
