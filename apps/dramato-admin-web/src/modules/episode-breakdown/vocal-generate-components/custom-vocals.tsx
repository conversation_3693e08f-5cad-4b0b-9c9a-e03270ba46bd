import { createComponent } from '@skynet/shared'
import { useVocalGenerate } from '../use-vocal-generate'
import { VocalCard } from './vocal-card'
type CustomVocalsOptions = {
  props: {}
}
export const CustomVocals = createComponent<CustomVocalsOptions>({
  props: {},
}, props => {
  const { vocalGenerateData } = useVocalGenerate()
  return () => (
    <x-vocal-content id="custom-vocals" class="border border-1 border-base-300 rounded-lg p-4 h-[320px] flex flex-col gap-y-4 overflow-y-auto">
      { vocalGenerateData.value?.custom_vocals && vocalGenerateData.value?.custom_vocals.length > 0
        ? (
            vocalGenerateData.value?.custom_vocals.map(i => <VocalCard origin="customer" vocal={i} key={i.vocal_id} />)
          )
        : <x-empty class="w-full flex justify-center items-center">暂无数据</x-empty>}
    </x-vocal-content>
  )
})
