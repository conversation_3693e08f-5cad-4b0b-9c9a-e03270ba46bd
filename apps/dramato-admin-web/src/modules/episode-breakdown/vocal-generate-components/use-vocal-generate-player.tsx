import { ref } from 'vue'

export const useVocalGeneratePlayer = () => {
  return {
    // TODO: implement
    serialNumber,
    actionType,
    playerTitle,
    updateVocalGeneratePlayer,
    resetVocalGeneratePlayer,
    videoList,
  }
}

export const VocalActions = [
  {
    label: '新增NPC',
    value: 1,
  },
  {
    label: '编辑NPC',
    value: 2,
  },
  {
    label: '新增声纹',
    value: 3,
  },
  {
    label: '编辑声纹',
    value: 4,
  },
]

const serialNumber = ref<number>(0)
const actionType = ref<number>(2)
const playerTitle = ref<string>('自定义NPC')

const videoList = ref<M.VocalGenerate.Vocal[]>([])

const updateVocalGeneratePlayer = (d: {
  serialNumber: number
  actionType: number
  playerTitle: string
  videoList: M.VocalGenerate.Vocal[]
}) => {
  serialNumber.value = d.serialNumber
  actionType.value = d.actionType
  playerTitle.value = d.playerTitle
  videoList.value = d.videoList
}

const resetVocalGeneratePlayer = () => updateVocalGeneratePlayer({
  serialNumber: 0,
  actionType: 0,
  playerTitle: '',
  videoList: [],
})
