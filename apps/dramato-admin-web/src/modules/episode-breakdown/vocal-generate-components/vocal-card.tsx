import { createComponent, mc } from '@skynet/shared'
import { Icon, showAlert } from '@skynet/ui'
import { MSConfig } from 'src/modules/episode-clip-material/episode-material-player'
import { useVocalGenerate } from '../use-vocal-generate'

type VocalCardOptions = {
  props: {
    vocal: M.VocalGenerate.Vocal
    origin: 'ai_generate' | 'customer'
  }
}
export const VocalCard = createComponent<VocalCardOptions>({
  props: {
    vocal: {},
    origin: 'ai_generate',
  },
}, props => {
  const {
    onVocalEdit,
    onDeleteVocal,
    vocalGenerateData,
    currentVocal,
    vocalOrigin,
  } = useVocalGenerate()

  const check = () => vocalGenerateData.value.vocal_generate === 1

  const onEdit = () => {
    if (props.origin === 'ai_generate' && !check()) {
      return showAlert('正在生成声纹聚合类，暂时不可进行操作，请稍后重试!', 'error')
    }
    onVocalEdit(props.vocal, props.origin)
  }

  const onDelete = () => {
    // TODO: 删除
    if (props.origin === 'ai_generate' && !check()) {
      return showAlert('正在生成声纹聚合类，暂时不可进行操作，请稍后重试!', 'error')
    }
    onDeleteVocal(props.vocal)
  }

  return () => (
    <x-vocal-item
      id={props.origin + props.vocal.vocal_id}
      class={mc(currentVocal.value.vocal_id === props.vocal.vocal_id && vocalOrigin.value === props.origin && '!text-primary')}
    >
      <x-serial-num>{props.vocal.serial_number || ''}</x-serial-num>
      <x-vocal-duration class="flex flex-row items-center gap-x-2">
        {MSConfig.transformTimeMsToStr((props.vocal.start_stamp || 0) / 1000)}
        --{'>'}
        {MSConfig.transformTimeMsToStr((props.vocal.end_stamp || 0) / 1000)}
        <Icon name="ant-design:play-circle-outlined" class="cursor-pointer" onClick={() => onEdit()} title="查看" />
        <Icon name="ant-design:delete-outlined" class="cursor-pointer" title="删除" onClick={() => onDelete()} />
      </x-vocal-duration>
      <x-npc-name>{props.vocal.role || ''}</x-npc-name>
    </x-vocal-item>
  )
})
