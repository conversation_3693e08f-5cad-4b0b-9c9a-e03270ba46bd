import { createComponent, mc } from '@skynet/shared'
import { NpcCard } from './npc-card'

type RolesOptions = {
  props: {
    roles: <PERSON><PERSON>VocalGenerate.Role[]
    showMore: boolean
    origin: 'ai_generate' | 'customer'
  }
}

export const Roles = createComponent<RolesOptions>({
  props: {
    roles: [],
    showMore: false,
    origin: 'ai_generate',
  },
}, props => {
  return () => props.roles && props.roles.length > 0
    ? (
        <x-avatar-list class={
          mc('w-full overflow-hidden flex flex-row flex-wrap justify-start items-start gap-x-2 gap-y-4 ',
            !props.showMore ? 'h-[68px]' : '',
          )
        }
        >
          {
            props.roles.map(item => (
              <NpcCard origin={props.origin} npc={item} key={item.role_id} />
            ))
          }

        </x-avatar-list>
      )
    : <x-empty class="w-full flex justify-center items-center">暂无数据</x-empty>
})
