/* eslint-disable @typescript-eslint/no-explicit-any */
declare namespace M {
    type VideoClipSubtitleType = 1 | 2 | 3

    interface VideoClipParams {
      series_key: string
      vocal_desc: string // '1\n10.10 --> 10.5 \n你好...'
      // subtitle_type?: VideoClipSubtitleType // 1: OCR+ASR 2:ASR 3:OCR
      single_prompt_num?: number
      serial_numbers: number[] | string
      ocr_lang: string // 中文或者英文或者无
      asr_lang: string // 中文或者英文或者无
    }

    type VideoClipStep = 'start' | 'pending' | 'all_pending' | 'completed' | 'fail'

    // 拆解状态 0 未分析 1 声纹拆解中 2 剧集拆解中 3.剧集汇总中 4 拆解完成 5 拆解失败
    type VideoClipStatus = 0 | 1 | 2 | 3 | 4 | 5

    interface VideoClipPeople {
      [key: string]: number
    }

    // '{1: [[47.66, 9], [101.6, 8]], 2: [[13.1, 6], [24.86, 8], [42.94, 7], [29.62, 5], [69.14, 7]], 3: [[31.3, 8], [41.96, 6], [83.02, 7], [108.9, 5]], 4: [[16.98, 7], [74.9, 9], [81.48, 8]], 5: [[11.84, 7], [40.24, 6], [61.4, 8]]}'
    interface VideoClipPlot {
      [key: number]: number[][]
    }

    interface VideoClipName {
      [key: number]: string[]
    }

    // 2. 全集维度
    interface VideoClipSeries {
      people: VideoClipPeople // 角色戏份汇总
      hook_plot: VideoClipPlot // 钩子分布
      cool_plot: VideoClipPlot // 爽点分布
      story_plot: VideoClipPlot // 高光分布
      synopsis: string
      evaluate: string
      hook_names: VideoClipName
      cool_names: VideoClipName
      story_names: VideoClipName
    }

    // 1. 单集维度
    interface VideoClipEpisode {
      serial_number: number
      res_srt: string // 台词
      scripts: string // 剧本
      characters_part: string // 角色戏份
      hook: string // 钩子
      cool: string // 爽点
      synopsis: string // 剧情概括
      storyboard: string[][] // 镜头拆解
      characters_inform: string // 角色分析
      prompt_num: number
      [key: string]: any
    }

    interface VideoClipInfo {
      analyse_status?: VideoClipStatus
      vocal_desc?: string
      series?: VideoClipSeries
      finished?: number[]
      episode_count?: number// 剧集总集数
      updated_count?: number
      tips?: string
      episode_updated?: number// 剧集更新时间
      vocal_updated?: number// 声纹最后更新时间
      single_updated?: number// 单集拆解最后时间
      summary_updated?: number// 单集拆解最后时间
      single_prompt_num?: number// 单集拆解prompt编号
      summary_prompt_num?: number// 全剧拆解prompt编号
      curr_total: number
      curr_finish: number
      stopping?: boolean
      ocr_lang?: string // 中文或者英文或者无
      asr_lang?: string // 中文或者英文或者无
      series_resource_id?: number
      series_key?: string
      subtitle_language_code?: string
      vocal_language_code?: string
      vocal_subtitle_language_code?: string
      subtitle_up_language_code?: string
    }

    interface Prompt {
      prompt_id: number
      created: number// 时间戳
      prompt?: string
    }

}
