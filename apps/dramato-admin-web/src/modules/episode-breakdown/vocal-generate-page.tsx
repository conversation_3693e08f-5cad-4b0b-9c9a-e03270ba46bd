/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { EpisodeMaterialPlayer } from '../episode-clip-material/episode-material-player'
import { Button, Icon, openDialog, usePopover } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { useVocalGenerate } from './use-vocal-generate'
import { Roles } from './vocal-generate-components/roles'
import { AiVocals } from './vocal-generate-components/ai-vocals'
import { CustomVocals } from './vocal-generate-components/custom-vocals'
import { useVocalGeneratePlayer, VocalActions } from './vocal-generate-components/use-vocal-generate-player'
import { Uploader } from '../common/uploader/uploader'
import { useEpisodeClipMaterialPlayer } from '../episode-clip-material/use-episode-clip-material-player'
import { useRoute } from 'vue-router'
import { httpClient } from 'src/lib/http-client'
import { useEpisodeBreakdown } from './use-episode-breakdown'
import { Ava<PERSON><PERSON>ropper } from './vocal-generate-components/avatar-cropper'
import { apiGenerateRoleExtract, apiGetRoleState } from './episode-breakdown-api'

type VocalGeneratePageOptions = {
  props: {}
}


export const VocalGeneratePage = createComponent<VocalGeneratePageOptions>({
  props: {},
}, props => {
  const route = useRoute()

  const {
    seriesKey,
    vocalGenerateData,
    getVocalGenerateData,
    onSerialNumberChange,
    currentNpc,
    currentVocal,
    onAddVocal,
    onConfirm,
    onAIVocalGenerateClick,
    resetPlayer,
    onPre,
    onNext,
  } = useVocalGenerate()


  const {
    videoClipParams,
    newSeriesKey
  } = useEpisodeBreakdown()

  const {
    serialNumber,
    actionType,
    playerTitle,
  } = useVocalGeneratePlayer()


  const generateRoleState = ref({
    status: 1,
    tips: '',
  })

  const {
    videoUrl,
    player,
  } = useEpisodeClipMaterialPlayer()

  const blobToFile = (theBlob: Blob): File => {
    return new File([theBlob], 'xxx.png', {
      lastModified: new Date() as any,
      // lastModifiedDate: new Date() as any,
      type: theBlob.type,
    })
  }

  const captureFrame = () => {
    if (!player.value) {
      return
    }
    // // 获取画布元素
    const canvas: HTMLCanvasElement = document.createElement('canvas')
    canvas.width = 350
    canvas.height = 550
    // 获取画布上下文，这里使用2D上下文
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      return
    }

    console.log(3)

    // 将视频当前播放到的帧绘制到画布上
    ctx?.drawImage(player.value, 0, 0, 350, 550)

    // 将画布内容转换为图片数据
    const imageData = canvas.toDataURL('image/png')

    void fetch(imageData).then(res => res.blob()).then(blob => {
      console.log('blob', blob)

      const file = blobToFile(blob)
      console.log('file', file)

      void httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>('/vocal/role_upload', {
        file,
      }, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then(rs => {
        currentNpc.value = {
          ...currentNpc.value,
          icon: rs?.data?.cover || rs?.data?.image || rs?.data?.path || '',
        }
        showCropper(currentNpc.value.icon || '')
      },
      )
    })
  }

  const triggerElementRef = ref()

  usePopover({
    triggerElement: triggerElementRef,
    content: () => (
      <x-tips>
        1.声纹标注规范<br />
        【声纹纯净性】<br />
        - 请务必保证标注准确！<br />
        - 请不要标注轻声讲话的声纹条目；<br />
        - 请不要标注存在大段时间无人讲话的条目；<br />
        - 请确保每段声纹仅有一个说话人（混入其他人的一点声音都不可以）；<br />
        【声纹多样性】<br />
        - 建议对每个角色标注的声纹尽量多样化，覆盖丰富的语气、情绪、音调等；<br />
        【角色命名规则】<br />
        - 当本剧存在多种语言的人名时，请标注出全部名称，格式："朱棣（Judy）"；<br />
        - 请避免不同NPC间重名（"JUDY"和"judy"视为重名）；<br />
        【标注数量规则】<br />
        - 当某类别下声纹数量较多：不论包含几个角色，每个角色至少标注1条即可，所以一共标注角色数*1条即可，不用多标；<br />
        - 当某类别下声纹数量较少（2-3条），建议都进行标注；<br />
        - 对戏份本就少的配角可以适当自行新增，多标一些声纹；<br />
        - 全部标完后，对于未标注任何声纹的""NPC列表""中的角色，请回到其出场帧附近，补充标注几条；<br />
        5.操作tips<br />
        - 当声纹片段没有给说话人特写时，可以前后调整时间框看这个说话的到底是谁，不用担心丢失原本的时间框，再点""播放键""会复原调整前的时间框。<br />
        2.人脸标注规范<br />
        【人脸纯净性】<br />
        - 请务必保证标注准确！<br />
        - 请不要标注模糊不清的人脸图片；<br />
        - 请不要标注存在大片空白的人脸图片；<br />
        - 请确保每张人脸仅人（混入其他人的一点影像都不可以）；<br />
        【人脸多样性】<br />
        - 建议对每个角色标注的人脸尽量多样化，覆盖多种角度、表情、光线等；<br />
        【角色命名规则】（同声纹)<br />
        【标注数量规则】（同声纹）<br />
        【操作tips】<br />
        - 当一时脸盲辨认不出人脸属于谁时，可以前后拖动视频自带进度条看这个说话的到底是谁，不会影响存储的时间戳。<br />
      </x-tips>
    ),
    placement: 'bottom-start',
    class: 'overflow-visible',
    offset: 10,
    arrowVisible: false,
    triggerType: 'hover',
    wrapperClass: 'p-2 bg-white border-[1px] border-[#eee] border-[solid] rounded-xs',
  })

  onMounted(() => {
    seriesKey.value = newSeriesKey.value as string
    getVocalGenerateData()
    circleRefreshGenerateNpcProgress()
  })

  const showCropper = (url: string) => {
    const hideCropper = openDialog({
      title: '裁剪',
      body: () => (
        <AvatarCropper url={url} onHide={hideCropper} onSuccess={d => {
          const file = blobToFile(d)
          console.log('file', file)

          void httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>('/vocal/role_upload', {
            file,
          }, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }).then(rs => {
            currentNpc.value = {
              ...currentNpc.value,
              icon: rs?.data?.cover || rs?.data?.image || rs?.data?.path || '',
            }
            hideCropper()
          },
          )
        }}
        />
      ),
      customClass: '!w-[390px]',
    })
  }



  const circleRefreshGenerateNpcProgress = () => {
    const process = () => {
      void apiGetRoleState({ series_key: route.params.seriesKey as string }).then(res => {
        generateRoleState.value = res?.data as {
          status: number
          // 0:未提取 1:提取中 2:提取完毕
          tips: string
        }

        setTimeout(process, 5000)
      })
    }

    process()
  }

  return () => (
    <x-vocal-generate-page class="flex flex-row justify-start gap-x-2">
      <x-left-panel class="flex-1">
        <Wrapper>
          <h1 class="flex items-center gap-x-2">声线辅助标注工具
            <Icon ref={triggerElementRef} name="ant-design:question-circle-outlined" class="size-5 tooltip" data-tip="" />
          </h1>
          <div class="flex justify-between">
            <x-title class="font-medium flex flex-row justify-between">
              NPC列表
            </x-title>
            <Button
              class="btn btn-sm !w-fit btn-primary"
              disabled={[1, 3].includes(generateRoleState.value.status) || !videoClipParams.value.ocr_lang || !videoClipParams.value.asr_lang}
              onClick={() => {
                void apiGenerateRoleExtract({ series_key: newSeriesKey.value }).then(() => {
                  circleRefreshGenerateNpcProgress()
                })
              }}
            >
              {generateRoleState.value.status === 3 ? '字幕提取，排队中...' : generateRoleState.value.status === 1 ? generateRoleState.value.tips : '提取NPC'}
            </Button>
          </div>
          <Roles origin="ai_generate" roles={vocalGenerateData.value.ai_roles || []} showMore />
        </Wrapper>
        <Wrapper>
          <x-title class="font-medium flex flex-row justify-between">
            自定义NPC列表
            {/* <Button class="btn btn-sm btn-primary" onClick={onAddCustomNpc}>添加NPC</Button> */}
          </x-title>
          <Roles origin="customer" roles={vocalGenerateData.value.custom_roles || []} showMore />
        </Wrapper>
        <Wrapper>
          <x-title class="font-medium flex flex-row justify-between">
            声纹聚合类结果
            <Button
              class="btn btn-sm btn-primary"
              onClick={onAIVocalGenerateClick}
              disabled={vocalGenerateData.value?.vocal_generate !== 1}
            >
              {vocalGenerateData.value?.vocal_generate === 1 ? '生成' : '生成中...'}
            </Button>
          </x-title>
          <AiVocals />
        </Wrapper>
        <Wrapper>
          <x-title class="font-medium flex flex-row justify-between">
            自定义声纹
            <Button class="btn btn-sm btn-primary" onClick={onAddVocal}>添加声纹</Button>
          </x-title>
          <CustomVocals />
        </Wrapper>
      </x-left-panel>

      <EpisodeMaterialPlayer customClass="m-0 mt-4 bg-white h-[calc(100vh_-_var(--top-bar-height))] sticky top-[var(--top-bar-height)] overflow-auto  right-0 py-2">
        {{
          title: () => (
            <x-player-header class="px-2 flex flex-col gap-y-2">
              <x-title>{playerTitle.value}</x-title>
              <x-type-select class="flex items-center">
                <label class="w-120px">操作类型</label>
                <select
                  // id={id}
                  class="select select-bordered select-sm w-full"
                  value={actionType.value}
                  onInput={(e: any) => actionType.value = e.target.value}
                  disabled
                >
                  {
                    VocalActions.map(option => <option value={option.value}>{option.label}</option>)
                  }
                </select>
              </x-type-select>
              <x-type-select class="flex items-center">
                <label class="w-120px">剧集选择</label>
                <select
                  class="select select-bordered select-sm w-full"
                  value={serialNumber.value}
                  onInput={(e: any) => {
                    serialNumber.value = +e.target.value
                    onSerialNumberChange(+e.target.value, actionType.value)
                  }}
                  disabled={[2, 4].includes(actionType.value)}
                >
                  {
                    vocalGenerateData.value.videos.map(option => <option value={option.serial_number}>第{option.serial_number}集</option>)
                  }
                </select>
              </x-type-select>
            </x-player-header>
          ),
          otherForm: () => (
            <>
              <x-type-select class="flex items-center">
                <label class="w-120px">NPC姓名</label>
                <input
                  class="flex-1 input input-bordered input-sm"
                  value={[1, 2].includes(actionType.value) ? currentNpc.value?.name : currentVocal.value?.role}
                  onInput={(e: any) => {
                    if ([1, 2].includes(actionType.value)) {
                      currentNpc.value = {
                        ...currentNpc.value,
                        name: e.target.value,
                      }
                      return
                    }

                    currentVocal.value = {
                      ...currentVocal.value,
                      role: e.target.value,
                    }
                  }}
                  disabled={videoUrl.value === ''}
                />
              </x-type-select>
              {
                actionType.value === 2 && (
                  <x-type-select class="flex items-center">
                    <label class="w-120px">NPC描述</label>
                    <input
                      class="flex-1 input input-bordered input-sm"
                      value={currentNpc.value?.desc || ''}
                      onInput={(e: any) => {
                        currentNpc.value = {
                          ...currentNpc.value,
                          desc: e.target.value,
                        }
                      }}
                      disabled={videoUrl.value === ''}
                    />
                  </x-type-select>
                )
              }
              <x-type-select class="flex items-start">
                <x-upload-cover class="gap-x-2 flex items-end">
                  {
                    !videoUrl.value
                      ? (
                        <x-uploader-disable class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-not-allowed">
                          <span class="size-full flex items-center justify-center">上传NPC头像</span>
                        </x-uploader-disable>
                      )
                      : (
                        <Uploader
                          accept="png,jpg,jpeg"
                          maxsize={1024 * 1024 * 10}
                          class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                          onUploadSuccess={d => {
                            currentNpc.value = {
                              ...currentNpc.value,
                              icon: d.temp_path,
                            }
                          }}
                          isImage={true}
                          uploadUrl="/vocal/role_upload"
                        >
                          {
                            currentNpc.value.icon
                              ? <img src={currentNpc.value.icon.includes('https://') ? currentNpc.value.icon : 'https://us-dramato-prod.oss-us-east-1.aliyuncs.com/' + currentNpc.value.icon} class="size-full object-cover" />
                              : <span class="size-full flex items-center justify-center">上传NPC头像</span>
                          }
                        </Uploader>
                      )
                  }
                  <x-upload-cover-tip class="text-gray-600 text-sm flex flex-col gap-y-2">
                    <x-button-group class="flex gap-x-2">
                      <Button class="btn btn-primary btn-sm w-fit" onClick={captureFrame}>截图</Button>
                      {/* {currentNpc.value && currentNpc.value.icon && <Button class="btn btn-primary btn-sm w-fit" onClick={() => showCropper(currentNpc.value.icon || '')}>裁剪</Button>} */}
                    </x-button-group>

                    png,jpg,jpeg格式，大小限制10M
                  </x-upload-cover-tip>
                </x-upload-cover>
              </x-type-select>
            </>
          ),
          footer: () => (
            <>
              {
                actionType.value === 4 && (
                  <>
                    <Button class="btn btn-primary btn-sm" onClick={onPre}>上一条</Button>
                    <Button class="btn btn-primary btn-sm" onClick={onNext}>下一条</Button>
                  </>
                )
              }
              <Button class="btn btn-sm" onClick={resetPlayer}>取消</Button>
              <Button class="btn btn-primary btn-sm" onClick={onConfirm}>确定</Button>
            </>
          ),
        }}
      </EpisodeMaterialPlayer>
    </x-vocal-generate-page>
  )
})

export default VocalGeneratePage
