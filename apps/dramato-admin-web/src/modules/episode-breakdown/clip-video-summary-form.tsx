/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { useEpisodeBreakdownStep } from './use-episode-breakdown-step'
import { ref } from 'vue'
import { Button, CreateForm, openDialog, showAlert, transformInteger } from '@skynet/ui'
import { z } from 'zod'
import { usePrompt } from './use-prompt'
import { requiredLabel } from 'src/lib/required-label'
import dayjs from 'dayjs'
import { apiClipSummaryVideo } from './episode-breakdown-api'
import { set } from 'lodash-es'
import { useEpisodeBreakdown } from './use-episode-breakdown'
import { PromptSetting } from './prompt-setting'
type ClipVideoSummaryFormOptions = {
  props: {
    series_key: string
    episodeName: string
  }
  emits: {
    hide: () => void
  }
}
export const ClipVideoSummaryForm = createComponent<ClipVideoSummaryFormOptions>({
  props: {
    series_key: '',
    episodeName: '',
  },
  emits: {
    hide: () => {},
  },
}, ({ series_key, episodeName }, { emit }) => {
  const { setStep } = useEpisodeBreakdownStep()
  const { videoClipInfo, getVideoClipInfo, resetClipVideo } = useEpisodeBreakdown()
  const config = ref<{
    summary_prompt_num?: number
  }>({})

  const Form = CreateForm<{
    summary_prompt_num?: number
  }>()

  const formRules = z.object({
    summary_prompt_num: z.number({
      message: '请选择Prompt编号',
    }).min(1),
  })

  const { error, validateAll } = useValidator(config, formRules)

  const { promptList, getPromptList } = usePrompt()

  return () => (
    <x-clip-video-summary-confirm-dialog class="flex flex-col gap-y-[25px]">
      <x-clip-video-summary-body>
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value: any) => {
            set(config.value, path, value)
          }}
          items={[
            [
              '短剧名称',
              '',
              {
                type: 'custom',
                render: () => episodeName,
              },
            ],
            [
              '短剧总集数',
              '',
              {
                type: 'custom',
                render: () => videoClipInfo.value?.episode_count,
              },
            ],
            [
              requiredLabel('Prompt编号'),
              'summary_prompt_num',
              {
                type: 'select',
                placeholder: '请选择Prompt编号',
                options: promptList.value.map(i => {
                  return {
                    label: dayjs(i.created * 1000).format('YYYY-MM-DD HH:mm:ss') + `/ 编号-${i.prompt_id}`,
                    value: i.prompt_id,
                  }
                }),
              },
              {
                transform: transformInteger,
              },
            ],
            [
              '',
              '',
              {
                type: 'custom',
                render: () => (
                  <Button class="btn btn-outline btn-sm w-[80px]" onClick={() => {
                    openDialog({
                      title: '高级设置',
                      body: () => <PromptSetting prompt_type={2} />,
                      beforeClose: () => {
                        void getPromptList({ prompt_type: 2 })
                      },
                      mainClass: 'w-[820px] flex justify-center items-center',
                      customClass: '!w-[820px]',
                    })
                  }}
                  >高级设置
                  </Button>
                ),
              },
            ],
          ]}
          data={config.value}
        />
      </x-clip-video-summary-body>
      <x-clip-video-summary-footer class="w-full flex justify-end gap-x-[10px]">
        <button class="btn btn-ghost btn-sm" onClick={() => emit('hide')}>取消</button>
        <button class="btn btn-primary btn-sm" onClick={() => {
          if (!validateAll({ exclude: [] })) {
            return
          }
          void apiClipSummaryVideo({ series_key, summary_prompt_num: config.value.summary_prompt_num as number })
            .then(() => {
              setStep('all_pending')
              emit('hide')
              resetClipVideo.value = false

              void getVideoClipInfo({ series_key })
            })
            .catch((error: any) => {
              console.log('clip summary video error', error)
              showAlert(error.response.data.message || '提交失败', 'error')
            })
        }}
        >
          确定
        </button>
      </x-clip-video-summary-footer>
    </x-clip-video-summary-confirm-dialog>
  )
})
