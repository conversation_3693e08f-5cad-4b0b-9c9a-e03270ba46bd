/* eslint-disable @typescript-eslint/no-explicit-any */
export const transformPeopleChartData = (data: M.VideoClipPeople) => {
  const xAxis: string[] = []
  const yAxis: number[] = []
  Object.keys(data).forEach((key: string) => {
    xAxis.push(key)
    yAxis.push(data[key])
  })

  return {
    xAxis,
    yAxis,
  }
}

export const getPeopleChartData = (data: M.VideoClipPeople) => {
  const { xAxis, yAxis } = transformPeopleChartData(data)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        saveAsImage: { show: true },
      },
    },
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: xAxis,
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        type: 'bar',
        barGap: 0,
        emphasis: {
          focus: 'series',
        },
        barWidth: 20,
        data: yAxis,
        itemStyle: {
          color: '#4a00ff',
        },
      },
    ],
  }

  return option
}

export const getCharactersPartChartData = (data: Array<{ value: number, name: string }>) => {
  if (!data) {
    return null
  }
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        saveAsImage: { show: true },
      },
    },
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: data.map((item: { value: number, name: string }) => item.name),
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        type: 'bar',
        barGap: 0,
        emphasis: {
          focus: 'series',
        },
        barWidth: 20,
        data: data.map((item: { value: number, name: string }) => item.value),
        itemStyle: {
          color: '#4a00ff',
        },
      },
    ],
  }

  return option
}

export const transformPlotChartData = (data: M.VideoClipPlot, plotName?: M.VideoClipName) => {
  const xAxis: string[] = []
  const yAxis: number[] = []
  const markPointData: Array<{ coord: [number, number], value: number, num: number }> = []
  const markLineData: Array<{ xAxis: number }> = []
  const names: string[] = []
  let xAxisStart = -1

  Object.keys(data).forEach((key: string) => {
    xAxisStart = xAxisStart + data[+key].length
    markLineData.push({ xAxis: xAxisStart })
    names.push(...(plotName && plotName[+key] ? plotName[+key].map(i => i.replace(/\d+：/g, '')) : []))

    data[+key].forEach((item: number[]) => {
      xAxis.push(`${item[0]}`)
      yAxis.push(item[1])
      markPointData.push({ coord: [xAxis.length - 1, item[1]], value: item[1], num: +key })
    })
  })

  console.log('xAxis', xAxis)

  console.log(markPointData, '>>> markPointData')

  return { xAxis, yAxis, markPointData, markLineData: markLineData.slice(0, markLineData.length - 1), names }
}

export const getPlotChartOption = (data: M.VideoClipPlot, plotName?: M.VideoClipName) => {
  const { xAxis, yAxis, markPointData, markLineData, names } = transformPlotChartData(data, plotName)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      show: true,
      formatter: (params: any) => {
        return `第${markPointData[params[0].dataIndex].num}集-${(names[params[0].dataIndex] || '').split('：')[0]}<br />镜头：${(names[params[0].dataIndex] || '').split('：')[1] + ''}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis,
    },
    yAxis: {
      type: 'value',
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 10,
      },
    ],
    series: [
      {
        type: 'line',
        stack: 'Total',
        data: yAxis,
        markPoint: {
          data: markPointData,
          symbol: 'pin',
          symbolSize: 40,
          itemStyle: {
            color: '#4a00ff',
          },
        },
        markLine: {
          symbol: ['none', 'none'],
          label: { show: false },
          data: markLineData,
        },
        lineStyle: {
          color: '#4a00ff',
        },
      },
    ],
  }

  return option
}
