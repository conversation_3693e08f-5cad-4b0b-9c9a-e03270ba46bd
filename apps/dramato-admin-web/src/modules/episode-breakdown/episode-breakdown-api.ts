import { httpClient } from 'src/lib/http-client'

/** 视频拆解开始 */
export const apiClipVideo = (data: M.VideoClipParams) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/clip', data)

/** 视频拆解信息获取 */
export const apiGetVideoClipInfo = (data: { series_key: string }) =>
  httpClient.post<ApiResponse<M.VideoClipInfo>>('/videoclip/info', data)

/** 视频拆解信息获取 */
export const apiGetVideoClipEpisode = (data: { series_key: string, serial_number: number }) =>
  httpClient.post<ApiResponse<{
    episode: M.VideoClipEpisode
  }>>(`/videoclip/info_episode`, data)

export const apiGetPromptList = (data: {
  prompt_type: number // 1: 单集拆解，2: 全集拆解
}) => httpClient.post<ApiResponse<{ list: M.Prompt[] }>>('/videoclip/prompt_list', data)

export const apiStopVideoClip = (data: { series_key: string, clip_type: number }) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/stop', data)

/** 视频拆解开始 */
export const apiClipSummaryVideo = (data: {
  series_key: string
  summary_prompt_num: number
}) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/clip_summary', data)

export const apiGetPromptInfo = (data: {
  prompt_type: number // 1: 单集拆解，2: 全集拆解
  prompt_id: number
}) => httpClient.post<ApiResponse<{ prompts: {
  [key: string]: string
} }>>('/videoclip/prompt_info', data)

export const apiAddPrompt = (data: {
  prompt_type: number // 1: 单集拆解，2: 全集拆解
  prompts: {
    [key: string]: string
  }
}) => httpClient.post<ApiResponse<{
  success: boolean
  prompts: {
    [key: string]: string
  }
}>>('/videoclip/prompt_add', data)

export const apiUpdatePrompt = (data: {
  prompt_type: number // 1: 单集拆解，2: 全集拆解
  prompt_id: number
  prompts: {
    [key: string]: string
  }
}) => httpClient.post<ApiResponse<{
  success: boolean
}>>('/videoclip/prompt_update', data)

export const apiGetVocalStatus = (data: {
  series_key: string
}) => httpClient.post<ApiResponse<{
  vocal_status: number
}>>('/videoclip/vocal_status', data)

export const apiUpdateVocalStatus = (data: {
  series_key: string
  vocal_desc: string
  ocr_lang: string // 中文或者英文或者无
  asr_lang: string // 中文或者英文或者无
}) => httpClient.post<ApiResponse<{
  success: boolean
}>>('/videoclip/vocal_extract', data)

export const apiSaveLang = (data: {
  series_key: string
  ocr_lang: string // 中文或者英文或者无
  asr_lang: string // 中文或者英文或者无
}) => httpClient.post<ApiResponse<{
  success: boolean
}>>('/vocal/lang_save', data)

export const apiGenerateRoleExtract = (data: {
  series_key: string
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/role_extract', data)

export const apiGetRoleState = (data: {
  series_key: string
}) => httpClient.post<ApiResponse<{
  status: number // 0:未提取 1:提取中 2:提取完毕
  tips: string
}>>('/vocal/refresh_vocal_status', data)
