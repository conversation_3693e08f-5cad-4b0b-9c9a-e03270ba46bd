import { ref } from 'vue'
import { usePrompt } from './use-prompt'

export const videoClipStep = {
  start: 'start',
  pending: 'pending',
  all_pending: 'all_pending',
  completed: 'completed',
  fail: 'fail',
}

const step = ref<M.VideoClipStep>('start')

const setStep = (_step: M.VideoClipStep) => {
  step.value = _step

  if (_step === 'start') {
    const { getPromptList } = usePrompt()
    void getPromptList({
      prompt_type: 1,
    })
  }
}

export const useEpisodeBreakdownStep = () => {
  return {
    step,
    setStep,
  }
}
