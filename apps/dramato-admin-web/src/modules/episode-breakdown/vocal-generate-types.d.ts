declare namespace M {
  namespace VocalGenerate {
    type Vocal = {
      vocal_id?: number // 流水号
      serial_number?: number // 来自级数
      video_url?: string
      start_stamp?: number// 出现起始时间戳 毫秒
      end_stamp?: number// 出现截止时间戳 浩渺
      role?: string // 可以为空字符串
      icon?: string
    }

    type Role = {
      role_id?: number
      name?: string
      desc?: string
      icon?: string
      serial_number?: number // 来自级数
      video_url?: string
      start_stamp?: number// 出现起始时间戳 毫秒
      end_stamp?: number// 出现截止时间戳 浩渺
    }

    type AIVocal = {
      label: string
      vocals: Vocal[]
    }

    type Serial = {
      serial_number: number
      video_url: string
    }

    type VocalGenerateDate = {
      role_generate: number
      vocal_generate: number
      ai_roles: Role[]
      custom_roles: Role[]
      ai_vocals: AIVocal[]
      custom_vocals: Vocal[]
      videos: Serial[]
    }

    type EditRoleParams = {
      role_id: number
      name: string
      desc: string
    }

    type AddRoleParams = {
      name: string
      icon: string
      serial_number: number // 来自级数
      start_stamp: number// 出现起始时间戳 毫秒
      end_stamp: number// 出现截止时间戳 浩渺
    }
  }
}
