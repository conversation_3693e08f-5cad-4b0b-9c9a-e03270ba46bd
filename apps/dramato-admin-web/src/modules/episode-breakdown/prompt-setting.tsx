/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { <PERSON><PERSON>, <PERSON><PERSON>, showAlert } from '@skynet/ui'
import { usePrompt } from './use-prompt'
import { onMounted, ref } from 'vue'
import { apiAddPrompt, apiGetPromptInfo } from './episode-breakdown-api'

type PromptSettingOptions = {
  props: {
    prompt_type: number
  }
}

interface PromptDetails {
  [key: string]: string
}

export const PromptSetting = createComponent<PromptSettingOptions>({
  props: {
    prompt_type: 1,
  },
}, ({ prompt_type }) => {
  const { promptList, getPromptList } = usePrompt()
  const currentPromptId = ref<number>(0)
  const currentNav = ref<string>('synopsis')
  const currentPrompt = ref<PromptDetails>()
  const needEditPromptId = ref<number[]>([])

  const fetchPromptList = () => {
    void getPromptList({ prompt_type }).then(() => {
      currentPromptId.value = promptList.value[0].prompt_id
      void apiGetPromptInfo({ prompt_id: currentPromptId.value, prompt_type }).then(res => {
        currentPrompt.value = res.data?.prompts || {}
        currentNav.value = Object.keys(res.data?.prompts || {})[0]
        console.log('currentPrompt.value', currentPrompt.value)
      })
    })
  }

  onMounted(() => {
    fetchPromptList()
  })

  return () => (
    <x-prompt-setting class="w-[800px] h-[500px] flex flex-col gap-y-2 overflow-hidden">
      <x-prompt-setting-header class="text-md flex justify-between items-center">
        Prompt管理
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const prompt_id = promptList.value[0].prompt_id + 1

          let prePromptId = prompt_id - 1
          if (needEditPromptId.value.includes(prePromptId)) {
            const list = promptList.value.filter(j => !(needEditPromptId.value.includes(j.prompt_id)))
            prePromptId = list[0]?.prompt_id
          }

          void apiGetPromptInfo({ prompt_id: prePromptId, prompt_type }).then(res => {
            currentPrompt.value = res.data?.prompts || {}
            currentPromptId.value = prompt_id
            promptList.value.unshift({ ...promptList.value[0], prompt_id })
            needEditPromptId.value.push(prompt_id || 0)
            console.log('currentPrompt.value', currentPrompt.value)
            currentNav.value = Object.keys(res.data?.prompts || {})[0]
          })
        }}
        >添加
        </Button>
      </x-prompt-setting-header>
      <x-prompt-setting-container class="flex-1 flex gap-x-2 overflow-hidden">
        <x-prompt-setting-nav class="h-full tabs tab-sm tabs-boxed gap-y-2 flex flex-col overflow-y-auto hide-scrollbar">
          {
            promptList && promptList.value.length > 0 && promptList.value.map(i => (
              <a
                role="tab"
                class={mc('tab w-20 min-w-20 max-w-20 bg-gray-300 text-black', currentPromptId.value === i.prompt_id ? 'tab-active' : '')}
                onClick={() => {
                  currentPromptId.value = i.prompt_id
                  let prompt_id = i.prompt_id
                  if (needEditPromptId.value.includes(i.prompt_id)) {
                    const list = promptList.value.filter(j => !(needEditPromptId.value.includes(j.prompt_id)))
                    prompt_id = list[0]?.prompt_id
                  }

                  void apiGetPromptInfo({ prompt_id, prompt_type }).then(res => {
                    currentPrompt.value = res.data?.prompts || {}
                    currentNav.value = Object.keys(res.data?.prompts || {})[0]
                    console.log('currentPrompt.value', currentPrompt.value)
                  })
                }}
              >
                {i.prompt_id}
              </a>
            ))
          }
        </x-prompt-setting-nav>
        <x-prompt-setting-content class="h-full p-2 gap-y-2 flex-1 flex flex-col border border-solid rounded-md border-1 border-gray-300 overflow-hidden">
          <x-prompt-setting-tabs class="tabs-boxed gap-x-2 flex flex-row">
            {
              Object.keys(currentPrompt.value || {}).map((key: string) => {
                return (
                  <a
                    class={mc('tab w-30 min-w-30 max-w-30 bg-gray-300 text-black', currentNav.value === key ? 'tab-active' : '')}
                    onClick={() => {
                      currentNav.value = key
                    }}
                  >
                    {key}
                  </a>
                )
              })
            }
          </x-prompt-setting-tabs>
          {
            !(needEditPromptId.value.includes(currentPromptId.value || 0))
              ? (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    {
                      currentPrompt.value && currentPrompt.value[currentNav.value]
                        ? <Markdown content={currentPrompt.value[currentNav.value]} class="markdown-body bg-white" />
                        : <div class="flex-1 flex justify-center items-center">暂无数据</div>
                    }

                  </x-prompt-setting-tab-panel>
                )
              : (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    {
                      currentPrompt.value
                        ? (
                            <textarea
                              class={mc('textarea textarea-bordered w-full h-full')}
                              value={currentPrompt.value[currentNav.value]}
                              onInput={(e: Event) => {
                                currentPrompt.value = {
                                  ...currentPrompt.value,
                                  [currentNav.value]: (e.target as HTMLTextAreaElement).value,
                                }
                              }}
                            />
                          )
                        : <div class="flex-1 flex justify-center items-center">暂无数据</div>
                    }

                  </x-prompt-setting-tab-panel>
                )
          }
          {
            !!(needEditPromptId.value.includes(currentPromptId.value || 0))
              ? (
                  <x-prompt-setting-tab-panel-btn class="flex justify-end items-center gap-4">
                    <Button class="btn btn-sm" onClick={() => {
                      needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPromptId.value)
                      promptList.value = promptList.value.filter(item => item.prompt_id !== currentPromptId.value)

                      let prompt_id = promptList.value[0]?.prompt_id
                      if (needEditPromptId.value.includes(prompt_id)) {
                        const list = promptList.value.filter(j => !(needEditPromptId.value.includes(j.prompt_id)))
                        prompt_id = list[0]?.prompt_id
                      }

                      void apiGetPromptInfo({ prompt_id, prompt_type }).then(res => {
                        currentPrompt.value = res.data?.prompts || {}
                        currentPromptId.value = promptList.value[0]?.prompt_id
                        currentNav.value = Object.keys(res.data?.prompts || {})[0]
                        console.log('currentPrompt.value', currentPrompt.value)
                      })
                    }}
                    >取消
                    </Button>
                    <Button class="btn btn-sm btn-primary" onClick={() => {
                      const errorKeys: string[] = []
                      const prompts = currentPrompt.value as PromptDetails
                      Object.keys(currentPrompt.value || {}).forEach(key => {
                        if (!prompts[key]) {
                          errorKeys.push(key)
                        }
                      })
                      if (errorKeys.length) {
                        return showAlert(`请填写${errorKeys.join('、')}`, 'error')
                      }
                      void apiAddPrompt({
                        prompt_type,
                        prompts,
                      }).then(() => {
                        showAlert('添加成功', 'success')
                        needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPromptId.value)

                        void getPromptList({ prompt_type }).then(() => {
                          promptList.value = promptList.value.map(item => {
                            if (item.prompt_id !== currentPromptId.value) {
                              return item
                            }
                            return {
                              ...item,
                              ...prompts,
                            }
                          })
                          currentPromptId.value = promptList.value[0].prompt_id
                          void apiGetPromptInfo({ prompt_id: currentPromptId.value, prompt_type }).then(res => {
                            currentPrompt.value = res.data?.prompts || {}
                            currentNav.value = Object.keys(res.data?.prompts || {})[0]
                            console.log('currentPrompt.value', currentPrompt.value)
                          })
                        })
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message || '提交失败', 'error')
                        })
                    }}
                    >确认
                    </Button>
                  </x-prompt-setting-tab-panel-btn>
                )
              : null
          }
        </x-prompt-setting-content>
      </x-prompt-setting-container>
    </x-prompt-setting>
  )
})
