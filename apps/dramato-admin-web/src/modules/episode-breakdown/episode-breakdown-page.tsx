/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, DateTime, EChart, Markdown, MergeClass, openDialog, showAlert, transformInteger } from '@skynet/ui'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { useEpisodeBreakdownStep } from './use-episode-breakdown-step'
import { set } from 'lodash-es'
import { z } from 'zod'
import { useEpisodeBreakdown } from './use-episode-breakdown'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref, watch } from 'vue'
import { getCharactersPartChartData, getPeopleChartData, getPlotChartOption } from './e-chart-data-transform'
import { useClipboard } from '@vueuse/core'
import { apiGenerateRoleExtract, apiGetRoleState, apiGetVideoClipEpisode, apiGetVideoClipInfo, apiGetVocalStatus, apiSaveLang, apiUpdateVocalStatus } from './episode-breakdown-api'
import dayjs from 'dayjs'
import { PromptSetting } from './prompt-setting'
import { usePrompt } from './use-prompt'

type EpisodeBreakdownPageOptions = {
  props: {}
}

// 剧情梗概 剧本 钩子 爽点 镜头拆解 角色分析 角色戏份 台词
const titleMap: any = {
  synopsis: '剧情梗概',
  scripts: '剧本',
  hook: '钩子',
  cool: '爽点',
  storyboard: '镜头拆解',
  characters_inform: '角色分析',
  characters_part: '角色戏份',
  res_srt: '台词',
  // evaluate: '钩子标注',
}

const summaryTitleMap: any = {
  synopsis: '全剧剧情',
  evaluate: '剧集评分',
  people: '角色戏份汇总',
  hook_plot: '钩子分布',
  // hook_names: '钩子名称',
  cool_plot: '爽点分布',
  // cool_names: '爽点名称',
  story_plot: '高光分布',
  // story_names: '高光名称',
}

export const EpisodeBreakdownPage = createComponent<EpisodeBreakdownPageOptions>({
  props: {},
}, props => {
  const { copy, copied } = useClipboard()
  const router = useRouter()
  const { step } = useEpisodeBreakdownStep()
  const {
    videoClipParams,
    submitting,
    currentEpisodeNum,
    currentEpisode,
    clipVideo,
    resetVideoClipParams,
    getVideoClipInfo,
    videoClipInfo,
    stopClipVideoTask,
    clipAllVideo,
    newSeriesKey
  } = useEpisodeBreakdown()
  const { getPromptList, promptList } = usePrompt()
  const vocalStatus = ref(0)
  const Form = CreateForm<M.VideoClipParams>()
  const isGetVocal = ref<boolean>(false)

  const route = useRoute()

  const generateRoleState = ref({
    status: 1,
    tips: '',
  })

  const formRules = z.object({
    // subtitle_type: z.number({
    //   message: '请选择字幕提取方式',
    // }),
    vocal_desc: z.string({
      message: '请输入声纹标注',
    }).min(1),
    single_prompt_num: z.number({
      message: '请选择Prompt编号',
    }).min(1),
  })

  const { error, validateAll } = useValidator(videoClipParams, formRules)

  const updateVocalStatus = () => {
    void apiGetVocalStatus({ series_key: route.params.seriesKey as string }).then(res => {
      vocalStatus.value = res.data?.vocal_status || 0
    })
  }
  const languageKeys = {
    '': '无',
    'zh-CN': '中文',
    'en': '英文',
    'ja': '日语',
    'ko': '韩语',
    'es': '西语',
    'fr': '法语',
    'pt': '葡语',
    'id': '印尼语'
  }

  const circleRefreshGenerateNpcProgress = () => {
    const process = () => {
      // void apiGetRoleState({ series_key: route.params.seriesKey as string }).then(res => {
      // generateRoleState.value = res?.data as {
      //   status: number
      //   // 0:未提取 1:提取中 2:提取完毕
      //   tips: string
      // }
      updateVocalStatus()

      setTimeout(process, 5000)
      // })
    }

    process()
  }

  const getLanguageText = (val: string) => {
    if (!val) {
      return '无'
    } else {
      return JSON.parse(JSON.stringify(languageKeys))[val]
    }
  }


  onMounted(() => {
    resetVideoClipParams({ series_key: route.params.seriesKey as string })
    void getVideoClipInfo({ series_key: route.params.seriesKey as string })

    updateVocalStatus()
    circleRefreshGenerateNpcProgress()
  })

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  return () => (
    <MergeClass tag="div" baseClass="block space-y-4 py-4">
      {/* 面包屑导航 */}
      <section class="breadcrumbs text-sm px-4">
        <ul>
          <li><RouterLink to="/material-push">素材推送</RouterLink></li>
          <li>AI拆剧本</li>
        </ul>
      </section>

      {/* 过滤表单 */}
      <section class="bg-base-100 rounded-lg px-4 pt-4 pb-1 flex flex-col justify-center gap-y-8">
        {/* <PromptSetting prompt_type={1} /> */}
        <ul class="steps w-full">
          <li class={mc('step', ['start', 'pending', 'all_pending', 'completed', 'fail'].includes(step.value) ? 'step-primary' : '')}>开始</li>
          <li class={mc('step', ['pending', 'all_pending', 'completed', 'fail'].includes(step.value) ? 'step-primary' : '')}>单集拆解中</li>
          <li class={mc('step', ['all_pending', 'completed', 'fail'].includes(step.value) ? 'step-primary' : '')}>全集拆解中</li>
          <li class={mc('step', ['completed', 'fail'].includes(step.value) ? 'step-primary' : '', step.value === 'fail' ? 'step-error' : '')}>{step.value === 'fail' ? '拆解失败' : '拆解完成'}</li>
        </ul>
        {
          step.value === 'start'
            ? (
              <>
                <Form
                  class="grid gap-y-3 grid-cols-1"
                  hasAction={false}
                  error={error.value}
                  onChange={(path, value: any) => {
                    set(videoClipParams.value, path, value)
                  }}
                  items={[
                    [
                      '短剧名称',
                      '',
                      {
                        type: 'custom',
                        render: () => route.params.episodeName,
                      },
                    ],
                    [
                      '短剧总集数',
                      '',
                      {
                        type: 'custom',
                        render: () => route.params.episodeNum,
                      },
                    ],
                    [
                      '已上传集数',
                      '',
                      {
                        type: 'custom',
                        render: () => videoClipInfo.value?.updated_count,
                      },
                    ],
                    [
                      '资源ID',
                      '',
                      {
                        type: 'custom',
                        render: () => videoClipInfo.value?.series_resource_id,
                      },
                    ],
                    [
                      '剧集ID',
                      '',
                      {
                        type: 'custom',
                        render: () => videoClipInfo.value?.series_key,
                      },
                    ],
                    [
                      '内嵌字幕语言',
                      '',
                      {
                        type: 'custom',
                        render: () => getLanguageText(videoClipInfo.value?.vocal_subtitle_language_code || ''),
                      },
                    ],
                    [
                      '内嵌声音语言',
                      '',
                      {
                        type: 'custom',
                        render: () => getLanguageText(videoClipInfo.value?.vocal_language_code || ''),
                      },
                    ],
                    [
                      '上传字幕语言',
                      '',
                      {
                        type: 'custom',
                        render: () => getLanguageText(videoClipInfo.value?.subtitle_up_language_code || ''),
                      },
                    ],
                    [
                      '拆剧使用台词语言',
                      '',
                      {
                        type: 'custom',
                        render: () => getLanguageText(videoClipInfo.value?.subtitle_language_code || ''),
                      },
                    ],
                    [
                      '',
                      '',
                      {
                        type: 'custom',
                        render: () => (
                          <Button class="btn btn-outline btn-sm w-[80px]" onClick={() => {
                            void apiSaveLang({
                              series_key: newSeriesKey.value,
                              ocr_lang: videoClipParams.value.ocr_lang,
                              asr_lang: videoClipParams.value.asr_lang,
                            }).then(() => {
                              showAlert('保存成功')
                            }).catch(() => {
                              showAlert('保存失败')
                            })
                          }}
                          >
                            保存修改
                          </Button>
                        ),
                      },
                    ],
                    // [
                    //   '字幕提取',
                    //   '',
                    //   {
                    //     type: 'custom',
                    //     render: () => (
                    //       <Button
                    //         class="btn btn-outline btn-sm !w-fit"
                    //         disabled={[1, 3].includes(generateRoleState.value.status) || !videoClipParams.value.ocr_lang || !videoClipParams.value.asr_lang}
                    //         onClick={() => {
                    //           void apiGenerateRoleExtract({ series_key: newSeriesKey.value }).then(() => {
                    //             circleRefreshGenerateNpcProgress()
                    //           })
                    //         }}
                    //       >
                    //         {generateRoleState.value.status === 3 ? '字幕提取，排队中...' : generateRoleState.value.status === 1 ? generateRoleState.value.tips : '开始提取'}
                    //       </Button>
                    //     ),
                    //   },
                    // ],
                  ]}
                  data={videoClipParams.value}
                />
                <Form
                  class="grid gap-y-3 grid-cols-1"
                  hasAction={false}
                  error={error.value}
                  onChange={(path, value: any) => {
                    set(videoClipParams.value, path, value)
                  }}
                  items={[
                    [
                      () => (
                        <x-title class="flex justify-between items-center">
                          {requiredLabel('声纹标注')}
                          <Button
                            class="btn-sm btn-primary btn"
                            disabled={[1, 3].includes(generateRoleState.value.status) || !videoClipParams.value.ocr_lang || !videoClipParams.value.asr_lang}
                            onClick={() => {
                              void router.push(`/vocal-generate/${newSeriesKey.value}`)
                            }}
                          >
                            开始标注
                          </Button>
                        </x-title>
                      ),
                      'vocal_desc',
                      {
                        type: 'textarea',
                        placeholder: '请输入声纹标注',
                        disabled: true,
                        rows: 10,
                      },
                    ],
                    [
                      requiredLabel('拆解集数'),
                      'serial_numbers',
                      {
                        type: 'text',
                        placeholder: '请填写要拆解的集数，以英文逗号隔开，例如：1,2,5,9,26',
                      },
                    ],
                    [
                      requiredLabel('Prompt编号'),
                      'single_prompt_num',
                      {
                        type: 'select',
                        placeholder: '请选择字幕提取方式',
                        options: promptList.value.map(i => {
                          return {
                            label: dayjs(i.created * 1000).format('YYYY-MM-DD HH:mm:ss') + `/ 编号-${i.prompt_id}`,
                            value: i.prompt_id,
                          }
                        }),
                      },
                      {
                        transform: transformInteger,
                      },
                    ],
                    [
                      '',
                      '',
                      {
                        type: 'custom',
                        render: () => (
                          <Button class="btn btn-outline btn-sm w-[80px]" onClick={() => {
                            openDialog({
                              title: '高级设置',
                              body: () => <PromptSetting prompt_type={1} />,
                              beforeClose: () => {
                                void getPromptList({ prompt_type: 1 })
                              },
                              mainClass: 'w-[820px] flex justify-center items-center',
                              customClass: '!w-[820px]',
                            })
                          }}
                          >高级设置
                          </Button>
                        ),
                      },
                    ],
                  ]}
                  data={videoClipParams.value}
                />
              </>

            )
            : null
        }

        {
          step.value !== 'start'
            ? (
              <div class="flex flex-col gap-y-2 text-md relative">
                <div class="pr-[200px] w-full truncate" title={route.params.episodeName as string || ''}>短剧名称: {route.params.episodeName}</div>
                <div class="pr-[200px]">短剧总集数: {route.params.episodeNum}</div>
                <div class="pr-[200px]">已上传集数: {videoClipInfo.value?.updated_count}</div>
                <div class="w-full truncate">声纹标注:
                  <div class="p-4 border-solid border rounded-md">
                    <Markdown content={videoClipParams.value?.vocal_desc || '无'} class="markdown-body w-full h-24 overflow-auto bg-white" />
                  </div>
                </div>
                <div class="pr-[200px]">声纹最后更新时间: <DateTime value={videoClipInfo.value?.vocal_updated ? videoClipInfo.value?.vocal_updated * 1000 : undefined} /></div>
                <div class="pr-[200px]">剧集更新时间：<DateTime value={videoClipInfo.value?.episode_updated ? videoClipInfo.value?.episode_updated * 1000 : undefined} /></div>
                <Button class="btn  btn-primary btn-sm absolute right-[102px] top-0" onClick={() => {
                  stopClipVideoTask({
                    series_key: newSeriesKey.value,
                    desc: `确认重新单集拆解【${route.params.episodeName as string || ''}】吗？`,
                    title: '重新单集拆解',
                    clip_type: 1,
                  })
                }}
                >
                  单集拆解
                </Button>

                <Button
                  class="btn  btn-primary btn-sm absolute right-[0px] top-0"
                  onClick={() => {
                    clipAllVideo({ series_key: newSeriesKey.value, episodeName: route.params.episodeName as string })
                  }}
                >
                  全集拆解
                </Button>

                {
                  (step.value === 'pending' && videoClipInfo.value && videoClipInfo.value.curr_total > 0 && videoClipInfo.value?.curr_total !== videoClipInfo.value?.curr_finish)
                    || step.value === 'all_pending'
                    ? (
                      <Button
                        class="btn  btn-warning btn-sm absolute right-[204px] top-0"
                        disabled={!!videoClipInfo?.value?.stopping}
                        onClick={() => {
                          stopClipVideoTask({
                            series_key: newSeriesKey.value,
                            desc: `确认中止拆解【${route.params.episodeName as string || ''}】吗？`,
                            title: '中止拆解',
                            clip_type: step.value === 'pending' ? 1 : 2,
                          })
                        }}
                      >
                        中止任务
                      </Button>
                    )
                    : null
                }

                {
                  step.value === 'pending'
                    ? (
                      <>
                        <progress class={mc(
                          'progress w-full progress-primary',
                          videoClipInfo.value && videoClipInfo.value.curr_total > 0 && videoClipInfo.value?.curr_total === videoClipInfo.value?.curr_finish ? 'hidden' : '',
                          !!videoClipInfo?.value?.stopping ? 'progress-error' : '',
                        )}
                        />
                        <div>{videoClipInfo?.value?.tips || ''}
                        </div>
                      </>
                    )
                    : null
                }

                {
                  step.value === 'all_pending'
                    ? (
                      <>
                        <progress class={mc('progress w-full progress-primary', !!videoClipInfo?.value?.stopping ? 'progress-error' : '')} />
                        <div>{videoClipInfo?.value?.tips}，全集拆解中...</div>
                      </>
                    )
                    : null
                }

                {
                  step.value !== 'fail'
                    ? (
                      <>
                        <div
                          role="tablist"
                          id="tablist"
                          class={mc(
                            'tabs tab-sm tabs-boxed gap-2 flex flex-wrap max-w-full sticky top-[72px] z-shareButton',
                            videoClipInfo.value?.finished && videoClipInfo.value?.finished?.length > 0 ? '' : 'hidden',
                          )}
                        >
                          <a
                            role="tab"
                            class={mc('tab w-20 min-w-20 max-w-20 bg-gray-300 text-black', currentEpisodeNum.value === -1 ? 'tab-active' : '', step.value === 'completed' ? '' : 'hidden')}
                            onClick={() => {
                              currentEpisodeNum.value = -1
                              window.scrollTo({ top: 0, behavior: 'smooth' })
                            }}
                          >
                            全集
                          </a>
                          {
                            (videoClipInfo.value?.finished as number[])?.map(serial_number => (
                              <a
                                role="tab"
                                class={mc('tab w-20 min-w-20 max-w-20 bg-gray-300 text-black', currentEpisodeNum.value === serial_number ? 'tab-active' : '')}
                                onClick={() => {
                                  currentEpisodeNum.value = serial_number
                                  void apiGetVideoClipEpisode({
                                    serial_number,
                                    series_key: newSeriesKey.value,
                                  }).then(res => {
                                    currentEpisode.value = res.data?.episode
                                    window.scrollTo({ top: 0, behavior: 'smooth' })
                                  })
                                }}
                              >
                                {serial_number}
                              </a>
                            ))
                          }
                          <x-cat-dot
                            class={mc(
                              'tabs-boxed  absolute bottom-[-50px] z-shareButton px-2',
                              videoClipInfo.value?.finished && videoClipInfo.value?.finished?.length > 0 ? '' : 'hidden',
                            )}
                          >
                            目录锚点:
                            {
                              Object.keys(currentEpisodeNum.value !== -1 ? titleMap : summaryTitleMap).map((key: string) => {
                                return (
                                  <a
                                    class="tab cursor-pointer"
                                    onClick={() => {
                                      const element = document.getElementById(key) as HTMLElement
                                      const tablist = document.getElementById('tablist') as HTMLElement
                                      const top = element.offsetTop - tablist.offsetHeight
                                      window.scrollTo({ top, behavior: 'smooth' })
                                    }}
                                  >
                                    {currentEpisodeNum.value !== -1 ? titleMap[key] : summaryTitleMap[key]}
                                  </a>
                                )
                              })
                            }
                          </x-cat-dot>
                        </div>
                        {
                          currentEpisodeNum.value !== -1
                            ? (
                              videoClipInfo.value?.finished && videoClipInfo.value?.finished?.length > 0
                                ? (
                                  <>

                                    <div class="pr-[200px] mt-[52px]">单集拆解时间: <DateTime value={videoClipInfo.value?.single_updated ? videoClipInfo.value?.single_updated * 1000 : undefined} /></div>
                                    <div class="pr-[200px]">单集拆解Prompt编号: {currentEpisode.value?.prompt_num || '无'}</div>

                                    {
                                      Object.keys(currentEpisode.value || {}).map((key: string) => {
                                        if (key === 'serial_number' || key === 'prompt_num') return null
                                        if (key === 'storyboard') {
                                          let storyboard = ''
                                          return (
                                            <div class="flex flex-col gap-y-2" id={key}>
                                              <h1 class="text-lg font-medium flex justify-between items-center">
                                                {titleMap[key]} - {key}
                                                <Button class="btn btn-link" onClick={() => copy(storyboard)}>
                                                  复制
                                                </Button>
                                              </h1>
                                              {
                                                currentEpisode.value?.storyboard?.map((item: string[]) => {
                                                  storyboard = storyboard + '\n' + item.join('\n')
                                                  return (
                                                    <div class="p-4 border-solid border rounded-md">
                                                      <div class="flex flex-col gap-y-2">{item.map((desc: string) => <Markdown content={desc} class="markdown-body bg-white" />)}</div>
                                                    </div>
                                                  )
                                                })
                                              }
                                            </div>
                                          )
                                        }
                                        if (key === 'characters_part') {
                                          return (
                                            <div class="flex flex-col gap-y-2" id={key}>
                                              <h1 class="text-lg font-medium flex justify-between items-center">
                                                {titleMap[key]} - {key}
                                                <Button class="btn btn-link" onClick={() => copy('')}>
                                                  复制
                                                </Button>
                                              </h1>
                                              <EChart option={getCharactersPartChartData(JSON.parse(currentEpisode.value?.characters_part || '{}')?.data)} class="h-[320px]" />
                                            </div>
                                          )
                                        }
                                        const episode = currentEpisode.value as M.VideoClipEpisode
                                        return (
                                          <div class="flex flex-col gap-y-2" id={key}>
                                            <h1 class="text-lg font-medium flex justify-between items-center">
                                              {titleMap[key]} - {key}
                                              <Button class="btn btn-link" onClick={() => copy(episode[key])}>复制</Button>
                                            </h1>
                                            {!!episode && episode[key]
                                              ? (
                                                <div class="p-4 border-solid border rounded-md">
                                                  <Markdown content={episode[key]} class="markdown-body bg-white" />
                                                </div>
                                              )
                                              : null}
                                          </div>
                                        )
                                      })
                                    }
                                  </>
                                )
                                : null

                            )
                            : (
                              <x-all-episode-charts class={mc('flex flex-col gap-y-2 mt-[52px]', ['all_pending', 'completed', 'fail'].includes(step.value) ? '' : 'hidden')}>
                                <div class="pr-[200px] mt-[52px]">全集拆解时间: <DateTime value={videoClipInfo.value?.summary_updated ? videoClipInfo.value?.summary_updated * 1000 : undefined} /></div>
                                <div class="pr-[200px]">全集拆解Prompt编号: {videoClipInfo.value?.summary_prompt_num || '无'}</div>
                                <x-all-episode-charts-plot id="synopsis">
                                  <h1 class="text-lg font-medium flex justify-between items-center">
                                    全剧剧情 - synopsis
                                    <Button class="btn btn-link" onClick={() => copy(videoClipInfo.value?.series?.synopsis || '')}>复制</Button>
                                  </h1>
                                  <div class="p-4 border-solid border rounded-md">
                                    <Markdown content={videoClipInfo.value?.series?.synopsis || ''} class="markdown-body bg-white" />
                                  </div>
                                </x-all-episode-charts-plot>
                                <x-all-episode-charts-plot id="evaluate">
                                  <h1 class="text-lg font-medium flex justify-between items-center">
                                    剧集评分 - evaluate
                                    <Button class="btn btn-link" onClick={() => copy(videoClipInfo.value?.series?.evaluate || '')}>复制</Button>
                                  </h1>
                                  <div class="p-4 border-solid border rounded-md">
                                    <Markdown content={videoClipInfo.value?.series?.evaluate || ''} class="markdown-body bg-white" />
                                  </div>
                                </x-all-episode-charts-plot>
                                <x-all-episode-charts-people id="people">
                                  <h1 class="text-lg font-medium">角色戏份汇总 - people</h1>
                                  {videoClipInfo.value?.series?.people && <EChart class="h-[320px]" option={getPeopleChartData(videoClipInfo.value?.series?.people || {})} />}
                                </x-all-episode-charts-people>
                                <x-all-episode-charts-plot id="hook_plot">
                                  <h1 class="text-lg font-medium">钩子分布 - hook_plot</h1>
                                  {videoClipInfo.value?.series?.hook_plot && <EChart class="h-[320px]" option={getPlotChartOption(videoClipInfo.value?.series?.hook_plot || {}, videoClipInfo.value?.series?.hook_names)} />}
                                </x-all-episode-charts-plot>
                                <x-all-episode-charts-plot id="cool_plot">
                                  <h1 class="text-lg font-medium">爽点分布 - cool_plot</h1>
                                  {videoClipInfo.value?.series?.cool_plot && <EChart class="h-[320px]" option={getPlotChartOption(videoClipInfo.value?.series?.cool_plot || {}, videoClipInfo.value?.series?.cool_names)} />}
                                </x-all-episode-charts-plot>
                                <x-all-episode-charts-plot id="story_plot">
                                  <h1 class="text-lg font-medium">高光分布 - story_plot</h1>
                                  {videoClipInfo.value?.series?.story_plot && <EChart class="h-[320px]" option={getPlotChartOption(videoClipInfo.value?.series?.story_plot || {}, videoClipInfo.value?.series?.story_names)} />}
                                </x-all-episode-charts-plot>
                              </x-all-episode-charts>
                            )
                        }
                      </>

                    )
                    : null
                }

                {
                  step.value === 'fail'
                    ? <div>AI 视频拆解失败，请重新尝试</div>
                    : null
                }

              </div>
            )
            : null
        }

        <div class="flex justify-end gap-x-2 pb-2">
          {
            step.value === 'start'
              ? (
                <>
                  <Button class={mc('btn btn-primary btn-sm')}
                    disabled={isGetVocal.value}
                    onClick={() => {
                      if (isGetVocal.value) {
                        return
                      }
                      isGetVocal.value = true
                      void apiUpdateVocalStatus({
                        series_key: newSeriesKey.value,
                        vocal_desc: videoClipParams.value.vocal_desc || '',
                        ocr_lang: videoClipParams.value.ocr_lang || '',
                        asr_lang: videoClipParams.value.asr_lang || '',
                      }).then((res: ApiResponse<{ success: boolean }>) => {
                        // if (!res.data?.success) {
                        //   showAlert('声纹提取失败')
                        //   return
                        // }
                        showAlert('声纹提取成功')
                        updateVocalStatus()
                      }).catch(() => {
                        showAlert('声纹提取失败')
                      }).finally(() => {
                        isGetVocal.value = false
                      })
                    }}
                  >
                    声纹提取 {vocalStatus.value === 1 ? '（已提取）' : vocalStatus.value === 2 ? '（已失败）' : isGetVocal.value ? '（提取中）' : ''}
                  </Button>
                  <Button
                    class="btn btn-sm"
                    onClick={() => {
                      void apiGetVideoClipInfo({ series_key: newSeriesKey.value }).then(res => {
                        videoClipInfo.value = res.data as M.VideoClipInfo
                        if (videoClipInfo.value.analyse_status !== 0) {
                          const { setStep } = useEpisodeBreakdownStep()
                          const step = ['start', 'pending', 'pending', 'all_pending', 'completed', 'fail'][videoClipInfo.value?.analyse_status ?? 0] as M.VideoClipStep
                          setStep(step)
                        } else {
                          void router.push('/material-push')
                        }
                      })
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    class={mc('btn btn-primary btn-sm')}
                    disabled={submitting.value}
                    onClick={() => {
                      const exclude: string[] = []
                      if (!validateAll({ exclude })) {
                        return
                      }
                      void clipVideo({ series_key: newSeriesKey.value })
                    }}
                  >
                    {submitting.value && <span class="loading loading-spinner size-4" />}
                    {submitting.value ? '提交中' : '确定'}
                  </Button>
                </>
              )
              : null
          }
        </div>
      </section>
    </MergeClass>
  )
})

export default EpisodeBreakdownPage
