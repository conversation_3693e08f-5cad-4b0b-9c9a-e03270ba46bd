import { httpClient } from 'src/lib/http-client'

/** 27. 声纹主界面 */
export const apiGetVocalData = (data: {
  series_key: string
}) =>
  httpClient.post<ApiResponse<M.VocalGenerate.VocalGenerateDate>>('/vocal/main_page', data)

export const apiGenerateAIVocal = (data: {
  series_key: string
}) => httpClient.post<ApiResponse<M.VocalGenerate.VocalGenerateDate>>('/vocal/ai_vocal_generate', data)

/** 28. 自定义声纹添加 */
export const apiAddCustomerVocal = (data: {
  series_key: string
  serial_number: number // 来自级数
  start_stamp: number// 出现起始时间戳 毫秒
  end_stamp: number// 出现截止时间戳 浩渺
  role: string
  icon: string
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/vocal_add', data)

/** 28. 自定义声纹添加 */
export const apiDeleteCustomerVocal = (data: {
  vocal_id: number // 来自级数
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/vocal_delete', data)

export const apiEditVocal = (data: {
  vocal_id: number // 来自级数
  start_stamp: number// 出现起始时间戳 毫秒
  end_stamp: number// 出现截止时间戳 浩渺
  role: string
  icon: string
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/vocal_label', data)

export const apiDeleteRole = (data: {
  role_id: number
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/role_delete', data)

export const apiEditRole = (data: M.VocalGenerate.Role) => httpClient.post<ApiResponse<{ success: boolean }>>('/vocal/role_edit', data)
