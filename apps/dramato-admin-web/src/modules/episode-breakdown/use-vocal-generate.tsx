/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { useEpisodeClipMaterialPlayer } from '../episode-clip-material/use-episode-clip-material-player'
import { useVocalGeneratePlayer } from './vocal-generate-components/use-vocal-generate-player'
import { openDialog, showAlert } from '@skynet/ui'
import { apiAddCustomerVocal, apiDeleteCustomerVocal, apiDeleteRole, apiEditRole, apiEditVocal, apiGenerateAIVocal, apiGetVocalData } from './vocal-generate-api'

export const useVocalGenerate = () => {
  return {
    seriesKey,
    vocalGenerateData,
    getVocalGenerateData,
    currentNpc,
    onAddCustomNpc,
    onSerialNumberChange,
    onNpcCardEdit,
    onDeleteNpc,
    currentVocal,
    onAIVocalGenerateClick,
    vocalOrigin,
    onVocalEdit,
    onDeleteVocal,
    onAddVocal,
    onConfirm,
    resetPlayer,
    onPre,
    onNext,
  }
}

const currentNpc = ref<M.VocalGenerate.Role>({})
const currentVocal = ref<M.VocalGenerate.Vocal>({})
const vocalOrigin = ref<'ai_generate' | 'customer'>('ai_generate')
const seriesKey = ref('')

const InitVocalGenerateData = {
  role_generate: 1,
  vocal_generate: 1,
  ai_roles: [],
  custom_roles: [],
  ai_vocals: [],
  custom_vocals: [],
  videos: [],
}

const vocalGenerateData = ref<M.VocalGenerate.VocalGenerateDate>({ ...InitVocalGenerateData })

const getVocalGenerateData = () => {
  void apiGetVocalData({ series_key: seriesKey.value }).then(res => {
    if (res.code === 200) {
      vocalGenerateData.value = (res.data || { ...InitVocalGenerateData })
    }
  })
}

const onAddCustomNpc = () => {
  onSerialNumberChange(vocalGenerateData.value.videos[0].serial_number, 1)
}

const onSerialNumberChange = (serialNumber: number, actionType: number, playerTitle?: string) => {
  currentNpc.value = {}
  const {
    updateVocalGeneratePlayer,
  } = useVocalGeneratePlayer()
  const {
    changeVideoUrl,
  } = useEpisodeClipMaterialPlayer()

  updateVocalGeneratePlayer({
    serialNumber,
    actionType,
    playerTitle: playerTitle ? playerTitle : actionType === 2 ? '自定义NPC' : '自定义声纹',
    videoList: actionType === 2 ? [] : vocalGenerateData.value.custom_vocals,
  })

  const url = vocalGenerateData.value.videos.find((item: M.VocalGenerate.Serial) => item.serial_number === serialNumber)?.video_url || ''

  changeVideoUrl({
    url,
    startTime: 0,
    endTime: 0,
  })
}

const onNpcCardEdit = (npc: M.VocalGenerate.Role, origin: 'ai_generate' | 'customer') => {
  if (!npc || !npc.serial_number) return
  resetPlayer()
  currentNpc.value = npc

  const {
    updateVocalGeneratePlayer,
  } = useVocalGeneratePlayer()

  updateVocalGeneratePlayer({
    serialNumber: npc.serial_number,
    actionType: 2,
    playerTitle: `${origin === 'ai_generate' ? '' : '自定义'}NPC-${npc.name}`,
    videoList: [],
  })

  const {
    changeVideoUrl,
  } = useEpisodeClipMaterialPlayer()

  const url = vocalGenerateData.value.videos.find((item: M.VocalGenerate.Serial) => item.serial_number === npc.serial_number)?.video_url || ''

  changeVideoUrl({
    url,
    startTime: npc.start_stamp || 0,
    endTime: npc.end_stamp || 0,
  })
}

const onDeleteNpc = (npc: M.VocalGenerate.Role) => {
  const hideDeleteDialog = openDialog({
    title: '删除NPC',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-stop-clip-video-confirm-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-stop-clip-video-confirm-body>【{ npc.name }】将被删除，删除后无法恢复，是否确认删除？</x-stop-clip-video-confirm-body>
        <x-stop-clip-video-confirm-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiDeleteRole({ role_id: npc.role_id || 0 }).then(() => {
              showAlert('删除成功')
              getVocalGenerateData()
              hideDeleteDialog()
            }).catch(() => {
              showAlert('删除失败')
            })
          }}
          >
            确定
          </button>
        </x-stop-clip-video-confirm-footer>
      </x-stop-clip-video-confirm-confirm-dialog>
    ),
  })
}

/** ai声纹聚合类生成 */
const onAIVocalGenerateClick = async () => {
  try {
    vocalGenerateData.value.vocal_generate = 0
    await apiGenerateAIVocal({ series_key: seriesKey.value })
    vocalGenerateData.value.vocal_generate = 1
    showAlert('AI声纹聚合类生成中，请稍后查看')
    getVocalGenerateData()
  } catch (error: any) {
    showAlert(error.response.data.message || '提交失败', 'error')
  }
}

const onVocalEdit = (vocal: M.VocalGenerate.Vocal, origin: 'ai_generate' | 'customer') => {
  if (!vocal || !vocal.serial_number) return
  resetPlayer()
  currentVocal.value = vocal
  vocalOrigin.value = origin
  currentNpc.value = {
    icon: vocal.icon || '',
  }

  const {
    updateVocalGeneratePlayer,
  } = useVocalGeneratePlayer()

  const videoList: M.VocalGenerate.Vocal[] = []

  if (origin === 'ai_generate') {
    vocalGenerateData.value.ai_vocals.forEach(i => {
      videoList.push(...i.vocals)
    })
  } else {
    videoList.push(...vocalGenerateData.value.custom_vocals)
  }

  updateVocalGeneratePlayer({
    serialNumber: vocal.serial_number,
    actionType: 4,
    playerTitle: `${origin === 'ai_generate' ? '声纹聚合' : '自定义声纹'}-${vocal.role || '未知'}`,
    videoList,
  })

  const {
    changeVideoUrl,
  } = useEpisodeClipMaterialPlayer()

  const url = vocalGenerateData.value.videos.find((item: M.VocalGenerate.Serial) => item.serial_number === vocal.serial_number)?.video_url || ''

  changeVideoUrl({
    url,
    startTime: vocal.start_stamp || 0,
    endTime: vocal.end_stamp || 0,
  })

  const element = document.getElementById(origin + currentVocal.value.vocal_id) as HTMLElement
  element.scrollIntoView()
}

const onDeleteVocal = (vocal: M.VocalGenerate.Vocal) => {
  const hideDeleteDialog = openDialog({
    title: '删除声纹',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-stop-clip-video-confirm-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-stop-clip-video-confirm-body>删除后无法恢复，确认删除该条声纹吗？</x-stop-clip-video-confirm-body>
        <x-stop-clip-video-confirm-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiDeleteCustomerVocal({ vocal_id: vocal.vocal_id || 0 }).then(() => {
              showAlert('删除成功')
              getVocalGenerateData()
              hideDeleteDialog()
              resetPlayer()
            }).catch(() => {
              showAlert('删除失败')
            })
          }}
          >
            确定
          </button>
        </x-stop-clip-video-confirm-footer>
      </x-stop-clip-video-confirm-confirm-dialog>
    ),
  })
}

const onAddVocal = () => {
  onSerialNumberChange(vocalGenerateData.value.videos[0].serial_number, 3, '自定义声纹')
  currentVocal.value = {}
}

const validateRole = (role: M.VocalGenerate.Role) => {
  if (!role.name || role.name.length <= 0) {
    return 'NPC名称不能为空'
  }

  if (!role.start_stamp && !role.end_stamp) {
    return '开始时间/结束时间不能为空'
  }

  if (!role.icon) {
    return 'NPC头像不能为空'
  }

  return ''
}

const validateVocal = (vocal: M.VocalGenerate.Vocal, needValidSerialNumber = true) => {
  if (needValidSerialNumber && (!vocal.serial_number || vocal.serial_number <= 0)) {
    return '剧集不能为空'
  }

  if (!vocal.start_stamp && !vocal.end_stamp) {
    return '开始时间/结束时间不能为空'
  }

  return ''
}

const onConfirm = async () => {
  const { actionType, serialNumber } = useVocalGeneratePlayer()
  const { startTime, endTime } = useEpisodeClipMaterialPlayer()
  const serial_number = serialNumber.value

  if ([1, 2].includes(actionType.value)) {
    const {
      name, start_stamp, end_stamp, icon, role_id, desc,
    } = {
      ...currentNpc.value,
      start_stamp: startTime.value,
      end_stamp: endTime.value,
    }

    const role: M.VocalGenerate.Role = {
      name, start_stamp, end_stamp, icon,
    }

    const error = validateRole(role)

    if (error) {
      return showAlert(error, 'error')
    }

    try {
      await apiEditRole({
        role_id: role_id || 0,
        icon: icon || '',
        desc: desc || '',
        start_stamp: start_stamp || 0,
        end_stamp: end_stamp || 0,
        name: name || '',
      })

      showAlert('操作成功', 'success')
      void getVocalGenerateData()
      resetPlayer()
    } catch (error: any) {
      showAlert(error.response.data.message || '提交失败', 'error')
    }

    return
  }

  const {
    vocal_id, start_stamp, end_stamp, role,
  } = {
    ...currentVocal.value,
    start_stamp: startTime.value,
    end_stamp: endTime.value,
  }

  const error = validateVocal({ vocal_id, start_stamp, end_stamp, role, serial_number }, actionType.value === 3)

  if (error) {
    return showAlert(error, 'error')
  }

  try {
    if (actionType.value === 3) {
      await apiAddCustomerVocal({
        serial_number: serial_number || 0,
        start_stamp: start_stamp || 0,
        end_stamp: end_stamp || 0,
        role: role || '',
        icon: currentNpc.value.icon || '',
        series_key: seriesKey.value || '',
      })
    }

    if (actionType.value === 4) {
      await apiEditVocal({
        vocal_id: vocal_id || 0,
        start_stamp,
        end_stamp,
        role: role || '',
        icon: currentNpc.value.icon || '',
      })
    }

    showAlert('操作成功', 'success')
    void getVocalGenerateData()
    // resetPlayer()
  } catch (error: any) {
    showAlert(error.response.data.message || '提交失败', 'error')
  }
}

const resetPlayer = () => {
  currentNpc.value = {}
  currentVocal.value = {}
  vocalOrigin.value = 'ai_generate'
  onSerialNumberChange(0, 2)
}

const onPre = () => {
  const { vocal_id } = currentVocal.value

  if (!vocal_id) {
    return
  }

  const { videoList } = useVocalGeneratePlayer()

  const idx = videoList.value.findIndex(item => item.vocal_id === vocal_id)

  if (idx === 0) {
    return showAlert('已经是第一个了', 'error')
  }

  onVocalEdit(videoList.value[idx - 1], vocalOrigin.value)
}

const onNext = () => {
  const { vocal_id } = currentVocal.value

  if (!vocal_id) {
    return
  }

  const { videoList } = useVocalGeneratePlayer()

  const idx = videoList.value.findIndex(item => item.vocal_id === vocal_id)

  if (idx === videoList.value.length - 1) {
    return showAlert('没有更多了', 'error')
  }

  onVocalEdit(videoList.value[idx + 1], vocalOrigin.value)
}
