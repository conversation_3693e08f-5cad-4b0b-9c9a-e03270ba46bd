/* eslint-disable @typescript-eslint/no-explicit-any */
import { onUnmounted, ref } from 'vue'
import { apiClipVideo, apiGetVideoClipEpisode, apiGetVideoClipInfo, apiStopVideoClip } from './episode-breakdown-api'
import { useEpisodeBreakdownStep } from './use-episode-breakdown-step'
import { openDialog, showAlert } from '@skynet/ui'
import { usePrompt } from './use-prompt'
import { ClipVideoSummaryForm } from './clip-video-summary-form'

const InitVideoClipParams: M.VideoClipParams = {
  series_key: '',
  vocal_desc: '', // '1\n10.10 --> 10.5 \n你好...'
  // subtitle_type: undefined, // 1: OCR+ASR 2:ASR 3:OCR
  serial_numbers: '',
  single_prompt_num: undefined,
  ocr_lang: '无',
  asr_lang: '无',
}

const videoClipParams = ref<M.VideoClipParams>(InitVideoClipParams)

const videoClipInfo = ref<M.VideoClipInfo>()

const submitting = ref(false)
const newSeriesKey = ref('')

const currentEpisodeNum = ref<number>()
const currentEpisode = ref<M.VideoClipEpisode>()

const resetClipVideo = ref(false)
const timer = ref(0)

if (sessionStorage.newSeriesKey) {
  newSeriesKey.value = sessionStorage.newSeriesKey
}

export const resetVideoClipParams = ({ series_key }: { series_key: string }) => {
  videoClipParams.value = {
    ...InitVideoClipParams,
    series_key,
  }
}

const getVideoClipInfo = ({ series_key }: { series_key: string }) => {
  const { setStep, step: originStep } = useEpisodeBreakdownStep()

  const next = () => {
    if (resetClipVideo.value) {
      window.clearTimeout(timer.value)
      return
    }
    void apiGetVideoClipInfo({ series_key })
      .then(res => {
        if (resetClipVideo.value) {
          window.clearTimeout(timer.value)
          return
        }
        videoClipInfo.value = res.data as M.VideoClipInfo
        videoClipParams.value.vocal_desc = videoClipInfo.value?.vocal_desc || ''
        videoClipParams.value.asr_lang = videoClipInfo.value?.asr_lang || 'zh-cn'
        videoClipParams.value.ocr_lang = videoClipInfo.value?.ocr_lang || 'zh-cn'
        newSeriesKey.value = videoClipInfo.value?.series_key || ''
        localStorage.setItem('newSeriesKey', newSeriesKey.value)
        // TODO: fail 状态暂时不做跳转处理，当pending 状态处理
        const step = ['start', 'pending', 'pending', 'all_pending', 'completed', originStep.value][videoClipInfo.value?.analyse_status ?? 0] as M.VideoClipStep

        setStep(step)

        if (['completed', 'fail', 'start'].includes(step)) {
          timer.value && window.clearTimeout(timer.value)
          resetClipVideo.value = true

          if (step !== 'start') currentEpisodeNum.value = -1
          return
        }

        if (step.includes('pending')) {
          // if (step === 'pending' && videoClipInfo.value?.curr_total > 0 && videoClipInfo.value?.curr_total === videoClipInfo.value?.curr_finish) {
          //   window.clearTimeout(timer.value)
          //   resetClipVideo.value = true
          // }

          timer.value = window.setTimeout(() => {
            next()
          }, 1000 * 5)

          if (currentEpisodeNum.value !== undefined) {
            return
          }

          if (!videoClipInfo.value?.finished || videoClipInfo.value?.finished.length <= 0) {
            return
          }

          currentEpisodeNum.value = videoClipInfo.value?.finished[0]
          void apiGetVideoClipEpisode({
            serial_number: currentEpisodeNum.value,
            series_key,
          }).then(res => {
            currentEpisode.value = res.data?.episode
          })
        }
      })
      .catch(err => {
        setStep('fail')
        window.clearTimeout(timer.value)
        console.log('get video clip info error', err)
      })
  }

  next()
}

export const clipVideo = ({ series_key }: { series_key: string }) => {
  const { setStep } = useEpisodeBreakdownStep()
  submitting.value = true
  videoClipInfo.value = undefined
  void apiClipVideo({
    ...videoClipParams.value,
    serial_numbers: (videoClipParams.value.serial_numbers as string).split(',').map(Number),
  })
    .then(res => {
      if (res.data?.success) {
        setStep('pending')
        resetClipVideo.value = false
        console.log('clip video success')
        void getVideoClipInfo({ series_key })
      } else {
        showAlert('提交失败', 'error')
        console.log('clip video fail')
      }
    })
    .catch((error: any) => {
      console.log('clip video error', error)
      showAlert(error.response.data.message || '提交失败', 'error')
    })
    .finally(() => {
      submitting.value = false
    })
}

const clipAllVideo = ({ series_key, episodeName }: { series_key: string, episodeName: string }) => {
  const { getPromptList } = usePrompt()

  void getPromptList({ prompt_type: 2 }).then(() => {
    const hideDeleteDialog = openDialog({
      title: '全集拆解',
      mainClass: 'pb-0 px-5',
      body: () => <ClipVideoSummaryForm series_key={series_key} episodeName={episodeName} onHide={() => hideDeleteDialog()} />,
    })
  })
}

const stopClipVideoTask = ({ series_key, desc, title, clip_type }: { series_key: string, desc: string, title: string, clip_type: number }) => {
  const { step, setStep } = useEpisodeBreakdownStep()
  if (clip_type === 1 && title !== '中止拆解') {
    setStep('start')
    resetClipVideo.value = true
    window.clearTimeout(timer.value)
    return
  }

  const hideDeleteDialog = openDialog({
    title,
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-stop-clip-video-confirm-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-stop-clip-video-confirm-body>{desc}</x-stop-clip-video-confirm-body>
        <x-stop-clip-video-confirm-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            if (step.value === 'fail' || step.value === 'completed') {
              setStep(clip_type === 1 ? 'start' : 'pending')
              hideDeleteDialog()
              return
            }
            void apiStopVideoClip({ series_key, clip_type })
              .then(() => {
                if (clip_type === 1 && title === '中止拆解' && (videoClipInfo.value?.finished || [])?.length == 0) {
                  setStep('start')
                  resetClipVideo.value = true
                  window.clearTimeout(timer.value)
                }
                if (clip_type === 2) {
                  setStep('pending')
                  resetClipVideo.value = true
                  window.clearTimeout(timer.value)
                }
                hideDeleteDialog()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message || '提交失败', 'error')
              })
          }}
          >
            确定
          </button>
        </x-stop-clip-video-confirm-footer>
      </x-stop-clip-video-confirm-confirm-dialog>
    ),
  })
}

export const useEpisodeBreakdown = () => {
  onUnmounted(() => { window.clearTimeout(timer.value) })

  return {
    videoClipParams,
    getVideoClipInfo,
    newSeriesKey,
    videoClipInfo,
    clipVideo,
    resetVideoClipParams,
    submitting,
    resetClipVideo,
    currentEpisodeNum,
    currentEpisode,
    stopClipVideoTask,
    clipAllVideo,
  }
}
