declare namespace M {
  type Language = string

  interface BannerSearchProps {
    app_id?: number
    language?: Language
    home_tab_info_module_id?: number
    state?: 1 | 10 // 生效状态，默认不需要传参，1：已生效，10：已停用
    page_info?: {
      page_index?: number
      page_size?: number// 选填参数
    }
  }

  interface BannerListModel {
    id: number// banner_id
    sort_no: number// 排序
    title: string// 标题
    state: number// 状态，1：已生效，10：已停用
    updated: number// 更新时间
    op_user_name: string
    app_id: string
    cover: string
    language: Language
    start_time: number
    end_time: number
    banner_type?: number // banner类型
  }

  interface BannerListRequestResponse {
    total: number
    list: BannerListModel[]
  }

  interface Banner {
    id?: number// banner_id，编辑banner时必传
    app_id?: number | string
    language?: Language
    langs?: Language[]
    sort_no?: number//
    title?: string// 标题
    cover?: string// 图片地址，TODO:也许是oss对象信息
    link_type?: number// 跳转地址类型，1：deeplink，2：h5链接
    link?: string// 链接
    lower_version?: string// 生效版本号
    state?: number// 状态，1：已生效，10：已停用
    updated?: number// 更新时间
    op_user_name?: string
    timing_type?: number// 定时类型，0：不支持，1：定时停用，2：定时开启
    timing_time?: string // 定时生效/停止时间
    start_time?: number
    end_time?: number
    vertical_cover?: string // 竖屏图片地址，TODO:也许是oss对象信息
    bg_color?: string
    label_id?: number
    tags?: BTag[]
    seriesResourceId?: number
    deeplink?: string
    imageType?: number
    commonCover?: string
    home_tab_info_module_id?: number // 模块id
    banner_type?: number // banner类型
  }

  interface BTag {
    label_id: number
    content: string
    language: string
    language_code: string
  }

  // 广告位banner数据
  interface languageItem {
    language: string
    title: string
  }
  interface coverListItem {
    cover?: string
    language?: string[]
  }
  interface AdBanner {
    id?: number // banner_id，编辑banner时必传
    app_id?: number | string // 多个使用,分割开来，新增时支持多个，编辑时候只支持一个
    language?: string // 一个语言
    cover?: string
    start_time?: number
    end_time?: number
    link_type?: number // 跳转地址类型，1：deeplink，2：h5链接
    link?: string // 链接
    bg_color?: string
    home_tab_info_module_id?: number // 模块id
    sort_no?: number //
    show_title?: number // banner标题展示

    langs?: string[] // 多语言
    langs_content?: languageItem[] // 多个语言
    coverList?: coverListItem[]
  }
}
