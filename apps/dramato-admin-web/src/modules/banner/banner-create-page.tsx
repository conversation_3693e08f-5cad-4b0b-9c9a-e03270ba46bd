/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformInteger, transformNumber } from '@skynet/ui'
import dayjs from 'dayjs'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { z } from 'zod'
import { useAppAndLangOptions } from '../options/use-app-options'
import { apiBatchCreateBanner, apiGetLabels, apiGetResourceInfo } from './banner-api'
import { useBanner } from './use-banner'
import { useRouter } from 'vue-router'
import { useBannerCreate } from './use-banner-create'
import { useEpisodeTabs } from '../episode-tabs/use-episode-tabs.tsx'
import { storeToRefs } from 'pinia'

const languageMap: Record<string, string> = {
  ja: '日语',
  en: '英语',
  ko: '韩语',
  es: '西班牙语',
  th: '泰语',
  id: '印尼语',
  vi: '越南语',
  pt: '葡萄牙语',
  tl: '菲律宾语',
  it: '意大利语',
  fr: '法语',
  de: '德语',
  tr: '土耳其语',
  ru: '俄语',
  ms: '马来西亚语',
}

type BannerCreatePageOptions = {
  props: {}
}

export const BannerCreatePage = createComponent<BannerCreatePageOptions>({
  props: {},
}, props => {
  const { colors, getColors, getBannerPreview, bannerPreviewList } = useBanner()
  const { resetCurrentBanner } = useBannerCreate()
  const { currentBanner, bannerMap } = storeToRefs(useBannerCreate())
  const { list: tabList, tabParams, search } = useEpisodeTabs()
  const router = useRouter()
  const resourceInfo = ref<Array<
    {
      series_key: string
      title: string
      default_language_code: string
    }>>([])
  const Form = CreateForm<M.Banner>()
  const linkError = ref(false)
  const coverError = ref(false)

  // onUnmounted(() => {
  //   resetCurrentBanner()
  // })

  const formRules = z.object({
    app_id: z.number().min(1, '请选择生效应用'),
    imageType: z.number().min(1, '请选择图片类型'),
    seriesResourceId: z.number().min(1, '请填写'),
    langs: z.array(z.string()).min(1, '请选择语言'),
    deeplink: z.string().min(1, '请填写deeplink'),
    sort_no: z.number().min(1, '请填写客户端排序').max(9, '客户端排序最大为9'),
    start_time: z.number().refine(e => {
      if (e && currentBanner.value.end_time) return true
      return false
    }, {
      message: '请设置开始和结束时间！',
    }).refine(e => {
      if (e && currentBanner.value.end_time && new Date(e * 1000).getTime() < new Date(currentBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间必须晚于开始时间！',
    }).refine(e => {
      if (currentBanner.value.end_time && new Date().getTime() < new Date(currentBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间需晚于当前时间！',
    }),
    // bg_color: z.string().min(1, '请选择背景颜色'),
  })

  const { error, validateAll } = useValidator(currentBanner, formRules)
  const { appOptions, languageOptions } = useAppAndLangOptions(() => currentBanner.value.app_id)

  const getCurrentLangSerialized = (l: string) => {
    const lang = languageOptions.value.find(e => e.value === l)
    return resourceInfo.value.find(e => lang?.label.includes(e.default_language_code))
  }

  const onSubmit = async () => {
    const exclude: string[] = []
    if (!validateAll({ exclude })) {
      console.log('error', error.value)

      return
    }
    const res = await apiGetResourceInfo(currentBanner.value.seriesResourceId || 0)

    resourceInfo.value = res.data?.list || []

    if (!currentBanner.value.langs) {
      return
    }

    const r: {
      [key: string]: M.Banner
    } = {}
    currentBanner.value.langs.forEach(lang => {
      const link = `${currentBanner.value?.deeplink || ''}${getCurrentLangSerialized(lang)?.series_key || ''}`
      if (!bannerMap.value[lang]) {
        r[lang] = {
          cover: '',
          link: currentBanner.value.link_type === 1 ? link : '',
          label_id: undefined,
        }
        if (currentBanner.value.link_type === 1) {
          void apiGetLabels({
            app_id: +(currentBanner.value.app_id || 0),
            language: lang,
            link,
          })
            .then(rs => {
              bannerMap.value[lang].tags = rs.data?.list || []
            })
        }
      } else {
        r[lang] = {
          ...bannerMap.value[lang],
          link,
        }
      }
    })

    bannerMap.value = r
  }

  onMounted(() => {
    void getColors()

    if (currentBanner.value && Object.keys(bannerMap.value).length > 0) {
      void onSubmit()
    }
  })

  watch(() => currentBanner, () => {
    // if (currentBanner.value.app_id) {
    //   tabParams.value.app_id = Number(currentBanner.value.app_id)
    // }
    // if (currentBanner.value.langs) {
    //   tabParams.value.language = currentBanner.value.langs[0]
    // }
    // console.log(tabParams.value, currentBanner.value)
    // if (tabParams.value.app_id && tabParams.value.language) {
    //   void search()
    // }
    if (currentBanner.value.seriesResourceId) {
      void getBannerPreview(currentBanner.value.seriesResourceId)
    }
  }, { immediate: true, deep: true })

  watch(() => bannerPreviewList, () => {
    if (bannerPreviewList.value.length > 0) {
      currentBanner.value.bg_color = bannerPreviewList.value[0].bg_color
    }
  }, {
    immediate: true,
    deep: true,
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>创建Banner</li>
          </ul>
        ),
        form: () => (
          <x-form-wrap class="flex flex-col gap-y-2 pb-3">
            {/* {JSON.stringify(currentBanner.value)} */}
            <Form
              class="grid flex-1 grid-cols-3 gap-y-2"
              actionClass="col-span-1 flex justify-end"
              error={error.value}
              onChange={(path, value) => {
                set(currentBanner.value || {}, path, value)
              }}
              onSubmit={onSubmit}
              onReset={() => {
                currentBanner.value = {
                  link_type: 1,
                  langs: [],
                }
              }}
              items={[
                {
                  label: requiredLabel('banner类型'),
                  path: 'banner_type',
                  input: {
                    type: 'select',
                    disabled: true,
                    options: [
                      {
                        label: '广告位类型',
                        value: 2,
                        disabled: true,
                      },
                      {
                        label: '剧集类型',
                        value: 1,
                        disabled: true,
                      },
                    ],
                    autoInsertEmptyOption: false,
                    class: 'col-span-1',
                  },
                  class: 'col-span-1',
                },
                {
                  label: requiredLabel('资源ID'),
                  path: 'seriesResourceId',
                  input: {
                    type: 'number',
                  },
                  transform: transformNumber,
                },
                {
                  label: requiredLabel('选择生效应用'),
                  path: 'app_id',
                  input: {
                    type: 'select',
                    options: appOptions.value,
                  },
                  transform: transformNumber,
                },
                {
                  label: requiredLabel('语言'),
                  path: 'langs',
                  input: {
                    type: 'multi-select',
                    options: languageOptions.value,
                  },
                },
                {
                  label: requiredLabel('生效时间'),
                  path: 'start_time',
                  input: {
                    type: 'custom',
                    render: () => (
                      <div class="flex items-center gap-x-2">
                        <div class="input input-bordered input-sm flex items-center gap-2">
                          <input
                            type="datetime-local"
                            value={currentBanner.value.start_time ? dayjs(currentBanner.value.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                            onInput={(e: Event) => currentBanner.value.start_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                          />
                        </div>
                        <span>-</span>
                        <div class="input input-bordered input-sm flex items-center gap-2">
                          <input
                            type="datetime-local"
                            value={currentBanner.value.end_time ? dayjs(currentBanner.value.end_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                            onInput={(e: Event) => currentBanner.value.end_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                          />
                        </div>
                      </div>
                    ),
                  },
                  class: 'col-span-1',
                },
                {
                  label: requiredLabel('跳转类型'),
                  path: 'link_type',
                  transform: transformInteger,
                  input: {
                    type: 'select',
                    options: [
                      {
                        label: 'deeplink',
                        value: 1,
                      },
                      {
                        label: 'h5链接',
                        value: 2,
                      },
                    ],
                    autoInsertEmptyOption: false,
                    class: 'col-span-1',
                  },
                  class: 'col-span-1',
                },
                {
                  label: requiredLabel('Deeplink模板'),
                  path: 'deeplink',
                  input: {
                    type: 'text',
                  },
                  class: 'col-span-1',
                  hint: '将在模板后自动拼接剧集ID',
                },
                {
                  label: <div class="flex items-center">背景色<span class="block text-red-400 text-xs pl-2">注意：此处配置取默认，修改不生效</span></div>,
                  path: 'bg_color',
                  input: {
                    type: 'select',
                    options: colors.value.map(item => ({
                      label: item,
                      value: item,
                    }))
                    ,
                  },
                  class: 'col-span-1',
                  hint: () => currentBanner.value.bg_color
                    ? (
                        <div class="mt-2 !h-4 !w-4 rounded-sm" style={{
                          background: currentBanner.value.bg_color,
                        }}
                        />
                      )
                    : null,
                },
                {
                  label: requiredLabel('封面类型'),
                  path: 'imageType',
                  input: {
                    type: 'radio',
                    options: [
                      { label: '通用封面', value: 1 },
                      { label: '分语种配置封面', value: 2 },
                    ]
                    ,
                  },
                },
                ['所属Tab', 'home_tab_info_module_id', {
                  type: 'text',
                  placeholder: '请输入所属TabId',
                }, { transform: transformNumber }],
                ['客户端排序', 'sort_no', {
                  type: 'number',
                  placeholder: '请输入客户端排序',
                }, { transform: transformNumber }],
              ]}
              data={currentBanner.value}
            />
          </x-form-wrap>

        ),
        tableActions: () => (
          <x-table-actions class="flex w-full flex-col gap-y-2">
            <h1 class="font-semibold">多语言配置表单</h1>
          </x-table-actions>
        ),
        table: () => (
          <div class="flex flex-col gap-y-4 px-4">
            <h1 class="font-semibold">剧名信息</h1>
            {
              currentBanner.value.langs && currentBanner.value.langs.length > 0 && resourceInfo.value.length > 0
                ? (
                    <x-resource-info class="flex flex-col gap-y-2 rounded-lg bg-gray-100 p-4 text-[14px]">
                      {
                        currentBanner.value.langs.map(item => {
                          const l = getCurrentLangSerialized(item)
                          const lang = languageOptions.value.find(i => i.value === item)
                          if (!l) {
                            return (
                              <div class="flex flex-row gap-x-2">
                                <label>{lang?.label}：</label>
                                <label>暂无资源</label>
                              </div>
                            )
                          }
                          return (
                            <div class="flex flex-row gap-x-2">
                              <label>{languageMap[l.default_language_code]}-{l.default_language_code}：</label>
                              <label>{l.title}</label>
                            </div>
                          )
                        })
                      }
                    </x-resource-info>
                  )
                : <x-resource-info class="flex flex-col gap-y-2 rounded-lg bg-gray-100 p-4 text-[14px]"><div class="py-8 text-center">数据为空</div></x-resource-info>
            }
            <h1 class="font-semibold">跳转链接预览</h1>
            <x-links class="w-full">
              {
                Object.keys(bannerMap.value).filter(key => getCurrentLangSerialized(key)).map(key => (
                  <x-form class="flex flex-row items-center gap-x-2 pb-3" key={key}>
                    <label class="min-w-[100px]">{languageOptions.value.find(i => i.value === key)?.label}：</label>
                    <label class={mc('input input-bordered flex items-center gap-1 h-8 flex-1')}>
                      <input
                        type="text"
                        class={mc('grow w-100px')}
                        value={bannerMap.value[key].link}
                        onInput={(e: Event) => {
                          bannerMap.value[key].link = ((e.target as HTMLInputElement).value || '')
                        }}
                        onBlur={() => {
                          if (!bannerMap.value[key].link) {
                            return
                          }
                          void apiGetLabels({
                            app_id: +(currentBanner.value.app_id || 0),
                            language: key,
                            link: bannerMap.value[key].link,
                          })
                            .then(rs => {
                              bannerMap.value[key].tags = rs.data?.list || []
                            })
                        }}
                      />
                    </label>
                  </x-form>
                ))
              }
              {linkError.value && <div class="text-error-6 truncate text-xs">必填</div>}
            </x-links>
            <hr class="my-4" />
            <h1 class="font-semibold">剧标签（选择一个标签展示）</h1>
            <x-links class="w-full">
              {
                Object.keys(bannerMap.value).filter(key => getCurrentLangSerialized(key)).map(key => (
                  <x-form class="flex flex-row items-center gap-x-2 pb-3" key={key}>
                    <label class="min-w-[100px]">{languageOptions.value.find(i => i.value === key)?.label}：</label>
                    <select
                      class="select-bordered select-sm select w-full"
                      value={bannerMap.value[key].label_id}
                      onInput={(e: Event) => {
                        bannerMap.value[key].label_id = +((e.target as HTMLInputElement).value || '')
                      }}
                    >
                      <option value="">- 空 -</option>
                      {bannerMap.value[key]?.tags?.map((option: M.BTag) => (
                        <option value={option.label_id}>{option.content}</option>
                      ))}
                    </select>
                  </x-form>
                ))
              }
            </x-links>
            <hr class="my-4" />
            <h1 class="font-semibold">封面配置：</h1>
            {bannerPreviewList.value.map((item, index) => {
              return (
                <div class="" key={index}>
                  <div class="flex items-center gap-2">
                    <label>英语：</label>
                    <img class="size-[100px]" src={item.en_cover?.includes('https://') ? item.en_cover : 'https://static-v1.mydramawave.com/banner/cover/' + item.en_cover} />
                  </div>
                  <div class="my-2 flex items-center gap-2">
                    <label>兜底图：</label>
                    <img class="size-[100px]" src={item.other_cover?.includes('https://') ? item.other_cover : 'https://static-v1.mydramawave.com/banner/cover/' + item.other_cover} />
                  </div>
                  {(item.cover || []).map((i: any) => {
                    return (
                      <div class="my-2 flex items-center gap-2">
                        <label>{i.language.join('、')}：</label>
                        <img class="size-[100px]" src={i.cover?.includes('https://') ? i.cover : 'https://static-v1.mydramawave.com/banner/cover/' + i.cover} />
                      </div>
                    )
                  })}
                </div>
              )
            })}
            <div class="flex justify-end gap-x-2 px-[20px]">
              <Button class="btn  btn-sm" onClick={() => {
                void router.push('/banner')
              }}
              >取消
              </Button>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                const exclude: string[] = []
                if (!validateAll({ exclude })) {
                  console.log('error', error.value)

                  return
                }
                let hasEmptyLink = false
                // let hasEmptyCover = false

                Object.keys(bannerMap.value).filter(key => getCurrentLangSerialized(key)).forEach(lang => {
                  // if (currentBanner.value.imageType === 2 && !bannerMap.value[lang].cover) {
                  //   hasEmptyCover = true
                  // }
                  if (!bannerMap.value[lang].link) {
                    hasEmptyLink = true
                  }
                })

                // if (currentBanner.value.imageType === 1 && !currentBanner.value.commonCover) {
                //   hasEmptyCover = true
                // }
                linkError.value = hasEmptyLink
                // coverError.value = hasEmptyCover
                if (hasEmptyLink) {
                  return
                }

                const r: M.Banner[] = []
                Object.keys(bannerMap.value).forEach(lang => {
                  const i = {
                    ...bannerMap.value[lang],
                    ...currentBanner.value,
                    title: getCurrentLangSerialized(lang)?.title,
                    language: lang,
                    app_id: currentBanner.value.app_id + '',
                    series_resource_id: currentBanner.value.seriesResourceId,
                    state: 1,
                    cover: currentBanner.value.imageType === 1 ? currentBanner.value.commonCover : bannerMap.value[lang].cover,
                    sort_no: currentBanner.value.sort_no,
                  }
                  delete i.tags
                  delete i.langs
                  delete i.seriesResourceId
                  r.push(i)
                })

                void apiBatchCreateBanner(r, currentBanner.value.seriesResourceId).then(() => {
                  showAlert('创建成功')
                  void router.push('/banner')
                }).catch((error: any) => {
                  showAlert(error.response.data.message || '创建失败', 'error')
                })
              }}
              >确定
              </Button>
            </div>
          </div>
        ),
      }}
    </NavFormTablePager>
  )
})

export default BannerCreatePage
