import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, CreateForm, openDialog, transformInteger, transformNumber } from '@skynet/ui'
import { useBanner } from './use-banner.tsx'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader'
import { ref, watch } from 'vue'
import { apiCheckDeeplink } from '../notification/notification-api.ts'
import { useAppAndLangOptions } from '../options/use-app-options.tsx'
import dayjs from 'dayjs'
import { apiGetLabels } from './banner-api.ts'

export const BannerForm = createComponent(null, () => {
  const {
    currentBanner,
    onEdit,
    onCreate,
    onClear,
    closeEditBannerModal,
    colors,
    searchForm,
  } = useBanner()
  const Form = CreateForm<M.Banner>()
  const tags = ref<M.BTag[]>([])

  const formRules = z.object({
    title: z.string().min(1, '请输入标题').max(50, '最多50个字符'),
    app_id: z.number().min(1, '请选择生效应用'),
    link: z.string().min(1, '请输入连接'),
    cover: z.string().min(1, '请上传封面'),
    language: z.string().min(1, '请选择语言'),
    start_time: z.number().refine(e => {
      if (e && currentBanner.value.end_time) return true
      return false
    }, {
      message: '请设置开始和结束时间！',
    }).refine(e => {
      if (e && currentBanner.value.end_time && new Date(e * 1000).getTime() < new Date(currentBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间必须晚于开始时间！',
    }).refine(e => {
      if (currentBanner.value.end_time && new Date().getTime() < new Date(currentBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间需晚于当前时间！',
    }),
    bg_color: z.string().min(1, '请选择背景颜色'),
  })

  const { error, validateAll } = useValidator(currentBanner, formRules)
  const { appOptions, languageOptions } = useAppAndLangOptions(() => currentBanner.value.app_id)
  watch(
    () => currentBanner.value.app_id,
    id => {
      currentBanner.value.language = id ? (languageOptions.value[0]?.value) : ''
    },
  )

  watch(
    () => currentBanner.value.link,
    id => {
      if (!currentBanner.value.link) {
        return
      }
      void apiGetLabels({
        app_id: searchForm.value.app_id || 0,
        language: searchForm.value.language || '',
        link: currentBanner.value.link,
      })
        .then(rs => {
          tags.value = rs.data?.list || []
        })
    },
    {
      deep: true,
      immediate: true,
    },
  )

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid flex-1 grid-cols-3 gap-y-3"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentBanner.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel(currentBanner.value.banner_type == 2 ? 'Banner标题' : '标题'),
              path: 'title',
              input: {
                type: 'text',
                maxlength: 50,
                placeholder: '请输入标题，1-50个字符',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('Banner标题:'),
              path: 'show_title',
              input: {
                type: 'radio',
                options: [
                  {
                    value: 2,
                    label: '展示标题',
                  },
                  {
                    value: 1,
                    label: '不展示标题',
                  },
                ]
                ,
              },
              class: mc('col-span-3', currentBanner.value.banner_type == 2 ? '' : 'hidden'),
            },
            {
              label: requiredLabel('选择生效应用'),
              path: 'app_id',
              input: {
                type: 'select',
                options: appOptions.value,
              },
              transform: transformNumber,
              class: 'col-span-1',
            },
            {
              label: requiredLabel('语言'),
              path: 'language',
              input: {
                type: 'select',
                options: languageOptions.value,
              },
              class: 'col-span-1',
            },
            ['所属Tab (0表示空)', 'home_tab_info_module_id', {
              type: 'text',
              placeholder: '请输入模块ID',
            }, { transform: transformNumber }],
            {
              label: requiredLabel('生效时间'),
              path: 'start_time',
              input: {
                type: 'custom',
                render: () => (
                  <div class="flex items-center gap-x-2">
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentBanner.value.start_time ? dayjs(currentBanner.value.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentBanner.value.start_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                    <span>-</span>
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentBanner.value.end_time ? dayjs(currentBanner.value.end_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentBanner.value.end_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                  </div>
                ),
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('跳转类型'),
              path: 'link_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: [
                  {
                    label: 'deeplink',
                    value: 1,
                  },
                  {
                    label: currentBanner.value.banner_type == 2 ? '站内h5链接' : 'h5链接',
                    value: 2,
                  },
                ],
                autoInsertEmptyOption: false,
              },
            },
            {
              label: requiredLabel('跳转链接'),
              path: 'link',
              input: {
                type: 'text',
                placeholder: `${currentBanner.value.link_type === 1 ? '请输入deeplink' : '请输入h5链接'}`,
              },
              class: 'col-span-2',
            },
            {
              label: '剧标签（选择一个标签展示）',
              path: 'label_id',
              input: {
                type: 'select',
                options: tags.value.map(item => ({
                  label: item.content,
                  value: item.label_id,
                }))
                ,
              },
              transform: transformNumber,
              class: mc('col-span-3', currentBanner.value.banner_type == 2 ? 'hidden' : ''),
            },
            {
              label: requiredLabel('封面'),
              path: 'cover',
              input: {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] cursor-pointer overflow-hidden rounded-md border border-dashed"
                      onUploadSuccess={d => {
                        currentBanner.value.cover = d.temp_path
                      }}
                      isImage={true}
                      uploadUrl="/banner/upload/cover"
                    >
                      {
                        currentBanner.value.cover
                          ? <img src={currentBanner.value.cover.includes('https://') ? currentBanner.value.cover : 'https://static-v1.mydramawave.com/banner/cover/' + currentBanner.value.cover} class="size-full object-cover" />
                          : <span class="flex size-full items-center justify-center">上传封面</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                  </x-upload-cover>

                ),
              },
              hint: '横图适用1.0.90版本及以上',
            },
            {
              label: '旧版封面（竖图）',
              path: 'vertical_cover',
              input: {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] cursor-pointer overflow-hidden rounded-md border border-dashed"
                      onUploadSuccess={d => {
                        currentBanner.value.vertical_cover = d.temp_path
                      }}
                      isImage={true}
                      uploadUrl="/banner/upload/cover"
                    >
                      {
                        currentBanner.value.vertical_cover
                          ? <img src={currentBanner.value.vertical_cover.includes('https://') ? currentBanner.value.vertical_cover : 'https://static-v1.mydramawave.com/banner/cover/' + currentBanner.value.vertical_cover} class="size-full object-cover" />
                          : <span class="flex size-full items-center justify-center">上传封面</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                  </x-upload-cover>

                ),
              },
              hint: '适用1.0.8x版本及以下',
              class: mc(currentBanner.value.banner_type == 2 ? 'hidden' : ''),
            },
            {
              label: () => (
                <div>{requiredLabel('背景色')}
                  {currentBanner.value.bg_color && (
                    <span class="mt-2 !h-4 !w-4 rounded-sm" style={{ background: currentBanner.value.bg_color }} />
                  )}
                </div>
              ),
              path: 'bg_color',
              input: {
                type: 'select',
                options: colors.value.map(item => ({
                  label: item,
                  value: item,
                }))
                ,
              },
              class: 'col-span-3',
            },
          ]}
          data={currentBanner.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        {!currentBanner.value?.id && <Button class="btn btn-ghost  btn-sm" onClick={onClear}>清空</Button>}
        <Button class="btn  btn-sm" onClick={closeEditBannerModal.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            return
          }
          if (currentBanner.value.link_type === 1) {
            apiCheckDeeplink({
              target_app_ids: currentBanner.value.app_id + '', // 生效应用id,用逗号分割
              link: currentBanner.value.link!,
            }).then(() => {
              !currentBanner.value?.id ? void onCreate() : void onEdit()
            }).catch(() => {
              const close = openDialog({
                customClass: 'pb-0',
                title: '提示',
                body: (
                  <x-tips>
                    <x-tips-body class="flex flex-1 flex-col overflow-y-auto px-1">
                      deeplink 对应短剧已下线，请确认是否保存
                    </x-tips-body>
                    <x-tips-footer class="flex justify-end gap-x-2 p-[20px]">
                      <Button class="btn-default btn btn-sm" onClick={() => close()}>取消</Button>
                      <Button class="btn btn-primary btn-sm" onClick={() => {
                        !currentBanner.value?.id ? void onCreate() : void onEdit()
                        close()
                      }}
                      >确认
                      </Button>
                    </x-tips-footer>
                  </x-tips>
                ),
              })
            })
            return
          }
          !currentBanner.value?.id ? void onCreate() : void onEdit()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
