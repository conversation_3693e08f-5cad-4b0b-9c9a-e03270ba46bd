import { createComponent } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, showFailToast, transformNumber } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useBanner } from './use-banner.tsx'
import { ref, watch } from 'vue'
import { set } from 'lodash-es'
import { BannerForm } from './banner-form'
import dayjs from 'dayjs'
import { RouterLink, useRouter } from 'vue-router'
import { useAppAndLangOptions } from '../options/use-app-options.tsx'
import { useEpisodeTabs } from '../episode-tabs/use-episode-tabs.tsx'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const BannerPage = createComponent(null, () => {
  const { onSearch, total, searchForm, onReset, currentBanner, closeEditBannerModal, list, onPageChange, onPageSizeChange, onDeleteBanner, onSortBanner,
    onPreviewBanner, loading, getColors, onBatchDeleteBanner } = useBanner()
  const { search, tabParams, list: tabList } = useEpisodeTabs()

  const Form = CreateForm<M.BannerSearchProps>()
  const Table = CreateTableOld<M.BannerListModel>()
  const currentSortNo = ref(1)
  const router = useRouter()

  const columns: TableColumnOld<M.BannerListModel>[] = [
    ['ID', 'id', { class: 'w-[60px]' }],
    [
      'banner类型',
      row => row.banner_type == 2 ? '广告位Banner' : '普通Banner',
      { class: 'w-[150px]' },
    ],
    ['客户端排序', row => (
      <input
        type="number"
        class="input input-sm input-bordered w-full"
        maxlength={1}
        value={row.sort_no}
        onFocus={() => {
          currentSortNo.value = row.sort_no || 1
        }}
        onChange={(e: Event) => {
          currentSortNo.value = Number((e.target as HTMLInputElement).value) || 0
        }}
        onKeydown={(e: KeyboardEvent) => {
          if (e.key !== 'Enter') {
            return
          }
          if (currentSortNo.value === row.sort_no) {
            return
          }
          if (currentSortNo.value > 9 || currentSortNo.value < 1) {
            showFailToast('请输入1-9的整数')
            return
          }
          void onSortBanner({
            app_id: searchForm.value.app_id || 0,
            language: searchForm.value.language || 'Chinese',
            curr_row_id: row.id || 0,
            to_sort_no: currentSortNo.value,
          })
        }}
        onBlur={() => {
          if (currentSortNo.value === row.sort_no) {
            return
          }
          if (currentSortNo.value > 9 || currentSortNo.value < 1) {
            showFailToast('请输入1-9的整数')
            return
          }
          void onSortBanner({
            app_id: searchForm.value.app_id || 0,
            language: searchForm.value.language || 'Chinese',
            curr_row_id: row.id || 0,
            to_sort_no: currentSortNo.value,
          })
        }}
      />
    ), { class: 'w-[100px]' }],
    ['标题', 'title', { class: 'w-[160px]' }],
    ['语言', 'language', { class: 'w-[160px]' }],
    ['封面', banner => (
      <div
        key={banner.id}
        class="relative h-[160px] w-full overflow-hidden rounded-md opacity-100 transition-opacity duration-500"
      >
        <img src={banner.cover} alt={banner.title} class="size-full object-cover" />
        <div class="absolute bottom-0 left-0 w-full bg-black bg-opacity-50 p-2 text-white">
          {banner.title}
        </div>
      </div>
    ),
    { class: 'w-[320px]' },
    ],
    ['状态',
      row => (
        <div class="flex items-center space-x-1">
          {row.state === 1
            ? (
                <>
                  <div class="badge badge-xs bg-green-600" />
                  <div>已生效</div>
                </>
              )
            : row.state === 10
              ? (
                  <>
                    <div class="badge badge-xs bg-gray-300" />
                    <div>已停用</div>
                  </>
                )
              : (
                  <>
                    <div class="bg-blue-5000 badge badge-xs" />
                    <div>待生效</div>
                  </>
                )}
        </div>
      ),
      { class: 'w-[120px]' },
    ],
    ['有效期',
      row => (
        <>
          {
            row.start_time
              ? (
                  <div class="flex items-center justify-center">
                    <DateTime value={row.start_time * 1000} />
                    <span> 至 </span>
                    <DateTime value={row.end_time * 1000} />
                  </div>
                )
              : '-'
          }
        </>
      ),
      {
        class: 'w-[280px]',
      },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm') : '-',
      { class: 'w-[150px]' },
    ],
    ['更新人', 'op_user_name', { class: 'w-[200px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap gap-x-2">
          <Button class="btn btn-outline btn-xs" onClick={() => onDeleteBanner(row)}>
            删除
          </Button>
          <Button class="btn btn-error btn-xs text-white" onClick={() => onBatchDeleteBanner(row)}>
            删除全部
          </Button>
          <Button class="btn btn-outline btn-xs" onClick={() => {
            currentBanner.value = { ...row, app_id: Number(row?.app_id) }

            closeEditBannerModal.value = openDialog({
              title: row.banner_type == 2 ? '编辑广告位Banner' : '编辑Banner',
              body: <BannerForm />,
              mainClass: dialogMainClass,
              customClass: '!w-[800px]',
            })
          }}
          >编辑{row.banner_type == 2 ? '广告位' : ''}
          </Button>
        </div>
      ),
      { class: 'w-[280px]' },
    ],
  ]

  const { appOptions, languageOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: () => {
      getColors()
      onSearch()
      void search()
    },
  })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !searchForm.value.app_id) {
      searchForm.value.app_id = appOptions.value[0].value
      tabParams.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => searchForm.value.app_id, id => {
    if (searchForm.value.language && languageOptions.value.find(item => item.value === searchForm.value.language)) return
    searchForm.value.language = id ? (languageOptions.value[0]?.value) : ''
    tabParams.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/banner">Banner管理</RouterLink></li>
          </ul>
        ),
        form: () => (
          <Form
            onSubmit={() => onSearch(true)}
            onReset={onReset}
            data={searchForm.value}
            onChange={(path, value) => {
              if (path === 'app_id') {
                tabParams.value.app_id = value as number
                void search()
              }
              if (path === 'language') {
                tabParams.value.language = value as string
                void search()
              }
              set(searchForm.value, path, value)
            }}
            items={[
              ['应用', 'app_id', {
                type: 'select', autoInsertEmptyOption: false, options: appOptions.value,
              }, { transform: transformNumber }],
              ['语言', 'language', {
                type: 'select', autoInsertEmptyOption: false, options: languageOptions.value,
              }],
              ['归属tab', 'home_tab_info_module_id', {
                type: 'select',
                options: tabList.value.map(item => ({ label: item.tab_key, value: item.id })),
              }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-between">
            <span>Banner列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => {
                void router.push('/ad-create-banner')
              }}
              >新建广告位Banner
              </Button>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                void router.push('/create-banner')
              }}
              >新建Banner
              </Button>
              <Button class="btn btn-sm" onClick={onPreviewBanner}>预览</Button>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => <Table list={list.value} columns={columns} loading={loading.value} class="tm-table-fix-last-column" />,
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={searchForm.value.page_info?.page_index || 1}
                size={searchForm.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),
      }}
    </NavFormTablePager>
  )
})

export default BannerPage
