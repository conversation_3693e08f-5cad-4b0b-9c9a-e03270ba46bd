/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-floating-promises */
import { ref } from 'vue'
import { apiBatchDeleteBanner, apiDeleteBanner, apiEditBanner, apiGetBannerColorList, apiGetBannerList, apiSortBanner, apiBatchGetBannerPreview, apiBatchCreateAdBanner } from './banner-api'
import { openDialog, showAlert, showFailToast, showSuccessToast } from '@skynet/ui'
import { BannerPreview } from './banner-preview'
import { cloneDeep, omit } from 'lodash-es'
import router from 'src/router.tsx'

const searchForm = ref<M.BannerSearchProps>({
  language: 'Chinese',
  page_info: {
    page_index: 1,
    page_size: 10,
  },
  home_tab_info_module_id: undefined,
})

const list = ref<M.BannerListModel[]>([])
const total = ref<number>(0)
const applicationList = ref<Array<Required<M.Application>>>([])
const closeEditBannerModal = ref(() => {})
const loading = ref(false)
const colors = ref<string[]>([])

const bannerPreviewList = ref<M.BannerListModel[]>([])

const InitBannerOption: M.Banner = {
  link_type: 1,
  language: 'Chinese',
  state: 1,
  app_id: undefined,
  end_time: undefined,
  start_time: undefined,
}
const defaultAdBanner = {
  link_type: 1,
  show_title: 2,
  link: 'dramawave://dramawave.app/detail?id=',
  langs_content: [],
  coverList: [],
  banner_type: 2,
}
const currentBanner = ref<M.Banner>(InitBannerOption)
const currentAdBanner = ref<M.AdBanner>({ ...defaultAdBanner })

const getColors = () => {
  void apiGetBannerColorList().then(res => {
    colors.value = res.data?.list || []
  })
}

const getList = async () => {
  loading.value = true
  const rs = await apiGetBannerList(searchForm.value)

  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
  loading.value = false
}

const onSearch = (isFirst?: boolean) => {
  // TODO
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearch()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearch()
}

const onReset = () => {
  searchForm.value.app_id = applicationList.value[0]?.id
  onSearch(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  closeEditBannerModal.value && closeEditBannerModal.value()
  if (isCreate) {
    onSearch(true)
    return
  }
  onSearch()
}

const onClear = () => {
  currentBanner.value = {
    ...InitBannerOption,
    app_id: searchForm.value.app_id,
  }

  currentBanner.value.language = searchForm.value.language
}

const onDeleteBanner = (d: M.Banner) => {
  const hideDeleteDialog = openDialog({
    title: '删除',
    mainClass: 'pb-0 px-5',
    body: (
      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-delete-episode-body>确认删除 banner【{d.title}】吗？</x-delete-episode-body>
        <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await apiDeleteBanner({ id: d.id || 0 })
              onEditSuccess()
              showSuccessToast('删除成功')
              hideDeleteDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '删除失败')
            }
          }}
          >确定
          </button>
        </x-delete-episode-footer>
      </x-delete-episode-confirm-dialog>
    ),
  })
}

const onBatchDeleteBanner = (d: M.Banner) => {
  const hideDeleteDialog = openDialog({
    title: '删除',
    mainClass: 'pb-0 px-5',
    body: (
      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-delete-episode-body>确认删除 【{d.title}】全语言双端的banner吗？</x-delete-episode-body>
        <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={async () => {
            try {
              await apiBatchDeleteBanner({ id: d.id || 0 })
              onEditSuccess()
              showSuccessToast('删除成功')
              hideDeleteDialog()
            } catch (error: any) {
              showFailToast(error.response.data.message || '删除失败')
            }
          }}
          >确定
          </button>
        </x-delete-episode-footer>
      </x-delete-episode-confirm-dialog>
    ),
  })
}

const switchLevel = () => ({
  ...currentBanner.value,
  app_id: currentBanner.value.app_id + '',
  cover: (currentBanner.value.cover || '').replace('https://static-v1.mydramawave.com/banner/cover/', ''),
})

const onCreate = async () => {
  try {
    await apiEditBanner(switchLevel())
    showSuccessToast('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    showFailToast(error.response.data.message || '创建失败')
  }
}

const onEdit = async () => {
  try {
    await apiEditBanner(switchLevel())
    showSuccessToast('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    showFailToast(error.response.data.message || '编辑失败')
  }
}

const onSortBanner = async (d: {
  app_id: number
  language: M.Language
  curr_row_id: number
  to_sort_no: number
}) => {
  try {
    await apiSortBanner(d)
    showSuccessToast('排序成功')
    onEditSuccess()
  } catch (error: any) {
    showFailToast(error.response.data.message || '排序失败')
  }
}

const onPreviewBanner = () => {
  openDialog({
    title: 'Banner预览',
    mainClass: 'pb-0 px-5',
    body: <BannerPreview />,
  })
}

const getBannerPreview = async (series_resource_id: number) => {
  const res = await apiBatchGetBannerPreview(series_resource_id)
  bannerPreviewList.value = res.data?.list || []
  currentBanner.value.bg_color = bannerPreviewList.value[0]?.bg_color
  if (!bannerPreviewList.value[0].en_cover) {
    showAlert('该资源未配置banner图')
  }
  console.log('>>> bannerPreviewList', currentBanner.value.bg_color)
}
const checkFieldForUndefined = (arr: any, field: any) => {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][field] === undefined) {
      return true
    }
  }
  return false
}
const onSave = async (value: M.AdBanner) => {
  const data: any = cloneDeep(value)
  // 修改数据格式
  const langsData: any = {}
  data.coverList?.forEach((item: any) => {
    item.language?.map((lang: string) => {
      langsData[lang] = item.cover
    })
  })
  const delKeyData = omit(data, ['coverList', 'langs_content', 'langs', 'app_id'])
  const reqData = data.langs_content?.map((item: any) => {
    item.cover = langsData[item.language]
    item.app_id = data.app_id + ''
    item.state = 1
    item.banner_type = data.banner_type
    return Object.assign({}, item, delKeyData)
  })
  console.log(reqData)
  const hasUndefined = checkFieldForUndefined(reqData, 'cover')
  console.log(hasUndefined)
  if (hasUndefined) { // 检查 'cover' 字段是否包含 undefined,包含证明语言没有全部对应图片
    showFailToast('封面配置要包含所有语言哦～')
  } else {
    try {
      await apiBatchCreateAdBanner(reqData)
      showAlert('创建成功')
      currentAdBanner.value = { link_type: 1, // 重置
        show_title: 2,
        link: 'dramawave://dramawave.app/detail?id=',
        langs_content: [],
        coverList: [] }
      void router.push('/banner')
    } catch (error: any) {
      console.log(error)
      showFailToast(error.response.data.err_msg || '排序失败')
    }
  }
}
export const useBanner = () => {
  return {
    searchForm,
    applicationList,
    list,
    total,
    loading,
    closeEditBannerModal,
    InitBannerOption,
    currentBanner,
    currentAdBanner,
    onSearch,
    onPageChange,
    onPageSizeChange,
    onReset,
    onDeleteBanner,
    onCreate,
    onEdit,
    onClear,
    onSortBanner,
    onPreviewBanner,
    getColors,
    colors,
    onSave,
    onBatchDeleteBanner,
    getBannerPreview,
    bannerPreviewList,
  }
}
