import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useBannerCreate = defineStore('create-banner', () => {
  const defaultBanner = {
    link_type: 1,
    deeplink: 'dramawave://dramawave.app/detail?id=',
    imageType: 1,
    banner_type: 1,
  }

  const currentBanner = ref<M.Banner>({ ...defaultBanner })

  const resetCurrentBanner = () => {
    currentBanner.value = { ...defaultBanner }
  }

  const bannerMap = ref<{
    [key: string]: M.Banner
  }>({})

  return {
    currentBanner,
    bannerMap,
    resetCurrentBanner,
  }
})
