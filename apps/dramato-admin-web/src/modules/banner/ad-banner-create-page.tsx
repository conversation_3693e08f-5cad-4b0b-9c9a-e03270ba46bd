/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, showFailToast, transformInteger, transformNumber, RadioGroup, Icon } from '@skynet/ui'
import dayjs from 'dayjs'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader.tsx'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref } from 'vue'
import { z } from 'zod'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useBanner } from './use-banner'
import { RouterLink, useRouter } from 'vue-router'

type BannerCreatePageOptions = {
  props: {}
}

export const BannerCreatePage = createComponent<BannerCreatePageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { colors, getColors, onSave, currentAdBanner } = useBanner()
  const Form = CreateForm<M.AdBanner>()

  const formRules = z.object({
    app_id: z.number().min(1, '请选择生效应用'),
    langs: z.array(z.string()).min(1, '请选择语言'),
    link_type: z.number().min(1, '请选择类型'),
    link: z.string().min(1, '请填写link'),
    // sort_no: z.number().min(1, '请填写客户端排序').max(9, '客户端排序最大为9'),
    start_time: z.number().refine(e => {
      if (e && currentAdBanner.value.end_time) return true
      return false
    }, {
      message: '请设置开始和结束时间！',
    }).refine(e => {
      if (e && currentAdBanner.value.end_time && new Date(e * 1000).getTime() < new Date(currentAdBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间必须晚于开始时间！',
    }).refine(e => {
      if (currentAdBanner.value.end_time && new Date().getTime() < new Date(currentAdBanner.value.end_time * 1000).getTime()) return true
      return false
    }, {
      message: '结束时间需晚于当前时间！',
    }),
    bg_color: z.string().min(1, '请选择背景颜色'),
    langs_content: z.array(z.object({
      language: z.string().min(1, '请输入语言'),
      title: z.string().min(1, '请输入文案'),
    })).min(1, '请填写语言配置'),
    coverList: z.array(z.object({
      cover: z.string().min(1, '请上传图片'),
      language: z.array(z.string()).min(1, '请选择语言'),
    })).min(1, '请填写封面配置'),
  })

  const { error, validateAll } = useValidator(currentAdBanner, formRules)
  const { appOptions, languageOptions } = useAppAndLangOptions(() => currentAdBanner.value.app_id)

  const languageSureBtn = () => {
    currentAdBanner.value.langs_content = []
    currentAdBanner.value.langs?.map((value: any) => {
      currentAdBanner.value.langs_content?.push({
        language: value,
        title: '',
      })
    })
  }
  const optionSelectedStatus = ref<any>({})
  onMounted(() => {
    void getColors()
  })

  return () => (
    <>
      <div class="breadcrumbs ml-4 mt-8 text-sm">
        <ul>
          <li><RouterLink to="/banner">Banner管理</RouterLink></li>
          <li>创建广告位banner</li>
        </ul>
      </div>
      <x-form-wrap class="mt-6 flex flex-col gap-y-2 rounded-md bg-white p-6">
        <Form
          class="mb-3 grid flex-1 grid-cols-3 gap-y-3"
          actionClass="col-span-1 flex justify-end"
          error={error.value}
          hasAction={false}
          onChange={(path, value) => {
            if (path === 'langs') {
              console.log(value)
            }
            set(currentAdBanner.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('banner类型'),
              path: 'banner_type',
              input: {
                type: 'select',
                disabled: true,
                options: [
                  {
                    label: '广告位类型',
                    value: 2,
                    disabled: true,
                  },
                  {
                    label: '剧集类型',
                    value: 1,
                    disabled: true,
                  },
                ],
                autoInsertEmptyOption: false,
                class: 'col-span-1',
              },
              class: 'col-span-1',
            },
            {
              label: requiredLabel('生效时间'),
              path: 'start_time',
              input: {
                type: 'custom',
                render: () => (
                  <div class="flex flex-1 flex-wrap items-center gap-2">
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentAdBanner.value.start_time ? dayjs(currentAdBanner.value.start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentAdBanner.value.start_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                    <span>-</span>
                    <div class="input input-sm input-bordered flex items-center gap-2">
                      <input
                        type="datetime-local"
                        value={currentAdBanner.value.end_time ? dayjs(currentAdBanner.value.end_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                        onInput={(e: Event) => currentAdBanner.value.end_time = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                      />
                    </div>
                  </div>
                ),
              },
              class: 'col-span-1',
            },
            {
              label: requiredLabel('选择生效应用'),
              path: 'app_id',
              input: {
                type: 'select',
                options: appOptions.value,
              },
              transform: transformNumber,
              class: 'col-span-1',
            },
            {
              label: <div class="flex">{requiredLabel('语言')}<span class="pl-2 text-sm text-red-500">(修改语言后，点击下方确认按钮进行更新)</span></div>,
              path: 'langs',
              input: {
                type: 'multi-select',
                options: languageOptions.value,
              },
              class: 'col-span-1',
            },
            {
              label: requiredLabel('背景色'),
              path: 'bg_color',
              input: {
                type: 'select',
                options: colors.value.map(item => ({
                  label: item,
                  value: item,
                }))
                ,
              },
              class: 'col-span-1',
              hint: () => currentAdBanner.value.bg_color
                ? (
                    <div class="mt-2 !h-4 !w-4 rounded-sm" style={{
                      background: currentAdBanner.value.bg_color,
                    }}
                    />
                  )
                : null,
            },
            {
              label: requiredLabel('跳转类型'),
              path: 'link_type',
              transform: transformInteger,
              input: {
                type: 'select',
                options: [
                  {
                    label: 'deeplink',
                    value: 1,
                  },
                  {
                    label: '站内h5链接',
                    value: 2,
                  },
                  {
                    label: '站外h5链接',
                    value: 3,
                  },
                ],
                autoInsertEmptyOption: false,
                class: 'col-span-1',
              },
              class: 'col-span-1',
            },
            {
              label: requiredLabel('Deeplink模板'),
              path: 'link',
              input: {
                type: 'text',
              },
              class: 'col-span-1',
              hint: () => (<Button class="btn btn-link btn-sm" onClick={() => { window.open('https://rg975ojk5z.feishu.cn/docx/AkgadiZueoPf1mxwQSMct2HXn4h') }}>deeplink配置链接</Button>),
            },
            [<div>客户端排序<span class="pl-2 text-sm text-red-500">注意不要与之前的排序重复</span></div>, 'sort_no', {
              type: 'number',
              placeholder: '请输入客户端排序',
            }, { transform: transformNumber }],
            ['所属Tab', 'home_tab_info_module_id', {
              type: 'text',
              placeholder: '请输入所属TabId',
            }, { transform: transformNumber }],
            () => (
              <div class="flex flex-col">
                <button class="btn btn-primary btn-sm mt-[25px] w-[100px]" onClick={() => { void languageSureBtn() }}>语言确认</button>
              </div>
            ),
            () => (<hr class="col-span-3" />),
            [
              '多语言文案配置',
              'langs_content',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      <div class="flex pl-5">
                        {requiredLabel('banner标题:')}
                        <RadioGroup
                          class="tm-radio ml-6"
                          options={[
                            {
                              value: 2,
                              label: '展示标题',
                            },
                            {
                              value: 1,
                              label: '不展示标题',
                            },
                          ]}
                          modelValue={currentAdBanner.value.show_title}
                          onUpdate:modelValue={(e: unknown) => currentAdBanner.value.show_title = e as 2 | 1}
                        />
                      </div>
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <x-type-select class="mx-[20px] w-[200px]">
                                {requiredLabel('应用语言:')}
                                <input
                                  class="input input-bordered flex h-8 items-center gap-1 !border-gray-300"
                                  value={i.language || 0}
                                  disabled="true"
                                />
                              </x-type-select>
                              <div class="flex flex-col">
                                {requiredLabel('文案内容:')}
                                <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                                  <textarea
                                    rows="1"
                                    placeholder="请输入文案"
                                    class="textarea textarea-bordered w-[300px] border-none"
                                    value={i.title}
                                    onInput={(e: any) => {
                                      if (!currentAdBanner.value.langs_content) {
                                        return
                                      }
                                      const value = e.target.value
                                      currentAdBanner.value.langs_content[idx].title = value + ''
                                    }}
                                  />
                                </span>
                              </div>
                            </div>
                          ))
                      }
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg col-span-3',
              },
            ],
            () => (<hr class="col-span-3 my-4" />),
            () => (
              <div class="col-span-3">
                <div><span class="mr-1 text-[red]">*</span>封面配置</div>
                <Uploader
                  accept="png,jpg,jpeg"
                  maxsize={1024 * 1024 * 10}
                  class="btn btn-outline btn-sm m-2 cursor-pointer "
                  onUploadSuccess={d => {
                    const imgUrl = d.temp_path?.includes('https://') ? d.temp_path : 'https://static-v1.mydramawave.com/banner/cover/' + d.temp_path
                    if (!currentAdBanner.value?.coverList) {
                      currentAdBanner.value.coverList = []
                    }
                    currentAdBanner.value.coverList.push({
                      cover: imgUrl,
                      language: [],
                    })
                  }}
                  isImage={true}
                  uploadUrl="/banner/upload/cover"
                >
                  <span class="flex size-full items-center justify-center">上传图片</span>
                </Uploader>
                <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>

              </div>
            ),
            [
              '封面语言配置',
              'coverList',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      {
                        (r.value || [])
                          .map((i: M.BannerManageData.Save.coverLanguageItem, idx: number) => (
                            <div class={mc('flex items-center gap-2 pb-2 border-b-2 border-gray-300', i.is_delete ? 'hidden' : '')}>
                              <img src={i.cover} class="mr-6 h-auto w-[150px]" />
                              <div class="flex flex-1 flex-wrap gap-3">
                                {
                                  (currentAdBanner.value.langs || []).map((item: any) => (
                                    <label class="cursor-pointer">
                                      <input type="checkbox"
                                        value={item}
                                        checked={i.language.includes(item)}
                                        disabled={!i.language.includes(item) && optionSelectedStatus.value[item]}
                                        onChange={e => {
                                          const target = e.target as HTMLInputElement
                                          if (target.checked) {
                                            i.language.push(item)
                                            optionSelectedStatus.value[item] = true
                                          } else {
                                            const newArr1 = i.language.filter((s: string) => s !== item)
                                            i.language = newArr1
                                            optionSelectedStatus.value[item] = false
                                          }
                                        }}
                                        class="tm-radio mr-1 cursor-pointer" />
                                      {item}
                                    </label>
                                  ),
                                  )
                                }
                              </div>
                              <Icon name="ant-design:delete-filled" class="ml-[10px] size-5 cursor-pointer" onClick={() => {
                                if (!currentAdBanner.value?.coverList) {
                                  return
                                }
                                i.language.forEach(key => {
                                  if (optionSelectedStatus.value.hasOwnProperty(key)) {
                                    optionSelectedStatus.value[key] = false
                                  }
                                })
                                i.language = []
                                currentAdBanner.value.coverList.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4 bg-gray-100 rounded-lg col-span-3',
              },
            ],
          ]}
          data={currentAdBanner.value}
        />
      </x-form-wrap>
      <div class="flex justify-end gap-2 p-[20px]">
        <Button class="btn  btn-sm" onClick={() => {
          void router.push('/banner')
        }}
        >取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(currentAdBanner.value)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})

export default BannerCreatePage
