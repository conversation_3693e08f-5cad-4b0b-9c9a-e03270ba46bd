import { httpClient } from 'src/lib/http-client'

export const apiGetBannerList = (data: M.BannerSearchProps) =>
  httpClient.post<ApiResponse<M.BannerListRequestResponse>>('/banner/list', data)

export const apiGetBannerDetail = (data: { id: number }) =>
  httpClient.get<ApiResponse<M.Banner>>('/banner/get', data)

export const apiEditBanner = (data: M.Banner) =>
  httpClient.post<ApiResponse<null>>('/banner/save', data)

export const apiDeleteBanner = (data: { id: number }) =>
  httpClient.post<ApiResponse<null>>('/banner/delete', {
    banner_ids: [data.id],
  })

export const apiBatchDeleteBanner = (data: { id: number }) =>
  httpClient.post<ApiResponse<null>>('/banner/delete_batch', {
    banner_id: data.id,
    delete_all_language: true,
  })

export const apiSortBanner = (data: {
  app_id: number
  language: M.Language
  curr_row_id: number// 当前操作记录id
  to_sort_no: number
}) =>
  httpClient.post<ApiResponse<null>>('/banner/sort/update', data)

export const apiGetBannerColorList = () =>
  httpClient.post<ApiResponse<{ list: string[] }>>('/banner/bg_colors', {})

export const apiGetLabels = (data: {
  app_id: number
  language: M.Language
  link: string
}) =>
  httpClient.post<ApiResponse<{ list: M.BTag[] }>>('/banner/deeplink/label', data)

export const apiGetResourceInfo = (series_resource_id: number) =>
  httpClient.post<ApiResponse<{ list: Array<
    {
      series_key: string
      title: string
      default_language_code: string
    }> }>>('/banner/series', { series_resource_id })

export const apiBatchCreateBanner = (banner_list: M.Banner[], series_resource_id?: number) =>
  httpClient.post<ApiResponse<null>>('/banner/creat_batch', { banner_list, series_resource_id })

export const apiBatchGetBannerPreview = (series_resource_id: number) =>
  httpClient.post<ApiResponse<{ list: M.BannerListModel[] }>>('/banner/series-resource/banners', { series_resource_id })

export const apiBatchCreateAdBanner = (banner_list: M.AdBanner[]) =>
  httpClient.post<ApiResponse<null>>('/banner/ad/creat_batch', { banner_list })
