import { createComponent, mc } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { onMounted, onUnmounted, ref } from 'vue'
import { apiGetBannerList } from './banner-api'
import { useBanner } from './use-banner'

export const BannerPreview = createComponent(null, () => {
  const { searchForm } = useBanner()
  const list = ref<M.BannerListModel[]>([])
  const currentIndex = ref(0)
  const timer = ref<unknown>(0)

  const startAutoPlay = () => {
    timer.value = setInterval(() => {
      currentIndex.value = (currentIndex.value + 1) % list.value.length
    }, 3000)
  }

  const stopAutoPlay = () => {
    if (timer.value) {
      clearInterval(timer.value as number)
      timer.value = null
    }
  }

  onMounted(() => {
    void apiGetBannerList({
      state: 1,
      app_id: searchForm.value.app_id,
      language: searchForm.value.language,
    }).then(res => {
      list.value = res.data?.list || []
      startAutoPlay()
    })
  })

  onUnmounted(() => {
    stopAutoPlay()
  })

  const handleFirst = () => currentIndex.value = 0

  const handlePrev = () => {
    currentIndex.value = (currentIndex.value - 1 + list.value.length) % list.value.length
  }

  const handleNext = () => {
    currentIndex.value = (currentIndex.value + 1) % list.value.length
  }

  const handleLast = () => currentIndex.value = list.value.length - 1

  return () => (

    <x-banner-preview class="flex flex-col gap-y-2">
      <x-banner-preview-carousel class={mc('relative w-full h-64 overflow-hidden rounded-md', `w-full`)}>
        <x-banner-preview-carousel-track
          class={mc('flex h-full transition-transform duration-300 ease-in-out')}
          style={{
            width: `${list.value.length * 100}%`,
            transform: `translateX(-${currentIndex.value * 520}px)`,
          }}
        >
          {
            list.value.map(banner => (
              <x-banner-preview-carousel-item key={banner.id} class="w-full flex-0-0-100 h-full relative">
                <img src={banner.cover} alt={banner.title} class="w-full h-full object-cover" />
                <div class="absolute bottom-0 left-0 w-full bg-black bg-opacity-50 text-white p-2">
                  {banner.title}
                </div>
              </x-banner-preview-carousel-item>
            ))
          }
        </x-banner-preview-carousel-track>
      </x-banner-preview-carousel>
      <x-banner-preview-actions class="flex justify-between">
        <Button
          class="btn btn-link"
          onClick={handleFirst}
        >
          第一页
        </Button>
        <Button
          class="btn btn-link"
          onClick={handlePrev}
        >
          上一张
        </Button>
        <Button
          class="btn btn-link"
          onClick={handleNext}
        >
          下一张
        </Button>
        <Button
          class="btn btn-link"
          onClick={handleLast}
        >
          最后一页
        </Button>
      </x-banner-preview-actions>
    </x-banner-preview>
  )
})
