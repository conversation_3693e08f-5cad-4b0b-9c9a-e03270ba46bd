declare namespace M {
  namespace EpisodeClipMaterial {
    type Material = {
      material_number: number // 素材编号
      material_type: number // 切片类型 1到10
      duration: string //
      episodes: number[]
      prompt_id: number//
      scripts: string
      tags: string[]
      created: number
    }

    type Episode = {
      title?: string
      episode_num?: number
      upload_num?: number
      analyse_status?: number // 拆解状态 0-未拆解 1-开始开解，2-单集拆解中，3-全集拆解中，4-拆解完成
      analyse_updated?: number
      material_updated?: number
      material_status?: number // 0-未提取 1-提取中 2-提取完成 3-排队中 4-提取失败
      materials?: Material[]
    }

    type GenerateMaterialParams = {
      series_key?: string
      material_type?: number// 切片类型 1 : 顺序切片 。。。。TODO 补全类型
      duration?: string // 时长
      episodes?: string[] // 包含集数
      prompt_id?: number // prompt编号
      count?: number // 生成条数
      episodes_str?: string
    }

    type MaterialPrompt = {
      title: string
      content: string
    }

    type Prompt = {
      prompt_id?: number // 编号id
      prompts: MaterialPrompt[]
      created?: number
    }

    /** 素材片段 */
    type Fragments = {
      fragment_id: number // 生成的片段id
      time_area: number[] // 片段的时间片段
      video_url: string
      desc: string
    }

    /** 素材子片段 - 自定义片段 音轨 */
    type Segment = {
      cut_id?: number // 生成的片段id
      fragment_id?: number // 原片段id
      selected?: number // 0-未选中 1-选中
      time_area?: number[] // 片段的时间片段 [start_time, end_time]
      mark_id?: number // 标记id
    }

    /** 素材脚本 */
    type SegmentListItem = {
      segment_id: number // 原分段id
      fragments: Fragments[]
      cut_segments: Segment[]
      cut_wav_segments: Segment[]
      serial_number: number // 单集id
      order_id: number
    }

    /** 素材详情 */
    type MaterialDetails = M.EpisodeClipMaterial.Material & {
      download_status: number // 1-未下载 2-下载中 3-下载完成 4-下载失败
      download_url: string // 如果是下载完成 直接使用该url下载 如果是其他状态，字段为空字符串
      segment_list: SegmentListItem[]
      series_key: string
    }

    type CommonParams = {
      series_key: string
      material_number: number
      fragment_number?: number
    }

    /** 24. 素材片段保存 */
    type SaveMaterialSegmentParams = {
      serial_number?: number // 原分段id
      cut_type?: number // 保存类型  1:视频片段 2:音轨片段
      time_area?: number[] // 片段的时间片段 [start_time, end_time]
      order_id?: number
    } & CommonParams

    /** 25. 素材片段删除 */
    type DeleteMaterialSegmentParams = {
      segment_id: number // 原分段id
      cut_type: number // 保存类型  1:视频片段 2:音轨片段
      cut_id: 2 // 删除的片段id
    } & CommonParams

    /** 26. 素材自定义片段下载 */
    type DownloadMaterialSegmentParams = {
      cut_segments: Array<Pick<M.EpisodeClipMaterial.Segment, ['cut_id', 'fragment_id']> & { serial_number: number }>
    } & CommonParams
  }
}
