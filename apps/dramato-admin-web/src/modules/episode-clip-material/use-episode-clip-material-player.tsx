import { ref } from 'vue'
import { MSConfig } from './episode-material-player'

const duration = ref(0) // ms
const startTime = ref(0)
const startTimeDisplay = ref('')
const endTime = ref(0)
const endTimeDisplay = ref('')
const videoUrl = ref('')

const player = ref<HTMLVideoElement | null>(null)
const canvas = ref<HTMLCanvasElement | null>(null)
const loadVideoIdx = ref<string[]>([])

export const useEpisodeClipMaterialPlayer = () => {
  return {
    player,
    canvas,
    duration,
    startTime,
    startTimeDisplay,
    endTime,
    endTimeDisplay,
    videoUrl,
    changeVideoUrl,
    onEndTimeChange,
    onStartTimeChange,
    loadVideoIdx,
  }
}

const onStartTimeChange = (needChangeCurrentTime: boolean = false) => {
  startTimeDisplay.value = MSConfig.transformTimeMsToStr(startTime.value / 1000)

  if (!needChangeCurrentTime) {
    return
  }

  let curTime = 0
  if (startTime.value >= endTime.value) {
    curTime = endTime.value - 1000 > 0 ? endTime.value - 1000 : 0
  } else {
    curTime = startTime.value
  }

  if (player.value) {
    player.value.currentTime = curTime / 1000
    void player.value.play()
  }
}

const onEndTimeChange = (needChangeCurrentTime: boolean = false, needCurTimeMinus: boolean = false) => {
  endTimeDisplay.value = MSConfig.transformTimeMsToStr(endTime.value / 1000)

  if (!needChangeCurrentTime) {
    return
  }

  let curTime = 0
  if (endTime.value <= 0) {
    curTime = startTime.value
  } else {
    curTime = needCurTimeMinus ? endTime.value - 1000 : endTime.value
  }

  if (player.value) {
    player.value.currentTime = curTime / 1000
    void player.value.play()
  }
}

const changeVideoUrl = (
  d: {
    url: string
    startTime: number
    endTime: number
  },
) => {
  videoUrl.value = d.url
  startTime.value = d.startTime
  endTime.value = d.endTime
  onEndTimeChange(false)
  onStartTimeChange(false)

  if (loadVideoIdx.value.includes(videoUrl.value || '') && player.value) {
    player.value.currentTime = startTime.value / 1000
  }
}
