import { createComponent, mc } from '@skynet/shared'
import { Button, TableColumnOld, CreateTableOld, DateTime } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { UseEpisodeClipMaterialList } from './use-episode-clip-material-list'
import { MaterialType } from './episode-clip-material-config'
import { useRoute } from 'vue-router'
import { onMounted } from 'vue'

export const EpisodeClipMaterial = createComponent(null, () => {
  const {
    episode,
    materials,
    openGenerateMaterialModal,
    getMaterials,
    series_key,
    onDelete,
  } = UseEpisodeClipMaterialList()

  const route = useRoute()

  const Table = CreateTableOld<M.EpisodeClipMaterial.Material>()

  const columns: TableColumnOld<M.EpisodeClipMaterial.Material>[] = [
    ['编号', 'material_number', { class: 'w-[80px]' }],
    ['素材类型', row => ['-空-', ...MaterialType][row?.material_type || 0], { class: 'w-[140px]' }],
    ['时长', 'duration', { class: 'w-[140px]' }],
    ['包含集数', row => row.episodes?.join(', ') || '', { class: 'w-[140px]' }],
    ['Prompt编号', 'prompt_id', { class: 'w-[140px]' }],
    ['素材梗概', 'scripts', { class: 'w-[360px]' }],
    ['tags', row => row.tags?.join(', ') || '', { class: 'w-[180px]' }],
    ['生成时间', row => (<DateTime value={(row.created || 0) * 1000} />), { class: 'w-[150px]' }],
    [
      <span class="px-3">操作</span>,
      row => (
        <div class="flex flex-nowrap gap-x-2">
          <a
            target="_blank"
            class={mc('btn btn-link btn-xs', JSON.parse(window.localStorage.getItem(`dramato-admin-web.clip.${series_key.value}`) || '[]').includes(row.material_number) ? 'text-gray-600' : '')}
            href={`/episode-material/${route.params.seriesKey as string}/${row.material_number}/${encodeURIComponent(episode.value.title || '')}`}
            onClick={() => {
              const key = `dramato-admin-web.clip.${series_key.value}`
              const store = window.localStorage.getItem(key)
              const arr = store ? JSON.parse(store) : []

              if (!arr.includes(row.material_number)) {
                arr.push(row.material_number)
              }

              window.localStorage.setItem(key, JSON.stringify(arr))
            }}
          >
            查看
          </a>
          <Button class="btn btn-outline btn-xs" onClick={() => {
            onDelete(row.material_number)
          }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[140px]' },
    ],
  ]

  onMounted(() => {
    series_key.value = route.params.seriesKey as string
    void getMaterials()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>AI拆素材</li>
          </ul>
        ),
        form: () => (
          <div class="flex flex-col gap-y-2 text-[14px]">
            <div class="text-black font-semibold text-[18px]">短剧名称: {episode.value.title}</div>
            <div class="">总集数: {episode.value.episode_num}</div>
            <div class="">已上传集数: {episode.value.upload_num}</div>
            <div class="">AI拆剧本进度: {['未拆解', '开始拆解', '单集拆解中', '全集拆解中', '拆解完成'][episode.value?.analyse_status || 0]}</div>
            <div class="">AI拆剧本更新时间: <DateTime value={(episode.value.analyse_updated || 0) * 1000} /></div>
            <div class="">素材生存操作最新时间: <DateTime value={(episode.value.material_updated || 0) * 1000} /></div>
          </div>
        ),
        tableActions: () => (
          <x-table-actions class="w-full flex justify-between items-center">
            <span>素材列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button
                class={mc('btn btn-primary btn-sm')}
                disabled={[1, 3].includes(episode.value.material_status || 0)} onClick={() => {
                  openGenerateMaterialModal({
                    series_key: route.params.seriesKey as string,
                    title: episode.value.title || '',
                    total: episode.value.episode_num || 0,
                  })
                }}
              >
                {
                  [
                    '生成素材',
                    '生成中...',
                    '生成素材',
                    '排队中...',
                    '生成素材',
                  ][episode.value.material_status || 0]
                }
              </Button>
            </x-table-actions-right>
          </x-table-actions>
        ),
        table: () => <Table list={materials.value} columns={columns} loading={false} class="tm-table-fix-last-column" />,
      }}
    </NavFormTablePager>
  )
})

export default EpisodeClipMaterial
