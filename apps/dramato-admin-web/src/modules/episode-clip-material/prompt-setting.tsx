/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { But<PERSON>, Markdown } from '@skynet/ui'
import { onMounted } from 'vue'
import { useMaterialPrompt } from './use-material-prompt'

type PromptSettingOptions = {
  props: {
    material_type: number
  }
}

export const PromptSetting = createComponent<PromptSettingOptions>({
  props: {
    material_type: 1,
  },
}, ({ material_type }) => {
  const {
    promptList,
    getPromptList,
    addPrompt,
    cannelAddPrompt,
    confirmAddPrompt,
    currentPrompt,
    currentNav,
    needEditPromptId,
  } = useMaterialPrompt()

  onMounted(() => {
    void getPromptList({
      material_type,
    })
  })

  return () => (
    <x-prompt-setting class="w-[800px] h-[500px] flex flex-col gap-y-2 overflow-hidden">
      <x-prompt-setting-header class="text-md flex justify-between items-center">
        Prompt管理
        <Button class="btn btn-primary btn-sm" onClick={addPrompt}>
          添加
        </Button>
      </x-prompt-setting-header>
      <x-prompt-setting-container class="flex-1 flex gap-x-2 overflow-hidden">
        <x-prompt-setting-nav class="h-full  min-w-[88px] tabs tab-sm tabs-boxed gap-y-2 flex flex-col overflow-y-auto hide-scrollbar">
          {
            promptList && promptList.value.length > 0 && promptList.value.map(i => (
              <a
                role="tab"
                class={mc('tab w-20 min-w-20 max-w-20 bg-gray-300 text-black', currentPrompt.value?.prompt_id === i.prompt_id ? 'tab-active' : '')}
                onClick={() => {
                  currentPrompt.value = { ...i }
                  currentNav.value = i.prompts[0].title
                }}
              >
                {i.prompt_id}
              </a>
            ))
          }
        </x-prompt-setting-nav>
        <x-prompt-setting-content class="h-full p-2 gap-y-2 flex-1 flex flex-col border border-solid rounded-md border-1 border-gray-300 overflow-hidden">
          <x-prompt-setting-tabs class="tabs-boxed gap-x-2 flex flex-row">
            {
              currentPrompt.value?.prompts.map((key: M.EpisodeClipMaterial.MaterialPrompt) => {
                return (
                  <a
                    class={mc('tab w-30 min-w-30 max-w-30 bg-gray-300 text-black', currentNav.value === key.title ? 'tab-active' : '')}
                    onClick={() => {
                      currentNav.value = key.title
                    }}
                  >
                    {key.title}
                  </a>
                )
              })
            }
          </x-prompt-setting-tabs>
          {
            !(needEditPromptId.value.includes(currentPrompt.value?.prompt_id || 0))
              ? (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    {
                      currentPrompt.value && currentPrompt.value.prompts.find((key: M.EpisodeClipMaterial.MaterialPrompt) => key.title === currentNav.value)
                        ? <Markdown content={currentPrompt.value.prompts.find((key: M.EpisodeClipMaterial.MaterialPrompt) => key.title === currentNav.value)?.content || ''} class="markdown-body bg-white" />
                        : <div class="flex-1 flex justify-center items-center">暂无数据</div>
                    }

                  </x-prompt-setting-tab-panel>
                )
              : (
                  <x-prompt-setting-tab-panel class="flex-1 overflow-y-auto">
                    {
                      currentPrompt.value
                        ? (
                            <textarea
                              class={mc('textarea textarea-bordered w-full h-full')}
                              value={currentPrompt.value.prompts.find((key: M.EpisodeClipMaterial.MaterialPrompt) => key.title === currentNav.value)?.content || ''}
                              onInput={(e: Event) => {
                                if (!currentPrompt.value) {
                                  return
                                }

                                const idx = currentPrompt.value.prompts.findIndex((key: M.EpisodeClipMaterial.MaterialPrompt) => key.title === currentNav.value) || 0
                                currentPrompt.value.prompts[idx].content = (e.target as HTMLTextAreaElement).value
                              }}
                            />
                          )
                        : <div class="flex-1 flex justify-center items-center">暂无数据</div>
                    }

                  </x-prompt-setting-tab-panel>
                )
          }
          {
            !!(needEditPromptId.value.includes(currentPrompt.value?.prompt_id || 0))
              ? (
                  <x-prompt-setting-tab-panel-btn class="flex justify-end items-center gap-4">
                    <Button class="btn btn-sm" onClick={cannelAddPrompt}>
                      取消
                    </Button>
                    <Button class="btn btn-sm btn-primary" onClick={() => confirmAddPrompt(material_type)}>
                      确认
                    </Button>
                  </x-prompt-setting-tab-panel-btn>
                )
              : null
          }
        </x-prompt-setting-content>
      </x-prompt-setting-container>
    </x-prompt-setting>
  )
})
