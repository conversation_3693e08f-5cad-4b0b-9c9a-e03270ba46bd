/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { NeedDownloadSegment, useEpisodeClipMaterial } from './use-episode-clip-material'
import { onMounted, ref } from 'vue'
type PreviewMaterialOptions = {
  props: {}
}
export const PreviewMaterial = createComponent<PreviewMaterialOptions>({
  props: {},
}, props => {
  const {
    needDownloadSegments,
  } = useEpisodeClipMaterial()

  const currentSeg = ref<NeedDownloadSegment>()
  const currentIdx = ref(0)

  const player = ref<HTMLVideoElement | null>(null)
  const startTime = ref(0)
  const endTime = ref(0)

  const loadVideoIdx = ref<string[]>([])

  const getStartTime = () => {
    if (!currentSeg.value?.time_area || !currentSeg.value?.time_area[0]) {
      return 0
    }

    return currentSeg.value?.time_area[0]
  }

  const getEndTime = () => {
    if (!currentSeg.value?.time_area || !currentSeg.value?.time_area[1]) {
      return 0
    }

    return currentSeg.value?.time_area[1]
  }

  const changeVideo = (idx: number) => {
    if (!needDownloadSegments.value || !needDownloadSegments.value[idx]?.time_area || !player.value) {
      return
    }
    currentIdx.value = idx
    currentSeg.value = needDownloadSegments.value[idx]

    startTime.value = getStartTime()
    endTime.value = getEndTime()

    // TODO： 视频非首次加载不走 onLoadedmetadata，所以需要js触发视频当前播放时间的改变
    if (loadVideoIdx.value.includes(currentSeg.value?.video_url || '')) {
      player.value.currentTime = getStartTime() / 1000
    }

    console.info('>>> fragment_id - mark_id', currentSeg.value?.fragment_id, currentSeg.value?.mark_id)
    console.log('current video seg', currentSeg.value, startTime.value, player.value.currentTime)
  }

  onMounted(() => {
    changeVideo(0)
  })

  return () => (
    <x-preview-seg>
      <x-title>片段 {currentSeg.value?.fragment_id}-{currentSeg.value?.mark_id}</x-title>
      <video
        class={mc('w-[350px] min-h-[550px] max-h-[550px] bg-black ')}
        ref={player}
        onLoadedmetadata={(e: any) => {
          if (!player.value) {
            return
          }

          console.log('onLoadedmetadata')

          // TODO: 视频首次加载切换视频当前播放时间需在onLoadedmetadata中设置,loadVideoIdx缓存已加载视频的索引
          loadVideoIdx.value.push(currentSeg.value?.video_url || '')
          player.value.currentTime = getStartTime() / 1000
        }}
        onTimeupdate={(e: any) => {
          const ct = Math.round(e.target.currentTime * 1000)

          if (ct >= endTime.value && player.value) {
            let idx = 0
            if (currentIdx.value !== needDownloadSegments.value.length - 1) {
              idx = currentIdx.value + 1

              console.log(idx, '>>>')
            }

            changeVideo(idx)

            void player.value.play()
          }
        }}
        src={currentSeg.value?.video_url || ''}
        controls
        autoplay
      />
    </x-preview-seg>

  )
})
