import { httpClient } from 'src/lib/http-client'

export const apiGetMaterialList = (data: {
  series_key: string
}) => httpClient.post<ApiResponse<M.EpisodeClipMaterial.Episode>>('/videoclip/material_clip_list', data)

export const apiGenerateMaterial = (data: M.EpisodeClipMaterial.GenerateMaterialParams) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/material_generate', data)

export const apiDeleteMaterial = (data: {
  series_key: string
  material_number: number
}) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/material_delete', data)

export const apiGetPromptList = (data: {
  material_type: number
}) => httpClient.post<ApiResponse<{
  list: M.EpisodeClipMaterial.Prompt[]
  material_type: number
}>>('/videoclip/material_prompt_list', data)

export const apiAddPrompt = (data: {
  material_type: number
  prompts: M.EpisodeClipMaterial.MaterialPrompt[]
}) => httpClient.post<ApiResponse<{
  prompt: M.EpisodeClipMaterial.Prompt
  material_type: number
}>>('/videoclip/material_prompt_add', data)

export const apiGetMaterialDetails = (data: {
  series_key: string
  material_number: number
}) => httpClient.post<ApiResponse<M.EpisodeClipMaterial.MaterialDetails>>('/videoclip/material_clip_info', data)

export const apiSaveMaterialSegment = (data: M.EpisodeClipMaterial.SaveMaterialSegmentParams) => httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/material_segment_save', data)

export const apiDeleteMaterialSegment = (data: {
  cut_id: number
}) => httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/material_segment_delete', data)

export const apiDownloadMaterialSegment = (data: M.EpisodeClipMaterial.DownloadMaterialSegmentParams) => httpClient.post<ApiResponse<{ success: boolean }>>('/videoclip/material_download', data)
