/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { GenerateMaterialForm } from './generate-material-form'
import { apiDeleteMaterial, apiGetMaterialList } from './episode-clip-material-api'

export const UseEpisodeClipMaterialList = () => {
  return {
    episode,
    materials,
    openGenerateMaterialModal,
    series_key,
    getMaterials,
    generateMaterialParams,
    onDelete,
  }
}

const series_key = ref<string>('')
const episode = ref<M.EpisodeClipMaterial.Episode>({})

const materials = ref<M.EpisodeClipMaterial.Material[]>([])

const generateMaterialParams = ref<M.EpisodeClipMaterial.GenerateMaterialParams>({
  material_type: 1,
})

const onGenerateSuccess = (hide: () => void) => {
  hide()
  void getMaterials()
}

const openGenerateMaterialModal = ({
  series_key,
  title,
  total,
}: {
  series_key: string
  title: string
  total: number
}) => {
  const hideGenerateMaterialModal = openDialog({
    title: '生成素材',
    body: () => <GenerateMaterialForm series_key={series_key} title={title} total={total} onHide={hideGenerateMaterialModal} onSuccess={() => onGenerateSuccess(hideGenerateMaterialModal)} />,
    customClass: '!pb-0',
  })
}

// 用于存储轮询的定时器ID
const pollInterval = ref(0)

// 发起轮询请求的函数
const startPolling = (next: () => void) => {
  pollInterval.value = window.setTimeout(() => {
    next()
  }, 5000) // 这里设置轮询间隔为5秒，可根据需要调整
}

// 停止轮询请求的函数
const stopPolling = () => {
  if (pollInterval.value) {
    window.clearTimeout(pollInterval.value)
    pollInterval.value = 0
    console.log('Polling stopped.')
  }
}

const getMaterials = () => {
  const next = async () => {
    try {
      const rs = await apiGetMaterialList({ series_key: series_key.value })

      episode.value = rs.data || {}
      materials.value = rs.data?.materials || []

      if ([1, 3].includes(episode.value.material_status || 0)) {
        startPolling(next)
      } else {
        stopPolling()
      }
    } catch (error) {
      console.log('>>> getMaterials error', error)
      stopPolling()
    }
  }

  void next()
}

const onDelete = (material_number: number) => {
  const hideDeleteDialog = openDialog({
    title: '',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-stop-clip-video-confirm-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-stop-clip-video-confirm-body>确认删除编号【{material_number}】的素材吗？</x-stop-clip-video-confirm-body>
        <x-stop-clip-video-confirm-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            void apiDeleteMaterial({ series_key: series_key.value, material_number })
              .then(() => {
                hideDeleteDialog()
                getMaterials()
              })
              .catch((error: any) => {
                showAlert(error.response.data.message || '提交失败', 'error')
              })
          }}
          >
            确定
          </button>
        </x-stop-clip-video-confirm-footer>
      </x-stop-clip-video-confirm-confirm-dialog>
    ),
  })
}
