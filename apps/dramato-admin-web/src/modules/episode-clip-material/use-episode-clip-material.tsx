/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { apiDeleteMaterialSegment, apiDownloadMaterialSegment, apiGetMaterialDetails, apiSaveMaterialSegment } from './episode-clip-material-api'
import { useEpisodeClipMaterialPlayer } from './use-episode-clip-material-player'
import { openDialog, showAlert } from '@skynet/ui'
import { PreviewMaterial } from './preview-material'

export const useEpisodeClipMaterial = () => {
  return {
    material,
    getMaterialDetails,
    palyVideo,
    currentFragmentId,
    currentCutId,
    currentSerialNumber,
    isWave,
    saveMp4,
    saveWave,
    onDelete,
    onPreview,
    onGenerate,
    onDownload,
    // selectSegment,
    currentOrderId,
    needDownloadSegments,
  }
}

const material = ref<M.EpisodeClipMaterial.MaterialDetails>()

const currentOrderId = ref<number>()
const currentFragmentId = ref<number>()
const currentCutId = ref<number>()
const currentSerialNumber = ref<number>()
const isWave = ref<boolean>(false)
export type NeedDownloadSegment = M.EpisodeClipMaterial.Segment & { video_url: string, serial_number: number, isWave?: boolean }
const needDownloadSegments = ref<NeedDownloadSegment[]>([])

const getNeedDownloadSegments = () => {
  needDownloadSegments.value = []
  material.value?.segment_list.forEach(item => {
    const serialNumber = item.serial_number

    item.fragments.forEach(fragment => {
      const cuts = item.cut_segments.filter(c => c.fragment_id === fragment.fragment_id) || []

      cuts.forEach(cut => {
        if (cut.selected === 1) {
          needDownloadSegments.value.push({
            ...cut,
            video_url: fragment?.video_url || '',
            serial_number: serialNumber,
          })
        }
      })
    })
  })
}

const onPreview = () => {
  getNeedDownloadSegments()
  console.log('>>> getNeedDownloadSegments', needDownloadSegments.value)
  if (needDownloadSegments.value.length <= 0) {
    return showAlert('未选择任何内容！', 'error')
  }
  openDialog({
    title: '素材预览',
    body: () => <PreviewMaterial />,
    customClass: '!pb-0 !w-[380px]',
    mainClass: '!w-[380px] flex flex-col items-center',
  })
}

const onGenerate = () => {
  getNeedDownloadSegments()
  console.log('>>> getNeedDownloadSegments', needDownloadSegments.value)

  if (needDownloadSegments.value.length <= 0) {
    return showAlert('未选择任何内容！', 'error')
  }

  void apiDownloadMaterialSegment({
    series_key: material.value?.series_key || '',
    material_number: material.value?.material_number || 0,
    cut_segments: needDownloadSegments.value.map(i => ({
      serial_number: i.serial_number || 0,
      fragment_id: i.fragment_id,
      cut_id: i.cut_id,
    })),
  })
    .then(rs => {
      showAlert('保存成功', 'success')
      getMaterialDetails({ series_key: material.value?.series_key || '', material_number: material.value?.material_number || 0 })
    })
    .catch((error: any) => {
      showAlert(error.response.data.message, 'error')
    })
}

const onDownload = () => {
  const zipUrl = material.value?.download_url || ''

  // 创建一个隐藏的a标签
  const aTag = document.createElement('a')
  aTag.href = zipUrl
  aTag.download = 'file.zip' // 设置下载后的文件名，可自定义
  aTag.style.display = 'none'

  // 将a标签添加到文档 body 中
  document.body.appendChild(aTag)

  // 模拟点击a标签，触发下载
  aTag.click()

  // 下载完成后，移除a标签
  document.body.removeChild(aTag)
}

const getMaterialDetails = (params: M.EpisodeClipMaterial.CommonParams) => {
  const next = async () => {
    try {
      const rs = await apiGetMaterialDetails(params)

      material.value = rs.data

      if ([2].includes(material.value?.download_status || 0)) {
        startPolling(next)
      } else {
        stopPolling()
      }
    } catch (error) {
      console.log('>>> getMaterials error', error)
      stopPolling()
    }
  }

  void next()
}

const palyVideo = (data: M.EpisodeClipMaterial.Segment & { video_url: string, serial_number: number, order_id: number, isWave?: boolean }) => {
  const { changeVideoUrl } = useEpisodeClipMaterialPlayer()
  if (!data.time_area || data.time_area.length < 2) return
  currentFragmentId.value = data.fragment_id
  currentCutId.value = data.cut_id
  currentSerialNumber.value = data.serial_number
  currentOrderId.value = data.order_id
  isWave.value = !!data.isWave
  changeVideoUrl({
    url: data.video_url,
    startTime: data.time_area[0],
    endTime: data.time_area[1],
  })
}

const onSave = (d: M.EpisodeClipMaterial.SaveMaterialSegmentParams) => {
  const { startTime, endTime } = useEpisodeClipMaterialPlayer()
  void apiSaveMaterialSegment({
    ...d,
    time_area: [startTime.value, endTime.value],
    fragment_number: currentFragmentId.value,
    serial_number: currentSerialNumber.value,
    order_id: currentOrderId.value,
  })
    .then(() => {
      const { series_key, material_number } = d
      showAlert('保存成功', 'success')
      getMaterialDetails({ series_key, material_number })
    })
    .catch((error: any) => {
      showAlert(error.response.data.message || '添加失败', 'error')
    })
}

const saveMp4 = (d: M.EpisodeClipMaterial.SaveMaterialSegmentParams) => {
  onSave({
    ...d,
    cut_type: 1,
  })
}

const saveWave = (d: M.EpisodeClipMaterial.SaveMaterialSegmentParams) => {
  onSave({
    ...d,
    cut_type: 2,
  })
}

const onDelete = (d: { cut_id: number }) => {
  void apiDeleteMaterialSegment(d)
    .then(() => {
      const { changeVideoUrl } = useEpisodeClipMaterialPlayer()
      showAlert('保存成功', 'success')
      getMaterialDetails({ series_key: material.value?.series_key || '', material_number: material.value?.material_number || 0 })
      changeVideoUrl({
        url: '',
        startTime: 0,
        endTime: 0,
      })
    })
    .catch((error: any) => {
      showAlert(error.response.data.message || '删除失败', 'error')
    })
}

// 用于存储轮询的定时器ID
const pollInterval = ref(0)

// 发起轮询请求的函数
const startPolling = (next: () => void) => {
  pollInterval.value = window.setTimeout(() => {
    next()
  }, 5000) // 这里设置轮询间隔为5秒，可根据需要调整
}

// 停止轮询请求的函数
const stopPolling = () => {
  if (pollInterval.value) {
    window.clearTimeout(pollInterval.value)
    pollInterval.value = 0
    console.log('Polling stopped.')
  }
}
