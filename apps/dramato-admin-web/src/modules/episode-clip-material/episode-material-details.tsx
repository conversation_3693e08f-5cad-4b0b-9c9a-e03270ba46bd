/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateTableOld, Icon, showAlert } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useRoute } from 'vue-router'
import { useEpisodeClipMaterial } from './use-episode-clip-material'
import { onMounted, watch } from 'vue'
import { MaterialType } from './episode-clip-material-config'
import { EpisodeMaterialPlayer, MSConfig } from './episode-material-player'
import { useClipboard } from '@vueuse/core'

export const EpisodeMaterialDetails = createComponent(null, () => {
  const {
    material,
    getMaterialDetails,
    palyVideo,
    currentSerialNumber,
    currentFragmentId,
    currentCutId,
    isWave,
    saveMp4,
    saveWave,
    onDelete,
    onPreview,
    onGenerate,
    onDownload,
  } = useEpisodeClipMaterial()

  const route = useRoute()

  const Table = CreateTableOld<M.EpisodeClipMaterial.SegmentListItem>()

  const { copy, copied } = useClipboard()

  const columns: TableColumnOld<M.EpisodeClipMaterial.SegmentListItem>[] = [
    ['素材脚本区', row => (
      <>
        <x-title class="w-full flex flex-row gap-x-2 mb-4 items-center">
          第{row.serial_number}集
          <Icon
            name="ant-design:copy-outlined"
            onClick={() => {
              void copy(`第${row.serial_number}集\n` + (row.fragments || []).map(i => `${i.desc}\n ${MSConfig.transformTimeMsToStr(i.time_area[0] / 1000)}-${MSConfig.transformTimeMsToStr(i.time_area[1] / 1000)}`).join('\n'))
            }}
          />
        </x-title>
        {
          (row.fragments || []).map(i => row.order_id !== i.fragment_id ? null : (
            <x-fragment class="w-full flex flex-col gap-y-2 mb-4">
              <x-fragment-desc>{i.desc}</x-fragment-desc>
              <x-fragment-desc> {MSConfig.transformTimeMsToStr(i.time_area[0] / 1000)}-{MSConfig.transformTimeMsToStr(i.time_area[1] / 1000)}</x-fragment-desc>
            </x-fragment>
          ))
        }

      </>
    ), { class: 'w-[calc(100%_-_400px)] min-w-[calc(100%_-_400px)]' }],
    ['视频片段区',
      row => (
        <x-material-fragment class="flex flex-col gap-y-2">
          {
            (row.fragments || []).map(i => row.order_id !== i.fragment_id ? null : (
              <x-material-fragment-item key={i.fragment_id} title={`点击原片段${row.serial_number}播放`} class="p-4 border border-solid rounded-lg cursor-pointer flex flex-row items-start gap-x-2">
                <x-fragment class="w-[200px] flex flex-col gap-y-2">
                  <x-title class={mc('font-bold flex flex-row items-center gap-x-2', !currentCutId.value && currentSerialNumber.value === row.serial_number && currentFragmentId.value === i.fragment_id && 'text-blue-500')}>
                    原片段{i.fragment_id}
                    <Icon name="ant-design:play-circle-outlined" onClick={() => palyVideo({ ...i, serial_number: row.serial_number, order_id: row.order_id })} />

                  </x-title>
                </x-fragment>
                <x-material-segment class="flex flex-col gap-y-2">
                  <x-title class="font-bold border-l-2 border-solid pl-2">自定义片段/音轨</x-title>
                  <x-material-segment-content class="flex flex-row gap-x-2 pl-2">
                    <x-cut-segments class={mc('flex flex-col gap-y-2', (row.cut_segments || []).filter(seg => i.fragment_id === seg.fragment_id).length === 0 && 'hidden')}>
                      {
                        (row.cut_segments || []).filter(seg => i.fragment_id === seg.fragment_id).map(seg => (
                          <x-cut-segment key={`pd-${i.fragment_id}-${seg.cut_id}`} class="flex flex-row items-center">
                            <Checkbox
                              label={(
                                <x-title
                                  onClick={(e: any) => {
                                    e.stopPropagation()
                                    e.preventDefault()
                                  }}
                                  class={mc('flex flex-row items-center gap-x-1', !isWave.value && currentSerialNumber.value === row.serial_number && currentFragmentId.value === i.fragment_id && currentCutId.value === seg.cut_id && 'text-blue-500')}
                                >
                                  片段{i.fragment_id}-{seg.mark_id}
                                  <Icon name="ant-design:play-circle-outlined" onClick={() => palyVideo({ ...seg, video_url: i.video_url, serial_number: row.serial_number, order_id: row.order_id, isWave: false })} />
                                  <Icon name="ant-design:delete-outlined" onClick={() => onDelete({ cut_id: seg.cut_id || 0 })} />
                                </x-title>
                              )}
                              disabled={material.value?.download_status === 2}
                              modelValue={seg.selected === 1}
                              onUpdate:modelValue={(v: boolean) => {
                                seg.selected = v ? 1 : 0
                              }}
                            />
                          </x-cut-segment>
                        ),
                        )
                      }
                    </x-cut-segments>
                    <x-wav-cut-segments class="flex flex-col gap-y-2">
                      {
                        (row.cut_wav_segments || []).filter(seg => i.fragment_id === seg.fragment_id).map(seg => (
                          <x-cut-segment key={`yg-${i.fragment_id}-${seg.cut_id}`} class="flex flex-row items-center">
                            <Checkbox
                              label={(
                                <x-title
                                  onClick={(e: any) => {
                                    e.stopPropagation()
                                    e.preventDefault()
                                  }}
                                  class={mc('flex flex-row items-center gap-x-1', isWave.value && currentSerialNumber.value === row.serial_number && currentFragmentId.value === i.fragment_id && currentCutId.value === seg.cut_id && 'text-blue-500')}
                                >
                                  音轨{i.fragment_id}-{seg.mark_id}
                                  <Icon name="ant-design:play-circle-outlined" onClick={() => palyVideo({ ...seg, video_url: i.video_url, serial_number: row.serial_number, order_id: row.order_id, isWave: true })} />
                                  <Icon name="ant-design:delete-outlined" onClick={() => onDelete({ cut_id: seg.cut_id || 0 })} />
                                </x-title>
                              )}
                              modelValue={seg.selected === 1}
                              onUpdate:modelValue={(v: boolean) => {
                                seg.selected = v ? 1 : 0
                              }}
                            />
                          </x-cut-segment>
                        ),
                        )
                      }
                    </x-wav-cut-segments>
                  </x-material-segment-content>
                </x-material-segment>
              </x-material-fragment-item>
            ))
          }
        </x-material-fragment>
      ),
      {
        class: 'w-[400px]',
      },
    ],
  ]

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  onMounted(() => {
    void getMaterialDetails({
      series_key: route.params.seriesKey as string,
      material_number: +(route.params.materialNumber as string),
    })

    // TEST
    // changeVideoUrl({
    //   url: 'https://img.tianmai.cn/resource/test/9980/2b3aa976-25d4-48a8-a049-0a1e75ca9d10.mp4',
    //   startTime: 0,
    //   endTime: 12220,
    // })
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>AI拆素材-结果页</li>
          </ul>
        ),
        form: () => (
          <div class="flex flex-col gap-y-2 text-[14px]">
            <div class="text-black font-semibold text-[20px]">短剧名称: {route.params.title as string || ''}</div>
            <div>素材编号：{route.params.materialNumber as string}</div>
            <div>素材类型: {['', ...MaterialType][material.value?.material_type || 0] || '未知'}</div>
            <div class="">时长: {material.value?.duration}</div>
            <div class="">包含集数: {(material.value?.episodes || []).join(',') || '-空-'}</div>
            <div class="">素材梗概: {material.value?.scripts || '-空-'}</div>
            <div class="">tags: {(material.value?.tags || []).join(',') || '-空-'}</div>
          </div>
        ),
        tableActions: () => (
          <x-table-actions class="w-full flex justify-between items-center">
            <span>片段列表</span>
            <x-table-actions-right class="flex gap-x-2">
              <Button
                class="btn btn-primary btn-sm"
                disabled={material.value?.download_status === 2}
                onClick={() => {
                  onPreview()
                }}
              >
                预览
              </Button>

              <Button
                class="btn btn-primary btn-sm"
                disabled={material.value?.download_status === 2}
                onClick={() => {
                  onGenerate()
                }}
              >
                {
                  [
                    '生成',
                    '生成',
                    '生成中',
                    '重新生成',
                    '重新生成（生成失败）',
                  ][material.value?.download_status || 0]
                }
              </Button>

              <Button
                class="btn btn-primary btn-sm"
                disabled={material.value?.download_status !== 3 || !material.value?.download_url}
                onClick={() => {
                  onDownload()
                }}
              >
                下载
              </Button>
            </x-table-actions-right>
          </x-table-actions>
        ),
        table: () => (
          <x-material-content class="w-full flex gap-x-2 bg-none">
            <Table list={material.value?.segment_list || []} columns={columns} loading={false} class="tm-table-fix-last-column flex-1" />
            <EpisodeMaterialPlayer timeDisable={!!currentCutId.value} customClass="h-[calc(100vh_-_var(--top-bar-height))] sticky top-[var(--top-bar-height)] overflow-auto">
              {{
                footer: () => currentCutId.value
                  ? null
                  : (
                      <>
                        <Button class="btn btn-primary btn-xs" onClick={() => saveMp4({
                          series_key: route.params.seriesKey as string,
                          material_number: +(route.params.materialNumber as string),
                          // order_id:
                        })}
                        >
                          保存片段
                        </Button>
                        <Button class="btn btn-primary btn-xs" onClick={() => saveWave({
                          series_key: route.params.seriesKey as string,
                          material_number: +(route.params.materialNumber as string),
                        })}
                        >
                          保存音轨
                        </Button>
                      </>
                    ),
              }}
            </EpisodeMaterialPlayer>
          </x-material-content>
        ),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeMaterialDetails
