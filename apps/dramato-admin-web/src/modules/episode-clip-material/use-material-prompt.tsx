/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog } from '@skynet/ui'
import { showAlert } from '@skynet/ui/alert/alert'
import { ref } from 'vue'
import { PromptSetting } from './prompt-setting'
import { apiAddPrompt, apiGetPromptList } from './episode-clip-material-api'

export const useMaterialPrompt = () => {
  return {
    promptList,
    getPromptList,
    addPrompt,
    cannelAddPrompt,
    confirmAddPrompt,
    currentNav,
    currentPrompt,
    needEditPromptId,
    openPromptSettingModal,
  }
}

// const currentPromptId = ref<number>(0)
const currentNav = ref<string>('')
const currentPrompt = ref<M.EpisodeClipMaterial.Prompt>()
const needEditPromptId = ref<number[]>([])

const promptList = ref<M.EpisodeClipMaterial.Prompt[]>([])

const getPromptList = async (data: {
  material_type: number
}) => {
  const rs = await apiGetPromptList(data)
  promptList.value = rs.data?.list || []
  currentPrompt.value = promptList.value[0]
  currentNav.value = currentPrompt.value?.prompts[0]?.title
}

const addPrompt = () => {
  let prompt_id = 1
  let prePrompt: M.EpisodeClipMaterial.Prompt = {
    prompts: [
      {
        title: '粗略素材脚本',
        content: 'prompt 内容',
      },
      {
        title: '精细素材脚本',
        content: 'prompt 内容',
      },
    ],
  }

  if (promptList.value.length > 0) {
    prompt_id = (promptList.value[0]?.prompt_id || 0) + 1
    prePrompt = promptList.value[0]
  }

  if (needEditPromptId.value.includes(prePrompt?.prompt_id || 0)) {
    const list = promptList.value.filter(j => !(needEditPromptId.value.includes(j.prompt_id || 0)))
    prePrompt = list[0]
  }

  const newPrompt = Object.assign({}, prePrompt, {
    prompts: [...prePrompt.prompts.map(i => ({ ...i }))],
    prompt_id,
  })

  currentPrompt.value = newPrompt
  currentNav.value = currentPrompt.value?.prompts[0]?.title
  needEditPromptId.value.push(prompt_id || 0)
  promptList.value.unshift(newPrompt)
}

const cannelAddPrompt = () => {
  needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPrompt.value?.prompt_id)
  promptList.value = promptList.value.filter(item => item.prompt_id !== currentPrompt.value?.prompt_id)

  currentPrompt.value = promptList.value[0]
  currentNav.value = currentPrompt.value?.prompts[0]?.title
}

const confirmAddPrompt = async (material_type: number) => {
  try {
    const errorKeys: string[] = []
    const prompt = currentPrompt.value as M.EpisodeClipMaterial.Prompt

    prompt.prompts.forEach(item => {
      if (!item.content) {
        errorKeys.push(item.title)
      }
    })

    if (errorKeys.length) {
      return showAlert(`请填写${errorKeys.join('、')}`, 'error')
    }

    needEditPromptId.value = needEditPromptId.value.filter(id => id !== currentPrompt.value?.prompt_id)

    const rs = await apiAddPrompt({
      material_type: +material_type,
      prompts: prompt.prompts,
    })

    if (rs.code !== 200) {
      return showAlert(`添加失败,${rs.message}`, 'error')
    }

    showAlert('添加成功', 'success')

    // 模拟add响应数据
    const newPrompt: M.EpisodeClipMaterial.Prompt = rs.data?.prompt || {
      prompts: [
        {
          title: '粗略素材脚本',
          content: 'prompt 内容',
        },
        {
          title: '精细素材脚本',
          content: 'prompt 内容',
        },
      ],
    }

    promptList.value = promptList.value.map(item => {
      if (item.prompt_id !== currentPrompt.value?.prompt_id) {
        return item
      }
      return {
        ...item,
        ...newPrompt,
      }
    })
    currentPrompt.value = { ...newPrompt }
  } catch (error: any) {
    showAlert(error.response.data.message || '添加失败', 'error')
  }
}

const openPromptSettingModal = (material_type: number) => {
  openDialog({
    title: '提示设置',
    body: (
      <PromptSetting material_type={material_type} />
    ),
    beforeClose: () => {
      void getPromptList({ material_type })
    },
    mainClass: 'w-[820px] flex justify-center items-center',
    customClass: '!w-[820px]',
  })
}
