/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Icon, showAlert } from '@skynet/ui'
import { watch } from 'vue'
import { useEpisodeClipMaterialPlayer } from './use-episode-clip-material-player'
type EpisodeMaterialPlayerOptions = {
  props: {
    customClass?: string
    timeDisable?: boolean
  }
  slots: any
}

export const EpisodeMaterialPlayer = createComponent<EpisodeMaterialPlayerOptions>({
  props: {
    customClass: '',
    timeDisable: false,
  },
}, (props, { slots }) => {
  const {
    player,
    duration,
    startTime,
    startTimeDisplay,
    endTime,
    endTimeDisplay,
    videoUrl,
    onStartTimeChange,
    onEndTimeChange,
    loadVideoIdx,
  } = useEpisodeClipMaterialPlayer()

  watch(
    () => videoUrl.value,
    () => {
      if (!videoUrl.value) duration.value = 0
    },
    { immediate: true },
  )

  const onMinus = (type: 'start' | 'end') => {
    if (videoUrl.value === '' || props.timeDisable) {
      return
    }
    let time = type === 'start' ? startTime.value : endTime.value
    if (time - 1000 <= 0) {
      time = 0
    } else {
      time -= 1000
    }
    if (type === 'start') {
      startTime.value = time
      onStartTimeChange(true)
    } else {
      endTime.value = time <= startTime.value ? startTime.value + 1000 > duration.value ? duration.value : startTime.value + 1000 : time
      onEndTimeChange(true, true)
    }
  }

  const onPlus = (type: 'start' | 'end') => {
    if (videoUrl.value === '' || props.timeDisable) {
      return
    }
    let time = type === 'start' ? startTime.value : endTime.value
    if (time + 1000 >= duration.value) {
      time = duration.value
    } else {
      time += 1000
    }
    if (type === 'start') {
      startTime.value = time > endTime.value ? endTime.value - 1000 <= 0 ? 0 : endTime.value - 1000 : time
      onStartTimeChange(true)
    } else {
      endTime.value = time
      onEndTimeChange(true, true)
    }
  }

  return () => (
    <div class={mc('border border-1 border-solid rounded-lg mr-2 mt-[40.5px] flex flex-col gap-y-2', props.customClass)}>
      {slots.title?.()}
      <video
        class={mc('w-[350px] min-h-[550px] max-h-[550px] bg-black ', slots.title ? '' : 'rounded-t-lg')}
        ref={player}
        crossorigin="anonymous"
        onLoadedmetadata={(e: any) => {
          if (!e.target.duration) {
            return
          }

          duration.value = Math.round(e.target.duration * 1000)

          if (player.value && startTime.value) {
            player.value.currentTime = startTime.value / 1000
            loadVideoIdx.value.push(videoUrl.value || '')
            void player.value.play()
          }
        }}
        onTimeupdate={(e: any) => {
          if (!e.target.duration) {
            return
          }

          const currentTime = Math.round(e.target.currentTime * 1000)

          if (startTime.value <= 0 && endTime.value <= 0) {
            return
          }

          if (currentTime < startTime.value && player.value) {
            player.value.pause()
            player.value.currentTime = startTime.value / 1000
          }

          if (currentTime > endTime.value && player.value) {
            player.value.pause()
            player.value.currentTime = endTime.value / 1000
          }

          if (currentTime === endTime.value && player.value) {
            player.value.currentTime = startTime.value / 1000
            void player.value.play()
          }
        }}
        src={videoUrl.value}
        controls
        autoplay
      />

      <x-player-time-clip class="flex flex-col gap-y-2 px-2 text-[14px] font-medium">
        <x-player-start-time-progress class="flex justify-between items-center">
          开始时间
          <x-input class="flex items-center gap-x-2">
            <Icon name="ant-design:minus-square-outlined" class="size-5 cursor-pointer" onClick={() => onMinus('start')} />
            <input
              type="text"
              value={startTimeDisplay.value}
              class="input input-bordered input-sm w-[126px]"
              placeholder="HH:mm:ss.SSS"
              disabled={videoUrl.value === '' || props.timeDisable}
              onBlur={(e: any) => {
                if (!isMsTimeStr(e.target.value)) {
                  showAlert('请输入正确的毫秒时间格式', 'error')
                  return
                }
                startTime.value = transformTimeStrToMs(e.target.value)
                onStartTimeChange(true)
              }}
            />
            <Icon name="ant-design:plus-square-outlined" class="size-5 cursor-pointer" onClick={() => onPlus('start')} />
          </x-input>

        </x-player-start-time-progress>
        <input
          type="range"
          id="startBar"
          min="0"
          max="100"
          value={duration.value <= 0 ? 0 : startTime.value / duration.value * 100}
          class={mc('range range-xs', videoUrl.value === '' ? '' : 'range-primary')}
          onChange={(e: any) => {
            const v = Math.round((e.target.value / 100) * duration.value)
            if (endTime.value && v >= endTime.value) {
              return showAlert('开始时间不能大于等于截止时间', 'error')
            }
            startTime.value = v
            onStartTimeChange(true)
          }}
          disabled={videoUrl.value === '' || props.timeDisable}
        />
        <x-player-end-time-progress class="flex justify-between items-center">
          截止时间
          <x-input class="flex items-center gap-x-2">
            <Icon name="ant-design:minus-square-outlined" class="size-5 cursor-pointer" onClick={() => onMinus('end')} />
            <input
              type="text"
              value={endTimeDisplay.value}
              class="input input-bordered input-sm w-[126px]"
              placeholder="HH:mm:ss.SSS"
              onBlur={(e: any) => {
                if (!isMsTimeStr(e.target.value)) {
                  showAlert('请输入正确的毫秒时间格式', 'error')
                  return
                }
                endTime.value = transformTimeStrToMs(e.target.value)
                onEndTimeChange(true, true)
              }}
              disabled={videoUrl.value === '' || props.timeDisable}
            />
            <Icon name="ant-design:plus-square-outlined" class="size-5 cursor-pointer" onClick={() => onPlus('end')} />
          </x-input>
        </x-player-end-time-progress>
        <input
          type="range"
          id="endBar"
          min="0"
          max="100"
          value={duration.value <= 0 ? 0 : endTime.value / duration.value * 100}
          class={mc('range range-xs', videoUrl.value === '' ? '' : 'range-primary')}
          onChange={(e: any) => {
            const v = Math.round((e.target.value / 100) * duration.value)
            if (startTime.value && v <= startTime.value) {
              return showAlert('截止时间不能小于等于开始时间', 'error')
            }
            endTime.value = Math.round((e.target.value / 100) * duration.value)
            onEndTimeChange(true, true)
          }}
          disabled={videoUrl.value === '' || props.timeDisable}
        />
        {slots.otherForm?.()}
      </x-player-time-clip>

      <x-player-footer class="flex justify-end gap-x-2 px-2 py-2">
        {/* <Button class="btn btn-primary btn-sm" onClick={captureFrame}>截图</Button> */}
        {slots.footer?.()}
        {/* <Button class="btn btn-primary btn-xs">保存片段</Button>
        <Button class="btn btn-primary btn-xs">保存音轨</Button> */}
      </x-player-footer>
    </div>
  )
})

const isMsTimeStr = (timeStr: string) => {
  console.log('e.target.value', timeStr, /\d{2}:\d{2}:\d{2}\.\d{3}$/.test(timeStr))

  return /^\d{2}:\d{2}:\d{2}\.\d{3}$/.test(timeStr.trim())
}

const transformTimeStrToMs = (timeStr: string) => {
  const [hours, minutes, secondsAndMillis] = timeStr.split(':')
  const [seconds, milliseconds] = secondsAndMillis.split('.')
  const totalSeconds = parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds)
  const timestamp = totalSeconds * 1000 + parseInt(milliseconds)

  return timestamp
}

const fillTimeStrFor0 = (t: number, c: number = 2) => t.toString().padStart(c, '0')

const transformTimeMsToStr = (timeMs: number) => {
  const hours = Math.floor(timeMs / 3600)
  const minutes = Math.floor((timeMs % 3600) / 60)
  const seconds = Math.floor(timeMs % 60)
  const milliseconds = Math.floor((timeMs % 1) * 1000)
  return `${fillTimeStrFor0(hours)}:${fillTimeStrFor0(minutes)}:${fillTimeStrFor0(seconds)}.${fillTimeStrFor0(milliseconds, 3)}`
}

export const MSConfig = {
  isMsTimeStr,
  transformTimeStrToMs,
  transformTimeMsToStr,
  fillTimeStrFor0,
}
