/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformNumber, transformStringArray } from '@skynet/ui'
import set from 'lodash-es/set'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { MaterialDuration, MaterialType } from './episode-clip-material-config'
import dayjs from 'dayjs'
import { useMaterialPrompt } from './use-material-prompt'
import { UseEpisodeClipMaterialList } from './use-episode-clip-material-list'
import { watch } from 'vue'
import { apiGenerateMaterial } from './episode-clip-material-api'

type GenerateMaterialFormOptions = {
  props: {
    series_key: string
    title: string
    total: number
  }
  emits: {
    hide: () => void
    success: () => void
  }
}
export const GenerateMaterialForm = createComponent<GenerateMaterialFormOptions>({
  props: {
    series_key: '',
    title: '',
    total: 0,
  },
  emits: {
    hide: () => { },
    success: () => { },
  },
}, (props, { emit }) => {
  const {
    promptList,
    openPromptSettingModal,
    getPromptList,
  } = useMaterialPrompt()

  const {
    generateMaterialParams: material,
    series_key,
  } = UseEpisodeClipMaterialList()
  const Form = CreateForm<M.EpisodeClipMaterial.GenerateMaterialParams>()

  const formRules = z.object({
    material_type: z.number({
      message: '请选择素材类型',
    }),
    duration: z.string({
      message: '请选择时长',
    }).min(1),
    episodes: z.array(z.string()).min(1),
    prompt_id: z.number({
      message: '请选择prompt',
    }),
    count: z.number({
      message: '请输入数量',
    }).min(1),
  })

  const { error, validateAll } = useValidator(material, formRules)

  const fillAllEpisodeNum = () => {
    material.value.episodes = Array.from({ length: props.total }, (_, i) => String(i + 1))
  }

  watch(
    () => material.value.material_type,
    () => {
      if (!material.value.material_type) {
        return
      }
      void getPromptList({ material_type: material.value.material_type })
    },
    {
      deep: true,
      immediate: true,
    },
  )

  return () => (
    <x-generate-material-model-content>
      <Form
        class="grid gap-y-3 grid-cols-1"
        hasAction={false}
        error={error.value}
        onChange={(path, value: any) => {
          set(material.value, path, value)
        }}
        items={[
          [
            '短剧名称',
            '',
            {
              type: 'custom',
              render: () => props.title || '-空-',
            },
          ],
          [
            '短剧总集数',
            '',
            {
              type: 'custom',
              render: () => props.total,
            },
          ],
          [
            requiredLabel('素材类型'),
            'material_type',
            {
              type: 'select',
              name: 'name',
              placeholder: '请选择素材类型',
              options: MaterialType.map((item: string, idx: number) => ({
                label: item,
                value: idx + 1,
              })),
              autoInsertEmptyOption: false,
            },
            {
              transform: transformNumber,
            },
          ],
          [
            requiredLabel('时长'),
            'duration',
            {
              type: 'select',
              name: 'name',
              placeholder: '请选择素材类型',
              options: MaterialDuration.map((item: string) => ({
                label: item,
                value: item,
              })),
              autoInsertEmptyOption: false,
            },
          ],
          [
            requiredLabel('包含集数'),
            'episodes',
            {
              type: 'textarea',
              placeholder: '请填写要拆解的集数，以英文逗号隔开，例如：1,2,5,9,26',
              rows: 4,
            },
            {
              transform: transformStringArray,
              hint: () => <Button class="btn btn-outline btn-xs mt-2 self-end" onClick={fillAllEpisodeNum}>全部</Button>,
            },
          ],
          [
            requiredLabel('Prompt编号'),
            'prompt_id',
            {
              type: 'select',
              placeholder: '请选择字幕提取方式',
              options: promptList.value.map(i => {
                return {
                  label: dayjs((i.created || 0) * 1000).format('YYYY-MM-DD HH:mm:ss') + `/ 编号-${i.prompt_id}`,
                  value: i.prompt_id as number,
                }
              }),
              // autoInsertEmptyOption: false,
            },
            {
              transform: transformNumber,
              hint: () => (
                <Button class="btn btn-outline btn-xs mt-2 self-end"
                  onClick={() => {
                    if (!material.value.material_type) {
                      return showAlert('请先选择素材类型', 'error')
                    }

                    openPromptSettingModal(material.value.material_type)
                  }}
                >
                  高级设置
                </Button>
              ),
            },
          ],
          [
            requiredLabel('生成条数'),
            'count',
            {
              type: 'number',
              placeholder: '请填写生成条数...例: 5',
              max: 10,
            },
            {
              transform: transformNumber,
            },
          ],
        ]}
        data={material.value}
      />
      <x-generate-material-model-footer class="flex justify-end gap-x-2">
        <Button class="btn btn-sm" onClick={() => emit('hide')}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            return
          }

          void apiGenerateMaterial({
            ...material.value,
            series_key: series_key.value,
            episodes: material.value?.episodes?.map(episode => +(episode)),
          })
            .then(() => {
              emit('success')

              const key = `dramato-admin-web.clip.${series_key.value}`

              window.localStorage.setItem(key, JSON.stringify([]))
            })
            .catch((error: any) => {
              // do nothing
              showAlert(error.response.data.message || '提交失败', 'error')
            })
        }}
        >确定
        </Button>
      </x-generate-material-model-footer>
    </x-generate-material-model-content>
  )
})
