import { httpClient } from 'src/lib/http-client'

export const apiGetAdUnitList = (data: M.AdUnit.params) =>
  httpClient.post<ApiResponse<M.AdUnit.ListResponse>>('/ads/units/list', data)

export const apiCreateAdUnit = (data: M.AdUnit.CreateAd) =>
  httpClient.post<ApiResponse<boolean>>('ads/units/save', data)

export const apiEditAdUnit = (data: M.AdUnit.CreateAd) =>
  httpClient.post<ApiResponse<boolean>>('/ads/units/update', data)
