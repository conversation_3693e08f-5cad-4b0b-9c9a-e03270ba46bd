import { createComponent } from '@skynet/shared'
import { <PERSON><PERSON>, Pager } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useAdUnit } from './use-unit'
import { onMounted } from 'vue'
import { Countries } from '../material-push/config'
type AdUnitOptions = {
  props: {}
}
export const AdUnit = createComponent<AdUnitOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    onCreateBtnClick,
    onEditBtnClick,
  } = useAdUnit()

  onMounted(() => {
    void search(1)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>剧集定价</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { platform: 'ios' }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['平台', 'platform', { type: 'select', options: [{ label: 'ios', value: 'ios' }, { label: 'android', value: 'android' }], autoInsertEmptyOption: false }],
              ['Ad Unit', 'ad_unit', { type: 'text' }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            广告单元表
            <Button class="btn-primary btn btn-sm" onClick={onCreateBtnClick}>新增广告单元</Button>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['优先级', 'priority', { class: 'w-[100px]' }],
            ['广告单元名称', 'name', { class: 'w-[200px]' }],
            ['Ad Unit', 'ad_unit', { class: 'w-[300px]' }],
            ['聚合平台', 'ad_platform', { class: 'w-[100px]' }],
            ['广告单元低价', r => (
              <x-price class="flex flex-col gap-2">
                {Object.keys(JSON.parse(r.price_config || '{}')).map((key: string) => (
                  <x-price-item>
                    {[{ value: 'ALL', label: '全部' }, ...Countries].find(i => i.value === key)?.label || key}-{JSON.parse(r.price_config || '{}')[key]}
                  </x-price-item>
                ))}
              </x-price>
            ), { class: 'w-[200px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
              </div>
            ), {
              class: 'w-[100px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default AdUnit
