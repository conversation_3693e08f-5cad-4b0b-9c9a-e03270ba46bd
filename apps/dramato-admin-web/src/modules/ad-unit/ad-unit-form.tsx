/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useAdUnit } from './use-unit'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { Countries } from '../material-push/config'
type AdUnitFormOptions = {
  props: {}
}
const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()

export const AdUnitForm = createComponent<AdUnitFormOptions>({
  props: {},
}, props => {
  const {
    currentAd,
    closeAdDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useAdUnit()

  const Form = CreateForm<M.AdUnit.CreateAd>()
  const formRules = z.object({
    priority: z.number().min(1, '请填写优先级').max(999, '支持输入1-999的整数'),
    name: z.string().min(1, '请输入广告单元名称'),
    ad_unit: z.string().min(1, '请输入广告单元ID'),
    price_config_list: z.array(z.object({
      v: z.number().min(0, '请输入价格').max(999999, '支持输入0-999999的整数'),
      c: z.array(z.string()).min(1, '请选择国家'),
    })).min(1, '请填写定价'),
    ad_platform: z.string().min(1, '请选择广告平台'),
  })

  const { error, validateAll } = useValidator(currentAd, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentAd.value || {}, path, value)
          }}
          items={[
            [requiredLabel('平台'), 'platform', { type: 'select', options: [{ label: 'ios', value: 'ios' }, { label: 'android', value: 'android' }], autoInsertEmptyOption: false }],
            [
              requiredLabel('优先级（数字越大 优先级越高）'),
              'priority',
              {
                type: 'number',
                max: 999,
                min: 1,
                placeholder: '请输入优先级, 支持输入 1-999 的整数',
              },
              {
                transform: transformInteger,
              },
            ],
            [
              requiredLabel('广告单元名称'),
              'name',
              {
                type: 'text',
                placeholder: '请输入命名，以便识别',
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('聚合平台'),
              'ad_platform',
              {
                type: 'radio',
                options: [{ label: 'admob', value: 'admob' }, { label: 'max', value: 'max' }, { label: 'meta', value: 'meta' }],
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('Ad Unit'),
              'ad_unit',
              {
                type: 'text',
                placeholder: '请从广告平台提取输入',
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('定价'),
              'price_config_list',
              {
                type: 'custom',
                render: (r: any) => {
                  console.log('r.value', r.value)
                  return (
                    <x-push-desc-list class="flex flex-col gap-2">
                      {
                        (r.value || [])
                          .map((i: any, idx: number) => (
                            <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                              <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                                <input
                                  type="number"
                                  class="grow"
                                  value={i.v || 0}
                                  onInput={(e: any) => {
                                    if (!currentAd.value.price_config_list) {
                                      return
                                    }
                                    const value = e.target.value
                                    currentAd.value.price_config_list[idx].v = +value
                                  }}
                                />
                              </span>
                              <FormMultiSelect
                                search
                                class="w-full"
                                popoverWrapperClass="z-popover-in-dialog"
                                modelValue={i.c}
                                onUpdate:modelValue={e => i.c = e as string[]}
                                maxlength={1}
                                options={[{ value: 'ALL', label: '全部' }, ...Countries]}
                              />
                              <Icon name="ant-design:delete-filled" class="size-4 cursor-pointer" onClick={() => {
                                if (!currentAd.value.price_config_list) {
                                  return
                                }
                                currentAd.value.price_config_list.splice(idx, 1)
                              }}
                              />
                            </div>
                          ))
                      }
                      <Button
                        class={mc('btn btn-sm btn-outline')}
                        onClick={() => {
                          if (!currentAd.value.price_config_list) {
                            currentAd.value.price_config_list = []
                          }

                          currentAd.value.price_config_list.push({
                            c: [],
                            v: 0,
                          })
                        }}
                      >新增
                      </Button>
                    </x-push-desc-list>
                  )
                },
              },
            ],
          ]}
          data={currentAd.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeAdDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentAd.value?.id ? void onCreate() : void onEdit()

            try {
              const exclude: string[] = []

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
