/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateAdUnit, apiEditAdUnit, apiGetAdUnitList } from './ad-unit-api'
import { AdUnitForm } from './ad-unit-form'

export const useAdUnit = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    showADDialog,
    closeAdDialog,
    currentAd,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
  }
}

const Form = CreateForm<M.AdUnit.params>()
const params = ref<M.AdUnit.params>({
  platform: 'ios',
})

const Table = CreateTableOld<M.AdUnit.Ad>()
const list = ref<M.AdUnit.Ad[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)

const currentAd = ref<M.AdUnit.CreateAd>({})

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetAdUnitList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value } })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  page.value = _page
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeAdDialog = ref()

const showADDialog = () => {
  closeAdDialog.value = openDialog({
    title: currentAd.value.id ? '编辑广告单元' : '新建广告单元',
    body: () => <AdUnitForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentAd.value = {
    price_config_list: [],
    platform: params.value.platform,
  }
  showADDialog()
}

const onEditBtnClick = (r: M.AdUnit.Ad) => {
  currentAd.value = {
    ...r,
    price_config_list: Object.keys(JSON.parse(r.price_config || '')).map(k => ({
      c: [k],
      v: JSON.parse(r.price_config || '')[k],
    })),
    platform: params.value.platform,
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeAdDialog.value && closeAdDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search(page.value)
}

const switchRequestParams = () => ({
  ...currentAd.value,
  price_config: JSON.stringify((currentAd.value.price_config_list || []).reduce((acc, cur) => ({ ...acc, [cur.c[0]]: cur.v }), {})),
//   id: currentPopupScene.value.id || 0,
  //   start_time: currentPopupScene.value.start_time ? new Date(currentPopupScene.value.start_time || '').getTime() / 1000 : undefined,
  //   end_time: currentPopupScene.value.end_time ? new Date(currentPopupScene.value.end_time || '').getTime() / 1000 : undefined,
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateAdUnit(switchRequestParams())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditAdUnit(switchRequestParams())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}
