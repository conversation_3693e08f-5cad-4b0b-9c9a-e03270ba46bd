declare namespace M {
  namespace AdUnit {
    interface params {
      page_info?: {
        offset?: number
        size?: number// 选填参数
      }
      ad_unit?: string
      platform?: string
    }

    interface Ad extends CreateAd {
      created: number
      updated: number
      updated_operator_name: string
    }

    interface ListResponse {
      list: ListItem[]
      page_info: PageInfo2
    }

    interface PriceConfig {
      c: string[]
      v: number
    }

    interface CreateAd {
      id?: number
      platform?: string
      priority?: 2
      name?: string
      price_config?: string
      ad_unit?: string
      price_config_list?: PriceConfig[]
      ad_platform?: string
    }
  }
}
