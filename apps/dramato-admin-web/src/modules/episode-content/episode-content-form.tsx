import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useEpisodeContents } from './use-episode-content.tsx'
import { manualContentKey } from '../episode-modules/options.ts'

export const EpisodeTagsForm = createComponent(null, () => {
  const {
    tags,
    tab,
    hideTagsFormDialog,
    onSave,
  } = useEpisodeContents()
  const Form = CreateForm<M.Banner>()

  const formRules = z.object({
    content_name: z.string().min(1, '请输入内容名称').max(50, '最多50个字符'),
    rule_note: z.string().min(1, '请输入规则备注'),
  })

  const { error, validateAll } = useValidator(tab, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            if (path === 'label_ids') {
              const ids = (value as string || '').split(',').map(Number)
              const labels = tags.value.filter(item => ids.includes(item.label_id))
              set(tab.value || {}, 'label_names', labels.map(item => item.content).join(','))
            }
            set(tab.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('内容源类型（前端页面默认 人工配置，不可修改）'),
              path: 'content_key',
              transform: transformInteger,
              input: {
                type: 'select',
                options: manualContentKey,
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('内容源配置'),
              path: 'content_name',
              transform: transformInteger,
              input: {
                type: 'select',
                options: manualContentKey,
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('内容源名称'),
              path: 'content_name',
              input: {
                type: 'text',
                placeholder: '请输入内容源名称',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('规则备注'),
              path: 'rule_note',
              input: {
                type: 'text',
                placeholder: '请输入规则备注',
              },
              class: 'col-span-3',
            },
          ]}
          data={tab.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideTagsFormDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave(tab.value as M.EpisodeContent.List.Item)
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
