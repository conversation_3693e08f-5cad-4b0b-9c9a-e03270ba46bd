/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Checkbox, DateTime, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, watch } from 'vue'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useEpisodeContents } from './use-episode-content'

type EpisodeTagsOptions = {
  props: {}
}
export const EpisodeTags = createComponent<EpisodeTagsOptions>({
  props: {},
}, () => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
  } = useEpisodeContents()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => params.value.app_id, {
    onSuccess: search,
  })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !params.value.app_id) {
      params.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => params.value.app_id, id => {
    params.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  onMounted(() => {

  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>模块内容源列表</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              void search()
            }}
            onSubmit={() => search()}
            data={params.value}
            items={[
              {
                label: requiredLabel('应用'),
                path: 'app_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  class: 'w-[240px]',
                  autoInsertEmptyOption: false,
                  options: appOptions.value,
                },
              },
              {
                label: requiredLabel('语言'),
                path: 'language',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: languageOptions.value,
                },
              },
            ]}
          />
        ),
        // tableActions: () => (
        // <div class="flex justify-between items-center">
        //   -
        //   <x-actions class="flex gap-x-2">
        //     <Button class="btn btn-primary btn-sm" disabled={checkedItems.value.length === 0} onClick={batchDelete}>批量删除</Button>
        //     <Button class="btn btn-primary btn-sm" onClick={() => showTagsFormDialog({}, '创建内容源')}>创建内容源</Button>
        //   </x-actions>
        // </div>
        // ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            // ['', row => (
            //   <Checkbox
            //     label=""
            //     modelValue={!!checkedItems.value.find(i => i.id === row.id)}
            //     onUpdate:modelValue={() => {
            //       const idx = checkedItems.value.findIndex(i => i.id === row.id)
            //       if (idx > -1) {
            //         checkedItems.value.splice(idx, 1)
            //       } else {
            //         checkedItems.value.push(row)
            //       }
            //     }}
            //   />
            // ), { class: 'w-[40px]' }],
            ['内容源key（复制）', 'id', { class: 'w-[100px]' }],
            ['内容源类型', 'content_key', { class: 'w-[100px]' }],
            ['内容源名称', 'content_name', { class: 'w-[100px]' }],
            ['规则备注', 'rule_note', { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.operator_name, { class: 'w-[100px]' }],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default EpisodeTags
