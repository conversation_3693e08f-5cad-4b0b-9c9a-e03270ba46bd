
import { httpClient } from 'src/lib/http-client'

export const apiGetEpisodeContentList = (data: M.EpisodeContent.List.Request) =>
  httpClient.post<ApiResponse< M.EpisodeContent.List.Response>>('/hometab/content/list', data)

export const apiUpdateEpisodeContentStatus = (data: M.EpisodeContent.UpdateStatus.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/tab/listing_status', data)

export const apiSaveEpisodeContent = (data: M.EpisodeContent.Save.Request) =>
  httpClient.post<ApiResponse<M.EpisodePricing.Save.Params>>('/hometab/tab/save', data)

export const apiDeleteEpisodeContent = (d: M.EpisodeContent.Delete.Request) =>
  httpClient.post<ApiResponse<boolean>>('/homepage/module_config/tab_content/delete', d)

export const apiSortEpisodeContent = (d: M.EpisodeContent.Sort.Request) =>
  httpClient.post<ApiResponse<boolean>>('/hometab/tab/sort/update', d)

export const apiEpisodeContentPreview = (d: M.EpisodeContent.PreviewTab.Request) =>
  httpClient.post<ApiResponse<M.EpisodeContent.PreviewTab.Response>>('/hometab/tab/tab_preview', d)

export const apiEpisodeContentContentPreview = (d: M.EpisodeContent.ContentPreviewTab.Request) =>
  httpClient.post<ApiResponse<M.EpisodeContent.ContentPreviewTab.Response>>('/hometab/tab/index_preview', d)
