declare namespace M {
  namespace EpisodeContent {
    namespace List {
      interface Request {
        app_id?: number
        language?: string
      }

      interface Item {
        id: number
        app_id: number // 应用 ID
        app_key: string // 应用 key
        platform: number // 平台 1-IOS 2-Android
        language: string // 语言类型
        content_name: string // 内容名称
        content_key: string // 内容 key
        rule_note: string // 规则备注
        operator_name: string // 修改人
        operator_id: number // 修改人 ID
        is_delete: number // 是否删除
        created: number // 创建时间
        updated: number // 更新时间
      }

      interface Response {
        list: Item[]
      }
    }

    namespace Save {
      interface Request extends List.Request {
        item: {
          id?: number
          platform: number // 平台 1-IOS 2-Android
          tab_name: string // 名称
          tab_index: number // 排序
          tab_key: string // 唯一key
          listing_status: number // 状态 1 上架 2 下架
        }
      }
    }

    namespace UpdateStatus {
      interface Request extends List.Request {
        ids: number[]
        is_up: number // 是否上架  1 上架 2 下架
      }
    }

    // TODO
    namespace GetSeriesCountRequest {
      interface Request extends List.Request {
        label_ids: string
      }

      interface Response {
        count: number
      }
    }

    namespace Sort {
      interface Request extends List.Request {
        curr_row_id: number
        to_sort_no: number // 目标位置
        to_pre_row_id: number // 目标位置的前一个元素
        to_next_row_id: number // 目标位置的后一个元素
      }
    }

    namespace PreviewTab {
      interface Request extends List.Request {}

      interface Response {
        tab_list: {
          tab_name: string
          tab_key: string
        }[]
      }
    }

    namespace ContentPreviewTab {
      interface Request extends List.Request {
        home_tab_info_id: number
      }

      interface Response {

      }
    }

    namespace Delete{
      interface Request extends List.Request {
        ids: string
      }
    }
  }
}
