/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiDeleteEpisodeContent, apiGetEpisodeContentList, apiUpdateEpisodeContentStatus } from './episode-content-api'
import { EpisodeTagsForm } from './episode-content-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useEpisodeContents = () => {
  return {
    tags,
    Form,
    params,
    Table,
    list,
    loading,
    search,
    changeTab,
    tab,
    checkedItems,
    batchDelete,
    batchUp,
    batchDown,
    showTagsFormDialog,
    hideTagsFormDialog,
    onSave,
  }
}

const Form = CreateForm<M.EpisodeContent.List.Request>()
const params = ref<M.EpisodeContent.List.Request>({})

const Table = CreateTableOld<M.EpisodeContent.List.Item>()
const list = ref<M.EpisodeContent.List.Item[]>([])
const loading = ref<boolean>(false)
const tags = ref<M.ITag[]>([
//   {
//     language_code: 'zh-CN',
//     label_id: 29,
//     content: '婚前恋爱',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 30,
//     content: '爱情',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 31,
//     content: '命运',
//   },
//   {
//     language_code: 'zh-CN',
//     label_id: 32,
//     content: '甜蜜恋爱',
//   },
])

const voidTab = {
  id: 0,
  platform: 1, // 平台 1-IOS 2-Android
  content_name: '', // 名称
  tab_index: 1, // 排序
  tab_key: '', // 唯一key
  listing_status: 1, // 状态 1 上架 2 下架
}
const tab = ref<Partial<M.EpisodeContent.List.Item>>({
  ...voidTab,
})

const checkedItems = ref<M.EpisodeContent.List.Item[]>([])

const getTags = async (language: string) => {
  // const rs = await apiGetLabelList({
  //   language,
  // })
  // tags.value = rs.data?.list || []
}

const search = async () => {
  loading.value = true
  void getTags(params.value.language || '')
  const res = await apiGetEpisodeContentList({
    ...params.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.list || []
  return list.value
}

const changeTab = (item: Partial<M.EpisodeContent.List.Item>) => {
  tab.value = Object.assign({}, voidTab, item)
}

const hideTagsFormDialog = ref()

const showTagsFormDialog = (d: Partial<M.EpisodeContent.List.Item>, title: string) => {
  changeTab(d)
  hideTagsFormDialog.value = openDialog({
    title,
    body: () => <EpisodeTagsForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const onSave = (d: M.EpisodeContent.List.Item) => {
  // void apiSaveEpisodeContent({
  //   app_id: params.value.app_id,
  //   language: params.value.language,
  //   item: d,
  // }).then(d => {
  //   console.log('>>> d')

  //   hideTagsFormDialog.value && hideTagsFormDialog.value()
  //   void search()
  // }).catch((error: any) => {
  //   showAlert(error.response.data.message || '保存失败', 'error')
  // })
}

const batchUp = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认上架tab【{checkedItems.value.map(item => item.content_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeContentStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 1,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDown = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认下架tab【{checkedItems.value.map(item => item.content_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiUpdateEpisodeContentStatus({
                ids: checkedItems.value.map(item => item.id || 0),
                is_up: 2,
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}

const batchDelete = () => {
  const showTipsDialog = () => {
    const hideDialog = openDialog({
      title: '',
      mainClass: 'pb-0 px-5',
      body: (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否确认删除tab【{checkedItems.value.map(item => item.content_name).join('、')}】?</x-status-body>
          <x-status-footer class="w-full flex justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={() => {
              void apiDeleteEpisodeContent({
                ids: checkedItems.value.map(item => item.id).join(','),
              })
                .then(() => {
                  void search()
                })
                .catch((error: any) => {
                  showAlert(error.response.data.message, 'error')
                })
              hideDialog()
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  showTipsDialog()
}
