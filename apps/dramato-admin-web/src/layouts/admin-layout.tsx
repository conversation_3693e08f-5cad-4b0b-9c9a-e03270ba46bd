import { createComponent } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { LeftAside } from 'src/modules/left-aside/left-aside'
import { TopBar } from 'src/modules/top-bar/top-bar'
import { Component, KeepAlive } from 'vue'
import { RouterView, useRoute } from 'vue-router'
type AdminLayoutOptions = {
  props: {}
}
type RouterViewSlot = {
  Component: Component
}
export const AdminLayout = createComponent<AdminLayoutOptions>({
  name: 'TopLeftMainRightLayout',
  props: {},
}, () => {
  const route = useRoute()
  // if (route.meta.title) {
  //   document.title = route.meta.title
  // }
  return () => (
    <MergeClass baseClass="block bg-fill-2 min-h-screen relative">
      <header class="sticky top-0 z-top-bar block h-top-bar bg-fill-4 shadow">
        <RouterView name="top">
          {{
            default: ({ Component }: RouterViewSlot) => Component ? Component : <TopBar />,
          }}
        </RouterView>
      </header>
      <div class="relative flex flex-nowrap">
        <aside class="sticky top-top-bar h-[calc(100vh_-_var(--top-bar-height))] w-[var(--left-aside-width)] shrink-0 grow-0 overflow-auto">
          <RouterView name="left">
            {{
              default: ({ Component }: RouterViewSlot) => Component ? Component : <LeftAside />,
            }}
          </RouterView>
        </aside>
        <main class="relative max-w-[calc(100vw_-_var(--left-aside-width)_-_20px)] shrink grow px-4">
          <RouterView>
            {{
              default: ({ Component }: RouterViewSlot) => Component ? <KeepAlive include={['ShortDrama', 'ContentTemplate', 'ContentEvaluate']}>{Component}</KeepAlive> : null,
            }}
          </RouterView>
        </main>
        <RouterView name="right">
          {{
            default: ({ Component }: RouterViewSlot) => Component
              ? (
                  <aside class="shrink-0 grow-0">
                    {Component}
                  </aside>
                )
              : null,
          }}
        </RouterView>
      </div>
    </MergeClass>
  )
})
