@import './reset.css';
@import './vars.css';
@import './dark-mode-for-app.css';
@import './color-scheme.css';
@import './components.css';
@import './daisy-modify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* color-scheme: light, night; */
}


body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  /* 改为 tailwind 写法 */
  @apply antialiased bg-[var(--fill-4)] text-[var(--text-1)];
}

:where(.tm-no-dark) {
  @apply bg-[var(--fill-4)] text-[var(--text-1)];
}


body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Arial,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    sans-serif;
}

html {
  min-height: 100%;
  background-color: var(--fill-4);
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.no-tap-color {
  -webkit-tap-highlight-color: transparent;
  /* iOS Chrome */
  -ms-tap-highlight-color: transparent;
  /* IE */
  -moz-tap-highlight-color: transparent;
  /* Firefox */
  -o-tap-highlight-color: transparent;
  /* Opera */
}

.tm-shadow {
  box-shadow: 0 10px 15px -3px #0a66c220, 0 4px 6px -4px #0a66c220;
}

.no-dark {
  background-color: #fff !important;
  color: #141414 !important;

  * {
    background-color: #fff !important;
    color: #141414 !important;
  }
}
input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
input[type="color"]::-webkit-color-swatch {
  border: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

[widgetid='editor.contrib.resizableContentHoverWidget'] { margin-top: 75px; }

.prism-cue {
  @apply line-clamp-3 text-white;
  /*
   * 这里使用clamp()函数设置字体大小，它接受三个参数：最小值、首选值和最大值
   * 16px: 字体的最小尺寸，确保在小屏幕上文字不会太小而难以阅读
   * 2vw: 字体的首选尺寸，会随视窗宽度变化，2vw表示视窗宽度的2%
   * 23px: 字体的最大尺寸，确保在大屏幕上文字不会过大
   * !important: 提高此样式的优先级，确保它会覆盖其他可能的字体大小设置
   */
  font-size: clamp(12px, 23px, 30px) !important;
  top: 58.8% !important;
  padding: 0 7.5% !important;
  max-height: 96px !important;
  overflow: hidden;
}

.prism-cue div {
  background-color: transparent !important;
}

.prism-cue div {
  color: white; /* 文字颜色 */
  text-shadow:
    -1px -1px 0 black,
    1px -1px 0 black,
    -1px 1px 0 black,
    1px 1px 0 black;
}

.subtitle-highlight {
  @apply bg-primary text-white
}
