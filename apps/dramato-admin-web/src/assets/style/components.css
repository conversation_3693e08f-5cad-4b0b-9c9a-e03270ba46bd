.hide-scrollbar {
  -ms-overflow-style: none; /* for Internet Explorer, Edge */
  scrollbar-width: none; /* for Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
  @apply hidden;
}

.tm-table-fix-last-column {
  @apply [&_tr]:relative
    [&_td:last-child]:sticky [&_td:last-child]:bg-white [&_td:last-child]:right-0
    [&_th:last-child]:sticky [&_th:last-child]:bg-white [&_th:last-child]:right-0;
}
.tm-table-fix-first-column {
  @apply [&_tr]:relative
    [&_td:first-child]:sticky [&_td:first-child]:bg-white [&_td:first-child]:left-0 [&_td:first-child]:z-[3]
    [&_th:first-child]:sticky [&_th:first-child]:bg-white [&_th:first-child]:left-0 z-[3];
}

.tm-table-fix-header {
  @apply
    [&_tr]:relative
    [&_td:first-child]:sticky [&_td:first-child]:bg-white [&_td:first-child]:left-0
    [&_th:first-child]:sticky [&_th:first-child]:bg-white [&_th:first-child]:left-0 z-[2];

  /* 使thead固定在页面顶部 */
  thead {
    position: sticky;
    top: 0;
    background-color: white; /* 你可以设置背景色，避免与滚动内容重叠 */
    z-index: 1; /* 确保thead在其他内容上方 */
  }
}

.highlight-subtitle {
  @apply bg-primary font-bold !text-white
}


.spend-board-material.multiselect {
  min-height: 32px;
  .multiselect__select {
    height: 32px;
  }
  .multiselect__tags {
    min-height: 32px;
    padding-top: 3px;
    .multiselect__placeholder {
      margin-bottom: 4px;
    }
  }
}