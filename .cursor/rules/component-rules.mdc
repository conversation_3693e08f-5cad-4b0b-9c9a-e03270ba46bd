---
description: 组件编写规范
globs: *.tsx
alwaysApply: false
---

# 组件编写规范

标准组件写法：

```tsx
import { createComponent, getQuery, on } from '@skynet/shared'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { CT } from '../i18n/common.i18n'
import { useSeriesItem } from './use-series-item'
type Options = {
  // 这里是 props 的类型定义
  props: {
    propOne: string
  }
  // 这里是 emits 的类型定义，可选
  emits: {
    change: (v: string) => void
  } 
  // 这里是 slots 的类型定义，可选
  slots: {
    default: () => VNodeChild
  } 
}
export const SomePage = createComponent<Options>({
  props: {
    propOne: '1' // 这里是 props 的默认值
  },
}, (props, {emit}) => {
  const route = useRoute()
  const id = computed(() => route.params.id.toString() ?? '')
  const { item, fetchItem, fetchingItem } = useSeriesItem(id.value)
  onMounted(async () => {
    await fetchItem()
  })
  return () => (
    <div>{CT.hi()}</div>
  )
})
export default SomePage

```
