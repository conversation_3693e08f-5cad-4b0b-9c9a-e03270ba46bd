---
description: Shala Player Class
globs: *.tsx,*.ts
alwaysApply: false
---

Class: shaka.Player
The main player object for Shaka Player.
Constructor
new Player(mediaElementopt, videoContaineropt, dependencyInjectoropt)
The main player object for Shaka Player.

Parameters:
Name	Type	Attributes	Default	Description
mediaElement	HTMLMediaElement	<optional>
When provided, the player will attach to mediaElement, similar to calling attach. When not provided, the player will remain detached.
videoContainer	HTMLElement	<optional>
null	The videoContainer to construct UITextDisplayer
dependencyInjector	function	<optional>
Optional callback which is called to inject mocks into the Player. Used for testing.
Implements:
shaka.util.IDestroyable
Source:
lib/player.js, line 589
Members
LoadMode :number
In order to know what method of loading the player used for some content, we have this enum. It lets us know if content has not been loaded, loaded with media source, or loaded with src equals. This enum has a low resolution, because it is only meant to express the outer limits of the various states that the player is in. For example, when someone calls a public method on player, it should not matter if they have initialized drm engine, it should only matter if they finished loading content.
Type:
number
Properties:
Name	Value	Type	Description
DESTROYED	0	number	
NOT_LOADED	1	number	
MEDIA_SOURCE	2	number	
SRC_EQUALS	3	number	
Source:
lib/player.js, line 8441
version :string
A version number taken from git at compile time.
Type:
string
Source:
lib/player.js, line 8466
Methods
isBrowserSupported() → {boolean}
Return whether the browser provides basic support. If this returns false, Shaka Player cannot be used at all. In this case, do not construct a Player instance and do not use the library.
Source:
lib/player.js, line 1112
Returns:
Type
boolean
probeSupport(promptsOkayopt) → {Promise.<shaka.extern.SupportType>}
Probes the browser to determine what features are supported. This makes a number of requests to EME/MSE/etc which may result in user prompts. This should only be used for diagnostics.
NOTE: This may show a request to the user for permission.

Parameters:
Name	Type	Attributes	Default	Description
promptsOkay	boolean	<optional>
true	
Source:
lib/player.js, line 1153
See:
https://bit.ly/2ywccmH
Returns:
Type
Promise.<shaka.extern.SupportType>
registerSupportPlugin(name, callback)
Registers a plugin callback that will be called with support(). The callback will return the value that will be stored in the return value from support().
Parameters:
Name	Type	Description
name	string	
callback	function():*	
Source:
lib/player.js, line 1089
setAdManagerFactory(factorynon-null)
Set a factory to create an ad manager during player construction time. This method needs to be called before instantiating the Player class.
Parameters:
Name	Type	Description
factory	shaka.extern.IAdManager.Factory	
Source:
lib/player.js, line 1100
addChaptersTrack(uri, language, mimeTypeopt) → {Promise.<shaka.extern.Track>}
Adds the given chapters track to the loaded manifest. load() must resolve before calling. The presentation must have a duration. This returns the created track.
Parameters:
Name	Type	Attributes	Description
uri	string		
language	string		
mimeType	string	<optional>
Source:
lib/player.js, line 6271
Returns:
Type
Promise.<shaka.extern.Track>
addFont(name, url)
Load a new font on the page. If the font was already loaded, it does nothing.
Parameters:
Name	Type	Description
name	string	
url	string	
Source:
lib/player.js, line 7892
addTextTrackAsync(uri, language, kind, mimeTypeopt, codecopt, labelopt, forcedopt) → {Promise.<shaka.extern.Track>}
Adds the given text track to the loaded manifest. load() must resolve before calling. The presentation must have a duration. This returns the created track, which can immediately be selected by the application. The track will not be automatically selected.
Parameters:
Name	Type	Attributes	Default	Description
uri	string			
language	string			
kind	string			
mimeType	string	<optional>
codec	string	<optional>
label	string	<optional>
forced	boolean	<optional>
false	
Source:
lib/player.js, line 5962
Returns:
Type
Promise.<shaka.extern.Track>
addThumbnailsTrack(uri, mimeTypeopt) → {Promise.<shaka.extern.Track>}
Adds the given thumbnails track to the loaded manifest. load() must resolve before calling. The presentation must have a duration. This returns the created track, which can immediately be used by the application.
Parameters:
Name	Type	Attributes	Description
uri	string		
mimeType	string	<optional>
Source:
lib/player.js, line 6112
Returns:
Type
Promise.<shaka.extern.Track>
attach(mediaElementnon-null, initializeMediaSourceopt) → {Promise}
Attaches the player to a media element. If the player was already attached to a media element, first detaches from that media element.
Parameters:
Name	Type	Attributes	Default	Description
mediaElement	HTMLMediaElement			
initializeMediaSource	boolean	<optional>
true	
Source:
lib/player.js, line 1203
Returns:
Type
Promise
attachCanvas(canvas)
Calling attachCanvas will tell the player to set canvas element for LCEVC decoding.
Parameters:
Name	Type	Description
canvas	HTMLCanvasElement	
Source:
lib/player.js, line 1253
cancelTrickPlay()
Cancel trick-play. If the player has not loaded content or is still loading content this will be a no-op.
Source:
lib/player.js, line 4755
configurationForLowLatency(confignon-null)
Changes low latency configuration settings on the Player.
Parameters:
Name	Type	Description
config	Object	This object should follow the shaka.extern.PlayerConfiguration object. Not all fields need to be set; unset fields retain their old values.
Source:
lib/player.js, line 4132
configure(config, valueopt) → {boolean}
Changes configuration settings on the Player. This checks the names of keys and the types of values to avoid coding errors. If there are errors, this logs them to the console and returns false. Correct fields are still applied even if there are other errors. You can pass an explicit undefined value to restore the default value. This has two modes of operation:
First, this can be passed a single "plain" object. This object should follow the shaka.extern.PlayerConfiguration object. Not all fields need to be set; unset fields retain their old values.

Second, this can be passed two arguments. The first is the name of the key to set. This should be a '.' separated path to the key. For example, 'streaming.alwaysStreamText'. The second argument is the value to set.

Parameters:
Name	Type	Attributes	Description
config	string | Object		This should either be a field name or an object.
value	*	<optional>
In the second mode, this is the value to set.
Source:
lib/player.js, line 3869
Returns:
True if the passed config object was valid, false if there were invalid entries.
Type
boolean
destroy() → {Promise}
After destruction, a Player object cannot be used again.
Implements:
shaka.util.IDestroyable#destroy
Source:
lib/player.js, line 1014
Returns:
Type
Promise
destroyAllPreloads()
Calls |destroy| on each PreloadManager object this player has created.
Source:
lib/player.js, line 2002
detach(keepAdManageropt) → {Promise}
Detach the player from the current media element. Leaves the player in a state where it cannot play media, until it has been attached to something else.
Parameters:
Name	Type	Attributes	Default	Description
keepAdManager	boolean	<optional>
false	
Source:
lib/player.js, line 1267
Returns:
Type
Promise
detachAndSavePreload(keepAdManageropt, saveLivePositionopt) → {Promise.<?shaka.media.PreloadManager>}
Detach the player from the current media element, if any, and returns a PreloadManager that contains the loaded manifest of that asset, if any. Allows for the asset to be re-loaded by this player faster, in the future. When in src= mode, this detach but does not make a PreloadManager. Leaves the player in a state where it cannot play media, until it has been attached to something else.
Parameters:
Name	Type	Attributes	Default	Description
keepAdManager	boolean	<optional>
false	
saveLivePosition	boolean	<optional>
false	
Source:
lib/player.js, line 1910
Returns:
Type
Promise.<?shaka.media.PreloadManager>
drmInfo() → {shaka.extern.DrmInfo}
Get the drm info used to initialize EME. If EME is not being used, this will return null. If the player is idle or has not initialized EME yet, this will return null.
Source:
lib/player.js, line 4622
Returns:
Type
shaka.extern.DrmInfo
getActiveSessionsMetadata() → {Array.<shaka.extern.DrmSessionMetadata>}
Returns the active sessions metadata
Source:
lib/player.js, line 4658
Returns:
Type
Array.<shaka.extern.DrmSessionMetadata>
getAdManager() → {shaka.extern.IAdManager}
Returns a shaka.ads.AdManager instance, responsible for Dynamic Ad Insertion functionality.
Source:
lib/player.js, line 4439
Returns:
Type
shaka.extern.IAdManager
getAllThumbnails(trackId) → {Promise.<?Array.<!shaka.extern.Thumbnail>>}
Returns Thumbnail objects for each thumbnail for a given image track ID. If the player has not loaded content, this will return a null.
Parameters:
Name	Type	Description
trackId	number	
Source:
lib/player.js, line 4884
Returns:
Type
Promise.<?Array.<!shaka.extern.Thumbnail>>
getAssetUri() → {string}
Get the uri to the asset that the player has loaded. If the player has not loaded content, this will return null.
Source:
lib/player.js, line 4428
Returns:
Type
string
getAudioLanguages() → {Array.<string>}
Return a list of audio languages available. If the player has not loaded any content, this will return an empty list.
Source:
lib/player.js, line 5286
Returns:
Type
Array.<string>
getAudioLanguagesAndRoles() → {Array.<shaka.extern.LanguageRole>}
Return a list of audio language-role combinations available. If the player has not loaded any content, this will return an empty list.
Source:
lib/player.js, line 5264
Returns:
Type
Array.<shaka.extern.LanguageRole>
getBufferedInfo() → {shaka.extern.BufferedInfo}
Get information about what the player has buffered. If the player has not loaded content or is currently loading content, the buffered content will be empty.
Source:
lib/player.js, line 5811
Returns:
Type
shaka.extern.BufferedInfo
getBufferFullness() → {number}
Returns the ratio of video length buffered compared to buffering Goal
Source:
lib/player.js, line 4338
Returns:
Type
number
getChapters(language) → {Array.<shaka.extern.Chapter>}
This returns the list of chapters.
Parameters:
Name	Type	Description
language	string	
Source:
lib/player.js, line 5567
Returns:
Type
Array.<shaka.extern.Chapter>
getChaptersTracks() → {Array.<shaka.extern.Track>}
Return a list of chapters tracks.
Source:
lib/player.js, line 5550
Returns:
Type
Array.<shaka.extern.Track>
getConfiguration() → {shaka.extern.PlayerConfiguration}
Return a copy of the current configuration. Modifications of the returned value will not affect the Player's active configuration. You must call player.configure() to make changes.
Source:
lib/player.js, line 4282
Returns:
Type
shaka.extern.PlayerConfiguration
getConfigurationForLowLatency() → {Object}
Return a copy of the current configuration for low latency.
Source:
lib/player.js, line 4297
Returns:
Type
Object
getExpiration() → {number}
Get the next known expiration time for any EME session. If the session never expires, this will return Infinity. If there are no EME sessions, this will return Infinity. If the player has not loaded content, this will return Infinity.
Source:
lib/player.js, line 4648
Returns:
Type
number
getFetchedPlaybackInfo() → {shaka.extern.PlaybackInfo}
Gets information about the currently fetched video, audio, and text. In the case of a multi-codec or multi-mimeType manifest, this can let you determine the exact codecs and mimeTypes being fetched at the moment.
Source:
lib/player.js, line 6533
Returns:
Type
shaka.extern.PlaybackInfo
getImageTracks() → {Array.<shaka.extern.Track>}
Return a list of image tracks that can be switched to. If the player has not loaded content, this will return an empty list.
Source:
lib/player.js, line 4866
Returns:
Type
Array.<shaka.extern.Track>
getKeyStatuses() → {Object.<string, string>}
Gets a map of EME key ID to the current key status.
Source:
lib/player.js, line 4668
Returns:
Type
Object.<string, string>
getLoadMode() → {shaka.Player.LoadMode}
Get the current load mode.
Source:
lib/player.js, line 4382
Returns:
Type
shaka.Player.LoadMode
getManifest() → {shaka.extern.Manifest}
Get the manifest that the player has loaded. If the player has not loaded any content, this will return null. NOTE: This structure is NOT covered by semantic versioning compatibility guarantees. It may change at any time! This is marked as deprecated to warn Closure Compiler users at compile-time to avoid using this method.
Deprecated:
Yes
Source:
lib/player.js, line 6505
Returns:
Type
shaka.extern.Manifest
getManifestParserFactory() → {shaka.extern.ManifestParser.Factory}
Get the type of manifest parser that the player is using. If the player has not loaded any content, this will return null.
Source:
lib/player.js, line 6521
Returns:
Type
shaka.extern.ManifestParser.Factory
getManifestType() → {string}
Get the current manifest type.
Source:
lib/player.js, line 4392
Returns:
Type
string
getMediaElement() → {HTMLMediaElement}
Get the media element that the player is currently using to play loaded content. If the player has not loaded content, this will return null.
Source:
lib/player.js, line 4407
Returns:
Type
HTMLMediaElement
getNetworkingEngine() → {shaka.net.NetworkingEngine}
Source:
lib/player.js, line 4417
Returns:
A reference to the Player's networking engine. Applications may use this to make requests through Shaka's networking plugins.
Type
shaka.net.NetworkingEngine
getNonDefaultConfiguration() → {Object}
Return a copy of the current non default configuration. Modifications of the returned value will not affect the Player's active configuration. You must call player.configure() to make changes.
Source:
lib/player.js, line 4309
Returns:
Type
Object
getPlaybackRate() → {number}
Get the playback rate of what is playing right now. If we are using trick play, this will return the trick play rate. If no content is playing, this will return 0. If content is buffering, this will return the expected playback rate once the video starts playing.
If the player has not loaded content, this will return a playback rate of 0.

Source:
lib/player.js, line 4701
Returns:
Type
number
getPlayheadTimeAsDate() → {Date}
Get the current playhead position as a date.
Source:
lib/player.js, line 5713
Returns:
Type
Date
getPresentationStartTimeAsDate() → {Date}
Get the presentation start time as a date.
Source:
lib/player.js, line 5755
Returns:
Type
Date
getSegmentAvailabilityDuration() → {number}
Get the presentation segment availability duration. This should only be called when the player has loaded a live stream. If the player has not loaded a live stream, this will return null.
Source:
lib/player.js, line 5788
Returns:
Type
number
getStats() → {shaka.extern.Stats}
Get statistics for the current playback session. If the player is not playing content, this will return an empty stats object.
Source:
lib/player.js, line 5855
Returns:
Type
shaka.extern.Stats
getTextLanguages() → {Array.<string>}
Return a list of text languages available. If the player has not loaded any content, this will return an empty list.
Source:
lib/player.js, line 5297
Returns:
Type
Array.<string>
getTextLanguagesAndRoles() → {Array.<shaka.extern.LanguageRole>}
Return a list of text language-role combinations available. If the player has not loaded any content, this will be return an empty list.
Source:
lib/player.js, line 5275
Returns:
Type
Array.<shaka.extern.LanguageRole>
getTextTracks() → {Array.<shaka.extern.Track>}
Return a list of text tracks that can be switched to.
If the player has not loaded content, this will return an empty list.

Source:
lib/player.js, line 4834
Returns:
Type
Array.<shaka.extern.Track>
getThumbnails(trackId, time) → {Promise.<?shaka.extern.Thumbnail>}
Return a Thumbnail object from a image track Id and time. If the player has not loaded content, this will return a null.
Parameters:
Name	Type	Description
trackId	number	
time	number	
Source:
lib/player.js, line 4962
Returns:
Type
Promise.<?shaka.extern.Thumbnail>
getVariantTracks() → {Array.<shaka.extern.Track>}
Return a list of variant tracks that can be switched to.
If the player has not loaded content, this will return an empty list.

Source:
lib/player.js, line 4778
Returns:
Type
Array.<shaka.extern.Track>
goToLive()
Go to live in a live stream.
Source:
lib/player.js, line 4584
isAudioOnly() → {boolean}
Check if the manifest contains only audio-only content. If the player has not loaded content, this will return false.
The player does not support content that contain more than one type of variants (i.e. mixing audio-only, video-only, audio-video). Content will be filtered to only contain one type of variant.

Source:
lib/player.js, line 4499
Returns:
Type
boolean
isBuffering() → {boolean}
Check if the player is currently in a buffering state (has too little content to play smoothly). If the player has not loaded content, this will return false.
Source:
lib/player.js, line 4680
Returns:
Type
boolean
isEnded() → {boolean}
Indicate if the video has ended.
Source:
lib/player.js, line 8418
Returns:
Type
boolean
isFullyLoaded() → {boolean}
Indicates if the player has fully loaded the stream.
Source:
lib/player.js, line 4598
Returns:
Type
boolean
isInProgress() → {boolean}
Get if the player is playing in-progress content. If the player has not loaded content, this will return false.
Source:
lib/player.js, line 4481
Returns:
Type
boolean
isLive() → {boolean}
Get if the player is playing live content. If the player has not loaded content, this will return false.
Source:
lib/player.js, line 4461
Returns:
Type
boolean
isRemotePlayback() → {boolean}
Indicate if we are using remote playback.
Source:
lib/player.js, line 8405
Returns:
Type
boolean
isTextTrackVisible() → {boolean}
Check if the text displayer is enabled.
Source:
lib/player.js, line 5529
Returns:
Type
boolean
keySystem() → {string}
Get the key system currently used by EME. If EME is not being used, this will return an empty string. If the player has not loaded content, this will return an empty string.
Source:
lib/player.js, line 4610
Returns:
Type
string
load(assetUriOrPreloader, startTimeopt, nullable, mimeTypeopt, nullable) → {Promise}
Loads a new stream. If another stream was already playing, first unloads that stream.
Parameters:
Name	Type	Attributes	Default	Description
assetUriOrPreloader	string | shaka.media.PreloadManager			
startTime	number	<optional>
<nullable>
null	When startTime is null or undefined, playback will start at the default start time (0 for VOD and liveEdge for LIVE).
mimeType	string	<optional>
<nullable>
Source:
lib/player.js, line 1581
Returns:
Type
Promise
preload(assetUri, startTimeopt, nullable, mimeTypeopt, nullable) → {Promise.<?shaka.media.PreloadManager>}
Starts to preload a given asset, and returns a PreloadManager object that represents that preloading process. The PreloadManager will load the manifest for that asset, as well as the initialization segment. It will not preload anything more than that; this feature is intended for reducing start-time latency, not for fully downloading assets before playing them (for that, use |shaka.offline.Storage|). You can pass that PreloadManager object in to the |load| method on this Player instance to finish loading that particular asset, or you can call the |destroy| method on the manager if the preload is no longer necessary. If this returns null rather than a PreloadManager, that indicates that the asset must be played with src=, which cannot be preloaded.
Parameters:
Name	Type	Attributes	Default	Description
assetUri	string			
startTime	number	<optional>
<nullable>
null	When startTime is null or undefined, playback will start at the default start time (0 for VOD and liveEdge for LIVE).
mimeType	string	<optional>
<nullable>
Source:
lib/player.js, line 1984
Returns:
Type
Promise.<?shaka.media.PreloadManager>
releaseAllMutexes()
Releases all of the mutexes of the player. Meant for use by the tests.
Source:
lib/player.js, line 3426
resetConfiguration()
Reset configuration to default.
Source:
lib/player.js, line 4363
retryStreaming(retryDelaySecondsopt) → {boolean}
Retry streaming after a streaming failure has occurred. When the player has not loaded content or is loading content, this will be a no-op and will return false.
If the player has loaded content, and streaming has not seen an error, this will return false.

If the player has loaded content, and streaming seen an error, but the could not resume streaming, this will return false.

Parameters:
Name	Type	Attributes	Default	Description
retryDelaySeconds	number	<optional>
0.1	
Source:
lib/player.js, line 6485
Returns:
Type
boolean
seekRange() → {{start: number, end: number}}
Get the range of time (in seconds) that seeking is allowed. If the player has not loaded content and the manifest is HLS, this will return a range from 0 to 0.
Source:
lib/player.js, line 4540
Returns:
Type
{start: number, end: number}
selectAudioLanguage(language, roleopt, channelsCountopt, safeMarginopt, codecopt, spatialAudioopt, labelopt)
Sets the current audio language and current variant role to the selected language, role and channel count, and chooses a new variant if need be. If the player has not loaded any content, this will be a no-op.
Parameters:
Name	Type	Attributes	Default	Description
language	string			
role	string	<optional>
channelsCount	number	<optional>
0	
safeMargin	number	<optional>
0	
codec	string	<optional>
spatialAudio	boolean	<optional>
false	
label	string	<optional>
Source:
lib/player.js, line 5315
selectTextLanguage(language, roleopt, forcedopt)
Sets the current text language and current text role to the selected language and role, and chooses a new variant if need be. If the player has not loaded any content, this will be a no-op.
Parameters:
Name	Type	Attributes	Default	Description
language	string			
role	string	<optional>
forced	boolean	<optional>
false	
Source:
lib/player.js, line 5404
selectTextTrack(track)
Select a specific text track. track should come from a call to getTextTracks. If the track is not found, this will be a no-op. If the player has not loaded content, this will be a no-op.
Note that AdaptationEvents are not fired for manual track selections.

Parameters:
Name	Type	Description
track	shaka.extern.Track	
Source:
lib/player.js, line 5067
selectVariantsByLabel(label, clearBufferopt, safeMarginopt)
Select variant tracks that have a given label. This assumes the label uniquely identifies an audio stream, so all the variants are expected to have the same variant.audio.
Parameters:
Name	Type	Attributes	Default	Description
label	string			
clearBuffer	boolean	<optional>
true	Optional clear buffer or not when switch to new variant Defaults to true if not provided
safeMargin	number	<optional>
0	Optional amount of buffer (in seconds) to retain when clearing the buffer. Defaults to 0 if not provided. Ignored if clearBuffer is false.
Source:
lib/player.js, line 5458
selectVariantTrack(track, clearBufferopt, safeMarginopt)
Select a specific variant track to play. track should come from a call to getVariantTracks. If track cannot be found, this will be a no-op. If the player has not loaded content, this will be a no-op.
Changing variants will take effect once the currently buffered content has been played. To force the change to happen sooner, use clearBuffer with safeMargin. Setting clearBuffer to true will clear all buffered content after safeMargin, allowing the new variant to start playing sooner.

Note that AdaptationEvents are not fired for manual track selections.

Parameters:
Name	Type	Attributes	Default	Description
track	shaka.extern.Track			
clearBuffer	boolean	<optional>
false	
safeMargin	number	<optional>
0	Optional amount of buffer (in seconds) to retain when clearing the buffer. Useful for switching variant quickly without causing a buffering event. Defaults to 0 if not provided. Ignored if clearBuffer is false. Can cause hiccups on some browsers if chosen too small, e.g. The amount of two segments is a fair minimum to consider as safeMargin value.
Source:
lib/player.js, line 5178
setMaxHardwareResolution(width, height)
Set the maximum resolution that the platform's hardware can handle.
Parameters:
Name	Type	Description
width	number	
height	number	
Source:
lib/player.js, line 6463
setTextTrackVisibility(isVisible)
Enable or disable the text displayer. If the player is in an unloaded state, the request will be applied next time content is loaded.
Parameters:
Name	Type	Description
isVisible	boolean	
Source:
lib/player.js, line 5653
setVideoContainer(videoContainer)
Set the videoContainer to construct UITextDisplayer.
Parameters:
Name	Type	Description
videoContainer	HTMLElement	
Source:
lib/player.js, line 6632
trickPlay(rate, useTrickPlayTrackopt)
Enable trick play to skip through content without playing by repeatedly seeking. For example, a rate of 2.5 would result in 2.5 seconds of content being skipped every second. A negative rate will result in moving backwards.
If the player has not loaded content or is still loading content this will be a no-op. Wait until load has completed before calling.

Trick play will be canceled automatically if the playhead hits the beginning or end of the seekable range for the content.

Parameters:
Name	Type	Attributes	Default	Description
rate	number			
useTrickPlayTrack	boolean	<optional>
true	
Source:
lib/player.js, line 4728
unload(initializeMediaSourceopt, keepAdManageropt) → {Promise}
Unloads the currently playing stream, if any.
Parameters:
Name	Type	Attributes	Default	Description
initializeMediaSource	boolean	<optional>
true	
keepAdManager	boolean	<optional>
false	
Source:
lib/player.js, line 1326
Returns:
Type
Promise
unloadAndSavePreload(initializeMediaSourceopt, keepAdManageropt) → {Promise.<?shaka.media.PreloadManager>}
Unloads the currently playing stream, if any, and returns a PreloadManager that contains the loaded manifest of that asset, if any. Allows for the asset to be re-loaded by this player faster, in the future. When in src= mode, this unloads but does not make a PreloadManager.
Parameters:
Name	Type	Attributes	Default	Description
initializeMediaSource	boolean	<optional>
true	
keepAdManager	boolean	<optional>
false	
Source:
lib/player.js, line 1890
Returns:
Type
Promise.<?shaka.media.PreloadManager>
updateStartTime(startTime)
Provides a way to update the stream start position during the media loading process. Can for example be called from the manifestparsed event handler to update the start position based on information in the manifest.
Parameters:
Name	Type	Description
startTime	number	
Source:
lib/player.js, line 1564
Events
AbrStatusChangedEvent
Fired when the state of abr has been changed. (Enabled or disabled).
Properties:
Name	Type	Description
type	string	'abrstatuschanged'
newStatus	boolean	The new status of the application. True for 'is enabled' and false otherwise.
Source:
lib/player.js, line 397
AdaptationEvent
Fired when an automatic adaptation causes the active tracks to change. Does not fire when the application calls selectVariantTrack(), selectTextTrack(), selectAudioLanguage(), or selectTextLanguage().
Properties:
Name	Type	Description
type	string	'adaptation'
oldTrack	shaka.extern.Track	
newTrack	shaka.extern.Track	
Source:
lib/player.js, line 289
AudioTrackChangedEvent
Fired when the audio track changes at the playhead. That may be caused by a user requesting to chang audio tracks.
Properties:
Name	Type	Description
type	string	'audiotrackchanged'
mediaQuality	shaka.extern.MediaQualityInfo	Information about media quality at the playhead position.
position	number	The playhead position.
Source:
lib/player.js, line 210
BufferingEvent
Fired when the player's buffering state changes.
Properties:
Name	Type	Description
type	string	'buffering'
buffering	boolean	True when the Player enters the buffering state. False when the Player leaves the buffering state.
Source:
lib/player.js, line 224
Complete
Fires when the content completes playing. Only for VoD.
Properties:
Name	Type	Description
type	string	'complete'
Source:
lib/player.js, line 538
DownloadCompleted
Fired when a download has completed.
Properties:
Name	Type	Description
type	string	'downloadcompleted'
request	shaka.extern.Request	
response	shaka.extern.Response	
Source:
lib/player.js, line 109
DownloadFailed
Fired when a download has failed, for any reason. 'downloadfailed'
Properties:
Name	Type	Attributes	Description
request	shaka.extern.Request		
error	shaka.util.Error	<nullable>
httpResponseCode	number		
aborted	boolean		
Source:
lib/player.js, line 120
DownloadHeadersReceived
Fired when the networking engine has received the headers for a download, but before the body has been downloaded. If the HTTP plugin being used does not track this information, this event will default to being fired when the body is received, instead.
Properties:
Name	Type	Description
headers	Object.<string, string>	
request	shaka.extern.Request	
type	shaka.net.NetworkingEngine.RequestType	'downloadheadersreceived'
Source:
lib/player.js, line 132
DrmSessionUpdateEvent
Fired when the CDM has accepted the license response.
Properties:
Name	Type	Description
type	string	'drmsessionupdate'
Source:
lib/player.js, line 146
EmsgEvent
Fired when an emsg box is found in a segment. If the application calls preventDefault() on this event, further parsing will not happen, and no 'metadata' event will be raised for ID3 payloads.
Properties:
Name	Type	Description
type	string	'emsg'
detail	shaka.extern.EmsgInfo	An object which contains the content of the emsg box.
Source:
lib/player.js, line 96
ErrorEvent
Fired when a playback error occurs.
Properties:
Name	Type	Description
type	string	'error'
detail	shaka.util.Error	An object which contains details on the error. The error's category and code properties will identify the specific error that occurred. In an uncompiled build, you can also use the message and stack properties to debug.
Source:
lib/player.js, line 73
ExpirationUpdatedEvent
Fired when there is a change in the expiration times of an EME session.
Properties:
Name	Type	Description
type	string	'expirationupdated'
Source:
lib/player.js, line 332
FirstQuartile
Fires when the content playhead crosses first quartile. Only for VoD.
Properties:
Name	Type	Description
type	string	'firstquartile'
Source:
lib/player.js, line 508
GapJumpedEvent
Fired when the GapJumpingController jumps over a gap in the buffered ranges. An app may want to look at getStats() to see what happened.
Properties:
Name	Type	Description
type	string	'gapjumped'
Source:
lib/player.js, line 467
KeyStatusChanged
Fired when the key status changed.
Properties:
Name	Type	Description
type	string	'keystatuschanged'
Source:
lib/player.js, line 478
LoadedEvent
Fired when the player ends the load.
Properties:
Name	Type	Description
type	string	'loaded'
Source:
lib/player.js, line 247
LoadingEvent
Fired when the player begins loading. The start of loading is defined as when the user has communicated intent to load content (i.e. Player.load has been called).
Properties:
Name	Type	Description
type	string	'loading'
Source:
lib/player.js, line 236
ManifestParsedEvent
Fired after the manifest has been parsed, but before anything else happens. The manifest may contain streams that will be filtered out, at this stage of the loading process.
Properties:
Name	Type	Description
type	string	'manifestparsed'
Source:
lib/player.js, line 342
ManifestUpdatedEvent
Fired after the manifest has been updated (live streams).
Properties:
Name	Type	Description
type	string	'manifestupdated'
isLive	boolean	True when the playlist is live. Useful to detect transition from live to static playlist..
Source:
lib/player.js, line 353
MediaQualityChangedEvent
Fired when the media quality changes at the playhead. That may be caused by an adaptation change or a DASH period transition. Separate events are emitted for audio and video contentTypes.
Properties:
Name	Type	Description
type	string	'mediaqualitychanged'
mediaQuality	shaka.extern.MediaQualityInfo	Information about media quality at the playhead position.
position	number	The playhead position.
Source:
lib/player.js, line 187
MediaSourceRecoveredEvent
Fired when MediaSource has been successfully recovered after occurrence of video error.
Properties:
Name	Type	Description
type	string	'mediasourcerecovered'
Source:
lib/player.js, line 201
MetadataEvent
Triggers after metadata associated with the stream is found. Usually they are metadata of type ID3.
Properties:
Name	Type	Attributes	Description
type	string		'metadata'
startTime	number		The time that describes the beginning of the range of the metadata to which the cue applies.
endTime	number	<nullable>
The time that describes the end of the range of the metadata to which the cue applies.
metadataType	string		Type of metadata. Eg: 'org.id3' or 'com.apple.quicktime.HLS'
payload	shaka.extern.MetadataFrame		The metadata itself
Source:
lib/player.js, line 365
Midpoint
Fires when the content playhead crosses midpoint. Only for VoD.
Properties:
Name	Type	Description
type	string	'midpoint'
Source:
lib/player.js, line 518
NoSpatialVideoInfoEvent
Fired when the video no longer has spatial video information. For it to be fired, the shaka.Player.SpatialVideoInfoEvent event must have been previously fired.
Properties:
Name	Type	Description
type	string	'nospatialvideoinfo'
Source:
lib/player.js, line 560
ProducerReferenceTimeEvent
Fired when the content includes ProducerReferenceTime (PRFT) info.
Properties:
Name	Type	Description
type	string	'prft'
detail	shaka.extern.ProducerReferenceTime	An object which contains the content of the PRFT box.
Source:
lib/player.js, line 571
RateChangeEvent
Fired when the video's playback rate changes. This allows the PlayRateController to update it's internal rate field, before the UI updates playback button with the newest playback rate.
Properties:
Name	Type	Description
type	string	'ratechange'
Source:
lib/player.js, line 410
SegmentAppended
Fired when a segment is appended to the media element.
Properties:
Name	Type	Description
type	string	'segmentappended'
start	number	The start time of the segment.
end	number	The end time of the segment.
contentType	string	The content type of the segment. E.g. 'video', 'audio', or 'text'.
isMuxed	boolean	Indicates if the segment is muxed (audio + video).
Source:
lib/player.js, line 421
SessionDataEvent
Fired when the manifest parser find info about session data. Specification: https://tools.ietf.org/html/rfc8216#section-*******
Properties:
Name	Type	Description
type	string	'sessiondata'
id	string	The id of the session data.
uri	string	The uri with the session data info.
language	string	The language of the session data.
value	string	The value of the session data.
Source:
lib/player.js, line 438
SpatialVideoInfoEvent
Fired when the video has spatial video info. If a previous event was fired, this include the new info.
Properties:
Name	Type	Description
type	string	'spatialvideoinfo'
detail	shaka.extern.SpatialVideoInfo	An object which contains the content of the emsg box.
Source:
lib/player.js, line 548
StallDetectedEvent
Fired when a stall in playback is detected by the StallDetector. Not all stalls are caused by gaps in the buffered ranges. An app may want to look at getStats() to see what happened.
Properties:
Name	Type	Description
type	string	'stalldetected'
Source:
lib/player.js, line 456
Started
Fires when the content starts playing. Only for VoD.
Properties:
Name	Type	Description
type	string	'started'
Source:
lib/player.js, line 498
StateChanged
Fired when player state is changed.
Properties:
Name	Type	Description
type	string	'statechanged'
newstate	string	The new state.
Source:
lib/player.js, line 487
StateChangeEvent
Fired when the player changes load states.
Properties:
Name	Type	Description
type	string	'onstatechange'
state	string	The name of the state that the player just entered.
Source:
lib/player.js, line 86
StreamingEvent
Fired after the manifest has been parsed and track information is available, but before streams have been chosen and before any segments have been fetched. You may use this event to configure the player based on information found in the manifest.
Properties:
Name	Type	Description
type	string	'streaming'
Source:
lib/player.js, line 385
TextChangedEvent
Fired when a call from the application caused a text stream change. Can be triggered by calls to selectTextTrack() or selectTextLanguage(). An app may want to look at getStats() or getTextTracks() to see what happened.
Properties:
Name	Type	Description
type	string	'textchanged'
Source:
lib/player.js, line 319
TextTrackVisibilityEvent
Fired when text track visibility changes. An app may want to look at getStats() or getVariantTracks() to see what happened.
Properties:
Name	Type	Description
type	string	'texttrackvisibility'
Source:
lib/player.js, line 266
ThirdQuartile
Fires when the content playhead crosses third quartile. Only for VoD.
Properties:
Name	Type	Description
type	string	'thirdquartile'
Source:
lib/player.js, line 528
TimelineRegionAddedEvent
Fired when a media timeline region is added.
Properties:
Name	Type	Description
type	string	'timelineregionadded'
detail	shaka.extern.TimelineRegionInfo	An object which contains a description of the region.
Source:
lib/player.js, line 155
TimelineRegionEnterEvent
Fired when the playhead enters a timeline region.
Properties:
Name	Type	Description
type	string	'timelineregionenter'
detail	shaka.extern.TimelineRegionInfo	An object which contains a description of the region.
Source:
lib/player.js, line 166
TimelineRegionExitEvent
Fired when the playhead exits a timeline region.
Properties:
Name	Type	Description
type	string	'timelineregionexit'
detail	shaka.extern.TimelineRegionInfo	An object which contains a description of the region.
Source:
lib/player.js, line 177
TracksChangedEvent
Fired when the list of tracks changes. For example, this will happen when new tracks are added/removed or when track restrictions change. An app may want to look at getVariantTracks() to see what happened.
Properties:
Name	Type	Description
type	string	'trackschanged'
Source:
lib/player.js, line 277
UnloadingEvent
Fired when the player unloads or fails to load. Used by the Cast receiver to determine idle state.
Properties:
Name	Type	Description
type	string	'unloading'
Source:
lib/player.js, line 256
VariantChangedEvent
Fired when a call from the application caused a variant change. Can be triggered by calls to selectVariantTrack() or selectAudioLanguage(). Does not fire when an automatic adaptation causes a variant change. An app may want to look at getStats() or getVariantTracks() to see what happened.
Properties:
Name	Type	Description
type	string	'variantchanged'
oldTrack	shaka.extern.Track	
newTrack	shaka.extern.Track	
Source:
lib/player.js, line 303

Documentation generated by JSDoc 3.6.10 on Wed Mar 05 2025 10:14:57 GMT+0000 (Coordinated Universal Time)