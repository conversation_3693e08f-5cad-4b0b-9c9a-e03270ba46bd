---
description: Shaka Player Usage
globs: *.tsx, *.ts
alwaysApply: false
---
---
description: shaka player usage
globs: *.tsx
alwaysApply: false
---
# Basic Usage

Basic usage of Shaka Player is very easy:

Start with Welcome to Shaka Player and compile the library.
Create a simple HTML page with a video or audio element.
In your application's JavaScript -
Install Shaka's polyfills.
Check for browser support.
Create a Player object to wrap the media element.
Listen for errors.
Load a manifest.

```html
<!DOCTYPE html>
<html>
  <head>
    <!-- Shaka Player compiled library: -->
    <script src="dist/shaka-player.compiled.js"></script>
    <!-- Your application source: -->
    <script src="myapp.js"></script>
  </head>
  <body>
    <video id="video"
           width="640"
           poster="//shaka-player-demo.appspot.com/assets/poster.jpg"
           controls autoplay></video>
  </body>
</html>

```
```js
// myapp.js

const manifestUri =
    'https://storage.googleapis.com/shaka-demo-assets/angel-one/dash.mpd';

function initApp() {
  // Install built-in polyfills to patch browser incompatibilities.
  shaka.polyfill.installAll();

  // Check to see if the browser supports the basic APIs Shaka needs.
  if (shaka.Player.isBrowserSupported()) {
    // Everything looks good!
    initPlayer();
  } else {
    // This browser does not have the minimum set of APIs we need.
    console.error('Browser not supported!');
  }
}

async function initPlayer() {
  // Create a Player instance.
  const video = document.getElementById('video');
  const player = new shaka.Player();
  await player.attach(video);

  // Attach player to the window to make it easy to access in the JS console.
  window.player = player;

  // Listen for error events.
  player.addEventListener('error', onErrorEvent);

  // Try to load a manifest.
  // This is an asynchronous process.
  try {
    await player.load(manifestUri);
    // This runs if the asynchronous load is successful.
    console.log('The video has now been loaded!');
  } catch (e) {
    // onError is executed if the asynchronous load fails.
    onError(e);
  }
}

function onErrorEvent(event) {
  // Extract the shaka.util.Error object from the event.
  onError(event.detail);
}

function onError(error) {
  // Log the error.
  console.error('Error code', error.code, 'object', error);
}

document.addEventListener('DOMContentLoaded', initApp);
```

That's it!


# Configuration

The goal of this tutorial is to introduce Shaka's configuration system and the concepts on which it is built. More detail can be found in the API docs.

Shaka's Player object has a hierarchical configuration. The overall player config contains sub-configs for various parts of the system, such as manifests, streaming, and DRM.

To see the current config, you can use player.getConfiguration(). If you run this without setting anything first, you get the default configuration.

Player also has a configure() method that takes it's argument(s) in one of two forms:

A plain, anonymous object. Any fields you leave out of the config object will retain their existing values, and any fields you explicitly set as undefined will be reverted to their default value. For example:

```js
player.configure({
  streaming: {
    bufferingGoal: 120
  }
});
```

For setting a single field, a name (full path) of the field can be provided as the first argument (a string) and value of the field as the second. For example:
player.configure('streaming.bufferingGoal', 120);
You can use the code from Basic Usage and try these examples in the JS console:

```js
player.getConfiguration();

=> Object
     abr: Object
       bandwidthDowngradeTarget: 0.95
       bandwidthUpgradeTarget: 0.85
       defaultBandwidthEstimate: 500000
       enabled: true
       restrictions: Object
       switchInterval: 8
     abrFactory: Function
     drm: Object
       advanced: Object
       clearKeys: Object
       retryParameters: Object
         backoffFactor: 2
         baseDelay: 1000
         fuzzFactor: 0.5
         maxAttempts: 2
         timeout: 0
       servers: Object
     manifest: Object
       dash: Object
       retryParameters: Object
     playRangeEnd: Infinity
     playRangeStart: 0
     preferredAudioLanguage: ""
     preferredAudioLabel: ""
     preferredVideoLabel: ""
     preferredTextLanguage: ""
     restrictions: Object
     streaming: Object
       bufferBehind: 30
       bufferingGoal: 10
       durationBackoff: 1
       failureCallback: Function
       ignoreTextStreamFailures: false
       alwaysStreamText: false
       rebufferingGoal: 0
       retryParameters: Object
       startAtSegmentBoundary: false
       safeSeekOffset: 5
       segmentPrefetchLimit: 0
     textDisplayFactory: Function
```


```js
// Example configurations, all of which are arbitrary:

// set audio language preference to Canadian French:
player.configure('preferredAudioLanguage', 'fr-CA');
// NOTE: language preferences affect the next call to load()

// set text language preference to Greek and buffering goal to 2 minutes:
player.configure({
  preferredTextLanguage: 'el',
  streaming: {
    bufferingGoal: 120
  }
});

// check audio language preference, which is still Canadian French:
player.getConfiguration().preferredAudioLanguage

// check buffering goal, which is 2 minutes:
player.getConfiguration().streaming.bufferingGoal

// check rebuffering goal, which is still the default of 0 seconds:
player.getConfiguration().streaming.rebufferingGoal

// set the rebuffering goal to 15 seconds and revert buffering goal to default:
player.configure({
  streaming: {
    bufferingGoal: undefined,
    rebufferingGoal: 15
  }
});
```

Some of these fields have immediate effects (such as networking settings and buffering settings) while some will not have any effect until the next call to load() (such as DRM settings, manifest settings, and language settings).

Low latency streaming
With .streaming.lowLatencyMode set to true and the manifest is Low Latency, Shaka uses a Low Latency config: .streaming.inaccurateManifestTolerance is set to 0 by default, .streaming.segmentPrefetchLimit is set to 2 by default, .streaming.updateIntervalSeconds is set to 0.1 by default, .streaming.maxDisabledTime is set to 1 by default, .streaming.retryParameters.baseDelay is set to 100 by default, .manifest.dash.autoCorrectDrift is set to false by default, .manifest.retryParameters.baseDelay is set to 100 by default, and .drm.retryParameters.baseDelay is set to 100 by default.

To customize the values of inaccurateManifestTolerance, rebufferingGoal, segmentPrefetchLimit, updateIntervalSeconds and baseDelay with low latency mode, you can set the fields in the call to configurationForLowLatency().

```js
player.configurationForLowLatency({
  streaming: {
    inaccurateManifestTolerance: 0,
    segmentPrefetchLimit: 2,
    updateIntervalSeconds: 0.1,
    maxDisabledTime: 1,
    retryParameters: {
      baseDelay: 100,
    },
  },
  manifest: {
    dash: {
      autoCorrectDrift: true,
    },
    retryParameters: {
      baseDelay: 100,
    },
  },
  drm: {
    retryParameters: {
      baseDelay: 100,
    },
  },
});
```

## Detailed API Docs
For more detail on individual configuration options, please see the API docs for shaka.extern.PlayerConfiguration and shaka.Player#configure.


# API List

## Classes

shaka.abr.SimpleAbrManager
shaka.ads.AdManager
shaka.ads.ClientSideAd
shaka.ads.InterstitialAd
shaka.ads.InterstitialStaticAd
shaka.ads.MediaTailorAd
shaka.ads.ServerSideAd
shaka.ads.Utils
shaka.cast.CastProxy
shaka.cast.CastReceiver
shaka.cea.CeaDecoder
shaka.cea.Mp4CeaParser
shaka.cea.TsCeaParser
shaka.dash.DashParser
shaka.dependencies
shaka.hls.HlsParser
shaka.lcevc.Dec
shaka.log
shaka.media.AdaptationSet
shaka.media.ClosedCaptionParser
shaka.media.InitSegmentReference
shaka.media.ManifestParser
shaka.media.MetaSegmentIndex
shaka.media.PreloadManager
shaka.media.PresentationTimeline
shaka.media.SegmentIndex
shaka.media.SegmentIterator
shaka.media.SegmentReference
shaka.mss.MssParser
shaka.net.DataUriPlugin
shaka.net.HttpFetchPlugin
shaka.net.HttpPluginUtils
shaka.net.HttpXHRPlugin
shaka.net.NetworkingEngine
shaka.net.NetworkingEngine.NumBytesRemainingClass
shaka.net.NetworkingEngine.PendingRequest
shaka.offline.OfflineScheme
shaka.offline.Storage
shaka.offline.StorageMuxer
shaka.Player
shaka.polyfill
shaka.polyfill.Aria
shaka.polyfill.EncryptionScheme
shaka.polyfill.Fullscreen
shaka.polyfill.MediaCapabilities
shaka.polyfill.MediaSource
shaka.polyfill.Orientation
shaka.polyfill.PatchedMediaKeysApple
shaka.polyfill.PatchedMediaKeysCert
shaka.polyfill.PatchedMediaKeysWebkit
shaka.polyfill.PiPWebkit
shaka.polyfill.RandomUUID
shaka.polyfill.Symbol
shaka.polyfill.VideoPlaybackQuality
shaka.polyfill.VideoPlayPromise
shaka.polyfill.VTTCue
shaka.text.Cue
shaka.text.CueRegion
shaka.text.LrcTextParser
shaka.text.Mp4TtmlParser
shaka.text.Mp4VttParser
shaka.text.SbvTextParser
shaka.text.SimpleTextDisplayer
shaka.text.SrtTextParser
shaka.text.SsaTextParser
shaka.text.StubTextDisplayer
shaka.text.TextEngine
shaka.text.TtmlTextParser
shaka.text.UITextDisplayer
shaka.text.VttTextParser
shaka.text.WebVttGenerator
shaka.transmuxer.AacTransmuxer
shaka.transmuxer.Ac3Transmuxer
shaka.transmuxer.Ec3Transmuxer
shaka.transmuxer.Mp3Transmuxer
shaka.transmuxer.MpegTsTransmuxer
shaka.transmuxer.MssTransmuxer
shaka.transmuxer.TransmuxerEngine
shaka.transmuxer.TsTransmuxer
shaka.ui.AdCounter
shaka.ui.AdPosition
shaka.ui.AdStatisticsButton
shaka.ui.AirPlayButton
shaka.ui.AudioLanguageSelection
shaka.ui.BigPlayButton
shaka.ui.CastButton
shaka.ui.ChapterSelection
shaka.ui.ContextMenu
shaka.ui.Controls
shaka.ui.Element
shaka.ui.FastForwardButton
shaka.ui.FullscreenButton
shaka.ui.HiddenFastForwardButton
shaka.ui.HiddenRewindButton
shaka.ui.HiddenSeekButton
shaka.ui.Localization
shaka.ui.LoopButton
shaka.ui.MuteButton
shaka.ui.OverflowMenu
shaka.ui.Overlay
shaka.ui.PipButton
shaka.ui.PlaybackRateSelection
shaka.ui.PlayButton
shaka.ui.PresentationTimeTracker
shaka.ui.RangeElement
shaka.ui.RecenterVRButton
shaka.ui.RemoteButton
shaka.ui.ResolutionSelection
shaka.ui.RewindButton
shaka.ui.SaveVideoFrameButton
shaka.ui.SeekBar
shaka.ui.SeekBar.Factory
shaka.ui.SettingsMenu
shaka.ui.SkipAdButton
shaka.ui.SmallPlayButton
shaka.ui.Spacer
shaka.ui.StatisticsButton
shaka.ui.TextSelection
shaka.ui.ToggleStereoscopicButton
shaka.ui.VolumeBar
shaka.ui.Watermark
shaka.util.AbortableOperation
shaka.util.BufferUtils
shaka.util.CmsdManager
shaka.util.ConfigUtils
shaka.util.DataViewReader
shaka.util.Dom
shaka.util.Error
shaka.util.EventManager
shaka.util.ExpGolomb
shaka.util.FairPlayUtils
shaka.util.FakeEvent
shaka.util.FakeEventTarget
shaka.util.Id3Utils
shaka.util.LanguageUtils
shaka.util.MimeUtils
shaka.util.Mp4Parser
shaka.util.PeriodCombiner
shaka.util.PlayerConfiguration
shaka.util.StreamUtils
shaka.util.StringUtils
shaka.util.Timer
shaka.util.TsParser
shaka.util.Uint8ArrayUtils

## Enums

shaka.config.AutoShowText
shaka.config.CodecSwitchingStrategy

## Interfaces

shaka.extern.AbrManager
shaka.extern.Error
shaka.extern.IAbortableOperation
shaka.extern.IAd
shaka.extern.IAdManager
shaka.extern.ICaptionDecoder
shaka.extern.ICeaParser
shaka.extern.IUIElement
shaka.extern.IUIElement.Factory
shaka.extern.IUIPlayButton
shaka.extern.IUIRangeElement
shaka.extern.IUISeekBar
shaka.extern.IUISeekBar.Factory
shaka.extern.IUISettingsMenu
shaka.extern.ManifestParser
shaka.extern.SegmentIndex
shaka.extern.TextDisplayer
shaka.extern.TextParser
shaka.extern.Transmuxer
shaka.media.AdaptationSetCriteria
shaka.media.IClosedCaptionParser
shaka.util.IDestroyable
shaka.util.IReleasable

## Events

shaka.ads.AdManager.AdBufferingEvent
shaka.ads.AdManager.AdClickedEvent
shaka.ads.AdManager.AdClickEvent
shaka.ads.AdManager.AdClosedEvent
shaka.ads.AdManager.AdCompleteEvent
shaka.ads.AdManager.AdContentPauseRequestedEvent
shaka.ads.AdManager.AdContentResumeRequestedEvent
shaka.ads.AdManager.AdCuePointsChangedEvent
shaka.ads.AdManager.AdDurationChangedEvent
shaka.ads.AdManager.AdFirstQuartileEvent
shaka.ads.AdManager.AdImpressionEvent
shaka.ads.AdManager.AdInteractionEvent
shaka.ads.AdManager.AdLinearChangedEvent
shaka.ads.AdManager.AdLoadedEvent
shaka.ads.AdManager.AdMetadataEvent
shaka.ads.AdManager.AdMidpointEvent
shaka.ads.AdManager.AdMutedEvent
shaka.ads.AdManager.AdPausedEvent
shaka.ads.AdManager.AdProgressEvent
shaka.ads.AdManager.AdRecoverableErrorEvent
shaka.ads.AdManager.AdResumedEvent
shaka.ads.AdManager.AdSkippedEvent
shaka.ads.AdManager.AdSkipStateChangedEvent
shaka.ads.AdManager.AdsLoadedEvent
shaka.ads.AdManager.AdStartedEvent
shaka.ads.AdManager.AdStoppedEvent
shaka.ads.AdManager.AdThirdQuartileEvent
shaka.ads.AdManager.AdVolumeChangedEvent
shaka.ads.AdManager.AllAdsCompletedEvent
shaka.ads.AdManager#AdBreakReadyEvent
shaka.ads.AdManager#ImaAdManagerLoadedEvent
shaka.ads.AdManager#ImaStreamManagerLoadedEvent
shaka.ads.Utils.AD_ERROR
shaka.cast.CastProxy.CastStatusChangedEvent
shaka.net.NetworkingEngine.RetryEvent
shaka.Player.AbrStatusChangedEvent
shaka.Player.AdaptationEvent
shaka.Player.AudioTrackChangedEvent
shaka.Player.BufferingEvent
shaka.Player.Complete
shaka.Player.DownloadCompleted
shaka.Player.DownloadFailed
shaka.Player.DownloadHeadersReceived
shaka.Player.DrmSessionUpdateEvent
shaka.Player.EmsgEvent
shaka.Player.ErrorEvent
shaka.Player.ExpirationUpdatedEvent
shaka.Player.FirstQuartile
shaka.Player.GapJumpedEvent
shaka.Player.KeyStatusChanged
shaka.Player.LoadedEvent
shaka.Player.LoadingEvent
shaka.Player.ManifestParsedEvent
shaka.Player.ManifestUpdatedEvent
shaka.Player.MediaQualityChangedEvent
shaka.Player.MediaSourceRecoveredEvent
shaka.Player.MetadataEvent
shaka.Player.Midpoint
shaka.Player.NoSpatialVideoInfoEvent
shaka.Player.ProducerReferenceTimeEvent
shaka.Player.RateChangeEvent
shaka.Player.SegmentAppended
shaka.Player.SessionDataEvent
shaka.Player.SpatialVideoInfoEvent
shaka.Player.StallDetectedEvent
shaka.Player.Started
shaka.Player.StateChanged
shaka.Player.StateChangeEvent
shaka.Player.StreamingEvent
shaka.Player.TextChangedEvent
shaka.Player.TextTrackVisibilityEvent
shaka.Player.ThirdQuartile
shaka.Player.TimelineRegionAddedEvent
shaka.Player.TimelineRegionEnterEvent
shaka.Player.TimelineRegionExitEvent
shaka.Player.TracksChangedEvent
shaka.Player.UnloadingEvent
shaka.Player.VariantChangedEvent
shaka.ui.Controls#CaptionSelectionUpdatedEvent
shaka.ui.Controls#CastStatusChangedEvent
shaka.ui.Controls#ErrorEvent
shaka.ui.Controls#LanguageSelectionUpdatedEvent
shaka.ui.Controls#ResolutionSelectionUpdatedEvent
shaka.ui.Controls#SubMenuOpenEvent
shaka.ui.Controls#TimeAndSeekRangeUpdatedEvent
shaka.ui.Controls#UIUpdatedEvent
shaka.ui.Controls#VRStatusChangedEvent
shaka.ui.Localization.LocaleChangedEvent
shaka.ui.Localization.MissingLocalizationsEvent
shaka.ui.Localization.UnknownLocalesEvent


