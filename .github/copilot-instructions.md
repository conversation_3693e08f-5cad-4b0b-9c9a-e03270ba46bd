# Alias - 快捷方式

* gc 表示 "generate commit message using Chinese, then apply it to the current file"

# 代码规范

* 能用 TypeScript 也能用 JavaScript 的时候，必须使用 TypeScript 而不是 JavaScript
* 在上文中出现过的代码不要重复出现在下文中，用内容为“与上文相同”的注释来代替即可
* 不允许缩写，比如 password 不能缩写为 pwd。只有两种情况例外
  1. 外部标识符，比如后台返回的数据字段为 pwd，那么前端也可以用 pwd
  2. 缩写已经是规范用法，比如 html、css 等

# commit message 规范

* 用中文写 commit message，输出 commit message 时候，把所有内容写在 code block 中
* 不需要添加 "feat:" "chore:" 之类的前缀
* 如果只有一个更新点，那么就只写标题
* 如果有多个更新点，那么格式为：标题，然后空行，最后列出所有更新。不需要出现"更新列表""更新"之类的字样，尽量简洁准确，示例：
  ```
  修复了 xxx 问题

  - 添加了 a 属性
  - 当 b 属性为空时隐藏弹出内容
  ```

# 代码逻辑

* 如果要把一个请求和一个 loading 绑定，那么应该使用 bindLoading 函数
